//状态：0-草稿，10-已发布（待审核），20-审核通过，30-被拒绝，40-生效中，50-已失效
const typeState: any = {
  0: {
    text: '草稿',
    color: '#ff6348',
  },
  10: {
    text: '已发布（待审核）',
    color: '#fac03d',
  },
  20: {
    text: '审核通过(待生效)',
    color: '#2ecc71',
  },
  30: {
    text: '被拒绝',
    color: '#ff4757',
  },
  40: {
    text: '生效',
    color: '#1abc9c',
  },
  50: {
    text: '失效',
    color: '#a4b0be',
  },
};

//包裹值
const typePackageList: any = [
  {
    value: 0,
    label: '不限',
  },
  {
    value: 1,
    label: '包裹',
  },
  {
    value: 2,
    label: '袋子',
  },
  {
    value: 4,
    label: '文件',
  },
];

//包裹类型
const typePackageType: any = {
  0: '不限',
  1: '包裹',
  2: '袋子',
  4: '文件',
};
/* 位运算 计算包裹类型值 */
function calculateValue(types: number[]) {
  let value = 0;

  for (const type of types) {
    value += type;
  }

  return value;
}
/* 回显方式2 */
function getCheckedValues(
  value: number,
  options: { label: string; value: number }[],
) {
  const binaryValue = value.toString(2); // 将输入值转换为二进制形式
  const checkedValues: number[] = [];
  if (value === 0) {
    checkedValues.push(0);
    return checkedValues;
  }
  options.forEach((option) => {
    const binaryOptionValue = option.value.toString(2); // 将选项的值转换为二进制形式
    if ((parseInt(binaryValue, 2) & parseInt(binaryOptionValue, 2)) !== 0) {
      // 按位与操作
      checkedValues.push(option.value);
    }
  });
  return checkedValues;
}

const optionDay = () => {
  const arr = [];
  for (let i = 1; i <= 61; i++) {
    arr.push({
      value: i,
      label: i + '天',
    });
  }
  return arr;
};

/* 产品类目 1-自营专线，2-同行专线，3-商业快递 */
const typeCategory: any = [
  {
    value: 1,
    label: '自营专线',
  },
  {
    value: 2,
    label: '同行专线',
  },
  {
    value: 3,
    label: '商业快递',
  },
];
/* 运输类型 1-空运，2-海运，3-卡车 */
const typeTransport: any = [
  {
    value: 1,
    label: '空运',
  },
  {
    value: 2,
    label: '海运',
  },
  {
    value: 3,
    label: '卡车',
  },
  {
    value: 4,
    label: '铁路',
  },
];
/* 计费模式 */
//0-件总重，1-票总重，2-件进位总重，3-每件进位票总重，4-每件票总重，5-每件票总重-分泡
const chargeWeightModeType: any = {
  0: '件总重',
  1: '票总重',
  2: '件进位总重',
  3: '每件进位票总重',
  4: '每件票总重',
  5: '每件票总重-分泡',
};

/* 货物类型 */
const typeCargoType: any = {
  10: '普货',
  20: '带磁',
  30: '带电',
  40: '带磁带电',
};

/* waybillFeeList  feeCategory */
const feeCategoryType: any = {
  10: '基础运费',
  20: '附加费',
  30: '服务费',
  40: '燃油费',
  50: '优惠费',
};
const feeTypes: any = {
  10: '非单独报关',
  20: '单独报关',
  30: '合并报关',
  40: '上门取件',
  50: '自行寄送',
  60: '空运磁检',
  70: '仓库服务',
};

export {
  typeState,
  typePackageList,
  typePackageType,
  optionDay,
  typeCategory,
  typeTransport,
  calculateValue,
  getCheckedValues,
  chargeWeightModeType,
  typeCargoType,
  feeCategoryType,
  feeTypes,
};
