.tabsWarp {
  padding: 0;
  margin: 0;

  :global {
    .ant-tabs-nav {
      display: none !important;
    }



    // .keep-alive-tabs {
    //   .ant-tabs-nav {
    //     margin: 0;
    //     // display: none;
    //   }
    // }

    // .ant-pro .ant-pro-layout .ant-pro-layout-content {
    //   padding: 0;
    // }

  }
}

.keep-alive-tabs {
  margin-top: 8px;

  :global {
    .ant-tabs-tab {
      border: none !important;
    }

    .ant-tabs-nav::before {
      border-bottom: none !important
    }

    .ant-tabs-nav {
      width: 90% !important;
    }
  }
}

/* src/styles.css */

:global {
  .ck-editor__editable_inline {
    min-height: 520px;
  }
}


// :global {
//   :where(.css-dev-only-do-not-override-iuzfft).ant-pro-layout .ant-pro-layout-content {
//     padding-inline: 0px !important
//   }
// }