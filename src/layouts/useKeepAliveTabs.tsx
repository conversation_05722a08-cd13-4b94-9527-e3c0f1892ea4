import { useCallback, useEffect, useState, useRef } from 'react';
import { useMatchRoute } from './useMatchRoute';
import { history } from 'umi';

export interface KeepAliveTab {
  title: string;
  routePath: string;
  key: string; // 这个key，后面刷新有用到它
  pathname: string;
  icon?: any;
  children: any;
  uniqueId?: string; // 添加新字段用于唯一标识
}

function getKey() {
  return new Date().getTime().toString();
}

export function useKeepAliveTabs() {
  const [keepAliveTabs, setKeepAliveTabs] = useState<KeepAliveTab[]>([]);
  const [activeTabRoutePath, setActiveTabRoutePath] = useState<string>('');
  const keepAliveShowEvents = useRef<Record<string, Array<() => void>>>({});
  const keepAliveHiddenEvents = useRef<Record<string, Array<() => void>>>({});

  const matchRoute = useMatchRoute();

  const onShow = useCallback(
    (cb: () => void) => {
      if (!keepAliveShowEvents.current[activeTabRoutePath]) {
        keepAliveShowEvents.current[activeTabRoutePath] = [];
      }
      keepAliveShowEvents.current[activeTabRoutePath].push(cb);
    },
    [activeTabRoutePath],
  );

  const onHidden = useCallback(
    (cb: () => void) => {
      if (!keepAliveHiddenEvents.current[activeTabRoutePath]) {
        keepAliveHiddenEvents.current[activeTabRoutePath] = [];
      }
      keepAliveHiddenEvents.current[activeTabRoutePath].push(cb);
    },
    [activeTabRoutePath],
  );

  // 关闭tab
  const closeTab = useCallback(
    (targetKey: string = activeTabRoutePath) => {
      // 查找标签时同时考虑 routePath 和 uniqueId
      const index = keepAliveTabs.findIndex(
        (o) => o.routePath === targetKey || o.uniqueId === targetKey,
      );

      if (index === -1) return; // 如果没找到对应的标签，直接返回

      const currentTab = keepAliveTabs[index];
      const tabKey = currentTab.uniqueId || currentTab.routePath;

      if (tabKey === activeTabRoutePath) {
        if (index > 0) {
          const prevTab = keepAliveTabs[index - 1];
          history.push(prevTab.pathname);
        } else if (keepAliveTabs.length > 1) {
          const nextTab = keepAliveTabs[index + 1];
          history.push(nextTab.pathname);
        }
      }

      keepAliveTabs.splice(index, 1);

      // 清除事件监听
      if (currentTab.uniqueId) {
        delete keepAliveHiddenEvents.current[currentTab.uniqueId];
        delete keepAliveShowEvents.current[currentTab.uniqueId];
      }
      delete keepAliveHiddenEvents.current[currentTab.routePath];
      delete keepAliveShowEvents.current[currentTab.routePath];

      setKeepAliveTabs([...keepAliveTabs]);
    },
    [activeTabRoutePath, keepAliveTabs],
  );

  // 关闭其他
  const closeOtherTab = useCallback(
    (targetKey: string = activeTabRoutePath) => {
      const toKeepTab = keepAliveTabs.find(
        (o) => o.routePath === targetKey || o.uniqueId === targetKey,
      );

      if (!toKeepTab) return;

      const toCloseTabs = keepAliveTabs.filter(
        (o) => o.routePath !== targetKey && o.uniqueId !== targetKey,
      );

      // 清除被关闭的tab注册的事件
      toCloseTabs.forEach((tab) => {
        if (tab.uniqueId) {
          delete keepAliveHiddenEvents.current[tab.uniqueId];
          delete keepAliveShowEvents.current[tab.uniqueId];
        }
        delete keepAliveHiddenEvents.current[tab.routePath];
        delete keepAliveShowEvents.current[tab.routePath];
      });

      setKeepAliveTabs([toKeepTab]);
    },
    [activeTabRoutePath],
  );

  // 刷新tab
  const refreshTab = useCallback(
    (routePath: string = activeTabRoutePath) => {
      setKeepAliveTabs((prev) => {
        const index = prev.findIndex((tab) => tab.routePath === routePath);
        if (index >= 0) {
          // 这个react的特性，key变了，组件会卸载重新渲染
          prev[index].key = getKey();
        }

        delete keepAliveHiddenEvents.current[prev[index]?.routePath];
        delete keepAliveShowEvents.current[prev[index]?.routePath];

        return [...prev];
      });
    },
    [activeTabRoutePath],
  );

  useEffect(() => {
    if (!matchRoute) return;

    if (matchRoute.routePath === '/report/particulars') {
      // 从 pathname 中提取 id 参数
      const urlParams = new URLSearchParams(matchRoute.pathname.split('?')[1]);
      const id = urlParams.get('id');

      // 使用 id 作为唯一标识和标题
      const uniqueId = id || matchRoute.pathname;
      const existSamePathTab = keepAliveTabs.find(
        (o) => o.uniqueId === uniqueId,
      );

      setActiveTabRoutePath(uniqueId);
      if (!existSamePathTab) {
        setKeepAliveTabs((prev) => [
          ...prev,
          {
            title: id || matchRoute.title, // 使用 id 作为标题
            key: getKey(),
            routePath: matchRoute.routePath,
            pathname: matchRoute.pathname,
            children: matchRoute.children,
            icon: matchRoute.icon,
            uniqueId,
          },
        ]);
      } else {
        (keepAliveShowEvents.current[uniqueId] || []).forEach((cb) => {
          cb();
        });
      }
    } else {
      // 其他路由保持原有逻辑
      const existKeepAliveTab = keepAliveTabs.find(
        (o) => o.routePath === matchRoute?.routePath,
      );

      setActiveTabRoutePath(matchRoute.routePath);

      if (!existKeepAliveTab) {
        setKeepAliveTabs((prev) => [
          ...prev,
          {
            title: matchRoute.title,
            key: getKey(),
            routePath: matchRoute.routePath,
            pathname: matchRoute.pathname,
            children: matchRoute.children,
            icon: matchRoute.icon,
          },
        ]);
      } else if (existKeepAliveTab.pathname !== matchRoute.pathname) {
        setKeepAliveTabs((prev) => {
          const index = prev.findIndex(
            (tab) => tab.routePath === matchRoute.routePath,
          );

          if (index >= 0) {
            prev[index].key = getKey();
            prev[index].pathname = matchRoute.pathname;
            prev[index].children = matchRoute.children;
          }

          delete keepAliveHiddenEvents.current[prev[index].routePath];
          delete keepAliveShowEvents.current[prev[index].routePath];

          return [...prev];
        });
      } else {
        (
          keepAliveShowEvents.current[existKeepAliveTab.routePath] || []
        ).forEach((cb) => {
          cb();
        });
      }
    }

    // 路由改变，执行上一个tab的onHidden事件
    (keepAliveHiddenEvents.current[activeTabRoutePath] || []).forEach((cb) => {
      cb();
    });
  }, [matchRoute]);
  const newkeepAliveTabs = keepAliveTabs?.map((item: any) => {
    // if (item?.pathname == '/report/particulars') {
    //   return {
    //     ...item, title: localStorage.getItem('statementName')
    //   }
    // }
    return item;
  });
  return {
    keepAliveTabs: newkeepAliveTabs,
    activeTabRoutePath,
    closeTab,
    refreshTab,
    closeOtherTab,
    onShow,
    onHidden,
  };
}
