import { ConfigProvider, Dropdown, Tabs } from 'antd';
import { useCallback, useMemo, useRef } from 'react';
import { history } from 'umi';
import styles from './index.less';
import { KeepAliveTab, useKeepAliveTabs } from './useKeepAliveTabs';
import type { ItemType, MenuInfo } from 'rc-menu/lib/interface';
import { KeepAliveTabContext } from './context';
import { useScroll } from 'ahooks';
import MyIcon from '@/components/MyIcon';
import RightContent from '@/components/RightContent';
import { Provider } from 'react-redux';
import store from '@/store';
//import '@/assets/font/iconfont.js'
import locale from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';

enum OperationType {
  REFRESH = 'refresh',
  CLOSE = 'close',
  CLOSEOTHER = 'close-other',
}

type MenuItemType = (ItemType & { key: OperationType }) | null;

const KeepAliveLayout = () => {
  const scrollRef = useRef(null);
  const MyScroll = useScroll(scrollRef);
  const {
    keepAliveTabs,
    activeTabRoutePath,
    closeTab,
    refreshTab,
    closeOtherTab,
    onHidden,
    onShow,
  } = useKeepAliveTabs();

  const menuItems: MenuItemType[] = useMemo(
    () =>
      [
        // {
        //   label: '刷新',
        //   key: OperationType.REFRESH,
        // },
        null,
        keepAliveTabs.length <= 1
          ? null
          : {
              label: '关闭',
              key: OperationType.CLOSE,
            },
        keepAliveTabs.length <= 1
          ? null
          : {
              label: '关闭其他',
              key: OperationType.CLOSEOTHER,
            },
      ].filter((o) => o),
    [keepAliveTabs],
  );

  const menuClick = useCallback(
    ({ key, domEvent }: MenuInfo, tab: KeepAliveTab) => {
      domEvent.stopPropagation();

      if (key === OperationType.REFRESH) {
        refreshTab(tab.routePath);
      } else if (key === OperationType.CLOSE) {
        closeTab(tab.routePath);
      } else if (key === OperationType.CLOSEOTHER) {
        closeOtherTab(tab.routePath);
        history.push(tab?.routePath);
      }
    },
    [closeOtherTab, closeTab, refreshTab],
  );

  const renderTabTitle = useCallback(
    (tab: KeepAliveTab) => {
      return (
        <Dropdown
          menu={{ items: menuItems, onClick: (e) => menuClick(e, tab) }}
          trigger={['contextMenu']}
        >
          <div style={{ margin: '-12px 0', padding: '12px 0' }}>
            {/* {tab.icon} */}
            {tab.icon ? (
              <MyIcon type={tab.icon} style={{ marginRight: '5px' }} />
            ) : null}
            {tab.title}
          </div>
        </Dropdown>
      );
    },
    [menuItems],
  );

  const tabItems = useMemo(() => {
    return keepAliveTabs.map((tab) => {
      const tabKey = tab.uniqueId || tab.routePath;
      return {
        key: tabKey,
        label: renderTabTitle(tab),
        children: (
          <div
            id="contentBodyBox"
            key={tab.key}
            ref={scrollRef}
            style={{
              height: 'calc(100vh - 49px) !important',
              overflow: 'auto',
            }}
            // style={{ height: `${tab.routePath == "/dinglkBooking" ? 'calc(100vh - 0px)' : 'calc(100vh - 112px)'}`, overflow: 'auto' }}
          >
            {tab.children}
          </div>
        ),
        closable: keepAliveTabs.length > 1,
      };
    });
  }, [keepAliveTabs?.length]);

  const onTabsChange = useCallback(
    (tabKey: string) => {
      const curTab = keepAliveTabs.find(
        (o) => (o.uniqueId || o.routePath) === tabKey,
      );
      if (curTab) {
        history.push(curTab?.pathname);
        // refreshTab(curTab?.pathname)
      }
    },
    [keepAliveTabs],
  );

  const onTabEdit = (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove',
  ) => {
    if (action === 'remove') {
      closeTab(targetKey as string);
    }
  };

  const keepAliveContextValue = useMemo(
    () => ({
      closeTab,
      closeOtherTab,
      refreshTab,
      onHidden,
      onShow,
      MyScroll,
      scrollRef,
    }),
    [
      closeTab,
      closeOtherTab,
      refreshTab,
      onHidden,
      onShow,
      MyScroll,
      scrollRef,
    ],
  );
  const activeTabRoute = (params: string) => {
    let noShow = ['/home', '/dinglkBooking'];
    if (noShow.includes(params)) {
      return { display: 'none',zIndex:10 };
    } else {
      return { margin: 0, border: 'none',zIndex:10 };
    }
  };
  return (
    <Provider store={store}>
      <ConfigProvider locale={locale}>
        <KeepAliveTabContext.Provider value={keepAliveContextValue}>
          <div className="absolute top-0px right-15px z-1">
            <RightContent />
          </div>
          <Tabs
            type="editable-card"
            items={tabItems}
            activeKey={activeTabRoutePath}
            onChange={onTabsChange}
            className={styles['keep-alive-tabs']}
            hideAdd
            animated={{ inkBar: false, tabPane: true }}
            onEdit={onTabEdit}
            tabBarStyle={activeTabRoute(activeTabRoutePath)}
          />
        </KeepAliveTabContext.Provider>
      </ConfigProvider>
    </Provider>
  );
};

export default KeepAliveLayout;
