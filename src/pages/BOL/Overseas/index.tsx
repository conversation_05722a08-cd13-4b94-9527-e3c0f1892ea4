import MrTable from "@/components/MrTable";
import { shippingAgencyListAPI } from "@/services/booking";
import { Space } from "antd";
import { useRef } from "react";

const Overseas = () => {
    const columns = [
        {

            title: '海外仓预报单号',
            dataIndex: 'departsCity',
            width: 200,
        },
        {

            title: '预报单状态',
            dataIndex: 'c',
            width: 200,
        },
        {

            title: '提单号',
            dataIndex: 'r',
            width: 200,
        },
        {

            title: '提单状态',
            dataIndex: 'a',
            width: 200,
        },
        {

            title: '已派送票数/总票数',
            dataIndex: 'p',
            width: 200,
        },
        {

            title: '海外仓代理渠道',
            dataIndex: 'e',
            width: 200,
        },
        {

            title: '海外仓服务商',
            dataIndex: 'd',
            width: 200,
        },
        {
            title: '操作',
            key: 'option',
            // align: 'left',
            width: 80,
            fixed: 'right',
            // hideInSearch: true,
            render: (text: any, record: any) => {
                return <Space>
                    <a key={'examine'}>查看</a>
                    <a key={'cancellation'} style={{ color: '#FF4040' }}>作废</a>
                </Space>
            }
        },
    ]
    const filters = {
        // "keyword": {
        //     "type": "keyword",
        //     "value": '',
        // },
        "text": {
            "desc": "单号",
            "type": "text",
            value: ''
        },
        "provider": {
            "desc": "服务商",
            "type": "text",
            value: ''
        },
        "type": {
            "desc": "类型",
            "type": "select",
            "range": [],
        },
        "oversea": {
            "desc": "海外仓",
            "type": "select",
            "range": [],
        },
        arriveTime: {
            "desc": "预报时间",
            type: 'dateTimeRange',
            startTime: '',
            endTime: '',
        },
        "state": {
            "desc": "提单状态",
            "type": "select",
            "range": [],
        },
        "forecastState": {
            "desc": "预报单状态",
            "type": "select",
            "range": [],
        },
    }
    const actionRef = useRef<any>()
    return <>
        <MrTable
            columns={columns}
            keyID={'overseas_warehouse'}
            request={async (params: any, action: any) => {
                actionRef.current = action;
                const msg = await shippingAgencyListAPI({
                    ...params,
                });
                return {
                    data: msg?.data?.list || [],
                    success: msg.status,
                    total: msg?.data?.total,
                };
            }}
            filters={filters}

        />
    </>
}
export default Overseas