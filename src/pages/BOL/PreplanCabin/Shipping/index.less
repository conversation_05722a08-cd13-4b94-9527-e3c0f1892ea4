// .inland_ransfer {
//     :global {
//         .ant-form {
//             display: none !important;
//         }

//         :where(.css-dev-only-do-not-override-z8we91).ant-pro-card-border {
//             border: none !important;
//         }
//     }

//     .inland_search {
//         //display: flex;
//         //height: 110px;
//         background-color: #fff;
//         box-sizing: border-box;
//         padding: 20px 20px 0 20px;
//         border-radius: 0 0 10px 10px;
//         align-items: center;

//         :global {
//             .ant-select {
//                 //height: 32px !important;
//             }

//             .ant-select-selector {
//                 background-color: #FBFBFB !important;
//                 border: none !important;
//             }

//             .ant-picker.ant-picker-range {
//                 border: none !important;
//             }
//         }

//         .search_wrap {
//             border: 1px solid #e8e8e8;
//             height: 80px;
//             display: flex;
//             border-radius: 8px;

//             :global(.ant-select-selector) {
//                 height: 100%;

//                 :global(.ant-select-selection-item) {
//                     display: flex;
//                     align-items: center;
//                 }
//             }
//         }

//         >div {
//             margin-left: 30px;
//         }
//     }
// }

.stayStyles {
    width: 100%;
    height: 64px;
    background-color: #EBF6FF;
    position: relative;
    line-height: 64px;
    // background-image: url('@/assets/images/cTop.png') ;
    background-repeat: no-repeat;
    margin-top: 20px;

    >span {
        margin-right: 5%;
    }

    >img {
        position: absolute;
        left: -24px;
        top: 0px;
        width: 800px;
        z-index: 999999;
    }
}