import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  message,
  Modal,
  Progress,
  Row,
  Select,
  Space,
  Tag,
} from 'antd';
import React, { FC, memo, useEffect, useMemo, useRef, useState } from 'react';
import { formatDate, formatTime, formatTimes } from '@/utils/format';
import { history } from '@umijs/max';
import MySearch from '@/components/MyProTable/MySearch';
import styles from './index.less';
import TablePage from '@/pages/Booking/components/TablePage';
import { saveAs } from 'file-saver';
import {
  configureListAPI,
  getConfigureExcelAPI,
  getNewPreplanCabin,
  getStuffingWarehouseListAPI,
  newConfigureListAPI,
} from '@/services/preplanCabin';
import { createFromIconfontCN, SwapRightOutlined } from '@ant-design/icons';
import imgTop from '@/assets/images/cTop.png';
import {
  getAgentListAPI,
  shippingAgencyListAPI,
  trailerplanSaveAPI,
} from '@/services/booking';
import AddressLibrary from '@/components/AddressLibrary';
import moment from 'moment';
import _ from 'lodash';
import { ProviderType } from '@/shared/ProviderTypeFn';
import AccessCard from '@/AccessCard';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import { ProFormDateRangePicker } from '@ant-design/pro-components';
import TextArea from 'antd/es/input/TextArea';
import MrTable from '@/components/MrTable';
import LogDashboard from '@/components/LogDashboard';
const MyIcon = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3877009_slyb3nrsy5.js',
});

const Shipping: FC = () => {
  const [toList] = usePermissionFiltering();
  const formRef = useRef<any>();
  const actionRef = useRef<any>(null);
  const [arrange, setArrange] = useState<any>();
  const [paramsData, setParamsData] = useState<any>({ termQuery: false });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [agency, setAgency] = useState<any>([]);
  const [data, setData] = useState<any>({});
  const [trailers, setTrailers] = useState([]);
  // 操作日志模块
  const getDepartment = async (keyword: any) => {
    const { status, data } = await getStuffingWarehouseListAPI({
      keyword,
      types: '1,2'
    });
    if (status) {
      return data?.list?.map((item: any) => ({
        label: item?.subject,
        value: item?.id,
      }));
    }
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
      tabs: true,
    },
    state: {
      desc: '状态',
      type: 'select',
      range: [
        {
          label: '全部',
          value: -1,
        },
        {
          label: '待配舱',
          value: 1,
        },
        {
          label: '已配舱',
          value: 10,
        },
      ],
      value: '',
    },

    time: {
      desc: '出发时间',
      type: 'dateTimeRange',
    },
    type: {
      desc: '类型',
      type: 'select',
      range: [
        {
          label: '空运',
          value: 1,
        },
        {
          label: '海运',
          value: 2,
        },
      ],
      value: '',
    },
    // stuffingWarehouseIds: {
    //   desc: '装柜地',
    //   type: 'warehouse',
    //   value: '',
    //   multi: true,

    // },
    stuffingWarehouseIds: {
      desc: '装柜地',
      type: 'warehouse',
      multi: true,
      value: '',
      // generate: (value: any) => getDepartment(value),
    },
  };
  const enumePar: any = {
    1: {
      text: '待配舱',
      color: 'magenta',
    },
    10: {
      text: '已配舱',
      color: 'red',
    },
    20: {
      text: '已提柜',
      color: '#ff6b6b',
    },
    30: {
      text: '已出库',
      color: '#48dbfb',
    },
    40: {
      text: '已进港',
      color: '#1dd1a1',
    },
    50: {
      text: '已开船',
      color: '#3742fa',
    },
    60: {
      text: '到达目的港',
      color: '#2ed573',
    },
    70: {
      text: '清关查验',
      color: '#ff6348',
    },
    80: {
      text: '海外提柜',
      color: '#8e44ad',
    },
    90: {
      text: '已还柜',
      color: '#00b894',
    },
    '-1': {
      text: '已转卖',
      color: '#ff4757',
    },
    '-2': {
      text: '已退订',
      color: '#ff4757',
    },
    0: {
      text: '未知',
      color: '#95a5a6',
    },
  };
  const statusEnum: any = {
    1: {
      text: '等待',
      color: 'magenta',
    },
    2: {
      text: '执行中',
      color: 'red',
    },
    3: {
      text: '已完成',
      color: 'orange',
    },
  };
  const trailerState: any = {
    1: {
      text: '等待安排司机',
      color: 'magenta',
    },
    2: {
      text: '已安排司机',
      color: 'volcano',
    },
  };
  // const columns = useMemo(() => {
  //   return [
  //     {
  //       title: '状态',
  //       hideInSearch: true,
  //       width: 120,
  //       dataIndex: 'state',
  //       render: (recode: any, record: any) => {
  //         return (
  //           <Tag color={enumePar[record?.state]?.color}>
  //             {enumePar[record?.state]?.text}
  //           </Tag>
  //         );
  //       },
  //     },
  //     {
  //       title: '提单号/进仓编号',
  //       dataIndex: 'info',
  //       width: 200,
  //       hideInSearch: true,
  //       render: (_: any, recode: any) => {
  //         if (recode?.info?.mode == 1) {
  //           return `${recode?.info?.blno}/${recode?.info?.airLicenseNo}`;
  //         } else {
  //           return `${recode?.info?.blno}`;
  //         }
  //       },
  //     },
  //     {
  //       title: '航程',
  //       dataIndex: 'departsCity',
  //       width: 220,
  //       render: (text: any, record: any) => {
  //         return (
  //           <>
  //             <Space>
  //               <div>
  //                 {record?.info?.mode === 1 ? (
  //                   <MyIcon type="icon-kongyun" />
  //                 ) : (
  //                   <MyIcon type="icon-haihang" />
  //                 )}
  //               </div>
  //               <div>{record?.info?.departsCity}</div>
  //               <SwapRightOutlined />
  //               <div>{record?.info?.deliveryCity}</div>
  //             </Space>
  //           </>
  //         );
  //       },
  //     },
  //     {
  //       title: '配舱率（m³）',
  //       hideInSearch: true,
  //       dataIndex: 'number',
  //       width: 220,
  //       render: (_: any, recode: any) => {
  //         const num = (recode?.volume / recode?.info?.volume) * 100;
  //         return (
  //           <div>
  //             {`${recode?.volume}/${recode?.info?.volume}`}&nbsp;&nbsp;
  //             <Progress percent={num} size="small" showInfo={false} />
  //           </div>
  //         );
  //       },
  //     },
  //     {
  //       title: '航线',
  //       dataIndex: 'vesselName',
  //       hideInSearch: true,
  //       width: 200,
  //       render: (recode: any, record: any) => {
  //         return <div>{record?.info?.vesselName || '-'}</div>
  //       },
  //     },
  //     {
  //       title: '箱型',
  //       hideInSearch: true,
  //       dataIndex: 'model',
  //       width: 120,
  //       render: (text: any, record: any) => {
  //         return <div>{record?.info?.model || '-'}</div>
  //       },
  //     },
  //     // {
  //     //     title: '类型',
  //     //     hideInSearch: true,
  //     //     dataIndex: 'type',
  //     //     align: 'center',
  //     //     width: 120,
  //     //     render:(_:any,recode:any) => {
  //     //         if(recode?.info?.mode == 1) {
  //     //             return '空运'
  //     //         } else {
  //     //             return '海运'
  //     //         }
  //     //     }

  //     // },
  //     // {
  //     //     title: '进仓编号',
  //     //     hideInSearch: true,
  //     //     dataIndex: 'airLicenseNo',
  //     //     align: 'center',
  //     //     // hideInTable: paramsData !== 1,
  //     //     width: 120,
  //     //     render:(_:any,recode:any) => {
  //     //         if(recode?.info?.mode == 1) {
  //     //             return recode?.info?.airLicenseNo
  //     //         } else {
  //     //             return ''
  //     //         }
  //     //     }

  //     // },
  //     {
  //       title: '装柜地',
  //       hideInSearch: true,
  //       width: 120,
  //       dataIndex: 'warehouseList',
  //       render: (_: any, recode: any) => {
  //         // 空运
  //         if (recode?.info?.mode == 1) {
  //           return recode?.info?.airWarehouseSnapshot
  //             ? JSON.parse(recode?.info?.airWarehouseSnapshot)?.name
  //             : '';
  //         } else {
  //           return (
  //             recode?.warehouseList
  //               ?.map((item: any) => item?.name)
  //               .filter(Boolean)
  //               ?.join(',') || ''
  //           );
  //         }
  //       },
  //     },
  //     // {
  //     //     title: '出发港',
  //     //     hideInSearch: true,
  //     //     dataIndex: 'info',
  //     //     align: 'center',
  //     //     width: 120,
  //     //     render: (text: any) => {
  //     //         return text?.departsCity
  //     //     }
  //     // },
  //     // {
  //     //     title: '到达港',
  //     //     hideInSearch: true,
  //     //     dataIndex: 'info',
  //     //     align: 'center',
  //     //     width: 120,
  //     //     render: (text: any) => {
  //     //         return text?.arrivesCity || '-'
  //     //     }
  //     // },
  //     {
  //       title: '最晚预报',
  //       hideInSearch: true,
  //       dataIndex: 'forecastLimitTime',
  //       width: 120,
  //       render: (text: any, record: any) => {
  //         return record?.info?.forecastLimitTime
  //           ? formatTimes(record?.info?.forecastLimitTime)
  //           : '-';
  //       },
  //     },
  //     {
  //       title: '最晚装柜/进仓',
  //       hideInSearch: true,
  //       dataIndex: 'airLicenseTime',
  //       width: 120,
  //       render: (text: any, record: any) => {
  //         if (record?.info?.mode == 1) {
  //           // 空运
  //           return record?.info?.airLicenseTime
  //             ? formatTimes(record?.info?.airLicenseTime)
  //             : '-';
  //         }
  //         return record?.info?.cargoLimitTime
  //           ? formatTimes(record?.info?.cargoLimitTime)
  //           : '-';
  //       },
  //     },
  //     {
  //       hideInSearch: true,
  //       title: '开船/起飞时间',
  //       dataIndex: 'departTime',
  //       width: 180,
  //       filter: {
  //         sort: 0
  //       },
  //       render: (text: any, record: any) => {
  //         return record?.info?.mode === 1
  //           ? formatTime(record?.info?.airDepartTime)
  //           : formatTime(record?.info?.estimatedDepartTime);
  //         // return text?.estimatedDepartTime ? formatTimes(text?.estimatedDepartTime) : '-'
  //       },
  //     },
  //     // {
  //     //     hideInSearch: true,
  //     //     title: '拖车状态',
  //     //     dataIndex: 'state',
  //     //     width: 120,
  //     //     align: 'center',
  //     //     render:(recode:any) => {
  //     //        return <Tag color={trailerState[recode]?.color}>{trailerState[recode]?.text}</Tag>
  //     //     }
  //     // },
  //     {
  //       title: '拖车信息',
  //       hideInSearch: true,
  //       dataIndex: 'trailerPlan',
  //       children: [
  //         {
  //           title: '状态',
  //           hideInSearch: true,
  //           dataIndex: 'recordedAmount',
  //           width: 120,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.state ? (
  //               <Tag color={statusEnum[recode?.trailerPlan?.state]?.color}>
  //                 {statusEnum[recode?.trailerPlan?.state]?.text}
  //               </Tag>
  //             ) : (
  //               ''
  //             );
  //           },
  //         },
  //         {
  //           title: '箱型',
  //           hideInSearch: true,
  //           dataIndex: 'models',
  //           width: 120,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.model;
  //           },
  //         },
  //         {
  //           title: '箱号',
  //           hideInSearch: true,
  //           dataIndex: 'containerNo',
  //           width: 120,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.containerNo;
  //           },
  //         },
  //         {
  //           title: '箱皮',
  //           hideInSearch: true,
  //           dataIndex: 'containerSkin',
  //           width: 120,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.containerSkin;
  //           },
  //         },
  //         {
  //           title: '封号',
  //           hideInSearch: true,
  //           dataIndex: 'sealNo',
  //           width: 140,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.sealNo;
  //           },
  //         },
  //         {
  //           title: '装柜仓库',
  //           hideInSearch: true,
  //           dataIndex: 'sealNso',
  //           width: 140,
  //           render: (_: any, recode: any) => {
  //             return recode?.warehouseList
  //               ? recode?.warehouseList?.map((item: any) => item.name).join(',')
  //               : '-';
  //           },
  //         },
  //         // {
  //         //     title: '目的港口',
  //         //     hideInSearch: true,
  //         //     dataIndex: 'voyage',
  //         //     align: 'center',
  //         //     width: 200,
  //         //     render: (recode: any) => {
  //         //         return recode?.vessel?.vesselProperties?.arrivesCity
  //         //     }
  //         // },
  //         // {
  //         //     hideInSearch: true,
  //         //     title: '装柜时间',
  //         //     dataIndex: 'loadingTime',
  //         //     align: 'center',
  //         //     width: 200,
  //         //     render: (recode: any) => {
  //         //         return recode && formatTime(recode)
  //         //     }
  //         // },
  //         {
  //           hideInSearch: true,
  //           title: '车牌号',
  //           dataIndex: 'licensePlateNo',
  //           width: 160,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.licensePlateNo;
  //           },
  //         },
  //         {
  //           hideInSearch: true,
  //           title: '司机',
  //           dataIndex: 'driverName',
  //           width: 160,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.driverName;
  //           },
  //         },
  //         {
  //           hideInSearch: true,
  //           title: '电话',
  //           dataIndex: 'driverPhone',
  //           width: 160,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.driverPhone;
  //           },
  //         },
  //         {
  //           hideInSearch: true,
  //           title: '拖车代理',
  //           dataIndex: 'agentName',
  //           width: 160,
  //           render: (_: any, recode: any) => {
  //             return recode?.trailerPlan?.trailerAgent?.name;
  //           },
  //         },
  //       ],
  //     },

  //     {
  //       title: '操作',
  //       valueType: 'option',
  //       align: 'center',
  //       fixed: 'right',
  //       width: 200,
  //       render: (_: any, recode: any) => {
  //         return (
  //           <Space
  //             size="middle"
  //             style={{ display: 'flex', justifyContent: 'center' }}
  //           >
  //             <a onClick={() => getgetConfigureExcel(recode?.spaceId)}>
  //               下载舱单
  //             </a>
  //             <a
  //               onClick={() => {
  //                 history.push('/BOL/PreplanCabin/particulars', {
  //                   makingId: recode?.id,
  //                 });
  //               }}
  //             >
  //               详情
  //             </a>

  //             <AccessCard accessible={[':Transport:TrailerPlanAccess']}>
  //               {recode?.info?.mode !== 1 && !recode?.trailerPlan && (
  //                 <a
  //                   onClick={() => {
  //                     setArrange({
  //                       blno: recode?.spaceId,
  //                       vesselName: recode?.info?.name,
  //                       shipName: recode?.info?.shipName,
  //                       code: recode?.info?.voyageCode,
  //                       model: recode?.info?.model,
  //                       arrivesCity: recode?.info?.arrivesCity,
  //                     });
  //                     trailer(recode?.spaceId);
  //                     setIsModalOpen(true);
  //                   }}
  //                 >
  //                   安排拖车
  //                 </a>
  //               )}
  //             </AccessCard>
  //             <LogDashboard extraId_1={recode?.id} btnType="link" btnText="日志" key="journal" />
  //           </Space>
  //         );
  //       },
  //     },
  //   ];
  // }, [paramsData?.type]);
  const columns = useMemo(() => {
    return [
      {
        title: '状态',
        hideInSearch: true,
        width: 120,
        dataIndex: 'state',
        render: (recode: any, record: any) => {
          return (
            <Tag color={enumePar[record?.state]?.color}>
              {enumePar[record?.state]?.text}
            </Tag>
          );
        },
      },
      {
        title: '提单号/进仓编号',
        dataIndex: 'info',
        width: 200,
        hideInSearch: true,
        render: (_: any, recode: any) => {
          if (recode?.info?.mode == 1) {
            return `${recode?.info?.blno}/${recode?.info?.airLicenseNo}`;
          } else {
            return `${recode?.info?.blno}`;
          }
        },
      },
      {
        title: '航程',
        dataIndex: 'departsCity',
        width: 220,
        render: (text: any, record: any) => {
          return (
            <>
              <Space>
                <div>
                  {record?.info?.mode === 1 ? (
                    <MyIcon type="icon-kongyun" />
                  ) : (
                    <MyIcon type="icon-haihang" />
                  )}
                </div>
                <div>{record?.info?.departsCity}</div>
                <SwapRightOutlined />
                <div>{record?.info?.deliveryCity}</div>
              </Space>
            </>
          );
        },
      },
      {
        title: '配舱率（m³）',
        hideInSearch: true,
        dataIndex: 'number',
        width: 220,
        render: (_: any, recode: any) => {
          const num = (recode?.volume / recode?.info?.volume) * 100;
          return (
            <div>
              {`${recode?.volume}/${recode?.info?.volume}`}&nbsp;&nbsp;
              <Progress percent={num} size="small" showInfo={false} />
            </div>
          );
        },
      },
      {
        title: '航线',
        dataIndex: 'vesselName',
        hideInSearch: true,
        width: 200,
        render: (recode: any, record: any) => {
          return <div>{record?.info?.vesselName || '-'}</div>
        },
      },
      {
        title: '箱型',
        hideInSearch: true,
        dataIndex: 'model',
        width: 120,
        render: (text: any, record: any) => {
          return <div>{record?.info?.model || '-'}</div>
        },
      },
      // {
      //     title: '类型',
      //     hideInSearch: true,
      //     dataIndex: 'type',
      //     align: 'center',
      //     width: 120,
      //     render:(_:any,recode:any) => {
      //         if(recode?.info?.mode == 1) {
      //             return '空运'
      //         } else {
      //             return '海运'
      //         }
      //     }

      // },
      // {
      //     title: '进仓编号',
      //     hideInSearch: true,
      //     dataIndex: 'airLicenseNo',
      //     align: 'center',
      //     // hideInTable: paramsData !== 1,
      //     width: 120,
      //     render:(_:any,recode:any) => {
      //         if(recode?.info?.mode == 1) {
      //             return recode?.info?.airLicenseNo
      //         } else {
      //             return ''
      //         }
      //     }

      // },
      {
        title: '装柜地',
        hideInSearch: true,
        width: 120,
        dataIndex: 'warehouseList',
        render: (_: any, recode: any) => {
          // 空运
          if (recode?.info?.mode == 1) {
            return recode?.info?.airWarehouseSnapshot
              ? JSON.parse(recode?.info?.airWarehouseSnapshot)?.name
              : '';
          } else {
            return (
              recode?.warehouseList
                ?.map((item: any) => item?.name)
                .filter(Boolean)
                ?.join(',') || ''
            );
          }
        },
      },
      // {
      //     title: '出发港',
      //     hideInSearch: true,
      //     dataIndex: 'info',
      //     align: 'center',
      //     width: 120,
      //     render: (text: any) => {
      //         return text?.departsCity
      //     }
      // },
      // {
      //     title: '到达港',
      //     hideInSearch: true,
      //     dataIndex: 'info',
      //     align: 'center',
      //     width: 120,
      //     render: (text: any) => {
      //         return text?.arrivesCity || '-'
      //     }
      // },
      {
        title: '最晚预报',
        hideInSearch: true,
        dataIndex: 'forecastLimitTime',
        width: 120,
        render: (text: any, record: any) => {
          return record?.info?.forecastLimitTime
            ? formatTimes(record?.info?.forecastLimitTime)
            : '-';
        },
      },
      {
        title: '最晚装柜/进仓',
        hideInSearch: true,
        dataIndex: 'airLicenseTime',
        width: 120,
        render: (text: any, record: any) => {
          if (record?.info?.mode == 1) {
            // 空运
            return record?.info?.airLicenseTime
              ? formatTimes(record?.info?.airLicenseTime)
              : '-';
          }
          return record?.info?.cargoLimitTime
            ? formatTimes(record?.info?.cargoLimitTime)
            : '-';
        },
      },
      {
        hideInSearch: true,
        title: '开船/起飞时间',
        dataIndex: 'departTime',
        width: 180,
        filter: {
          sort: 0
        },
        render: (text: any, record: any) => {
          return record?.info?.mode === 1
            ? formatTime(record?.info?.airDepartTime)
            : formatTime(record?.info?.estimatedDepartTime);
          // return text?.estimatedDepartTime ? formatTimes(text?.estimatedDepartTime) : '-'
        },
      },
      // {
      //     hideInSearch: true,
      //     title: '拖车状态',
      //     dataIndex: 'state',
      //     width: 120,
      //     align: 'center',
      //     render:(recode:any) => {
      //        return <Tag color={trailerState[recode]?.color}>{trailerState[recode]?.text}</Tag>
      //     }
      // },
      // {
      //   title: '拖车信息',
      //   hideInSearch: true,
      //   dataIndex: 'trailerPlan',
      //   children: [
      {
        title: '状态',
        hideInSearch: true,
        dataIndex: 'recordedAmount',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.state ? (
            <Tag color={statusEnum[recode?.trailerPlan?.state]?.color}>
              {statusEnum[recode?.trailerPlan?.state]?.text}
            </Tag>
          ) : (
            ''
          );
        },
      },
      {
        title: '箱型',
        hideInSearch: true,
        dataIndex: 'models',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.model;
        },
      },
      {
        title: '箱号',
        hideInSearch: true,
        dataIndex: 'containerNo',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.containerNo;
        },
      },
      {
        title: '箱皮',
        hideInSearch: true,
        dataIndex: 'containerSkin',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.containerSkin;
        },
      },
      {
        title: '封号',
        hideInSearch: true,
        dataIndex: 'sealNo',
        width: 140,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.sealNo;
        },
      },
      {
        title: '装柜仓库',
        hideInSearch: true,
        dataIndex: 'sealNso',
        width: 140,
        render: (_: any, recode: any) => {
          return recode?.warehouseList
            ? recode?.warehouseList?.map((item: any) => item.name).join(',')
            : '-';
        },
      },
      // {
      //     title: '目的港口',
      //     hideInSearch: true,
      //     dataIndex: 'voyage',
      //     align: 'center',
      //     width: 200,
      //     render: (recode: any) => {
      //         return recode?.vessel?.vesselProperties?.arrivesCity
      //     }
      // },
      // {
      //     hideInSearch: true,
      //     title: '装柜时间',
      //     dataIndex: 'loadingTime',
      //     align: 'center',
      //     width: 200,
      //     render: (recode: any) => {
      //         return recode && formatTime(recode)
      //     }
      // },
      {
        hideInSearch: true,
        title: '车牌号',
        dataIndex: 'licensePlateNo',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.licensePlateNo;
        },
      },
      {
        hideInSearch: true,
        title: '司机',
        dataIndex: 'driverName',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.driverName;
        },
      },
      {
        hideInSearch: true,
        title: '电话',
        dataIndex: 'driverPhone',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.driverPhone;
        },
      },
      {
        hideInSearch: true,
        title: '拖车代理',
        dataIndex: 'agentName',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.trailerPlan?.trailerAgent?.name;
        },
      },
      //   ],
      // },

      {
        title: '操作',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        width: 280,
        render: (_: any, recode: any) => {
          return (
            <Space
              size="middle"
              style={{ display: 'flex', justifyContent: 'center' }}
            >
              <a onClick={() => getgetConfigureExcel(recode?.spaceId)}>
                下载舱单
              </a>
              <a
                onClick={() => {
                  history.push('/BOL/PreplanCabin/particulars', {
                    makingId: recode?.id,
                  });
                }}
              >
                详情
              </a>

              <AccessCard accessible={[':Transport:TrailerPlanAccess']}>
                {recode?.info?.mode !== 1 && !recode?.trailerPlan && (
                  <a
                    onClick={() => {
                      setArrange({
                        blno: recode?.spaceId,
                        vesselName: recode?.info?.name,
                        shipName: recode?.info?.shipName,
                        code: recode?.info?.voyageCode,
                        model: recode?.info?.model,
                        arrivesCity: recode?.info?.arrivesCity,
                      });
                      trailer(recode?.spaceId);
                      setIsModalOpen(true);
                    }}
                  >
                    安排拖车
                  </a>
                )}
              </AccessCard>
              <LogDashboard extraId_1={recode?.id} btnType="link" btnText="日志" key="journal" />
            </Space>
          );
        },
      },
    ];
  }, [paramsData?.type]);
  // useEffect(() => {
  //     if(isModalOpen) {
  //         trailer()
  //     }
  // }, [isModalOpen])
  const getgetConfigureExcel = async (spaceId: string) => {
    try {
      const response: any = await getConfigureExcelAPI({ spaceId });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
      message.success('下载舱单成功');
      refresh();
      // actionRef?.current.reload()
    } catch (err) {
      console.log('下载舱单: ', err);
    }
  };
  function unique(arr1: any) {
    const res = new Map();
    return arr1.filter((item: any) => !res.has(item.id) && res.set(item.id, 1));
  }
  // 选择后的装柜地
  const assembleData = (data: any) => {
    const newData = unique([...agency, data]);
    setAgency(newData);
  };
  const onChange = (
    value: any['value'] | any['value'],
    dateString: [string, string] | string,
  ) => {
    setData({
      ...data,
      loadingTime: dateString,
    });
  };
  // 拖车代理列表
  const trailer = async (spaceId: string, keyword?: string) => {
    try {
      const { status, data } = await getAgentListAPI({
        start: 0,
        len: 20,
        spaceId,
        keyword,
      });
      if (status) {
        setTrailers(data);
      }
    } catch (error) {
      console.log(JSON.stringify(error), 'error');
    }
  };
  // 安排拖车
  const arrangeTrailer = async () => {
    if (agency?.length == 0) return message.warning('请选择装柜地！');
    if (!data?.loadingTime) return message.warning('时间不能为空！');
    // if (!data?.agentId) return message.warning('请选择拖车代理！')
    try {
      const { status } = await trailerplanSaveAPI({
        ...data,
        cabinId: arrange?.blno,
        stuffingWarehouseIds: agency?.map((item: any) => item.id)?.join(','),
      });
      if (status) {
        message.success('安排拖车成功');
        setAgency([]);
        setIsModalOpen(false);
        refresh();
      }
    } catch (error) {
      console.log(JSON.stringify(error), 'error');
    }
  };
  // 拖车代理搜索防抖
  const handChange = _.debounce((e: any) => {
    trailer(arrange?.blno, e);
  }, 1000);
  const refresh = () => actionRef?.current.reload();
  return (
    <div className={styles.inland_ransfer}>
      {/*      <div className={styles.inland_search}>
        <Row wrap={false} align={'middle'} gutter={[16, 0]}>
          <Col span={6}>
            <div className={styles.search_wrap} id="search_left">
              <Select
                defaultValue="批量"
                bordered={false}
                style={{
                  height: '100%',
                  border: 'none',
                  backgroundColor: '#f7f7f7',
                }}
                onChange={(value: any) => {
                  setParamsData({
                    ...paramsData,
                    termQuery: value,
                  });
                }}
              >
                <Option value="true">精确</Option>
                <Option value="false">批量</Option>
              </Select>
              <TextArea
                className={styles.text_area}
                id="search_text"
                style={{
                  border: 'none',
                  resize: 'none',
                  borderRadius: '0px',
                  paddingTop: '27px',
                }}
                onChange={(e: any) => {
                  const value = e.target.value;
                  console.log('value', e.target.value);
                  setParamsData({
                    ...paramsData,
                    keyword: value,
                  });
                }}
                placeholder="请输入ID,运单号，客户代码，客户名称，业务员搜索"
                onPressEnter={() => {
                  formRef?.current?.submit();
                }}
              />
              <Button
                style={{ height: '100%', color: '#537ffd', border: 'none' }}
                className={styles.search_back}
                onClick={() => {
                  formRef?.current?.submit();
                }}
              >
                搜索
              </Button>
            </div>
          </Col>
          <Col span={6} style={{ marginLeft: '5px' }}>
            <span>类型：</span>
            <Select
              placeholder="请选择"
              allowClear
              onChange={(e) => {
                setParamsData({
                  ...paramsData,
                  type: e,
                });
                formRef?.current?.submit();
              }}
              options={toList([
                {
                  value: 1,
                  label: '空运',
                  accessible: [':BL:ConfigureReadAccess'],
                  dataValidator: (path: any, data: any) => {
                    return /[01]/.test(data.type.value);
                  },
                },
                {
                  value: 2,
                  label: '海运',
                  accessible: [':BL:ConfigureReadAccess'],
                  dataValidator: (path: any, data: any) => {
                    return /[02]/.test(data.type.value);
                  },
                },
              ])}
            />
          </Col>
          <Col span={6}>
            <span>状态：</span>
            <Select
              placeholder="请选择"
              allowClear
              onChange={(e) => {
                setParamsData({
                  ...paramsData,
                  state: e,
                });
                formRef?.current?.submit();
              }}
              options={[
                { value: 1, label: '待配舱' },
                { value: 10, label: '已配舱' },
              ]}
            />
          </Col>
          <Col span={6} style={{ display: 'flex', flexWrap: 'nowrap' }}>
            <span style={{ minWidth: '5em', lineHeight: '32px' }}>
              出发时间：
            </span>
            <ProFormDateRangePicker
              placeholder={['起始', '截至']}
              colon={false}
              wrapperCol={{ span: 20 }}
              fieldProps={{
                className: styles.search_back,
              }}
              noStyle
              style={{ flex: 1 }}
              onChange={(time: any) => {
                setParamsData({
                  ...paramsData,
                  startTime: time ? formatDate(time[0]) + '00:00:00' : '',
                  endTime: time ? formatDate(time[1]) + '23:59:59' : '',
                });
                formRef?.current?.submit();
              }}
              showTime
            />
          </Col>
        </Row>
      </div>*/}
      <MrTable
        columns={columns}
        keyID={'BOL/PreplanCabin/shipping'}
        //rowSelectionCablck={rowSelectionCablck}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          const msg = await newConfigureListAPI({
            ...params,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg?.data?.total,
          };
        }}
        filters={filters}
        titleChidren
      />
      {/*      <TablePage
        columns={columns}
        optionRender={false}
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 1200 }}
        rowKey="id"
        request={async (params: { current: number; pageSize: number }) => {
          const { current: start, pageSize: len } = params;
          console.log('paramsData', paramsData);
          const res = await configureListAPI({
            start: (start - 1) * len,
            len,
            ...paramsData,
            // state
          });
          return {
            data: res?.data?.list || [],
            success: res.status,
            total: res.data?.total,
          };
        }}
      />*/}
      <Modal
        className={styles.stayStyles}
        style={{ minWidth: 800 }}
        destroyOnClose
        title="安排拖车"
        open={isModalOpen}
        onCancel={() => {
          setAgency([]);
          setIsModalOpen(false);
        }}
        footer={[
          <div
            style={{ display: 'flex', justifyContent: 'center' }}
            key="footer"
          >
            <Button
              key="link"
              onClick={() => {
                setAgency([]);
                setIsModalOpen(false);
              }}
            >
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                arrangeTrailer();
              }}
            >
              确定
            </Button>
          </div>,
        ]}
      >
        <div className={styles.stayStyles}>
          <img src={imgTop} />
          <div
            style={{
              width: 24,
              height: 64,
              backgroundColor: '#EBF6FF',
              position: 'absolute',
              top: '0px',
              left: '-24px',
            }}
          ></div>
          <span>{arrange?.blno || ''}</span>
          <span>{arrange?.vesselName || ''}</span>
          <span>{`${arrange?.shipName}/${arrange?.code}` || ''}</span>
          <span>{arrange?.model || ''}</span>
          <div
            style={{
              width: 24,
              height: 64,
              backgroundColor: '#EBF6FF',
              position: 'absolute',
              top: '0px',
              right: '-24px',
            }}
          ></div>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            margin: '30px 0 30px 0',
          }}
        ></div>
        <div
          style={{
            display: 'flex',
            padding: '0 30px',
            justifyContent: 'space-between',
            margin: '30px 0 30px 0',
          }}
        >
          <span>
            装柜仓库：
            <span style={{ color: '#e64360' }}>
              {agency.map((item: any) => (
                <Tag
                  closable
                  onClose={(e) => {
                    const newData = agency.filter((v: any) => v.id !== item.id);
                    setAgency([...newData]);
                  }}
                  color="green"
                  style={{ marginRight: 20, marginBottom: 5 }}
                  key={item.id}
                >
                  {item?.name}
                </Tag>
              ))}
            </span>
            &nbsp;&nbsp;&nbsp;
            <AddressLibrary
              key="add"
              btnTitle="添加"
              onChangeAddressLibrary={assembleData}
              isTabVal="3"
            />
          </span>
          <span>
            目的港口：<Tag color="#f50">{arrange?.arrivesCity || ''}</Tag>
          </span>
        </div>
        <div
          style={{
            display: 'flex',
            padding: '0 30px',
            justifyContent: 'space-between',
            margin: '30px 0 30px 0',
          }}
        >
          <div>
            装货时间：{' '}
            <DatePicker
              locale={moment.locale('zh-cn') as any}
              showTime={{ defaultValue: moment('00:00:00', 'HH:mm:ss') } as any}
              onChange={onChange}
            />
          </div>
          {/* <div>
                        拖车代理： <Select
                            placeholder="请选择拖车代理"
                            style={{ width: 160 }}
                            allowClear
                            onChange={(e) => {
                                setData({
                                    ...data,
                                    agentId: e
                                })
                            }}
                            fieldNames={{
                                label: 'name',
                                value: 'id'
                            }}
                            showSearch
                            onSearch={(e) => handChange(e)}
                            options={trailers}
                        />
                    </div> */}
          {/* <div style={{ paddingLeft: 30 }}>
                    是否为预提柜&nbsp;&nbsp;<Checkbox onChange={(e) => {
                        setData({
                            ...data,
                            estimatedPickup: e.target.checked ? 1 : 0
                        })
                    }}></Checkbox>
                </div> */}
        </div>
      </Modal>
    </div>
  );
};
export default memo(Shipping);
