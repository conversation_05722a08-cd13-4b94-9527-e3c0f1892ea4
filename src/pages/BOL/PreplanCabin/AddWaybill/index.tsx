import Time from '@/pages/Booking/components/Time';
import { getWarehouseListAPI } from '@/services/preplanCabin';
import { waybillListAPI } from '@/services/preplanCabin';
import { formatTime } from '@/utils/format';
import {
  ActionType,
  ProColumns,
  ProFormSelect,
} from '@ant-design/pro-components';
import _ from 'lodash';
import {
  Button,
  Col,
  Divider,
  Drawer,
  Input,
  message,
  Row,
  Select,
  Tag,
  Tooltip,
} from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import React, {
  forwardRef,
  FC,
  useState,
  useImperativeHandle,
  useRef,
  useMemo,
  useEffect,
} from 'react';
import styles from './index.less';
import { getProductList } from '@/services/productAndPrice/api';
import { waybillStatusEnum } from '@/utils/constant';
import { calculateSum } from '@/utils/utils';
import TextArea from 'antd/es/input/TextArea';
import QuickSelectModal from '@/pages/BOL/PreplanCabin/Waybill2/QuickSelectModal';
import MrTable from '@/components/MrTable';
interface Props {
  ref: any;
  // 选择运单的回调
  getputIntoConfigure: (data: string) => void;
  //  type 区分当前运单选择器实在哪里使用，这里只区分了预配舱，与 陆内转运
  type: 'match' | 'transfer';
  // 区分空运/海运
  headTransType: 1 | 2;
  /*已赔偿和空余*/
  title?: { has: string; noHas: string };
}
type GithubIssueItem = {
  url: string;
  id: number;
  number: number;
  title: string;
  labels: {
    name: string;
    color: string;
  }[];
  state: string;
  comments: number;
  created_at: string;
  updated_at: string;
  closed_at?: string;
};
const AddWaybill: FC<Props> = forwardRef((props, ref) => {
  const enumeration: any = {
    10: '非单独报关',
    20: '单独报关',
    30: '合并报关',
  };
  const { getputIntoConfigure, type, headTransType, title } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>(null);
  const [keyword, setKeyword] = useState('');
  const [pieceIds, setPieceIds] = useState('');
  const [state, setState] = useState<any>();
  const [time, settime] = useState({});
  const [volume, setVolume] = useState<any>();
  const [productId, setProductId] = useState<any>([]);
  const [shipment, setShipment] = useState<any>({});
  const [warehouseId, setWarehouseId] = useState<any>('');
  const [recipientZipCode, setRecipientZipCode] = useState<any>('');

  const [declarationTypeState, setDeclarationTypeState] = useState<any>();
  const [termQuery, setTermQuery] = useState<boolean>(false);
  const [HeadTransType, setHeadTransType] = useState(headTransType);

  const [total, setTotal] = useState<any>();
  //为了第一次不加载表格
  const [Search, setSearch] = useState(false);
  //统计的数据
  const [statistic, setSatistic] = useState<any>({});
  const [check, setCheck] = useState<any>([]);
  /*已选总数据*/
  const [totalState, setTotalState] = useState({
    select: 0,
    outerActualWeight: 0,
    OuterVolume: 0,
    chargeWeight: 0,
    outerChargeWeight: 0,
    outerVolumeWeight: 0,
    piece: 0,
  });
  //统计的数据
  const pieceData = useRef();
  //统计的数据
  const [checkData, setCheckData] = useState<any>([]);
  function getTableScroll(extraHeight: any) {
    return window.innerHeight - extraHeight;
  }
  const [scrollY, setScrollY] = useState('');
  //页面加载完成后才能获取到对应的元素及其位置
  useEffect(() => {
    if (isModalOpen) {
      setHeadTransType(headTransType);
      setScrollY(getTableScroll(360));
    } else {
      setSearch(false);
    }
  }, [isModalOpen]);
  const textEllipsis = (text: any) => {
    return (
      <div className="truncate">
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>
    );
  };

  const changeTime = (date: any) => {
    settime({
      startTime: date[0] + ' 00:00:00',
      endTime: date[1] + ' 23:59:59',
    });
    getSubmit();
  };
  /*统计数据*/
  const statisticalData = (selectedRowKeys: any, selectedRows: any) => {
    if (type === 'match') {
      const rowId = selectedRowKeys
        ?.filter((item: any) => item !== '' && !item.startsWith('parent'))
        ?.join(',');
      console.log('rowId', rowId);
      const select = rowId ? rowId?.split(',').length : 0;
      console.log('select', select);
      const outerActualWeight = calculateSum(
        selectedRows,
        'outerActualWeight',
        3,
      );
      /*过滤主单的体积后计算*/
      const subRecords = selectedRows.filter(
        (row: any) => row.productId === undefined,
      );
      console.log('subRecords', subRecords);
      const OuterVolume = calculateSum(subRecords, 'outerVolume', 5);
      /*过滤主单的体积重后计算*/
      const outerVolumeWeight = calculateSum(
        subRecords,
        'outerVolumeWeight',
        5,
      );
      console.log('outerVolumeWeight', outerVolumeWeight);
      //获取主单数据
      const uniquePrefixes = new Set(
        selectedRowKeys
          ?.filter((item: any) => item !== '' && !item.startsWith('parent'))
          ?.map((item: string | any[]) => item.slice(0, 17)),
      );
      console.log('uniquePrefixes', uniquePrefixes);
      //统计数据
      setTotalState({
        ...totalState,
        select,
        outerActualWeight: outerActualWeight,
        OuterVolume: OuterVolume,
        outerVolumeWeight: outerVolumeWeight,
        piece: uniquePrefixes.size,
      });
      setPieceIds(rowId);
    } else {
      setPieceIds(selectedRows);
    }
  };
  /*快速选择添加*/
  const handleSelect = (select: any, doSome: any) => {
    /*把数组合并*/
    function mergeArrays(arr: any[]) {
      console.log('arr', arr);
      return arr?.reduce((acc, val) => acc?.concat(val), []);
    }
    //过滤母单的ids
    const ids = select
      .map((item: any) => item.id)
      .filter((item: string) => !item.startsWith('parent'));
    if (doSome === 'add') {
      //重新统计数据
      setCheck((prevCheck: any) => {
        const newCheck = [...new Set([...prevCheck, ...ids])];
        console.log('check', newCheck); // 在这里直接打印最新的状态
        const allPieceData = mergeArrays(pieceData.current);
        const allSelect = allPieceData.filter((item) =>
          newCheck.includes(item.id),
        );
        setCheckData(allSelect);
        statisticalData(newCheck, allSelect);
        return newCheck;
      });
      /*所有选择的数据*/
    } else {
      console.log('ids', ids);
      //过滤删除的数据设置到统计
      console.log('check', check);
      setCheck((prevCheck: any[]) => {
        const newCheck = prevCheck.filter((item) => !ids?.includes(item));
        const allPieceData = mergeArrays(pieceData.current);
        const allSelect = allPieceData.filter((item: { id: any }) =>
          newCheck.includes(item.id),
        );
        setCheckData(allSelect);
        console.log('newCheck', newCheck);
        statisticalData(newCheck, allSelect);
        return newCheck;
      });
    }
  };

  const columns: ProColumns<GithubIssueItem>[] = useMemo(() => {
    return [
      {
        title: '预报打单状态',
        hideInTable: true,
        hideInSearch: type === 'match',
        renderFormItem: () => {
          return (
            <Select
              placeholder="请选择"
              allowClear
              style={{ width: 120 }}
              onChange={(e) => {
                setState(e);
                getSubmit();
              }}
              options={[
                { value: 0, label: '草稿' },
                { value: 10, label: '待签入' },
                { value: 20, label: '已签入' },
                { value: 40, label: '已确认' },
                { value: 60, label: '已配舱' },
                { value: 90, label: '已签出' },
                { value: 200, label: '运输中' },
                { value: 300, label: '已签收' },
                { value: 500, label: '已结束' },
              ]}
            />
          );
        },
      },
      {
        title: '渠道名称',
        hideInTable: true,
        hideInSearch: type === 'match',
        renderFormItem: () => {
          return (
            <ProFormSelect
              name="channelName"
              showSearch
              // rules={[{ required: true, message: '请输入站点' }]}
              labelCol={{ span: 5 }}
              wrapperCol={{ span: 12 }}
              request={async ({ keyWords }) => {
                const { status, data } = await getWarehouseListAPI({
                  type: 0,
                  len: 20,
                  keyword: keyWords,
                });
                if (status) {
                  return data.list.map((item: any) => {
                    return {
                      label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
                      value: item.id,
                    };
                  });
                }
                return [];
              }}
              fieldProps={{
                filterOption: false,
              }}
            />
          );
        },
      },
      {
        title: '运单号',
        dataIndex: 'waybillNO',
        align: 'center',
        width: 150,
        hideInSearch: true,
        filter: {
          sort: 0, //-1,1,
        },
      },
      {
        title: '件数',
        dataIndex: 'pieceNum',
        hideInSearch: true,
        align: 'center',
        width: '4em',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.pieceNum;
        },
      },
      {
        title: '出货方数',
        hideInSearch: true,
        dataIndex: 'signVolume',
        align: 'center',
        width: '10em',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.outerVolume;
        },
      },
      {
        title: '所在仓库',
        hideInSearch: true,
        dataIndex: 'currentWarehouseName',
        align: 'center',
        width: '7em',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.warehouseName;
        },
      },
      {
        title: '报关类型',
        hideInSearch: true,
        dataIndex: 'declarationType',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode ? enumeration[recode?.declarationType] : '';
        },
      },
      {
        title: '配舱状态',
        hideInSearch: true,
        width: '7em',
        dataIndex: 'configureState',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          if (recode?.state === '40') {
            return (
              <Tag
                style={{
                  ...waybillStatusEnum[recode?.state]?.styles,
                  color:
                    waybillStatusEnum[recode?.state]?.styles?.fontSizeColor,
                }}
                color={waybillStatusEnum[recode?.state]?.color}
              >
                待配仓
              </Tag>
            );
          } else if (recode?.state === '60' || recode?.blnos?.includes(',')) {
            return (
              <div>
                <Tag
                  style={{
                    ...waybillStatusEnum[recode?.state]?.styles,
                    color:
                      waybillStatusEnum[recode?.state]?.styles?.fontSizeColor,
                  }}
                  color={waybillStatusEnum[recode?.state]?.color}
                >
                  {waybillStatusEnum[recode?.state]?.text}
                </Tag>
                <Tag style={{ background: '#6003ce', color: 'white' }}>
                  分票
                </Tag>
              </div>
            );
          } else {
            return (
              <Tag
                style={{
                  ...waybillStatusEnum[recode?.state]?.styles,
                  color:
                    waybillStatusEnum[recode?.state]?.styles?.fontSizeColor,
                }}
                color={waybillStatusEnum[recode?.state]?.color}
              >
                {waybillStatusEnum[recode?.state]?.text}
              </Tag>
            );
          }
        },
      },
      {
        title: '提单号',
        hideInSearch: true,
        width: '12em',
        dataIndex: 'blno',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, record: any) => {
          if (record?.children) {
            return textEllipsis(record?.blnos);
          } else {
            return textEllipsis(
              record?.configState?.blno || record?.configState?.configName,
            );
          }
        },
      },
      {
        title: '目的地',
        hideInSearch: true,
        dataIndex: 'recipientAddress',
        align: 'left',
        width: '18em',
        filter: {
          condition: {
            type: 'search',
            value: '',
          },
          sort: 0, //0,1,-1
        },
        // filter: {
        //   sort: 0//-1,1,
        // },
        render: (_: any, recode: any) => {
          const data = recode?.recipientAddress;
          if (data?.isFBA == 1) {
            return (
              <div>
                <Tag color="purple">FBA</Tag>
                <span style={{ fontWeight: '600', marginRight: '10px' }}>
                  {data?.name}
                </span>
                {data?.zipCode}
              </div>
            );
          } else if (data && data?.isFBA !== 1) {
            return (
              <div>
                {textEllipsis(
                  `${data?.province}${data?.city}${data?.county}${data?.zipCode}`,
                )}
              </div>
            );
          }
        },
      },

      {
        title: '品名',
        hideInSearch: true,
        width: 120,
        dataIndex: 'goodsName',
        align: 'center',
        render: (_: any, recode: any) => {
          return recode?.goodsDescription
            ? textEllipsis(recode?.goodsDescription)
            : textEllipsis(recode?.goodsName);
        },
      },
      {
        title: '销售产品',
        hideInSearch: true,
        width: '18em',
        dataIndex: 'productId',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return textEllipsis(recode?.productName);
        },
      },
      {
        title: '转运状态',
        hideInSearch: true,
        width: '16em',
        dataIndex: 'orderState',
        align: 'left',
        filter: {
          sort: 0, //-1,1,
        },
        render: (recode: any, _: any) => {
          return recode?.state?.desc ? (
            <>
              <Tag color="magenta">{recode?.state?.desc}</Tag>{' '}
              {`${recode?.from?.name} -> ${recode?.to?.name}`}
            </>
          ) : (
            ''
          );
        },
      },
      {
        title: '签入时间',
        hideInSearch: true,
        dataIndex: 'signInTime',
        align: 'center',
        width: 200,
        filter: {
          sort: 0, //-1,1,
        },
        render: (recode: any, text: any) => {
          return text?.siginTime;
        },
      },

      {
        title: '客户',
        hideInSearch: true,
        dataIndex: 'clientId',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (text: any) => {
          return text?.name;
        },
      },
      {
        title: '业务员',
        hideInSearch: true,
        dataIndex: 'salesManId',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (text: any) => {
          return text?.name;
        },
      },
      {
        title: '操作',
        key: 'option',
        align: 'left',
        width: 100,
        fixed: 'right',
        hideInSearch: true,
        render: (text: any, record: any) => {
          if (record?.children)
            return (
              <QuickSelectModal
                btnText={'快速选择'}
                btnTitle={`子单选择 ${record.waybillNO}`}
                records={record?.children}
                key={record?.id}
                handleSelect={handleSelect}
              />
            );
        },
      },
    ];
  }, []);
  useImperativeHandle(ref, () => ({
    control() {
      setIsModalOpen(true);
    },
  }));
  // 预估票体积
  const handleInputChange = _.debounce((e: any) => {
    setVolume(e);
    getSubmit();
  }, 1000);

  useEffect(() => {
    /*初始值*/
    if (!isModalOpen) {
      formRef?.current?.resetFields();
      setTotalState({
        select: 0,
        outerActualWeight: 0,
        OuterVolume: 0,
        chargeWeight: 0,
        outerChargeWeight: 0,
        outerVolumeWeight: 0,
        piece: 0,
      });
      setKeyword('');
      settime({});
      setWarehouseId('');
      setDeclarationTypeState('');
      setTermQuery(false);
      setState('');
      setVolume('');
      setProductId('');
      setShipment({});
      setRecipientZipCode('');
      setCheck([]);
      //打开默认首页
      //getAddressInformation(1, 10);
    }
  }, [isModalOpen]);
  // 出货比 minRatio
  const shipmentRatio = _.debounce((e: any, type: string) => {
    if (type === 'min') {
      setShipment({ ...shipment, minRatio: e });
    } else if (type === 'max') {
      setShipment({ ...shipment, maxRatio: e });
    }
    getSubmit();
  }, 500);
  // 出货比 maxRatio
  const handleOk = () => {
    if (check?.length == 0) return message.warning('请选择运单');
    setIsModalOpen(false);
    /*过滤主单*/
    const rowId = check
      ?.filter((item: any) => item !== '' && !item.startsWith('parent'))
      ?.join(',');
    getputIntoConfigure(rowId);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  // 触发表单
  const getSubmit = () => actionRef.current?.reload();
  // 数据替换树形的，在这里就格式化时间表头写rednder格式化时间 children会有问题
  const processedData = (data: any) => {
    return data.map((item: any) => {
      const newItem = {
        ...item,
        siginTime: item?.siginTime ? formatTime(item.siginTime) : '',
        warehouseName: item?.currentWarehouse?.name,
        id: `parent${item.id}`,
      };
      if (newItem.pieceList) {
        newItem.children = newItem.pieceList.map(
          (childItem: any, index: number) => {
            if (childItem?.subWaybillNO) {
              childItem.waybillNO = childItem?.subWaybillNO;
            }
            delete childItem.subWaybillNO;
            return {
              ...childItem,
              pieceNum: `${index + 1}/${item.pieceNum}`,
              warehouseName: childItem?.warehouse?.name,
            };
          },
        );
        delete newItem.pieceList;
      }
      return newItem;
    });
  };

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: check,
    //preserveSelectedRowKeys: true,//快速选择导致无法重新统计，分页以后数据都变化了
    onChange: (selectedRowKeys, selectedRows: any) => {
      //id大于17为子单
      const ids = selectedRowKeys.filter((item: any) => item.length > 17);
      setCheck(ids);
      //统计数据
      statisticalData(selectedRowKeys, selectedRows);
    },
  };
  const flexParam = '1 0 0';
  const HeadTotal = () => {
    return (
      <div className={styles.total}>
        <Row>
          <Col style={{ width: '20em' }}>
            <span
              style={{ color: '#000', fontWeight: 600, marginRight: '1em' }}
            >
              总计
            </span>
            <span style={{ display: 'inline-block', width: '7em' }}>
              <span style={{ marginRight: '2px', color: '#BBB' }}>票：</span>
              <span style={{ color: '#6440FF' }}>{statistic?.sumCount}</span>
            </span>
            <span style={{ display: 'inline-block' }}>
              <span style={{ marginRight: '2px', color: '#BBB' }}>件：</span>
              <span style={{ color: '#6440FF' }}>{statistic?.sumPieceNum}</span>
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label}>出货立方：</span>{' '}
            <span style={{ color: '#00BAD9' }}>
              {statistic?.sumOuterVolume?.toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label}>实重：</span>
            <span style={{ color: '#333' }}>
              {statistic?.sumOuterActualWeight?.toFixed(2)}
            </span>
          </Col>

          <Col flex={flexParam}>
            <span className={styles.label}>密度比例：</span>{' '}
            <span style={{ color: '#333' }}>
              {statistic?.sumOuterActualWeight
                ? (
                    statistic?.sumOuterActualWeight / statistic?.sumOuterVolume
                  ).toFixed(2)
                : '-'}{' '}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label}>出货体积重：</span>{' '}
            <span style={{ color: '#333' }}>
              {statistic?.sumOuterVolumeWeight?.toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label}>收货计费重：</span>{' '}
            <span style={{ color: '#FF40A5' }}>
              {statistic?.sumChargeWeight?.toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label}>出货计费重：</span>{' '}
            <span style={{ color: '#FF40A5' }}>
              {statistic?.sumOuterChargeWeight?.toFixed(2)}{' '}
            </span>
          </Col>
        </Row>
        <Divider style={{ margin: '1em 0' }} />
        <Row>
          <Col style={{ width: '20em' }}>
            <span
              style={{ color: '#000', fontWeight: 600, marginRight: '1em' }}
            >
              已选
            </span>
            <span style={{ display: 'inline-block', width: '7em' }}>
              <span
                style={{
                  marginRight: '2px',
                  visibility: 'hidden',
                }}
              >
                票：
              </span>
              <span style={{ color: '#6440FF' }}>{totalState?.piece}</span>
            </span>
            <span style={{ display: 'inline-block' }}>
              <span
                style={{
                  marginRight: '2px',
                  visibility: 'hidden',
                  color: '#BBB',
                }}
              >
                件：
              </span>
              <span style={{ color: '#6440FF' }}>
                {pieceIds ? pieceIds?.split(',').length : '0'}
              </span>
            </span>
          </Col>
          <Col flex={flexParam}>
            <span style={{ visibility: 'hidden' }} className={styles.label}>
              出货立方：
            </span>
            <span style={{ color: '#00BAD9' }}>
              {' '}
              {Number(totalState?.OuterVolume).toFixed(2)}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label} style={{ visibility: 'hidden' }}>
              实重：
            </span>
            <span style={{ color: '#333' }}>
              {Number(totalState?.outerActualWeight).toFixed(2)}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span style={{ visibility: 'hidden' }} className={styles.label}>
              密度比例：
            </span>
            <span style={{ color: '#333' }}>
              {' '}
              {isNaN(totalState?.outerActualWeight / totalState?.OuterVolume)
                ? '0.00'
                : (
                    totalState?.outerActualWeight / totalState?.OuterVolume
                  ).toFixed(2)}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span className={styles.label} style={{ visibility: 'hidden' }}>
              出货体积重：
            </span>{' '}
            <span style={{ color: '#333' }}>
              {Number(totalState?.outerVolumeWeight).toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam}>
            <span style={{ visibility: 'hidden' }} className={styles.label}>
              收货计费重：
            </span>
            {/*   {statistic?.sumChargeWeight}*/}
          </Col>

          <Col flex={flexParam}>
            <span style={{ visibility: 'hidden' }}>
              {' '}
              className={styles.label}出货计费重:
            </span>
            {/*  {statistic?.sumOuterChargeWeight}*/}
            {/*  {statistic?.sumOuterChargeWeight}*/}
          </Col>
        </Row>
      </div>
    );
  };
  return (
    <Drawer
      title={
        title ? (
          <div className={styles.title}>
            <div style={{ marginRight: '32px' }}>选择运单</div>
            <span className={styles.label}>已配舱：</span>
            <span style={{ color: '#0443ae' }}>{title.has}</span>
            <div
              className={styles.label}
              style={{ marginRight: '32px', marginLeft: '5px' }}
            >
              m³
            </div>
            <span className={styles.label}>空余：</span>
            <span style={{ color: '#00BAD9' }}>{title.noHas}</span>
            <span className={styles.label} style={{ marginLeft: '5px' }}>
              m³
            </span>
          </div>
        ) : (
          '选择运单'
        )
      }
      style={{
        width: '100vw',
        position: 'fixed',
        top: '1px',
        bottom: '1px',
        // marginBottom: '-1em',
        right: '1px',
        left: '1px',
      }}
      destroyOnClose
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      onClose={handleCancel}
      footer={null}
    >
      <div
        id={'modal'}
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100% - 40px)',
        }}
      >
        <Row gutter={[4, 0]}>
          <Col span={6}>
            <div className={styles.search_wrap} id="search_left">
              <Select
                defaultValue="批量"
                bordered={false}
                style={{
                  height: '100%',
                  border: 'none',
                  backgroundColor: '#f7f7f7',
                }}
                onChange={(value: any) => {
                  setTermQuery(value);
                  getSubmit();
                }}
              >
                <Option value={true}>精确</Option>
                <Option value={false}>批量</Option>
              </Select>
              <TextArea
                className={styles.text_area}
                id="search_text"
                style={{
                  resize: 'none',
                  height: '77px',
                  borderRadius: '0px',
                  border: 'none',
                  paddingTop: '27px',
                }}
                onChange={(e: any) => setKeyword(e.target.value)}
                placeholder="请输入ID,运单号，客户代码，客户名称，业务员搜索"
                onPressEnter={() => {
                  setSearch(true);
                  getSubmit();
                }}
              />
              <Button
                style={{ height: '100%', color: '#537ffd', border: 'none' }}
                className={styles.search_back}
                onClick={() => {
                  setSearch(true);
                  getSubmit();
                }}
              >
                搜索
              </Button>
            </div>
          </Col>
          <Col span={18}>
            <Row style={{ boxSizing: 'border-box' }} wrap={false}>
              <Col span={4} className={styles.clientele}>
                <span className={styles.organization}>头程类型</span>
                <Select
                  placeholder="请输入"
                  style={{
                    border: 'none',
                    backgroundColor: 'rgb(251, 251, 251)',
                    width: 150,
                  }}
                  options={[
                    { label: '空运', value: 1 },
                    { label: '海运', value: 2 },
                  ]}
                  allowClear
                  value={HeadTransType}
                  onChange={(e) => {
                    setHeadTransType(e);
                    getSubmit();
                    // handleInputChange(e?.target?.value)
                  }}
                />
              </Col>
              <Col span={4} className={styles.clientele}>
                <span className={styles.organization}>报关类型</span>
                <Select
                  placeholder="请输入"
                  style={{
                    border: 'none',
                    backgroundColor: 'rgb(251, 251, 251)',
                    width: 150,
                  }}
                  options={[
                    { label: '非单独报关', value: 10 },
                    { label: '单独报关', value: 20 },
                    { label: '合并报关', value: 30 },
                  ]}
                  allowClear
                  onChange={(e) => {
                    setDeclarationTypeState(e);
                    getSubmit();
                    // handleInputChange(e?.target?.value)
                  }}
                />
              </Col>
              <Col span={4} className={styles.clientele}>
                <span className={styles.organization}>站点</span>
                <ProFormSelect
                  name="warehouse"
                  showSearch
                  // rules={[{ required: true, message: '请输入站点' }]}
                  labelCol={{ span: 5 }}
                  wrapperCol={{ span: 12 }}
                  noStyle
                  style={{ display: 'flex' }}
                  request={async ({ keyWords }) => {
                    const { status, data } = await getWarehouseListAPI({
                      type: 0,
                      len: 20,
                      keyword: keyWords,
                    });
                    if (status) {
                      return data.list.map((item: any) => {
                        return {
                          label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
                          value: item.id,
                        };
                      });
                    }
                    return [];
                  }}
                  fieldProps={{
                    onChange: (e, option) => {
                      setWarehouseId(e);
                      getSubmit();
                    },
                    filterOption: false,
                  }}
                ></ProFormSelect>
              </Col>
              <Col span={4}>
                <div className={styles.clientele}>
                  <span className={styles.organization}>邮编</span>
                  <Input
                    placeholder="请输入"
                    style={{ display: 'flex' }}
                    onChange={(e) => {
                      setRecipientZipCode(e.target.value);
                      getSubmit();
                    }}
                  />
                </div>
              </Col>
              <Col span={8} className={styles.clientele}>
                <span className={styles.organization}>创建时间</span>
                <Time changeTime={changeTime} style={{ width: '100%' }} />
              </Col>
            </Row>
            <Row wrap={false}>
              <Col span={4} className={styles.clientele}>
                <span className={styles.organization}>状态</span>
                <Select
                  placeholder="请选择"
                  allowClear
                  onChange={(e) => {
                    setState(e);
                    getSubmit();
                  }}
                  style={{ display: 'flex', marginRight: '8px' }}
                  options={[
                    { value: -1, label: '所有' },
                    //   { value: 0, label: '草稿' },
                    // { value: 10, label: '待签入' },
                    // { value: 20, label: '已签入' },
                    { value: 40, label: '待配仓' },
                    { value: 60, label: '已配舱' },
                    // { value: 90, label: '已签出' },
                    // { value: 200, label: '运输中' },
                    // { value: 300, label: '已签收' },
                    // { value: 500, label: '已结束' }
                  ]}
                />
              </Col>
              <Col span={4} className={styles.clientele}>
                <span className={styles.organization}>预估票体积</span>
                <Input
                  placeholder="请输入"
                  step="1"
                  type="number"
                  style={{ display: 'flex' }}
                  onChange={(e) => {
                    handleInputChange(e?.target?.value);
                  }}
                />
              </Col>
              <Col span={8} className={`${styles.clientele}  ${styles.sale}`}>
                <span className={`${styles.organization}`}>销售产品</span>
                <ProFormSelect
                  name="product"
                  showSearch
                  mode={'multiple'}
                  labelCol={{ span: 5 }}
                  wrapperCol={{ span: 12 }}
                  noStyle
                  bordered={false}
                  style={{ display: 'flex' }}
                  request={async ({ keyWords }) => {
                    const { status, data } = await getProductList({
                      type: 0,
                      len: 20,
                      keyword: keyWords,
                    });
                    if (status) {
                      return data.list.map((item: any) => {
                        return {
                          label: item?.name,
                          value: item?.id,
                        };
                      });
                    }
                    return [];
                  }}
                  fieldProps={{
                    onChange: (e, option) => {
                      setProductId(e);
                      getSubmit();
                    },
                    maxTagCount: 2,
                    bordered: false,
                    style: { display: 'flex' },
                    filterOption: false,
                  }}
                />
              </Col>
              <Col span={8} className={styles.clientele}>
                <div style={{ display: 'flex' }}>
                  <span className={styles.organization}>出货比重</span>
                  <Input
                    placeholder="请输入"
                    step="1"
                    type="number"
                    style={{ flex: 1 }}
                    onChange={(e) => {
                      shipmentRatio(e?.target?.value, 'min');
                    }}
                  />
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    -
                  </span>
                  <Input
                    placeholder="请输入"
                    step="1"
                    type="number"
                    style={{ flex: 1 }}
                    onChange={(e) => {
                      shipmentRatio(e?.target?.value, 'max');
                    }}
                  />
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
        <HeadTotal />
        {/* <div className={styles.tableWrap}> */}
        <MrTable
          columns={columns}
          checkData={checkData}
          keyID={'/BOL/PreplanCabin/particulars/addWaybill'}
          Search={Search}
          modalTableY
          //headerTitle={<HeadTotal />}
          modalCopy={{
            x: 100,
            y: 320,
          }}
          options={false}
          //rowSelectionCablck={rowSelectionCablck}
          request={async (params: any, action: any) => {
            actionRef.current = action;
            if (!Search) return;
            // const { current: start, pageSize: len } = params;
            // const param: any = {
            //   ...Params,
            //   start: (start - 1) * len,
            //   len,
            //   keyword,
            // };
            const msg = await waybillListAPI({
              ...params,
              condition: {
                keyword: {
                  value: keyword,
                  termQuery,
                },
                state: {
                  value: state,
                },
                time: {
                  ...time,
                },
                volume: {
                  value: volume,
                },
                productIds: {
                  value: productId ? productId?.join(',') : '',
                },
                headTransType: {
                  value: HeadTransType,
                },
                minRatio: {
                  value: shipment?.minRatio,
                },
                maxRatio: {
                  value: shipment?.maxRatio,
                },
                warehouseId: {
                  value: warehouseId,
                },
                recipientZipCode: {
                  value: recipientZipCode,
                },
                declarationType: {
                  value: declarationTypeState,
                },
                // keyword,
                // state,
                // ...time,
                // volume,
                // productIds: productId ? productId?.join(',') : '',
                // headTransType: HeadTransType,
                // ...shipment,
                // warehouseId,
                // termQuery,
                // recipientZipCode,
                // declarationType: declarationTypeState,
              },
            });
            const newData = processedData(msg?.data?.list || []);
            const PieceList: any = [];
            newData.map((item: any) => PieceList.push(item.children));
            pieceData.current = PieceList;
            //设置总数据
            setSatistic(msg?.data?.statistic);
            setTotal(msg.data.total);
            return {
              data: type === 'match' ? newData : msg?.data?.list || [],
              success: msg.status,
              total: msg.data.total,
            };
          }}
          rowSelectionCablck={(value: any) => {
            const ids = value
              ?.map((item: any) => item?.id)
              .filter((item: any) => item.length > 17);
            setCheck(ids);
            //统计数据
            statisticalData(ids, value);
          }}
        />
      </div>
      <div
        style={{
          height: '40px',
          width: '100%',
          display: total ? 'none' : 'block',
        }}
      ></div>
      <div
        key="btn"
        style={{
          position: 'absolute',
          bottom: '16px',
          left: '40px',
          zIndex: '10',
        }}
      >
        <Button
          key="back"
          onClick={handleCancel}
          style={{ background: '#d8d8d8' }}
        >
          取消
        </Button>
        <Button
          key="submit"
          type="primary"
          onClick={handleOk}
          style={{ marginLeft: 10, background: '#D91A58 ' }}
        >
          确认
        </Button>
      </div>
      {/* </div> */}
    </Drawer>
  );
});
export default AddWaybill;
