.waybill_modal {
    :global {
        .ant-select {
            width: 180px !important;
        }

        .ant-select-selector {
            background-color: #FBFBFB !important;
            border: none !important;
        }

        .ant-picker.ant-picker-range {
            border: none !important;
        }

        .ant-pro-table-list-toolbar-container {
            padding: 0;
            padding-bottom: 33px;
        }

        .ant-table-header {

            .resizable-handler:hover .resizable-line,
            .resizable-handler:active .resizable-line {
                background-color: #1677ff !important;
            }

            .resizable-handler {
                width: 3px;
                right: -3px
            }
        }

        .ant-table-thead {
            .ant-table-cell {
                background-color: #FBFBFD !important;
                color: #8098AB !important;
                font-weight: 400;
            }
        }

        .ant-pro-card-body {
            padding: 0 !important;
        }

        .ant-modal-content {
            padding: 16px 24px !important;
        }

        .ant-form-horizontal {
            padding: 0 !important;
        }

        .ant-pro-table-list-toolbar-container {
            padding-bottom: 10px !important;

        }

        .ant-table-body {
            height: calc(100vh - 290px) !important;
        }
    }

}

.tableWrap {
    :global(.ant-pro-table-list-toolbar-container) {
        height: 0;
    }
}

.kou {

    //background: #ffeae9;
    :global(.ant-table-cell-row-hover) {
        background: #FFE1E0 !important;
    }

    :global(.ant-table-cell-fix-right) {
        //background: #adad85 ;
    }
}

.total {
    font-size: 16px;
    width: 96vw;
}

.search_wrap {
    border: 1px solid #e8e8e8;
    height: 80px;
    display: flex;
    border-radius: 8px;

    :global(.ant-select-selector) {
        height: 100%;

        :global(.ant-select-selection-item) {
            display: flex;
            align-items: center;
        }
    }
}

.title {
    display: flex;

}

.label {
    color: #BBB;
    font-weight: 400;
    font-size: 16px;
}

.clientele {
    //width: calc(100% - 84px) !important;
    height: 38px;
    border-radius: 4px;
    border: 1px solid #E5E5E5;
    display: flex;
    margin-bottom: 8px;
    box-sizing: border-box;
    margin-right: 4px;

    .organization {
        display: flex;
        justify-content: left;
        align-items: center;
        width: 84px;
        height: 38px;
        font-weight: 400;
        font-size: 0.8em;
        color: #707070;
        flex-shrink: 0;
        border-right: 1px solid #E5E5E5;
        padding-left: 12px;
        box-sizing: border-box;
    }

    :global {
        .ant-select {
            width: calc(100% - 60px) !important;
            border: none;
        }

        .ant-select-single {
            width: 100% !important;
            height: 100% !important;

            .ant-select-selector {
                border: none;
            }
        }

        .ant-picker,
        .ant-input {
            border: none !important;
        }
    }
}

.sale {

    :global {
        .ant-select-selector {
            width: 100% !important;
            //border: none;
        }
    }
}