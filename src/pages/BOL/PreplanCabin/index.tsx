import React, { memo, useState, useMemo } from 'react';
import Shipping from './Shipping';
import Waybill from './Waybill';
import SetModule from './SetModule';
import Waybill2 from '@/pages/BOL/PreplanCabin/Waybill2';
import NewPreplanCabin from '@/pages/BOL/PreplanCabin/NewPreplanCabin';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import TabsType from '@/components/TabsType';

interface EditableRowProps {
  index?: number;
}
const PreplanCabin: React.FC = () => {
  const [toList] = usePermissionFiltering();
  const [activeKey, setActiveKey] = useState<string>('1');
  const onChange = (key: any) => {
    setActiveKey(key);
  };
  const items = [
    {
      label: '舱位',
      key: '1',
      //children: <ReportCustomList />,
      // accessible:[':Transport:TrailerPlanAccess']
    },
    {
      label: '预配舱',
      key: '2',
      //children: <ClearCustomList />,
      // accessible:[':Transport:TrailerExecutiveAccess']
    },

    /*{
      label: '运单222',
      key: '3',
      //children: <ReportGroup />,
    },*/
    {
      label: '运单',
      key: '4',
    },
  ];
  const tabsProps = useMemo(
    () => ({ activeKey, items: toList(items), onChange }),
    [activeKey],
  );
  const renderComponents: any = {
    '1': <Shipping />,
    '2': <NewPreplanCabin />,
    /*'3': <Waybill />,*/
    '4': <Waybill2 />,
    // '5':<SetModule/> //2024 01 05 要求暂时隐藏 需求人：张
  };
  return (
    <>
      <TabsType {...tabsProps} />
      <div style={{ height: 'calc(100vh - 115px)' }}>
        {renderComponents[activeKey]}
      </div>
    </>
  );
};
export default PreplanCabin;
