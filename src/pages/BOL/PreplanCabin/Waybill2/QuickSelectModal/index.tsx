import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Button, Form, Input, InputNumber, message, Space, Tag } from "antd";
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import { editWaybillLabel } from '@/services/Waybilladministration';
import { getLabelByValue, goodsTypeMap } from '@/utils/constant';
import { AddNewPreplanCabin } from '@/services/preplanCabin';
const { CheckableTag } = Tag;
import styles from "./index.less";
const QuickSelectModal = (props: any) => {
  const { records, btnTitle, btnType, length,setCheck,check,pieceIds,handleSelect,btnText } = props;
  const [vals, setVals] = useState<any>({
    start: '',
    end: '',
  });

  //console.log('record', records);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [selectedTags, setSelectedTags] = useState<number[]>(['Books']);
  const [modalVisit, setModalVisit] = useState(false);
  const showModal = () => {
      setModalVisit(true);
  };
  //console.log('check',check);
  /* 提交 */
  const onSelect = async () => {
    console.log('选择');
    const datas = records.filter((item, index) => {
      const number = index + 1;
      return vals?.start <= number && vals?.end >= number;
    });
    handleSelect(datas,'add');
    //setCheck([...check]);
    setModalVisit(false);
  };
  const onReSelect = async () => {
    console.log('反选');
    console.log('records',records);
    const datas = records?.filter((item, index) => {
      const number = index + 1;
      return vals?.start <= number && vals?.end >= number;
    });
    handleSelect(datas,'delete');
    //setCheck([...check]);
    setModalVisit(false);
  };
  return (
    <div>
      <Button style={{ color: '#1677ff' }} onClick={showModal} type={'link'}>
        {btnText}
      </Button>
      <ModalForm
        title={btnTitle}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        width={'398px'}
        open={modalVisit}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
          className:styles.modal
        }}
        submitter={{
          render: (props, doms) => {
            console.log(props);
            return [
              <Button
                key="wuliu"
                type={'dashed'}
                onClick={() =>  setModalVisit(false)}
                style={{color:'#707070',border: '1px solid #d8d8d8'}}
              >
                忽略
              </Button>,

              <Button
                key="rest"
                type={'dashed'}
                style={{color:'#4071FF',border: '1px solid #4071FF'}}
                onClick={() =>onReSelect()}
              >反选
              </Button>,
              <Button
                key="select"
                type={'primary'}
                onClick={() => props.form?.submit?.()}
              >
                选择
              </Button>,

            ];
          },
          submitButtonProps: {
            style: {
              display: 'none',
            },
          },
        }}
        layout="horizontal"
        submitTimeout={2000}
        onFinish={onSelect}
      >
        <div className="flex items-center justify-between" style={{marginTop:'20px'}}>
          <div className="mt-20px" style={{display:'flex'}}>
            <div className={styles.label}>序号范围</div>
            <InputNumber
              style={{ width: 120,fontSize:'13px' }}
              placeholder="<起始 (>=1)"
              value={vals.start}

              onChange={(e) => {
                setVals({
                  ...vals,
                  start: e,
                });
              }}
            />{' '}
            <span style={{margin:'0 8px',display:'flex',alignItems:'center'}}>-</span>
            <InputNumber
              style={{ width: 120,fontSize:'13px'  }}
              placeholder={`截止 (<=${records.length})`}
              value={vals.end}
              onChange={(e) => {
                setVals({
                  ...vals,
                  end: e,
                });
              }}
            />
          </div>

        </div>
      </ModalForm>
    </div>
  );
};
export default QuickSelectModal;
