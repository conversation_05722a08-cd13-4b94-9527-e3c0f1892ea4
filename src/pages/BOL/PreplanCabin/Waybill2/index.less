.total {
  padding: 20px 20px 5px 20px;
  font-size: 16px;
  width: 100%;
  background: #fff;
}

.inland_waybill {
  display: flex;
  flex-direction: column;

  /*去掉底部空间*/
  :global(.ant-pro-query-filter) {
    padding: 0 !important;
  }

  .waybill_modal {
    background: #FFFFFF;
    padding-top: 10px;

    .search_wrap {
      border: 1px solid #e8e8e8;
      height: 80px;
      display: flex;
      border-radius: 8px;

      :global(.ant-select-selector) {
        height: 100%;

        :global(.ant-select-selection-item) {
          display: flex;
          align-items: center;
        }
      }
    }

    .clientele {
      //width: calc(100% - 84px) !important;
      height: 38px;
      border-radius: 4px;
      border: 1px solid #E5E5E5;
      display: flex;
      margin-bottom: 8px;
      box-sizing: border-box;
      margin-right: 4px;

      .organization {
        display: flex;
        justify-content: left;
        align-items: center;
        width: 84px;
        height: 38px;
        font-weight: 400;
        font-size: 0.8em;
        color: #707070;
        flex-shrink: 0;
        border-right: 1px solid #E5E5E5;
        padding-left: 12px;
        box-sizing: border-box;

        :global(.ant-input-affix-wrapper) {
          //border-radius: 0 !important;
        }
      }

      :global {
        .ant-select {
          width: calc(100% - 60px) !important;
          border: none;
        }

        .ant-select-single {
          width: 100% !important;
          height: 100% !important;

          .ant-select-selector {
            border: none;
          }
        }

        .ant-picker,
        .ant-input {
          border: none !important;
        }
      }
    }
  }

  :global {
    .ant-form {
      //display: none !important;
    }

    .ant-pro-card-border {
      border: none !important;
    }

    .ant-pro-table-list-toolbar-title {
      font-size: 14px !important;
    }

    .ant-btn-default {
      border-color: #4071FF !important;
      color: #4071FF;
      margin-right: 10px;
    }
  }

  .waybill_search {
    display: flex;
    // height: 80px;
    background-color: #fff;
    flex-wrap: wrap;
    box-sizing: border-box;
    padding: 20px 20px 0 20px;
    border-radius: 0 0 10px 10px;

    >div {
      margin-bottom: 20px;
    }

    :global {
      .ant-select {
        height: 32px !important;
      }

      .ant-select-selector {
        background-color: #FBFBFB !important;
        border: none !important;
      }

      .ant-picker.ant-picker-range {
        border: none !important;
      }
    }

    >div {
      margin-left: 30px;
    }
  }

  .tab_title {
    display: flex;
    justify-content: space-between;
    width: 200px;
  }
}