import React, { FC, memo, useRef, useState, useMemo, useEffect } from 'react';
import {
  Button,
  Col,
  Divider,
  Input,
  message,
  Row,
  Select,
  Tag,
  Tooltip,
} from 'antd';
import { ProFormSelect, ProTable } from '@ant-design/pro-components';
import {
  getWarehouseListAPI,
  getputIntoConfigureAPI,
  waybillListAPI,
} from '@/services/preplanCabin';
import { formatTime } from '@/utils/format';
import type { TableRowSelection } from 'antd/es/table/interface';
import _ from 'lodash';
import { getProductList } from '@/services/productAndPrice/api';
import SelectShipping from '../SelectShipping';
import styles from './index.less';
import AccessCard from '@/AccessCard';

import TextArea from 'antd/es/input/TextArea';
import { printStatusEnume } from '@/pages/Waybilladministration/InlandTransfer/components/enumeration';
import Time from '@/pages/Booking/components/Time';
import AddCabinModal from '@/pages/BOL/PreplanCabin/Waybill2/AddCabinModal';
import MrTable, { textEllipsis } from '@/components/MrTable';
import { waybillEnume } from '@/pages/BOL/PreplanCabin/enumeration';
import { waybillStatusEnum } from '@/utils/constant';
import { calculateSum } from '@/utils/utils';
import QuickSelectModal from '@/pages/BOL/PreplanCabin/Waybill2/QuickSelectModal';
import { history } from '@@/core/history';
import { useSelector } from 'react-redux';
const Waybill2: FC = () => {
  const mrTableRef = useRef();
  const [keyword, setKeyword] = useState('');
  const formRef = useRef<any>(null);
  const actionRef = useRef<any>(null);
  const seleShippingRef = useRef<any>(null);
  const [tableTitleData, setTableTitleData] = useState<any>({
    volume: 0,
    weight: 0,
    outerChargeWeight: 0,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [prodData, setProdData] = useState([]);
  const previousValueRef = useRef<any>(null);
  const [check, setCheck] = useState<any>([]);
  const [warehouse, setWarehouse] = useState<any>([]);
  const [waybillData, setWaybillData] = useState<any>([]);
  const [inquirePara, setInquirePara] = useState<any>({});
  const [pieceIds, setPieceIds] = useState('');
  const [Search, setSearch] = useState(false);
  const subunit = useRef<any>([])
  const getChecked = useSelector(
    (state: any) => state?.tableFilter?.checkbox
  );
  /*已选总数据*/
  const [totalState, setTotalState] = useState({
    select: 0,
    outerActualWeight: 0,
    OuterVolume: 0,
    chargeWeight: 0,
    outerChargeWeight: 0,
    outerVolumeWeight: 0,
    piece: 0,
  });
  //统计的数据
  const pieceData = useRef();
  //统计的数据
  const [checkData, setCheckData] = useState<any>([]);

  const textEllipsis = (text: any) => {
    return (
      <div
        className="text-ellipsis"
        style={{
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          overflow: 'hidden',
        }}
      >
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>
    );
  };
  /*统计数据*/
  const statisticalData = (selectedRowKeys: any, selectedRows: any) => {
    const rowId = selectedRowKeys
      ?.filter((item: any) => item !== '' && !item.startsWith('parent'))
      ?.join(',');
    const select = rowId ? rowId?.split(',').length : 0;
    console.log('select', select);
    const outerActualWeight = calculateSum(
      selectedRows,
      'outerActualWeight',
      3,
    );
    /*过滤主单的体积后计算*/
    const subRecords = selectedRows.filter(
      (row: any) => row.productId === undefined,
    );
    console.log('subRecords', subRecords);
    const OuterVolume = calculateSum(subRecords, 'outerVolume', 5);
    /*过滤主单的体积重后计算*/
    const outerVolumeWeight = calculateSum(subRecords, 'outerVolumeWeight', 5);
    console.log('outerVolumeWeight', outerVolumeWeight);
    //获取主单数据
    const uniquePrefixes = new Set(
      selectedRowKeys
        ?.filter((item: any) => item !== '' && !item.startsWith('parent'))
        ?.map((item: string | any[]) => item.slice(0, 17)),
    );
    console.log('uniquePrefixes', uniquePrefixes);
    //统计数据
    setTotalState({
      ...totalState,
      select,
      outerActualWeight: outerActualWeight,
      OuterVolume: OuterVolume,
      outerVolumeWeight: outerVolumeWeight,
      piece: uniquePrefixes.size,
    });
    setPieceIds(rowId);
  };

  /*快速选择添加*/
  const handleSelect = (select: any, doSome: any) => {
    /*把数组合并*/
    function mergeArrays(arr: any[]) {
      console.log('arr', arr);
      return arr?.reduce((acc, val) => acc?.concat(val), []);
    }
    //过滤母单的ids
    const ids = select
      .map((item: any) => item.id)
      .filter((item: string) => !item.startsWith('parent'));
    if (doSome === 'add') {
      //重新统计数据
      setCheck((prevCheck: any) => {
        const newCheck = [...new Set([...prevCheck, ...ids])];
        console.log('check', newCheck); // 在这里直接打印最新的状态
        const allPieceData = mergeArrays(pieceData.current);
        const allSelect = allPieceData.filter((item) =>
          newCheck.includes(item.id),
        );
        setCheckData(allSelect);
        statisticalData(newCheck, allSelect);
        return newCheck;
      });
      /*所有选择的数据*/
    } else {
      console.log('ids', ids);
      //过滤删除的数据设置到统计
      console.log('check', check);
      setCheck((prevCheck: any[]) => {
        const newCheck = prevCheck.filter((item) => !ids?.includes(item));
        const allPieceData = mergeArrays(pieceData.current);
        const allSelect = allPieceData.filter((item: { id: any }) =>
          newCheck.includes(item.id),
        );
        console.log('newCheck', newCheck);
        subunit.current = []
        setCheckData(allSelect);
        statisticalData(newCheck, allSelect);
        return newCheck;
      });
    }
  };
  //统计的数据
  const [statistic, setSatistic] = useState<any>({});
  const [Params, setParams] = useState<any>({ termQuery: false });
  const setNewParams = (key: any, value: any) => {
    setSearch(true);
    setParams({ ...Params, [key]: value });
    refresh();
  };
  const enumeration: any = {
    10: '非单独报关',
    20: '单独报关',
    30: '合并报关',
  };
  const refresh = () => {
    actionRef.current?.reload();
  };
  const getKeywords = (value: any) => {
    let warehouseStates: any = []; //仓库已完成阶段
    let states: any = []; //运单状态
    let receiptKeywords: any = []; //目的地关键词
    let interceptFlags: any = []; //拦截状态
    let productTypes: any = []; //产品类型
    let holdFlags: any = []; //其他关键词
    let configureState: any = []; //配舱状态
    value.map((item: any) => {
      const i = item.value.split('-');
      if (i[0] === '1') {
        warehouseStates.push(i[1]);
      } else if (i[0] === '2') {
        states.push(i[1]);
      } else if (i[0] === '3') {
        receiptKeywords.push(i[1]);
      } else if (i[0] === '4') {
        interceptFlags.push(i[1]);
      } else if (i[0] === '5') {
        productTypes.push(i[1]);
      } else if (i[0] === '6') {
        holdFlags.push(i[1]);
      } else if (i[0] === '7') {
        configureState.push(i[1]);
      }
      return item.value;
    });
    setInquirePara({
      ...inquirePara,
      warehouseStates: warehouseStates.join(','),
      states: states.join(','),
      receiptKeywords: receiptKeywords.join(','),
      interceptFlags: interceptFlags.join(','),
      productTypes: productTypes.join(','),
      holdFlags: holdFlags.join(','),
      configureState: configureState.join(','),
    });
    refresh();
  };
  const columns: any = useMemo(() => {
    return [
      {
        title: '运单号',
        dataIndex: 'waybillNO',
        align: 'center',
        width: 150,
        hideInSearch: true,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return <div id='waybillNO' onClick={(e) => {
            if (recode?.children) {
              subunit.current = removeDuplicateById([...subunit.current, ...recode?.children])
            } else {
              subunit.current = removeDuplicateById([...subunit.current, recode])
            }
            const ids = subunit.current
              ?.map((item: any) => item?.id)
              .filter((item: any) => item.length > 17)
            // setCheck(ids)
            //统计数据
            statisticalData(ids, subunit.current);
            setCheckData(subunit.current)
          }}>{recode?.waybillNO}</div>
        }
      },
      {
        title: '件数',
        dataIndex: 'pieceNum',
        hideInSearch: true,
        align: 'center',
        width: 100,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.pieceNum;
        },
      },
      {
        title: '体积（m³）',
        hideInSearch: true,
        dataIndex: 'signVolume',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.siginVolume;
        },
      },
      {
        title: '配舱状态',
        hideInSearch: true,
        width: 120,
        dataIndex: 'configureState',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          if (recode?.state === '40') {
            return (
              <Tag
                style={{
                  ...waybillStatusEnum[recode?.state]?.styles,
                  color:
                    waybillStatusEnum[recode?.state]?.styles?.fontSizeColor,
                }}
                color={waybillStatusEnum[recode?.state]?.color}
              >
                待配仓
              </Tag>
            );
          } else if (recode?.state === '60' || recode?.blnos?.includes(',')) {
            return (
              <div>
                <Tag
                  style={{
                    ...waybillStatusEnum[recode?.state]?.styles,
                    color:
                      waybillStatusEnum[recode?.state]?.styles?.fontSizeColor,
                  }}
                  color={waybillStatusEnum[recode?.state]?.color}
                >
                  {waybillStatusEnum[recode?.state]?.text}
                </Tag>
                <Tag style={{ background: '#6003ce', color: 'white' }}>
                  分票
                </Tag>
              </div>
            );
          } else {
            return (
              <Tag
                style={{
                  ...waybillStatusEnum[recode?.state]?.styles,
                  color:
                    waybillStatusEnum[recode?.state]?.styles?.fontSizeColor,
                }}
                color={waybillStatusEnum[recode?.state]?.color}
              >
                {waybillStatusEnum[recode?.state]?.text}
              </Tag>
            );
          }
        },
      },
      {
        title: '提单号',
        hideInSearch: true,
        width: 140,
        dataIndex: 'blno',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, record: any) => {
          if (record?.children) {
            return textEllipsis(record?.blnos);
          } else {
            return textEllipsis(
              record?.configState?.blno || record?.configState?.configName,
            );
          }
        },
      },
      {
        title: '所在仓库',
        hideInSearch: true,
        dataIndex: 'currentWarehouseName',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.warehouseName;
        },
      },
      {
        title: '报关类型',
        hideInSearch: true,
        dataIndex: 'declarationType',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode ? enumeration[recode?.declarationType] : '';
        },
      },
      {
        title: '报关单',
        hideInSearch: true,
        dataIndex: 'declarationId',
        align: 'center',
        width: 120,
        ellipsis: true,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.declarationId;
        },
      },
      {
        title: '品名',
        hideInSearch: true,
        width: 120,
        dataIndex: 'goodsDescription',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        ellipsis: true,
        render: (_: any, recode: any) => {
          return recode?.goodsDescription;
        },
      },

      {
        title: '销售产品',
        hideInSearch: true,
        width: 150,
        dataIndex: 'productId',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return textEllipsis(recode?.productName);
        },
      },
      {
        title: '打单状态',
        hideInSearch: true,
        width: 200,
        dataIndex: 'orderState',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return (
            <Tag color={printStatusEnume[recode.orderState]?.color}>
              {printStatusEnume[recode.orderState]?.text}
            </Tag>
          );
        },
      },
      {
        title: '转运状态',
        hideInSearch: true,
        width: 300,
        dataIndex: 'transferState',
        align: 'center',
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, recode: any) => {
          return recode?.transferState?.state?.desc ? (
            <>
              <Tag color="magenta">{recode?.transferState?.state?.desc}</Tag>{' '}
              {`${recode?.transferState?.from?.name} -> ${recode?.transferState?.to?.name}`}
            </>
          ) : (
            ''
          );
        },
      },
      {
        title: '签入时间',
        hideInSearch: true,
        dataIndex: 'signInTime',
        align: 'center',
        width: 200,
        filter: {
          sort: 0, //-1,1,
        },
        render: (recode: any, text: any) => {
          return text?.siginTime;
        },
      },
      {
        title: '到货时间',
        hideInSearch: true,
        dataIndex: 'arriveTime',
        align: 'center',
        width: 200,
        filter: {
          sort: 0, //-1,1,
        },
        render: (recode: any, text: any) => {
          return formatTime(text?.arriveTime);
        },
      },
      // {
      //   title: '目的地',
      //   hideInSearch: true,
      //   dataIndex: 'recipientAddress',
      //   align: 'center',
      //   width: 200,
      //   ellipsis: true,
      //   filter: {
      //     condition: {
      //       type: "search",
      //       value: "",

      //     },
      //     sort: 0 //0,1,-1
      //   },
      //   render: (_: any, recode: any) => {
      //     const data = recode?.recipientAddress;
      //     if (data?.isFBA == 1) {
      //       return (
      //         <div>
      //           <Tag color="purple">FBA</Tag>
      //           {`${data?.name}  ${data?.zipCode}`}
      //         </div>
      //       );
      //     } else if (data && data?.isFBA !== 1) {
      //       return (
      //         <div>{`${data?.province}${data?.city}${data?.county}${data?.zipCode}`}</div>
      //       );
      //     }
      //   },
      // },
      {
        title: '目的邮编',
        hideInSearch: true,
        dataIndex: 'recipientAddress',
        align: 'center',
        width: 200,
        ellipsis: true,
        filter: {
          condition: {
            type: "search",
            value: "",

          },
        },
        render: (_: any, recode: any) => {
          const data = recode?.recipientAddress;
          return (
            <div>
              {data?.fbaCode}
            </div>
          );
        },
      },
      {
        title: '仓库代码',
        hideInSearch: true,
        dataIndex: 'warehouseCode',
        align: 'center',
        width: 200,
        ellipsis: true,
        filter: {
          condition: {
            type: "search",
            value: "",

          },
          sort: 0 //0,1,-1
        },
        render: (_: any, recode: any) => {
          const data = recode?.recipientAddress;
          return (
            <div>
              {data?.zipCode}
            </div>
          );
        },
      },
      {
        title: '客户',
        hideInSearch: true,
        dataIndex: 'client',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, text: any) => {
          return text?.client?.name;
        },
      },
      {
        title: '业务员',
        hideInSearch: true,
        dataIndex: 'salesman',
        align: 'center',
        width: 120,
        filter: {
          sort: 0, //-1,1,
        },
        render: (_: any, text: any) => {
          return text?.salesman?.name;
        },
      },
      {
        title: '操作',
        key: 'option',
        align: 'left',
        width: 100,
        fixed: 'right',
        hideInSearch: true,
        render: (text: any, record: any) => {
          if (record?.children)
            return (
              <QuickSelectModal
                btnText={'快速选择'}
                btnTitle={`快速选择 ${record.waybillNO}`}
                records={record?.children}
                key={record?.id}
                handleSelect={handleSelect}
              />
            );
        },
      },
    ];
  }, []);
  function removeDuplicateById(arr: any) {
    return arr.reduce((acc: any, curr: any) => {
      let exists = acc.some((item: any) => item.id === curr.id);
      if (!exists) {
        acc.push(curr);
      } else {
        acc = acc?.filter((item: any) => item?.id !== curr?.id)
      }
      return acc;
    }, []);
  }
  const flexParam = '1 0 0';
  const HeadTotal = () => {
    return (
      <div className={styles.total}>
        <Row wrap={false}>
          <Col style={{ width: '20em' }}>
            <span
              style={{ color: '#000', fontWeight: 600, marginRight: '1em' }}
            >
              总计
            </span>
            <span style={{ display: 'inline-block', width: '7em' }}>
              <span style={{ marginRight: '2px', color: '#BBB' }}>票：</span>
              <span style={{ color: '#6440FF' }}>{statistic?.sumCount}</span>
            </span>
            <span style={{ display: 'inline-block' }}>
              <span style={{ marginRight: '2px', color: '#BBB' }}>件：</span>
              <span style={{ color: '#6440FF' }}>{statistic?.sumPieceNum}</span>
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label}>出货立方：</span>{' '}
            <span style={{ color: '#00BAD9' }}>
              {statistic?.sumOuterVolume?.toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label}>实重：</span>
            <span style={{ color: '#333', fontWeight: 500 }}>
              {statistic?.sumOuterActualWeight?.toFixed(2)}
            </span>
          </Col>

          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label}>密度比例：</span>{' '}
            <span style={{ color: '#333', fontWeight: 500 }}>
              {statistic?.sumOuterActualWeight
                ? (
                  statistic?.sumOuterActualWeight / statistic?.sumOuterVolume
                ).toFixed(2)
                : '-'}{' '}
            </span>
          </Col>
          <Col flex={flexParam} className="text-ellipsis">
            <span className={styles.label}>出货体积重：</span>{' '}
            <span style={{ color: '#333', fontWeight: 500 }}>
              <Tooltip title={statistic?.sumOuterVolumeWeight?.toFixed(2)}>
                {' '}
                {statistic?.sumOuterVolumeWeight?.toFixed(2)}{' '}
              </Tooltip>
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label}>收货计费重：</span>{' '}
            <span style={{ color: '#FF40A5' }}>
              {statistic?.sumChargeWeight?.toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label}>出货计费重：</span>{' '}
            <span style={{ color: '#FF40A5' }}>
              {statistic?.sumOuterChargeWeight?.toFixed(2)}{' '}
            </span>
          </Col>
        </Row>
        <Divider style={{ margin: '0.3em 0' }} />
        <Row wrap={false}>
          <Col style={{ width: '20em' }}>
            <span
              style={{ color: '#000', fontWeight: 600, marginRight: '1em' }}
            >
              已选
            </span>
            <span style={{ display: 'inline-block', width: '7em' }}>
              <span
                style={{
                  marginRight: '2px',
                  visibility: 'hidden',
                }}
              >
                票：
              </span>
              <span style={{ color: '#6440FF' }}>{totalState?.piece}</span>
            </span>
            <span style={{ display: 'inline-block' }}>
              <span
                style={{
                  marginRight: '2px',
                  visibility: 'hidden',
                  color: '#BBB',
                }}
              >
                件：
              </span>
              <span style={{ color: '#6440FF' }}>
                {pieceIds ? pieceIds?.split(',').length : '0'}
              </span>
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span style={{ visibility: 'hidden' }} className={styles.label}>
              出货立方：
            </span>
            <span style={{ color: '#00BAD9' }}>
              {' '}
              {Number(totalState?.OuterVolume).toFixed(2)}
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label} style={{ visibility: 'hidden' }}>
              实重：
            </span>
            <span style={{ color: '#333', fontWeight: 500 }}>
              {Number(totalState?.outerActualWeight).toFixed(2)}
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span style={{ visibility: 'hidden' }} className={styles.label}>
              密度比例：
            </span>
            <span style={{ color: '#333', fontWeight: 500 }}>
              {' '}
              {isNaN(totalState?.outerActualWeight / totalState?.OuterVolume)
                ? '0.00'
                : (
                  totalState?.outerActualWeight / totalState?.OuterVolume
                ).toFixed(2)}
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span className={styles.label} style={{ visibility: 'hidden' }}>
              出货体积重：
            </span>{' '}
            <span style={{ color: '#333', fontWeight: 500 }}>
              {Number(totalState?.outerVolumeWeight).toFixed(2)}{' '}
            </span>
          </Col>
          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span style={{ visibility: 'hidden' }} className={styles.label}>
              收货计费重：
            </span>
            {/*   {statistic?.sumChargeWeight}*/}
          </Col>

          <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
            <span style={{ visibility: 'hidden' }}>
              {' '}
              className={styles.label}出货计费重:
            </span>
            {/*  {statistic?.sumOuterChargeWeight}*/}
            {/*  {statistic?.sumOuterChargeWeight}*/}
          </Col>
        </Row>
      </div>
    );
  };
  useEffect(() => {
    getProduct();
    getWarehouse();
  }, []);
  /*  useEffect(() => {
    previousValueRef.current = check;
  }, [check]);*/

  // 数据替换树形的，在这里就格式化时间表头写rednder格式化时间 children会有问题
  const processedData = (data: any) => {
    return data.map((item: any) => {
      const newItem = {
        ...item,
        siginTime: item?.siginTime ? formatTime(item.siginTime) : '',
        warehouseName: item?.currentWarehouse?.name,
      };
      if (newItem.pieceList) {
        newItem.children = newItem.pieceList.map(
          (childItem: any, index: number) => {
            if (childItem?.subWaybillNO) {
              childItem.waybillNO = childItem?.subWaybillNO;
            }
            delete childItem.subWaybillNO;
            return {
              ...childItem,
              pieceNum: `${index + 1}/${item.pieceNum}`,
              warehouseName: childItem?.warehouse?.name,
            };
          },
        );
        delete newItem.pieceList;
      }
      return newItem;
    });
  };
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: check,
    checkStrictly: true,
    fixed: true,
    //preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys, selectedRows) => {
      //id大于17为子单
      const ids = selectedRowKeys.filter((item: any) => item.length > 17);
      setCheck(ids);
      //统计数据
      statisticalData(ids, selectedRows);
      // 假设不支持分票，倒时以实际为准
      /*  let noLotting = true;
      if (noLotting) {
        // 此时用户取消运单操作/需要取消勾选所有的兄弟以及父级
        if (selectedRowKeys.length <= previousValueRef.current?.length) {
          // 找出当前取消的 key
          const differenceKey = previousValueRef.current.filter(
            (item: any) => !selectedRowKeys.includes(item),
          );
          // 拿取消的 key 去找他所有的兄弟以父级
          const resultIds = differenceKey.map((id: any) =>
            findIds(waybillData, id),
          );
          const uniqueIds = Array.from(new Set(resultIds.flat()));
          // 找到之后去当前勾选的里面去筛选掉，就是最终勾选的
          const newCheck = check.filter((ele: any) => !uniqueIds.includes(ele));
          setCheck(newCheck);
        } else {
          // 添加勾选运单操作/需要勾选所有兄弟以及父级
          const resultIds = selectedRowKeys.map((id: any) =>
            findIds(waybillData, id),
          );
          const uniqueIds = Array.from(new Set(resultIds.flat()));
          setCheck(uniqueIds);
        }
      } else {
        // 正常勾选，但是 map 出来的 n 个表格，每次勾选只能拿最近一次勾选的，这里需要叠加
        selectedRowKeys?.forEach((ele: any) => {
          const isCon = check.some((resultItem: any) => resultItem === ele);
          if (!isCon) {
            check.push(ele);
            setCheck([...check]);
          }
        });
        setCheck(selectedRowKeys);
      }
      const parentData = selectedRows?.filter((item: any) => item?.children);
      const number = parentData?.reduce((prev, cur) => prev + cur?.pieceNum, 0);
      const volume = parentData?.reduce(
        (prev, cur) => prev + parseFloat(cur?.siginVolume || 0),
        0,
      );
      const weight = parentData?.reduce(
        (prev, cur) => prev + Number(cur?.weight),
        0,
      );
      const outerChargeWeight = parentData?.reduce(
        (prev, cur) => prev + Number(cur?.outerChargeWeight),
        0,
      );
      setTableTitleData({
        // 已选票
        ticket: parentData.length,
        // 件数
        number,
        // 体积
        volume: Math.round(volume * 10000) / 10000,
        weight,
        outerChargeWeight,
      });*/
    },
  };
  // 拿到任意一个子级的 id 去找所有兄弟 id 如果是父级，返回即可
  function findIds(data: any, idToFind: any) {
    const result = [];
    for (const waybill of data) {
      if (waybill.id === idToFind) {
        result.push(waybill.id);
      } else {
        const children = waybill.children;
        for (const child of children) {
          if (child.id === idToFind) {
            const childIds = children.map((childItem: any) => childItem.id);
            result.push(...childIds);
            break;
          }
        }
      }
    }
    return result;
  }

  // 触发表单
  const getSubmit = () => actionRef?.current?.reload();
  // 产品 list
  const getProduct = async (keyWords?: string) => {
    try {
      const { status, data } = await getProductList({
        len: 20,
        keyword: keyWords,
      });
      if (status) {
        const newData = data?.list?.map((item: any) => ({
          value: item?.id,
          label: item?.name,
        }));

        setProdData(newData);
      }
    } catch (error) { }
  };
  const getWarehouse = async (keyword?: string) => {
    try {
      const { status, data } = await getWarehouseListAPI({
        start: 0,
        len: 20,
        keyword,
        type: 0,
      });
      if (status) {
        setWarehouse(
          data?.list?.map((item: any) => ({
            value: item?.id,
            label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
          })),
        );
      }
    } catch (error) { }
  };
  // 搜索，防抖
  const getProductAntiShake = _.debounce((e: string) => {
    getProduct(e);
  }, 200);
  const getWarehouseKeyword = _.debounce((e: any) => {
    getWarehouse(e);
  }, 100);
  // 选择舱位回调
  const moveMatch = async (spaceId: string) => {
    /*过滤掉主单*/
    const rowId = check
      ?.filter((item: any) => item !== '' && item.length > 20)
      ?.join(',');
    await getputIntoConfigure(rowId, spaceId);
  };
  const moveArrive = () => {
    if (pieceIds.split(',').length === 0) {
      return message.warning('请选择运单');
    }
    seleShippingRef?.current?.control();
  };
  // 配舱
  const getputIntoConfigure = async (pieceIds: string, spaceId?: string) => {
    try {
      const { status } = await getputIntoConfigureAPI({
        spaceId: spaceId,
        pieceIds,
      });
      if (status) {
        message.success('操作成功');
        actionRef.current?.reload();
        // makingParticulars(spaceId ?? state.id)
      }
    } catch (error) { }
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    headTransType: {
      desc: '头程类型',
      type: 'select',
      value: '',
      range: [
        { label: '空运', value: 1 },
        { label: '海运', value: 2 },
      ]
    },

    warehouseId: {
      desc: '站点',
      type: 'warehouse',
      value: '',
    },
    time: {
      desc: '创建时间',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    volume: {
      desc: '预估票体积',
      type: 'text',
      value: '',
    },
    productIds: {
      desc: '销售产品',
      type: 'product',
      multi: true,
      value: '',
    },
    minRatio: {
      desc: '最小出货比重',
      type: 'text',
      value: '',
    },
    maxRatio: {
      desc: '最大出货比重',
      type: 'text',
      value: '',
    },
    recipientZipCode: {
      desc: '邮编',
      type: 'text',
      value: '',
    },
    state: {
      desc: '状态',
      type: 'select',
      range: [
        { value: -1, label: '所有' },
        { value: 40, label: '待配仓' },
        { value: 60, label: '已配舱' },
      ],
    },
    declarationType: {
      desc: '报关类型',
      type: 'select',
      range: [
        { label: '非单独报关', value: 10 },
        { label: '单独报关', value: 20 },
        { label: '合并报关', value: 30 },
      ],
    }
  };
  return (
    <>
      <div className={styles.inland_waybill}>
        <div style={{ padding: '0 20px', background: '#fff' }}>
          {/* <Row gutter={[4, 0]} className={styles.waybill_modal}>
            <Col span={6}>
              <div className={styles.search_wrap} id="search_left">
                <Select
                  defaultValue="批量"
                  bordered={false}
                  style={{
                    height: '100%',
                    border: 'none',
                    backgroundColor: '#f7f7f7',
                  }}
                  onChange={(value: any) => {
                    setNewParams('termQuery', value);
                  }}
                >
                  <Option value={true}>精确</Option>
                  <Option value={false}>批量</Option>
                </Select>
                <TextArea
                  className={styles.text_area}
                  id="search_text"
                  style={{
                    resize: 'none',
                    height: '77px',
                    borderRadius: '0px',
                    border: 'none',
                    paddingTop: '27px',
                  }}
                  onChange={(e: any) => setKeyword(e.target.value)}
                  placeholder="请输入ID,运单号，客户代码，客户名称，业务员搜索"
                  onPressEnter={() => {
                    setSearch(true);
                    getSubmit();
                  }}
                />
                <Button
                  style={{ height: '100%', color: '#537ffd', border: 'none' }}
                  className={styles.search_back}
                  onClick={() => {
                    setSearch(true);
                    getSubmit();
                  }}
                >
                  搜索
                </Button>
              </div>
            </Col>
            <Col span={18}>
              <Row style={{ boxSizing: 'border-box' }} wrap={false}>
                <Col span={8} className={styles.clientele}>
                  <span className={styles.organization}>头程类型</span>
                  <Select
                    placeholder="请输入"
                    style={{
                      border: 'none',
                      backgroundColor: 'rgb(251, 251, 251)',
                      width: 150,
                    }}
                    options={[
                      { label: '空运', value: 1 },
                      { label: '海运', value: 2 },
                    ]}
                    onChange={(e) => {
                      setNewParams('headTransType', e);
                    }}
                    allowClear
                  />
                </Col>

                <Col span={8} className={styles.clientele}>
                  <span className={styles.organization}>站点</span>
                  <ProFormSelect
                    name="warehouse"
                    showSearch
                    // rules={[{ required: true, message: '请输入站点' }]}
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 12 }}
                    noStyle
                    style={{ display: 'flex' }}
                    request={async ({ keyWords }) => {
                      const { status, data } = await getWarehouseListAPI({
                        type: 0,
                        len: 20,
                        keyword: keyWords,
                      });
                      if (status) {
                        return data.list.map((item: any) => {
                          return {
                            label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
                            value: item.id,
                          };
                        });
                      }
                      return [];
                    }}
                    fieldProps={{
                      onChange: (e, option) => {
                        setNewParams('warehouseId', e);
                      },
                      filterOption: false,
                    }}
                  ></ProFormSelect>
                </Col>

                <Col span={8} className={styles.clientele}>
                  <span className={styles.organization}>创建时间</span>
                  <Time
                    changeTime={(e) => {
                      if (e[0]) {
                        setParams({
                          ...Params,
                          startTime: e[0] + ' 00:00:00',
                          endTime: e[1] + ' 23:59:59',
                        });
                        refresh();
                      } else {
                        setParams({
                          ...Params,
                          startTime: '',
                          endTime: '',
                        });
                      }
                    }}
                    style={{ width: '100%' }}
                  />
                </Col>
              </Row>
              <Row wrap={false}>
                <Col span={8} className={styles.clientele}>
                  <span className={styles.organization}>预估票体积</span>
                  <Input
                    placeholder="请输入"
                    step="1"
                    type="number"
                    style={{ display: 'flex' }}
                    onChange={(e) => {
                      setNewParams('volume', e.target.value);
                    }}
                  />
                </Col>
                <Col span={8} className={`${styles.clientele}`}>
                  <span className={`${styles.organization}`}>销售产品</span>
                  <ProFormSelect
                    name="product"
                    showSearch
                    mode={'multiple'}
                    className={styles.sale}
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 12 }}
                    noStyle
                    bordered={false}
                    request={async ({ keyWords }) => {
                      const { status, data } = await getProductList({
                        type: 0,
                        len: 20,
                        keyword: keyWords,
                      });
                      if (status) {
                        return data.list.map((item: any) => {
                          return {
                            label: item?.name,
                            value: item?.id,
                          };
                        });
                      }
                      return [];
                    }}
                    fieldProps={{
                      onChange: (e) => {
                        setNewParams('productIds', e?.join(','));
                      },
                      maxTagCount: 1,
                      bordered: false,
                      filterOption: false,
                    }}
                  />
                </Col>
                <Col span={8} className={styles.clientele}>
                  <div style={{ display: 'flex' }}>
                    <span className={styles.organization}>出货比重</span>
                    <Input
                      placeholder="请输入"
                      step="1"
                      type="number"
                      style={{ flex: 1 }}
                      onChange={(e) => {
                        setNewParams('minRatio', e.target.value);
                      }}
                    />
                    <span style={{ display: 'flex', alignItems: 'center' }}>
                      -
                    </span>
                    <Input
                      placeholder="请输入"
                      step="1"
                      type="number"
                      style={{ flex: 1 }}
                      onChange={(e) => {
                        setNewParams('maxRatio', e.target.value);
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </Col>
          </Row>
          <Row
            gutter={[4, 0]}
            className={styles.waybill_modal}
            style={{ paddingTop: '0' }}
          >
            <Col span={6}>
              <div className={styles.clientele}>
                <span className={styles.organization}>邮编</span>
                <Input
                  placeholder="请输入"
                  style={{ display: 'flex', borderRadius: 0 }}
                  allowClear
                  onChange={(e) => {
                    setNewParams('recipientZipCode', e.target.value);
                  }}
                />
              </div>
            </Col>
            <Col span={6} className={styles.clientele}>
              <span className={styles.organization}>状态</span>
              <Select
                placeholder="请选择"
                allowClear
                onChange={(e) => {
                  setNewParams('state', e);
                }}
                style={{ display: 'flex', marginRight: '8px' }}
                options={[
                  { value: -1, label: '所有' },
                  //   { value: 0, label: '草稿' },
                  // { value: 10, label: '待签入' },
                  // { value: 20, label: '已签入' },
                  { value: 40, label: '待配仓' },
                  { value: 60, label: '已配舱' },
                  // { value: 90, label: '已签出' },
                  // { value: 200, label: '运输中' },
                  // { value: 300, label: '已签收' },
                  // { value: 500, label: '已结束' }
                ]}
              />
            </Col>
            <Col span={6} className={styles.clientele}>
              <span className={styles.organization}>报关类型</span>
              <Select
                placeholder="请输入"
                style={{
                  border: 'none',
                  backgroundColor: 'rgb(251, 251, 251)',
                  width: 150,
                }}
                options={[
                  { label: '买单报关', value: 10 },
                  { label: '单独报关', value: 20 },
                  { label: '合并报关', value: 30 },
                ]}
                allowClear
                onChange={(e) => {
                  setNewParams('declarationType', e);
                }}
              />
            </Col>
          </Row> */}
          <HeadTotal />
        </div>
        <MrTable
          columns={columns}
          //ref={mrTableRef}
          checkData={checkData}
          keyID={'BOL/PreplanCabin/waybill'}
          Search={Search}
          filters={filters}
          // modalTableY
          modalCopy={{
            x: 300,
            y: 300,
          }}
          field={'holdFlag'}
          //rowSelectionCablck={rowSelectionCablck}
          request={async (params: any, action: any) => {
            actionRef.current = action;
            // if (!Search) return;
            // const { current: start, pageSize: len } = params;
            // const param: any = {
            //   ...Params,
            //   start: (start - 1) * len,
            //   len,
            //   keyword,
            // };
            const msg = await waybillListAPI({
              // ...params
              ...params,
              // condition: {
              //   keyword: {
              //     value: keyword,
              //     termQuery: Params?.termQuery,
              //   },
              //   headTransType: {
              //     value: Params?.headTransType,
              //   },
              //   warehouseId: {
              //     value: Params?.warehouseId,
              //   },
              //   time: {
              //     // value:Params?.headTransType,
              //     startTime: Params?.startTime,
              //     endTime: Params?.endTime,
              //   },
              //   recipientZipCode: {
              //     value: Params?.recipientZipCode,
              //   },
              //   volume: {
              //     value: Params?.volume,
              //   },
              //   productIds: {
              //     value: Params?.productIds,
              //   },
              //   maxRatio: {
              //     value: Params?.maxRatio,
              //   },
              //   minRatio: {
              //     value: Params?.minRatio,
              //   },
              //   state: {
              //     value: Params?.state,
              //   },
              //   declarationType: {
              //     value: Params?.declarationType,
              //   },
              //   // ...Params
              // },
            });
            const newData = processedData(msg?.data?.list || []);
            console.log('newData', newData);
            const PieceList: any = [];
            newData.map((item) => PieceList.push(item.children));
            console.log('PieceList', PieceList);
            pieceData.current = PieceList;
            setSatistic(msg?.data?.statistic);
            setWaybillData(newData);
            return {
              data: newData || [],
              success: msg.status,
              total: msg.data.total,
            };
          }}
          // filters={{}}
          toolbar={{
            subTitle: (
              <div style={{ display: 'flex', lineHeight: '32px' }}>
                {/*  已选*/}
                {/*   <span style={{ marginRight: '15px', color: '#4071FF' }}>
                &nbsp;{tableTitleData?.ticket || 0}&nbsp;
                <span style={{ color: '#333' }}>票</span>
              </span>
              <span style={{ marginRight: '15px', color: '#4071FF' }}>
                {tableTitleData?.number || 0}&nbsp;
                <span style={{ color: '#333' }}>件</span>
              </span>
              <span style={{ marginRight: '15px', color: '#4071FF' }}>
                {tableTitleData?.volume || 0}&nbsp;
                <span style={{ color: '#333' }}>m³</span>
              </span>*/}

                <Button onClick={() => moveArrive()}>配舱</Button>
                {/*    <AccessCard
                accessible={[':Waybill:TransferInnerFullAccess']}
              >
                <Button loading={loading}>仓库转运</Button>
              </AccessCard>
              <Button loading={loading}>签出</Button>*/}
                <AddCabinModal
                  btnText={'新建预配仓'}
                  btnTitle={'新建预配仓'}
                  record={check.join(',')}
                  length={pieceIds.split(',').length}
                  myList={[1]}
                />
              </div>
            ),
          }}
          rowSelectionCablck={(value: any, bool) => {
            if (bool) {
              value = value?.map((ele: any) => ele?.children)?.flat()
            }
            const ids = value
              ?.map((item: any) => item?.id)
              .filter((item: any) => item.length > 17)
            setCheck(ids)
            //统计数据
            statisticalData(ids, value);
            subunit.current = value
            setCheckData(value)
          }}
        />
      </div>
      {/* 选择舱位 */}
      <SelectShipping ref={seleShippingRef} moveMatch={moveMatch} />
    </>
  );
};
export default memo(Waybill2);
