import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { Button, Form, message, Space, Tag } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import { editWaybillLabel } from '@/services/Waybilladministration';
import { getLabelByValue, goodsTypeMap } from '@/utils/constant';
import { AddNewPreplanCabin } from '@/services/preplanCabin';
const { CheckableTag } = Tag;
const AddCabinModal = (props: any) => {
  const { record, btnTitle, btnType, length } = props;
  console.log('record', record);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [selectedTags, setSelectedTags] = useState<number[]>(['Books']);
  const [modalVisit, setModalVisit] = useState(false);
  const showModal = () => {
    if (!length) {
      console.log('由于myList为空而关闭弹窗');
      setModalVisit(false);
      message.error(`请选择需要${btnTitle}的运单`);
    } else {
      setModalVisit(true);
    }
  };

  /* 提交 */
  const onFinish = async (values: any) => {
    console.log('values: ', values);
    // message.success('提交成功');
    const { status } = await AddNewPreplanCabin({
      pieceIds:record,
      name: values?.name,
    });
    if (status) {
      message.success('提交成功');
      // props.refreshTable();
      setModalVisit(false);
      return true;
    } else {
      setModalVisit(false);
      // message.error('提交失败');
      return false;
    }
  };

  return (
    <>
      <Button style={{ color: '#1677ff' }} onClick={showModal}>
        {btnTitle}
      </Button>
      <ModalForm
        title={btnTitle}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        open={modalVisit}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        layout="horizontal"
        submitTimeout={2000}
        onFinish={onFinish}
      >
        <ProFormText
          name="name"
          rules={[{ required: true, message: '必填项不能为空' }]}
          label="名称"
        />
      </ModalForm>
    </>
  );
};
export default AddCabinModal;
