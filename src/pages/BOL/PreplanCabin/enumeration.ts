/**
 * 配仓详情枚举
 */
export const enumeration: any = {
    '上水滞留': 'magenta',
    '人工调配': 'blue',
    '降舱配送': 'cyan',
    '动态配舱': 'green',
    '升舱配货': 'orange',
    '合并报关级联': 'purple',
    '出库调仓': 'gold',
    '剩余': 'geekblue',
    '分票': 'lime',
    '异常': 'red',
}
export const enumePar: any = {
    1: {
        text: '空闲',
        color: 'magenta'
    },
    10: {
        text: '待出库',
        color: 'red'
    },
    20: {
        text: '已提柜',
        color: 'volcano'
    },
    30: {
        text: '已出库',
        color: 'orange'
    }
}
export const declaration: any = {
    10: '非单独报关',
    20: '单独报关',
    30: '合并报关',
}
export const waybillEnume: any = {
    0: {
        text: '草稿',
        color: 'magenta'
    },
    10: {
        text: '待签入',
        color: 'red'
    },
    20: {
        text: '已签入',
        color: 'volcano'
    },
    40: {
        text: '已确认',
        color: 'orange'
    },
    59: {
        text: '部分配舱',
        color: '#5e20aa'
    },
    60: {
        text: '已配舱',
        color: 'gold'
    },
    90: {
        text: '已签出',
        color: 'lime'
    },
    200: {
        text: '运输中',
        color: 'green'
    },
    300: {
        text: '已签收',
        color: 'cyan'
    },
    500: {
        text: '异常结束',
        color: 'geekblue'
    }
}