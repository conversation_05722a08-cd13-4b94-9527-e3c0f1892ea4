.par_top {
    height: 46px;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    align-items: center;
    >div {
        display: flex;
        align-items: center;
        >h3 {
            margin-top: 5px;
            margin-left: 20px;
        }
    }
}

.par_bot {
    display: flex;
    align-items: center;
    width: 860px;
    height: 46px;
    background-color: #F2F8FF;
    margin: 20px 0 40px 0;
    padding-left: 15px;
    >div:nth-child(1) {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
 
    // >div:nth-child(2) {
    //     width: 40%;
    //     box-sizing: border-box;
    //     padding-left: 186px;
    // }
}

.waybill_top {
    width: 100%;
    height: 50px;
    >div:nth-child(1) {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 50%;
        align-items: center;
        >div {
            display: flex;
            justify-content: space-between;
            width: 540px;
            .labelTag {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                height: 30px;
                border-radius: 6px;
                border: 1px solid #4071FF;
                color: #4071FF;
                font-size: 12px;
                padding: 0 8px;
            }
        }
    }
    >div:nth-child(2) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 200px;
        >h3 {
            margin-top: 5px;
        }
    }
}
.tables {
    :global {
        .ant-pro-card-body {
            padding-inline: 0px !important;
        }
        .ant-pro-table-alert-container {
            display: none;
        }
    }
}
.tab_title {
    display: flex;
    justify-content: space-between;
    width: 200px;
}
.partition {
    width: 520px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background: #F4F4F4;
    // margin-left: -24px;
    // margin-bottom: 15px;
    color: red;
    margin: 20px 0 15px -24px;
}