import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import {
  Card,
  Tag,
  Descriptions,
  Switch,
  Button,
  Space,
  Popconfirm,
  message,
  Modal,
  Checkbox,
} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import styles from './index.less';
import { formatTime } from '@/utils/format';
import AddWaybill from '../AddWaybill';
import SelectShipping from '../SelectShipping';
import { history, useLocation } from '@umijs/max';
import {
  getCancelSignOutAPI,
  getCompleteAPI,
  getcompleteSignOutAPI,
  getConfigureExcelAPI,
  getputIntoConfigureAPI,
  getRemoveFromConfigureAPI,
  getsignOutAPI,
  makingParticularsAPI,
} from '@/services/preplanCabin';
import { HomeOutlined, LeftOutlined } from '@ant-design/icons';
import type { TableRowSelection } from 'antd/es/table/interface';
import { saveAs } from 'file-saver';
import RollModal from '@/pages/Waybilladministration/InlandTransfer/WarehouseTransfer/RollModal';
import AddModal from '@/pages/Waybilladministration/InlandTransfer/AgencyWarehouse/AddModal';
import { declaration, enumePar, waybillEnume } from '../enumeration';
import AccessCard from '@/AccessCard';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import { calculateSum } from '@/utils/utils';
import { enumeration, waybillStatusEnum } from '@/utils/constant';
const Particulars: React.FC = (props) => {
  const [toList, IsAccessible] = usePermissionFiltering();
  const isAccessibleFlag = IsAccessible([':BL:ConfigureFullAccess']);
  const waybillRef = useRef<any>(null);
  const seleShippingRef = useRef<any>(null);
  const [detail, setDetail] = useState<any>({});
  const [waybillData, setWaybillData] = useState<any>([]);
  const [tableTitleData, setTableTitleData] = useState<any>({});
  const [check, setCheck] = useState<any>([]);
  const previousValueRef = useRef<any>(null);
  const [matchId, setMatchId] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [messageData, setMessageData] = useState('');
  const addRef = useRef<any>(null);
  const rollRef = useRef<any>(null);
  const [interior, setInterior] = useState([]);
  const { state }: any = useLocation();
  const [stuffing, setStuffing] = useState<any>({});
  const [checked, setChecked] = useState(false);
  const transferState: any = {
    1: {
      text: '已转出',
      color: 'magenta',
    },
    2: {
      text: '已完成',
      color: 'volcano',
    },
    3: {
      text: '货物异常',
      color: 'red',
    },
    4: {
      text: '已取消',
      color: 'orange',
    },
    0: {
      text: '未知',
      color: 'lime',
    },
  };
  const columns: any = useMemo(() => {
    return [
      {
        title: '运单号',
        dataIndex: 'waybillNO',
        key: 'waybillNO',
        align: 'center',
        width: 200,
        hideInSearch: true,
      },
      {
        title: '件数',
        dataIndex: 'pieceNum',
        key: 'pieceNum',
        hideInSearch: true,
        align: 'center',
        width: 100,
        render: (_: any, recode: any) => {
          return recode?.pieceNum;
        },
      },
      {
        title: '体积（m³）',
        hideInSearch: true,
        key: 'volume',
        dataIndex: 'volume',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.volume;
        },
      },
      {
        title: '原则',
        hideInSearch: true,
        key: 'reason',
        dataIndex: 'reason',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          if (recode?.reason) {
            return (
              <Tag
                style={{
                  ...enumeration[recode?.reason]?.styles,
                  color: enumeration[recode?.reason]?.styles?.fontSizeColor,
                }}
                color={enumeration[recode?.reason]?.color}
              >
                {recode?.reason}
              </Tag>
            );
          }
        },
      },

      {
        title: '状态',
        hideInSearch: true,
        key: 'state',
        width: 120,
        dataIndex: 'state',
        align: 'center',
        render: (_: any, recode: any) => {
          return (
            <Tag
              color={
                [0, 10, 20, 40, 60].includes(recode?.state)
                  ? 'red'
                  : waybillEnume[recode?.state]?.color
              }
            >
              {[0, 10, 20, 40, 60].includes(recode?.state)
                ? '待签出'
                : waybillEnume[recode?.state]?.text}
            </Tag>
          );
        },
      },
      {
        title: '转运',
        hideInSearch: true,
        key: 'state',
        width: 200,
        dataIndex: 'transferState',
        align: 'center',
        render: (recode: any) => {
          return (
            <>
              {recode?.state && (
                <Tag color={transferState[recode?.state?.type]?.color}>
                  {transferState[recode?.state?.type]?.text}
                </Tag>
              )}
              {recode?.from?.name && recode?.to?.name
                ? `${recode?.from?.name}-${recode?.to?.name}`
                : ''}
            </>
          );
        },
      },
      {
        title: '销售产品',
        hideInSearch: true,
        width: 200,
        key: 'productName',
        dataIndex: 'productName',
        align: 'center',
        render: (_: any, recode: any) => {
          return recode?.productName;
        },
      },
      {
        title: '报关类型',
        hideInSearch: true,
        dataIndex: 'clearanceType',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode ? declaration[recode?.declarationType] : '';
        },
      },
      {
        title: '报关单',
        hideInSearch: true,
        width: 200,
        key: 'declarationId',
        dataIndex: 'declarationId',
        align: 'center',
        render: (_: any, recode: any) => {
          return recode?.declarationId;
        },
      },
      // declarationId
      {
        title: '签入时间',
        hideInSearch: true,
        dataIndex: 'siginTimes',
        key: 'siginTimes',
        align: 'center',
        width: 200,
        render: (_: any, text: any) => {
          return text?.signInTime;
        },
      },
      {
        title: '目的地',
        hideInSearch: true,
        dataIndex: 'recipientAddress',
        key: 'recipientAddress',
        align: 'center',
        width: 200,
        render: (_: any, recode: any) => {
          const data = recode?.recipientAddress;
          if (data?.isFBA == 1) {
            return (
              <div>
                <Tag color="purple">FBA</Tag>
                {`${data?.name} ${data?.zipCode}`}
              </div>
            );
          } else if (data && data?.isFBA !== 1) {
            return (
              <div>{`${data?.province}${data?.city}${data?.county}${data?.zipCode}`}</div>
            );
          }
        },
      },
      {
        title: '客户',
        hideInSearch: true,
        dataIndex: 'client',
        key: 'client',
        align: 'center',
        width: 120,
        render: (text: any) => {
          return text?.name;
        },
      },
      {
        title: '业务员',
        hideInSearch: true,
        dataIndex: 'salesman',
        key: 'salesman',
        align: 'center',
        width: 120,
        render: (text: any) => {
          return text?.name;
        },
      },
      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        align: 'center',
        fixed: 'right',
        width: 200,
        render: (_: any, recode: any) => {
          return (
            <AccessCard accessible={[':BL:ConfigureFullAccess']}>
              <Space
                size="middle"
                style={{ display: 'flex', justifyContent: 'center' }}
              >
                {detail?.state < 30 &&
                  [0, 10, 20, 40, 60].includes(recode?.state) && (
                    <a
                      key="sign"
                      onClick={() =>
                        getsignOut(removePrefix(recode?.id, 'parent'))
                      }
                    >
                      签出
                    </a>
                  )}
                {detail?.state < 30 &&
                  ![0, 10, 20, 40, 60].includes(recode?.state) && (
                    <a
                      key="sales"
                      onClick={() =>
                        getCancelSignOut(removePrefix(recode?.id, 'parent'))
                      }
                      style={{ color: '#ccc' }}
                    >
                      退签
                    </a>
                  )}
                {/* {recode?.declarationType ? <a key="particulars" onClick={() => {
                            history.push('/Waybilladministration/detail', {
                                id: recode?.id.replace(/^parent/, "")
                            })
                        }}>详情</a> : ''} */}
                <a
                  key="particulars"
                  onClick={() => {
                    history.push('/Waybilladministration/detail', {
                      id: recode?.id.replace(/^parent/, ''),
                    });
                  }}
                >
                  详情
                </a>
              </Space>
            </AccessCard>
          );
        },
      },
    ];
  }, [state?.id, detail?.state]);
  // 这里后端给翻译过了，设置不同颜色

  useEffect(() => {
    if (state?.makingId) {
      makingParticulars(state.makingId);
      setMatchId(state?.makingId);
      setTableTitleData({});
    }
  }, [state?.makingId]);
  // 存储上次勾选的/取消勾选做对比区分
  useEffect(() => {
    previousValueRef.current = check;
  }, [check]);
  const makingParticulars = async (id: string) => {
    try {
      const { status, data } = await makingParticularsAPI({ configId: id });
      if (status) {
        setDetail(data);
        setWaybillData(processedData(data?.list));
        setStuffing(
          data?.info?.airWarehouseSnapshot
            ? JSON.parse(data?.info?.airWarehouseSnapshot)
            : {},
        );
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };
  // 数据整合成表格能渲染的树形结构与对应字段
  const processedData = (data: any) => {
    return data?.map((item: any) => {
      const waybillList = item?.waybillList?.map((fid: any) => {
        const newItem = {
          ...fid,
          signInTime: fid?.signInTime ? formatTime(fid.signInTime) : '',
          warehouseName: fid?.currentWarehouse?.name,
          id: `parent${fid.id}`,
        };
        if (newItem.pieceList) {
          newItem.children = newItem?.pieceList?.map(
            (childItem: any, index: number) => {
              if (childItem?.subWaybillNO) {
                childItem.waybillNO = childItem?.subWaybillNO;
              }
              delete childItem.subWaybillNO;
              return {
                ...childItem,
                pieceNum: `${index + 1}/${fid.pieceNum}`,
                warehouseName: childItem?.warehouse?.name,
              };
            },
          );
          delete newItem.pieceList;
        }
        return newItem;
      });
      return {
        ...item,
        waybillList,
      };
    });
  };
  const moveArrive = () => {
    if (check.length === 0) {
      return message.warning('请选择运单');
    }
    seleShippingRef?.current?.control();
  };
  // 将运单添加到配舱里/移动到公用
  const getputIntoConfigure = async (pieceIds: string, spaceId?: any) => {
    try {
      const { status } = await getputIntoConfigureAPI({
        pieceIds,
        configId: spaceId ?? matchId,
      });
      if (status) {
        message.success('操作成功');
        makingParticulars(spaceId ?? matchId);
        setMatchId(spaceId ?? matchId);
        setCheck([]);
      }
    } catch (error) {}
  };
  // 导出配舱单
  const exprotManifest = async () => {
    try {
      const response: any = await getConfigureExcelAPI({ spaceId: matchId });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
      message.success('下载舱单成功');
    } catch (err) {
      console.log('下载舱单: ', err);
    }
  };
  // 从配舱中移除掉
  const getRemoveFromConfigure = async () => {
    if (check?.length == 0) {
      return message.warning('请选择运单');
    }
    try {
      const { status } = await getRemoveFromConfigureAPI({
        configId: matchId,
        piecesIds: check?.join(','),
      });
      if (status) {
        message.success('从配舱单中移除');
        makingParticulars(matchId);
      }
    } catch (error) {}
  };
  // 移动配舱
  const moveMatch = async (spaceId: string) => {
    await getputIntoConfigure(check?.join(','), spaceId);
  };
  // 完成配舱
  const getComplete = async () => {
    try {
      const { status } = await getCompleteAPI({
        configId: matchId,
      });
      if (status) {
        message.success('完成配舱成功');
        makingParticulars(matchId);
      }
    } catch (error) {}
  };
  // 签出
  const getsignOut = async (signId?: string) => {
    try {
      const { status } = await getsignOutAPI({
        configId: matchId,
        pieceIds: signId ?? check?.join(','),
      });
      if (status) {
        message.success('操作成功');
        makingParticulars(matchId);
        setCheck([]);
      }
    } catch (error) {}
  };
  // 退签
  const getCancelSignOut = async (quitId?: string) => {
    try {
      const { status } = await getCancelSignOutAPI({
        configId: matchId,
        pieceIds: quitId ?? check?.join(','),
      });
      if (status) {
        message.success('操作成功');
        makingParticulars(matchId);
        setCheck([]);
      }
    } catch (error) {}
  };
  useEffect(() => {
    parentData();
  }, [check]);
  const parentData = () => {
    const uniqueDataMap = new Map();
    const newData: any = [];
    const wabiData = waybillData?.map((item: any) => item?.waybillList).flat();
    wabiData?.forEach((ele: any) => {
      ele?.children?.forEach((item: any) => {
        if (check.includes(item.id)) {
          newData.push(ele);
        }
      });
    });
    newData.forEach((obj: any) => {
      uniqueDataMap.set(obj.id, obj);
    });
    // 获取去重后的对象数组
    const uniqueData = Array.from(uniqueDataMap.values());
    let childrenData = uniqueData?.map((ele: any) => ele?.children).flat();
    const number = childrenData?.filter((ele: any) =>
      check.includes(ele?.id),
    ).length;
    const volume = childrenData
      ?.filter((ele: any) => check.includes(ele?.id))
      .reduce((prev: any, cur: any) => prev + parseFloat(cur?.volume), 0);
    setTableTitleData({
      // 已选票
      ticket: uniqueData.length,
      // 件数
      number,
      // 体积
      volume: Math.round(volume * 100000) / 100000,
    });
    // return uniqueData
  };
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: check,
    onSelect: (record, selected, selectedRows, _) => {
      const selectedRowKeys = dataSoucre(selectedRows);
      const flag = false;
      // 不支持分票情况，子集勾选兄弟根父级都勾选
      if (flag) {
        if (selected) {
          const resultIds = selectedRowKeys.map((id: any) =>
            findIds(waybillData, id),
          );
          const newCheck = Array.from(new Set(resultIds.flat()));
          setCheck(Array.from(new Set([...check, newCheck].flat())));
        } else {
          // 取消勾选
          if (record?.id.startsWith('parent')) {
            cancelParent(record);
          } else {
            const newCheck = check.filter(
              (ele: any) => !findIds(waybillData, record?.id).includes(ele),
            );
            setCheck([...newCheck]);
          }
        }
      } else {
        // 支持分票正常勾选
        if (selected) {
          addCheck(selectedRowKeys);
        } else {
          // 取消勾选
          if (record?.id.startsWith('parent')) {
            cancelParent(record);
          } else {
            const newCheck = check?.filter((item: any) => item !== record?.id);
            setCheck([...newCheck]);
          }
        }
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      const selectedRowKeys = dataSoucre(selectedRows);
      if (selected) {
        addCheck(selectedRowKeys);
      } else {
        const cancel = changeRows
          ?.map((item: any) => item.id)
          ?.filter((ele: any) => !ele.startsWith('parent'));
        const newCheck = check?.filter((fid: any) => !cancel.includes(fid));
        setCheck([...newCheck]);
      }
    },
  };
  // 勾选的情况
  const addCheck = (selectedRowKeys: any) => {
    selectedRowKeys?.forEach((ele: any) => {
      const isCon = check.some((resultItem: any) => resultItem === ele);
      if (!isCon) {
        check.push(ele);
      }
    });
    setCheck([...check]);
  };
  // 取消勾选，选中父级的情况
  const cancelParent = (record: any) => {
    // 用于存储符合条件的 children 的 id
    const childrenIds: any = [];
    // 找到外层 id 相等的对象
    const targetObjects = waybillData.filter((obj: any) =>
      obj.waybillList.some((waybill: any) => waybill.id === record.id),
    );
    // 提取符合条件的对象的 children 的 id
    targetObjects.forEach((obj: any) => {
      obj.waybillList.forEach((waybill: any) => {
        if (waybill.id === record.id) {
          waybill.children.forEach((child: any) => {
            childrenIds.push(child.id);
          });
        }
      });
    });
    const newCheck = check?.filter((item: any) => !childrenIds.includes(item));
    setCheck([...newCheck]);
  };
  const dataSoucre = (selectedRows: any) => {
    return selectedRows
      ?.map((item: any) => item?.id)
      ?.filter((item: any) => item !== '' && !item.startsWith('parent'));
  };
  // 拿到任意一个子级的 id 去找所有兄弟 id 如果是父级，返回即可
  function findIds(data: any, idToFind: any) {
    const result = [];
    for (const item of data) {
      const waybillList = item.waybillList;
      for (const waybill of waybillList) {
        if (waybill.id === idToFind) {
          result.push(waybill.id);
        } else {
          const children = waybill.children;
          for (const child of children) {
            if (child.id === idToFind) {
              const childIds = children.map((childItem: any) => childItem.id);
              result.push(...childIds);
              break;
            }
          }
        }
      }
    }
    return result;
  }
  // 字符串截取
  function removePrefix(str: any, prefix: any) {
    if (str.startsWith(prefix)) {
      return str.substring(prefix.length);
    } else {
      return str;
    }
  }
  //   完成出库
  const getcompleteSignOut = async (force?: number) => {
    try {
      const { status } = await getcompleteSignOutAPI(
        { configId: matchId ?? state?.makingId, force },
        function (data: any) {
          if (!data.status) {
            if (data.errorCode == 'UnSignedOutPiece') {
              setMessageData(data?.errorDesc);
              setIsModalOpen(true);
              // console.log("error occured,has been interruptted ->");
              // //展示一个弹框
              // return false;
            }
          }
          return true;
        },
      );
      if (status) {
        setIsModalOpen(false);
        message.success('操作成功');
        history.push('/BOL/listOfBillsOfLading');
      }
    } catch (error) {}
  };
  // 仓库间转运
  const warehouseTransfer = (flag?: boolean) => {
    if (check?.length == 0) return message.warning('请选择运单');
    const dataSource = detail?.list
      ?.map((item: any) => item?.waybillList)
      ?.flat();
    const matchingIds = Array.from(
      new Set(findMatchingIds(check, waybillData)),
    )?.map((str) => str.substring(6));
    const newData = dataSource?.filter((item: any) =>
      matchingIds.includes(item?.id),
    );
    setInterior(Object.values(findData(newData)));
    if (detail?.info?.mode == 1 && !flag) {
      // 空运仓库间转运
      addRef?.current?.isClaimOpen();
    } else if (detail?.info?.mode == 2 || flag) {
      // 海运仓库间转运
      rollRef?.current?.isClaimOpen();
    }
  };
  // const checkout = () => {
  // }
  // 根据选择的运单，筛选 （这里是内部转运，因为这边后端给的数据格式不一致，前端两边都需要处理整合成统一的格式）
  function findMatchingIds(dataId: any, dataList: any) {
    const result = [];
    for (const id of dataId) {
      for (const entry of dataList) {
        for (const waybill of entry.waybillList) {
          if (waybill.id === id) {
            result.push(waybill.id);
          } else {
            for (const child of waybill.children) {
              if (child.id === id) {
                result.push(waybill.id);
              }
            }
          }
        }
      }
    }

    return result;
  }
  function findData(data: any) {
    // 创建一个空对象，用于存储 productId 对应的数据
    const productDict: any = {};
    // 遍历原始数据，将相同 productId 的数据归类到 omsWaybills 数组中
    data.forEach((item: any) => {
      const productId = item.productId;
      if (!productDict[productId]) {
        productDict[productId] = {
          productName: item?.productName,
          // 产品下面总的单运单体积
          outerVolume: item?.volume,
          // 产品下面总的运单重量
          orderActualWeight: item?.weight,
          // 产品下面总的运单件数
          pieceNum: item?.pieceNum,
          // 产品下面总的运单详细信息
          omsWaybills: [],
        };
      }
    });

    // 将数据按照 productId 分类存储到 productDict 中
    data.forEach((item: any) => {
      const productId = item.productId;
      productDict[productId].omsWaybills.push({
        id: item.id,
        // 运单件数
        pieceNum: item?.pieceNum,
        // 运单号
        waybillNo: item?.waybillNO,
        // 运单体积
        outerVolume: item?.volume,
        // 运单重量
        orderActualWeight: item?.weight,
        productName: item?.productName,
        state: item?.state,
      });
    });
    return productDict;
  }
  const successCallback = () => {
    makingParticulars(matchId);
    setCheck([]);
  };
  return (
    <>
      <Card style={{ width: '100%' }}>
        <div className={styles.par_top}>
          <div>
            <span
              style={{ cursor: 'pointer' }}
              onClick={() => {
                history.back();
              }}
            >
              <LeftOutlined />
              &nbsp;返回
            </span>
            <h3>
              配舱详情&nbsp;&nbsp;&nbsp;{detail?.info?.blno || detail?.name}
            </h3>
          </div>
          <div>
            <AccessCard accessible={[':BL:ConfigureFullAccess']}>
              <a onClick={() => exprotManifest()}>导出配舱单</a>
            </AccessCard>
            &nbsp; &nbsp;
            <Popconfirm
              key="perform"
              title="完成配舱"
              disabled={!isAccessibleFlag}
              description="确定了吗，完成配舱?"
              onConfirm={() => {
                getComplete();
              }}
              onCancel={() => {
                message.info('已取消操作');
              }}
              okText="确认"
              cancelText="再想想"
            >
              {detail?.state == 1 && (
                <Button
                  disabled={!isAccessibleFlag}
                  style={{ color: '#fff', background: '#08979c' }}
                >
                  完成配舱
                </Button>
              )}
            </Popconfirm>
            &nbsp; &nbsp;
            <Popconfirm
              key="perform"
              title="完成出库"
              disabled={!isAccessibleFlag}
              description="确定了吗，完成出库?"
              onConfirm={() => {
                getcompleteSignOut();
              }}
              onCancel={() => {
                message.info('已取消操作');
              }}
              okText="确认"
              cancelText="再想想"
            >
              {detail?.state == 10 && (
                <Button style={{ color: '#fff', background: '#0958d9' }}>
                  完成出库
                </Button>
              )}
            </Popconfirm>
            {/* <Button type="primary" onClick={() => getcompleteSignOut()}>完成出库</Button> */}
          </div>
        </div>
        {detail?.info && (
          <div>
            <div className={styles.par_bot}>
              <div>
                {detail?.info?.mode == 2 && (
                  <span>箱型：{detail?.info?.model}</span>
                )}
                <span>
                  配舱率(m³)：{`${detail?.volume}/${detail?.info?.volume}`}
                </span>
                <span>
                  状态：{' '}
                  <Tag color={enumePar[detail?.state]?.color}>
                    {enumePar[detail?.state]?.text}
                  </Tag>
                </span>
              </div>
              <div></div>
            </div>
            <Descriptions>
              <Descriptions.Item label="航线">
                {detail?.info?.vesselName}
              </Descriptions.Item>
              {detail?.info?.mode == 2 && (
                <Descriptions.Item label="最晚预报">
                  {detail?.info?.forecastLimitTime
                    ? formatTime(detail?.info?.forecastLimitTime)
                    : ''}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="动态配舱">
                <Switch defaultChecked disabled={!isAccessibleFlag} />
              </Descriptions.Item>
              <Descriptions.Item label="出发港">
                {detail?.info?.departsCity}
              </Descriptions.Item>
              <Descriptions.Item
                label={detail?.info?.mode == 1 ? '航班' : '船名/航次'}
              >
                {detail?.info?.mode == 1
                  ? detail?.info?.airCode
                  : `${detail?.info?.shipName}/${detail?.info?.voyageCode}`}
              </Descriptions.Item>
              {detail?.info?.mode == 2 && (
                <Descriptions.Item label="最晚装柜">
                  {detail?.info?.cargoLimitTime
                    ? formatTime(detail?.info?.cargoLimitTime)
                    : ''}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="到达港">
                {detail?.info?.arrivesCity}
              </Descriptions.Item>
              <Descriptions.Item label="装柜地">
                {detail?.info?.mode == 1
                  ? `${stuffing?.province}${stuffing?.city}${stuffing?.county}${stuffing?.street}`
                  : detail?.warehouseList
                      ?.map((item: any) => item?.name)
                      ?.filter(Boolean)
                      ?.join(',')}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Card>
      <Card>
        <div className={styles.waybill_top}>
          <div>
            <div style={{ width: '620px' }}>
              <Checkbox
                style={{ marginBottom: '10px' }}
                checked={checked}
                onChange={(e) => {
                  setChecked(e?.target?.checked);
                  const ids = e?.target?.checked
                    ? waybillData
                        ?.map((item: any) => item?.waybillList)
                        .flat()
                        ?.map((ele: any) => ele?.children)
                        .flat()
                        ?.map((ele: any) => ele?.id)
                    : [];
                  setCheck([...ids]);
                  parentData();
                }}
              ></Checkbox>
              <h3>运单列表</h3>
              <span>
                已选&nbsp;
                <span style={{ color: '#4071FF' }}>
                  {tableTitleData?.ticket || 0}
                </span>
                &nbsp;票
              </span>
              <span>
                <span style={{ color: '#4071FF' }}>
                  {tableTitleData?.number || 0}
                </span>
                &nbsp;件
              </span>
              <span>
                <span style={{ color: '#4071FF' }}>
                  {tableTitleData?.volume || 0}
                </span>
                &nbsp;m³
              </span>
              {/*{detail?.state !== 30 && (*/}
              <>
                <AccessCard accessible={[':BL:ConfigureFullAccess']}>
                  <div className={styles.labelTag} onClick={() => moveArrive()}>
                    移动到
                  </div>
                </AccessCard>
                <AccessCard
                  accessible={
                    detail?.info?.mode == 1
                      ? [':Waybill:TransferAgentFullAccess']
                      : [':Waybill:TransferInnerFullAccess']
                  }
                >
                  <div
                    className={styles.labelTag}
                    onClick={() => {
                      warehouseTransfer();
                    }}
                  >
                    {detail?.info?.mode == 1 ? '转运至代理' : '仓库转运'}
                  </div>
                </AccessCard>
                <AccessCard accessible={[':BL:ConfigureFullAccess']}>
                  <div
                    className={styles.labelTag}
                    onClick={() => {
                      if (check.length === 0)
                        return message.warning('请选择签出运单');
                      getsignOut();
                    }}
                  >
                    签出
                  </div>
                  <div
                    className={styles.labelTag}
                    onClick={() => {
                      if (check?.length === 0)
                        return message.warning('请选择退签运单');
                      getCancelSignOut();
                    }}
                  >
                    退签
                  </div>
                  <Popconfirm
                    key="delete"
                    title="从配舱中移除"
                    description="想好了吗，从配舱中移除?"
                    onConfirm={() => {
                      getRemoveFromConfigure();
                    }}
                    onCancel={() => {
                      message.info('已取消操作');
                    }}
                    okText="确认"
                    cancelText="再想想"
                  >
                    <div className={styles.labelTag}>移除</div>
                  </Popconfirm>
                </AccessCard>
                {detail?.info?.mode == 1 && (
                  <div
                    className={styles.labelTag}
                    onClick={() => {
                      warehouseTransfer(true);
                      // rollRef?.current?.isClaimOpen()
                    }}
                  >
                    仓库转运
                  </div>
                )}
              </>
              {/* )}*/}
            </div>
            {/*  {detail?.state !== 30 && (先放开*/}

            <Button
              disabled={!isAccessibleFlag}
              type="primary"
              onClick={() => {
                waybillRef?.current?.control();
              }}
            >
              添加运单
            </Button>
            {/*  )}*/}
          </div>
        </div>
        {waybillData?.map((item: any, index: number) => (
          <ProTable<any>
            scroll={{ x: 1600 }}
            columns={columns}
            className={styles.tables}
            dataSource={item?.waybillList || []}
            rowKey="id"
            key={index}
            editable={{
              type: 'multiple',
            }}
            pagination={false}
            search={false}
            options={false}
            dateFormatter="string"
            toolbar={{
              subTitle: (
                <div style={{ display: 'flex' }}>
                  <HomeOutlined
                    style={{
                      marginTop: '-6px',
                      color: '#FF8450',
                      marginRight: '15px',
                    }}
                  />
                  {/* Math.round(volume * 100000) / 100000 */}
                  <h4 style={{ marginRight: '15px' }}>
                    {item?.warehouse?.name}
                  </h4>
                  <span style={{ marginRight: '15px', color: '#4071FF' }}>
                    {item?.waybillList?.length}
                    <span style={{ color: '#333' }}>&nbsp;票</span>
                  </span>
                  <span style={{ marginRight: '15px', color: '#4071FF' }}>
                    {item?.waybillList?.reduce(
                      (prev: any, cur: any) => prev + cur?.pieceNum,
                      0,
                    ) || 0}
                    <span style={{ color: '#333' }}>&nbsp;件</span>
                  </span>
                  <span style={{ marginRight: '15px', color: '#4071FF' }}>
                    {calculateSum(item?.waybillList, 'volume', 5)}
                    <span style={{ color: '#333' }}>&nbsp;收货体积（m³）</span>
                  </span>
                  <span style={{ marginRight: '15px', color: '#4071FF' }}>
                    {Math.round(
                      item?.waybillList?.reduce(
                        (prev: any, cur: any) =>
                          prev + Number(cur?.signInActualWeight),
                        0,
                      ) * 100,
                    ) / 100 || 0}
                    {calculateSum(item?.waybillList, 'signInActualWeight')}
                    <span style={{ color: '#333' }}>&nbsp;收货实重（kg）</span>
                  </span>
                </div>
              ),
            }}
            rowSelection={{ ...rowSelection, checkStrictly: false }}
          />
        ))}
      </Card>

      {/* 添加运单 */}
      <AddWaybill
        ref={waybillRef}
        getputIntoConfigure={getputIntoConfigure}
        type="match"
        title={{ has: detail?.volume, noHas: detail?.info?.volume }}
        headTransType={detail?.info?.mode}
      />
      {/* 选择舱位 */}
      <SelectShipping ref={seleShippingRef} moveMatch={moveMatch} />
      <Modal
        title="出库异常"
        open={isModalOpen}
        onOk={() => setIsModalOpen(false)}
        onCancel={() => setIsModalOpen(false)}
        footer={[
          <div
            style={{ display: 'flex', justifyContent: 'center' }}
            key="footer"
          >
            <Button
              key="link"
              onClick={() => {
                setIsModalOpen(false);
              }}
            >
              取消
            </Button>
            <Button
              style={{ border: '1px solid orange', color: 'orange' }}
              key="submit"
              onClick={() => {
                getcompleteSignOut(1);
              }}
            >
              移除并出库
            </Button>
            <Button
              style={{ border: '1px solid  #20d2d5', color: ' #20d2d5' }}
              key="abnormal"
              danger
              onClick={() => {
                getcompleteSignOut(2);
              }}
            >
              签出并出库
            </Button>
          </div>,
        ]}
      >
        <div className={styles.partition}>{messageData}</div>
      </Modal>
      {/* 仓库间转运（空运） */}
      <RollModal
        ref={rollRef}
        source={interior}
        successCallback={successCallback}
      />
      {/* 仓库间转运（海运） */}
      <AddModal
        ref={addRef}
        source={interior}
        infoData={detail}
        stuffing={stuffing}
        successCallback={successCallback}
      />
    </>
  );
};
export default memo(Particulars);
