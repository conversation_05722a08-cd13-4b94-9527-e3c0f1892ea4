import { FC, memo } from "react"
import { Button, Switch } from 'antd';
import styles from './index.less'
const SetModule: FC = () => {
    const switchOnchnage = () => {

    }
    return <div className={styles.set_module}>
        <div className={styles.set_content}>
            <div className={styles.set_each}>
                <span>支付分票</span>
                <Switch defaultChecked onChange={switchOnchnage} />
            </div>
            <div className={styles.set_independent}>
                <span>独立配舱FBA</span>
                <div className={styles.set_top}>
                  <a>添加</a>
                </div>
            </div>
            <div className={styles.set_deploy}>
                <span>调配分区表</span>
                <div className={styles.set_bot}>
                <a>添加</a>
                
                </div>
                
            </div>
            <div className={styles.bot_button}>
            <Button style={{marginRight:'20px'}}>返回</Button>
            <Button type="primary" >保存</Button>
            </div>
           
        </div>
    </div>
}
export default memo(SetModule)