import { ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';
import {
  Button,
  message,
  Popconfirm,
  Select,
  Space,
  Spin,
  Tag,
  Tooltip,
} from 'antd';
import { formatTime } from '@/utils/format';
import AccessCard from '@/AccessCard';
import styles from './index.less';
import { TableRowSelection } from 'antd/es/table/interface';
import {
  ignoreWaybill,
  rePushWaybill,
  SyncWaybill,
} from '@/services/Waybilladministration';
// import ShareProTable from '@/components/MrTable'
import { waybillStatusEnum } from '@/utils/constant';
import MrTable from '@/components/MrTable';
import { bindingCabin, getNewPreplanCabin, removeTempConfigure } from "@/services/preplanCabin";
import { history } from '@@/core/history';
import SelectShipping from '@/pages/BOL/PreplanCabin/SelectShipping';
import { enumePar } from "@/pages/BOL/PreplanCabin/enumeration";
const NewPreplanCabin = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [loading, setloading] = useState(false);
  const [param, setParams] = useState<any>({ ignoreFlag: 0 });
  /*点击的id*/
  const [selectedId, setSelectedId] = useState('');
  /*选择舱位*/
  const seleShippingRef = useRef<any>(null);

  const moveMatch = async (spaceId: string) => {
    const res = await bindingCabin({ spaceId, configId: selectedId });
    if (res?.status) {
      message.success('绑定成功');
      actionRef.current?.reload();
    }
  };
  //控制选择舱位开关
  const moveArrive = (configId: any) => {
    setSelectedId(configId);
    /* if (check.length === 0) {
      return message.warning('请选择运单');
    }*/
    seleShippingRef?.current?.control();
  };
  /*删除记录*/
  const deleteMsg = async (id: any) => {
    const res = await removeTempConfigure({ configId: id });
    if (res?.status) {
      message.success('删除成功');
    }
  };
  //
  const textEllipsis = (text: any, width: number) => {
    return (
      <div
        style={{
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          width: width,
        }}
      >
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>
    );
  };
  const columns: any = [
    /*    {
      title: '查询',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入要查询的内容',
      },
    },*/
    {
      title: '名字',
      dataIndex: 'name',
      // copyable: true,
      // ellipsis: true,
      width: 100,
      hideInSearch: true,
    },
    {
      title: '状态',
      hideInSearch: true,
      width: 120,
      dataIndex: 'state',
      align: 'center',
      render: (recode: any, record: any) => {
        console.log('record', record);
        return (
          <Tag color={enumePar[record?.state]?.color}>{enumePar[record?.state]?.text}</Tag>
        );
      },
    },
    {
      title: '已配体积（m³）',
      dataIndex: 'volume',
      // copyable: true,
      // ellipsis: true,
      width: 100,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      width: 200,
      render: (text: any, record: any) => {
        return record?.createTime ? (
          <span>{textEllipsis(formatTime(record?.createTime), 200)}</span>
        ) : (
          <span>-</span>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (text: any, record: any) => {
        return (
          <Space>
            {/*  <AccessCard key={1} accessible={[]}>*/}
            <a
              key="editable"
              onClick={() => {
                console.log('record', record);
                history.push('/BOL/PreplanCabin/particulars', {
                  makingId: record?.id,
                });
              }}
              style={{ marginRight: '10px' }}
            >
              详情
            </a>

            {/*  </AccessCard>*/}

            {/*  <AccessCard key={2} accessible={[]}>*/}
            {!record?.info && (
              <a key="editable2" onClick={() => moveArrive(record?.id)}>
                绑定提单
              </a>
            )}
            <Popconfirm
              key="publish"
              title="提示"
              description="确定删除该条信息吗?"
              onConfirm={() => {
                deleteMsg(record?.id);
              }}
              onCancel={() => {
                //message.info('已取消操作');
              }}
              okText="确认"
              cancelText="取消"
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
            {/*  </AccessCard>*/}
          </Space>
        );
      },
    },
  ];
  const actionRef = useRef<any>();
  const rowSelection: TableRowSelection<any> = {
    onChange: (Keys: any, selectedRows: any) => {
      setSelectedRowKeys(selectedRows);
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys: selectedRowKeys.map((item: any) => item?.id),
  };
  const filters = {
    /*    keyword: {
      type: 'keyword',
      termQuery: true,
      value: '',
    },*/
  };
  const rowSelectionCablck = (data: any) => {
    setSelectedRowKeys(data);
  };
  return (
    <>
      <MrTable
        columns={columns}
        keyID={'setting'}
        rowSelectionCablck={rowSelectionCablck}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          const msg = await getNewPreplanCabin({
            ...params,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg?.data?.total,
          };
        }}
        filters={filters}
      />
      <SelectShipping ref={seleShippingRef} moveMatch={moveMatch} />
    </>
  );
};
export default NewPreplanCabin;
