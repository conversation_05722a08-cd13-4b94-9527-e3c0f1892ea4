import MySearch from '@/components/MyProTable/MySearch';
import Selects from '@/pages/Booking/components/Select';
import { configureListAPI, getWarehouseListAPI } from '@/services/preplanCabin';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Progress, Tag } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import { FC, forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import styles from './index.less'
interface Props {
    ref: any
    moveMatch: (data: string) => void
}
const SelectShipping: FC<Props> = forwardRef((props, ref) => {
    const { moveMatch } = props
    const [isModalOpen, setIsModalOpen] = useState(false);
    const actionRef = useRef<ActionType>();
    const [keyword, setKeyword] = useState('')
    const formRef = useRef<any>(null)
    const [state, setState] = useState()
    const [pieceIds, setPieceIds] = useState('')
    const [warehouse, setWarehouse] = useState<any>([]);
    const [warehouseId, setWarehouseId] = useState('')
    useImperativeHandle(ref, () => ({
        control() {
            setIsModalOpen(true)
        }
    }))
    useEffect(() => {
        getWarehouse()
    }, [])
    const getWarehouse = async (keyword?: string) => {
        try {
            const { status, data } = await getWarehouseListAPI({
                start: 0,
                len: 20,
                keyword,
                type: 0,
            });
            if (status) {
                setWarehouse(
                    data?.list?.map((item: any) => ({
                        value: item?.id,
                        label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
                    })),
                );
            }
        } catch (error) { }
    };
    const enumePar: any = {
        1: {
            text: '空闲',
            color: 'magenta'
        },
        10: {
            text: '待出库',
            color: 'red'
        },
        20: {
            text: '已提柜',
            color: 'volcano'
        },
        30: {
            text: '已出库',
            color: 'orange'
        }
    }
    const columns: any[] = useMemo(() => {
        return [
            {
                hideInTable: true,
                fieldProps: {
                    placeholder: '请输入企业名称、ID搜索',
                },
                renderFormItem: () => {
                    return <MySearch
                        placeholder="请输入提单号"
                        allowClear
                        enterButton="搜索"
                        size="large"
                        style={{ background: '#FBFBFB' }}
                        onSearch={(e: any) => {
                            setKeyword(e)
                            formRef?.current?.submit()
                        }}
                    />
                }
            },
            {
                title: '站点',
                hideInTable: true,
                renderFormItem: () => {
                    return <Selects options={warehouse} onChange={(e) => {
                        setWarehouseId(e)
                        formRef?.current?.submit()

                    }} />
                }
            },
            {
                title: '状态',
                hideInTable: true,
                renderFormItem: () => {
                    return <Selects options={[
                        { value: 1, label: '空闲' },
                        { value: 10, label: '待出库' },
                        { value: 20, label: '已提柜' },
                        { value: 30, label: '已出库' },
                    ]} onChange={(e) => {
                        setState(e)
                        formRef?.current?.submit()
                    }} />
                }
            },
            {
                title: '提单号',
                dataIndex: 'info',
                align: 'center',
                width: 200,
                hideInSearch: true,
                render: (recode: any) => {
                    return recode?.blno || '-'
                }
            },
            {
                title: '航线',
                dataIndex: 'info',
                hideInSearch: true,
                align: 'center',
                width: 200,
                render: (recode: any) => {
                    return recode?.vesselName || '-'
                }
            },
            {
                title: '配舱率（m³）',
                hideInSearch: true,
                dataIndex: 'number',
                align: 'center',
                width: 220,
                render: (_: any, recode: any) => {
                    const num = (recode?.volume / recode?.info?.volume) * 100
                    return <div style={{ display: 'flex' }}>
                        {`${recode?.volume}/${recode?.info?.volume}`}&nbsp;&nbsp;<Progress percent={num} size="small" showInfo={false} />
                    </div>
                }

            },
            {
                title: '箱型',
                hideInSearch: true,
                dataIndex: 'info',
                align: 'center',
                width: 120,
                render: (text: any,) => {
                    return text?.model || '-'
                }

            },

            {
                title: '状态',
                hideInSearch: true,
                width: 120,
                dataIndex: 'state',
                align: 'center',
                render: (recode: any) => {
                    return <Tag color={enumePar[recode]?.color}>
                        {enumePar[recode]?.text}
                    </Tag>
                }

            },
            {
                title: '装柜地',
                hideInSearch: true,
                width: 120,
                dataIndex: 'info',
                align: 'center',
                render: (recode: any) => {
                    return recode?.warehouses?.map((item: any) => item?.name).filter(Boolean)?.join(',') || ''
                }

            },
        ];
    }, [warehouse?.length])
    const rowSelection: TableRowSelection<any> = {
        onChange: (selectedRowKeys, selectedRows) => {
            const rowId = selectedRowKeys?.filter(Boolean)?.join(',')
            setPieceIds(rowId)
        },
    };
    const handleOk = () => {
        if (pieceIds?.length === 0) {
            return message.warning('请选择舱位')
        }
        setIsModalOpen(false);
        moveMatch(pieceIds)
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };
    return <>
        <Modal title="选择舱位"
            style={{ minWidth: '900px' }}
            open={isModalOpen} onOk={handleOk} onCancel={handleCancel}
            footer={[
                <div key='btn' style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button key="back" onClick={handleCancel} style={{ marginRight: '10px' }}>取消</Button>
                    <Button key="submit" type="primary" onClick={handleOk}>确认</Button>
                </div>
            ]}>
            <ProTable<any>
                className={styles.select_shipping}
                columns={columns}
                scroll={{ y: 320 }}
                actionRef={actionRef}
                formRef={formRef}
                request={async (params: any) => {
                    const { current: start, pageSize: len } = params;
                    const res = await configureListAPI({
                        start: (start - 1) * len,
                        len,
                        keyword,
                        state,
                        warehouseId
                    })
                    return {
                        data: res?.data?.list || [],
                        success: res.status,
                        total: res.data?.total,
                    };
                }}
                editable={{
                    type: 'multiple',
                }}
                columnsState={{
                    persistenceKey: 'pro-table-singe-demos',
                    persistenceType: 'localStorage',
                    onChange(value) {
                    },
                }}
                rowKey="spaceId"
                search={{
                    labelWidth: 'auto',
                    collapsed: false,
                    optionRender: false

                }}
                options={false}
                dateFormatter="string"
                rowSelection={{ ...rowSelection, type: 'radio' }}
            />
        </Modal>
    </>
})
export default memo(SelectShipping)