import React, { FC, memo, useRef, useState, useMemo, useEffect } from 'react';
import MySearch from '@/components/MyProTable/MySearch';
import { Button, Input, message, Select, Tag } from 'antd';
import { ProFormDateRangePicker, ProTable } from '@ant-design/pro-components';
import {
  getWarehouseListAPI,
  getputIntoConfigureAPI,
  waybillListAPI,
} from '@/services/preplanCabin';
import { formatDate, formatTime } from '@/utils/format';
import type { TableRowSelection } from 'antd/es/table/interface';
import _ from 'lodash';
import { getProductList } from '@/services/productAndPrice/api';
import SelectShipping from '../SelectShipping';
import styles from './index.less';
import AccessCard from '@/AccessCard';
import KeywordsSelect from '@/components/KeywordsSelect';
import TextArea from 'antd/es/input/TextArea';
import { printStatusEnume } from '@/pages/Waybilladministration/InlandTransfer/components/enumeration';
const Waybill: FC = () => {
  const [keyword, setKeyword] = useState('');
  const formRef = useRef<any>(null);
  const actionRef = useRef<any>(null);
  const seleShippingRef = useRef<any>(null);
  const [tableTitleData, setTableTitleData] = useState<any>({});
  const [state, setState] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const [volume, setVolume] = useState<any>();
  const [productId, setProductId] = useState([]);
  const [prodData, setProdData] = useState([]);
  const previousValueRef = useRef<any>(null);
  const [check, setCheck] = useState<any>([]);
  const [warehouse, setWarehouse] = useState<any>([]);
  const [waybillData, setWaybillData] = useState<any>([]);
  const [warehouseId, setWarehouseId] = useState('');
  const [startArriveTime, setStartArriveTime] = useState('');
  const [endArriveTime, setEndArriveTime] = useState('');
  const [signInBeginTime, setSignInBeginTime] = useState('');
  const [signInnEndTime, setSignInEndTime] = useState('');
  const [inquirePara, setInquirePara] = useState<any>({});

  const enumeration: any = {
    10: '非单独报关',
    20: '单独报关',
    30: '合并报关',
  };
  const refresh = () => {
    actionRef.current?.reload();
  };
  const getKeywords = (value: any) => {
    let warehouseStates: any = []; //仓库已完成阶段
    let states: any = []; //运单状态
    let receiptKeywords: any = []; //目的地关键词
    let interceptFlags: any = []; //拦截状态
    let productTypes: any = []; //产品类型
    let holdFlags: any = []; //其他关键词
    let configureState: any = []; //配舱状态
    value.map((item: any) => {
      const i = item.value.split('-');
      if (i[0] === '1') {
        warehouseStates.push(i[1]);
      } else if (i[0] === '2') {
        states.push(i[1]);
      } else if (i[0] === '3') {
        receiptKeywords.push(i[1]);
      } else if (i[0] === '4') {
        interceptFlags.push(i[1]);
      } else if (i[0] === '5') {
        productTypes.push(i[1]);
      } else if (i[0] === '6') {
        holdFlags.push(i[1]);
      } else if (i[0] === '7') {
        configureState.push(i[1]);
      }
      return item.value;
    });
    setInquirePara({
      ...inquirePara,
      warehouseStates: warehouseStates.join(','),
      states: states.join(','),
      receiptKeywords: receiptKeywords.join(','),
      interceptFlags: interceptFlags.join(','),
      productTypes: productTypes.join(','),
      holdFlags: holdFlags.join(','),
      configureState: configureState.join(','),
    });
    refresh();
  };
  const columns: any = useMemo(() => {
    return [
      {
        title: '运单号',
        dataIndex: 'waybillNO',
        align: 'center',
        width: 200,
        hideInSearch: true,
      },
      {
        title: '件数',
        dataIndex: 'pieceNum',
        hideInSearch: true,
        align: 'center',
        width: 100,
        render: (_: any, recode: any) => {
          return recode?.pieceNum;
        },
      },
      {
        title: '体积（m³）',
        hideInSearch: true,
        dataIndex: 'siginVolume',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.siginVolume;
        },
      },
      {
        title: '所在仓库',
        hideInSearch: true,
        dataIndex: 'warehouseName',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.warehouseName;
        },
      },
      {
        title: '报关类型',
        hideInSearch: true,
        dataIndex: 'clearanceType',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode ? enumeration[recode?.declarationType] : '';
        },
      },
      {
        title: '报关单',
        hideInSearch: true,
        dataIndex: 'declarationId',
        align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.declarationId;
        },
      },
      {
        title: '品名',
        hideInSearch: true,
        width: 120,
        dataIndex: 'goodsName',
        align: 'center',
        render: (_: any, recode: any) => {
          return recode?.goodsName;
        },
      },

      {
        title: '销售产品',
        hideInSearch: true,
        width: 200,
        dataIndex: 'productName',
        align: 'center',
        render: (_: any, recode: any) => {
          return recode?.productName;
        },
      },
      {
        title: '打单状态',
        hideInSearch: true,
        width: 200,
        dataIndex: 'orderState',
        align: 'center',
        render: (_: any, recode: any) => {
          return (
            <Tag color={printStatusEnume[recode.orderState]?.color}>
              {printStatusEnume[recode.orderState]?.text}
            </Tag>
          );
        },
      },
      {
        title: '转运状态',
        hideInSearch: true,
        width: 300,
        dataIndex: 'transferState',
        align: 'center',
        render: (recode: any, _: any) => {
          return recode?.state?.desc ? (
            <>
              <Tag color="magenta">{recode?.state?.desc}</Tag>{' '}
              {`${recode?.from?.name} -> ${recode?.to?.name}`}
            </>
          ) : (
            ''
          );
        },
      },
      {
        title: '签入时间',
        hideInSearch: true,
        dataIndex: 'siginTimes',
        align: 'center',
        width: 200,
        render: (recode: any, text: any) => {
          return text?.siginTime;
        },
      },
      {
        title: '到货时间',
        hideInSearch: true,
        dataIndex: 'arriveTime',
        align: 'center',
        width: 200,
        render: (recode: any, text: any) => {
          return formatTime(text?.arriveTime);
        },
      },
      {
        title: '目的地',
        hideInSearch: true,
        dataIndex: 'recipientAddress',
        align: 'center',
        width: 200,
        render: (_: any, recode: any) => {
          const data = recode?.recipientAddress;
          if (data?.isFBA == 1) {
            return (
              <div>
                <Tag color="purple">FBA</Tag>
                {`${data?.name}  ${data?.zipCode}`}
              </div>
            );
          } else if (data && data?.isFBA !== 1) {
            return (
              <div>{`${data?.province}${data?.city}${data?.county}${data?.zipCode}`}</div>
            );
          }
        },
      },
      {
        title: '客户',
        hideInSearch: true,
        dataIndex: 'client',
        align: 'center',
        width: 120,
        render: (text: any) => {
          return text?.name;
        },
      },
      {
        title: '业务员',
        hideInSearch: true,
        dataIndex: 'salesman',
        align: 'center',
        width: 120,
        render: (text: any) => {
          return text?.name;
        },
      },
    ];
  }, []);
  useEffect(() => {
    getProduct();
    getWarehouse();
  }, []);
  useEffect(() => {
    previousValueRef.current = check;
  }, [check]);

  // 数据替换树形的，在这里就格式化时间表头写rednder格式化时间 children会有问题
  const processedData = (data: any) => {
    return data.map((item: any) => {
      const newItem = {
        ...item,
        siginTime: item?.siginTime ? formatTime(item.siginTime) : '',
        warehouseName: item?.currentWarehouse?.name,
      };
      if (newItem.pieceList) {
        newItem.children = newItem.pieceList.map(
          (childItem: any, index: number) => {
            if (childItem?.subWaybillNO) {
              childItem.waybillNO = childItem?.subWaybillNO;
            }
            delete childItem.subWaybillNO;
            return {
              ...childItem,
              pieceNum: `${index + 1}/${item.pieceNum}`,
              warehouseName: childItem?.warehouse?.name,
            };
          },
        );
        delete newItem.pieceList;
      }
      return newItem;
    });
  };
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: check,
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys, selectedRows) => {
      // 假设不支持分票，倒时以实际为准
      let noLotting = true;
      if (noLotting) {
        // 此时用户取消运单操作/需要取消勾选所有的兄弟以及父级
        if (selectedRowKeys.length <= previousValueRef.current?.length) {
          // 找出当前取消的 key
          const differenceKey = previousValueRef.current.filter(
            (item: any) => !selectedRowKeys.includes(item),
          );
          // 拿取消的 key 去找他所有的兄弟以父级
          const resultIds = differenceKey.map((id: any) =>
            findIds(waybillData, id),
          );
          const uniqueIds = Array.from(new Set(resultIds.flat()));
          // 找到之后去当前勾选的里面去筛选掉，就是最终勾选的
          const newCheck = check.filter((ele: any) => !uniqueIds.includes(ele));
          setCheck(newCheck);
        } else {
          // 添加勾选运单操作/需要勾选所有兄弟以及父级
          const resultIds = selectedRowKeys.map((id: any) =>
            findIds(waybillData, id),
          );
          const uniqueIds = Array.from(new Set(resultIds.flat()));
          setCheck(uniqueIds);
        }
      } else {
        // 正常勾选，但是 map 出来的 n 个表格，每次勾选只能拿最近一次勾选的，这里需要叠加
        selectedRowKeys?.forEach((ele: any) => {
          const isCon = check.some((resultItem: any) => resultItem === ele);
          if (!isCon) {
            check.push(ele);
            setCheck([...check]);
          }
        });
        setCheck(selectedRowKeys);
      }
      const parentData = selectedRows?.filter((item: any) => item?.children);
      const number = parentData?.reduce((prev, cur) => prev + cur?.pieceNum, 0);
      const volume = parentData?.reduce(
        (prev, cur) => prev + parseFloat(cur?.siginVolume || 0),
        0,
      );
      setTableTitleData({
        // 已选票
        ticket: parentData.length,
        // 件数
        number,
        // 体积
        volume: Math.round(volume * 100) / 100,
      });
    },
  };
  // 拿到任意一个子级的 id 去找所有兄弟 id 如果是父级，返回即可
  function findIds(data: any, idToFind: any) {
    const result = [];
    for (const waybill of data) {
      if (waybill.id === idToFind) {
        result.push(waybill.id);
      } else {
        const children = waybill.children;
        for (const child of children) {
          if (child.id === idToFind) {
            const childIds = children.map((childItem: any) => childItem.id);
            result.push(...childIds);
            break;
          }
        }
      }
    }
    return result;
  }
  const handleInputChange = _.debounce((e: any) => {
    setVolume(e);
    getSubmit();
  }, 1000);
  // 触发表单
  const getSubmit = () => formRef?.current?.submit();
  // 产品 list
  const getProduct = async (keyWords?: string) => {
    try {
      const { status, data } = await getProductList({
        len: 20,
        keyword: keyWords,
      });
      if (status) {
        const newData = data?.list?.map((item: any) => ({
          value: item?.id,
          label: item?.name,
        }));

        setProdData(newData);
      }
    } catch (error) { }
  };
  const getWarehouse = async (keyword?: string) => {
    try {
      const { status, data } = await getWarehouseListAPI({
        start: 0,
        len: 20,
        keyword,
        type: 0,
      });
      if (status) {
        setWarehouse(
          data?.list?.map((item: any) => ({
            value: item?.id,
            label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
          })),
        );
      }
    } catch (error) { }
  };
  // 搜索，防抖
  const getProductAntiShake = _.debounce((e: string) => {
    getProduct(e);
  }, 200);
  const getWarehouseKeyword = _.debounce((e: any) => {
    getWarehouse(e);
  }, 100);
  // 选择舱位回调
  const moveMatch = async (spaceId: string) => {
    await getputIntoConfigure(check?.join(','), spaceId);
  };
  const moveArrive = () => {
    if (check.length === 0) {
      return message.warning('请选择运单');
    }
    seleShippingRef?.current?.control();
  };
  // 配舱
  const getputIntoConfigure = async (pieceIds: string, spaceId?: string) => {
    try {
      const { status } = await getputIntoConfigureAPI({
        spaceId: spaceId,
        pieceIds,
      });
      if (status) {
        message.success('操作成功');
        actionRef.current?.reload();
        // makingParticulars(spaceId ?? state.id)
      }
    } catch (error) { }
  };

  return (
    <>
      <div className={styles.inland_waybill}>
        <div className={styles.waybill_search}>
          <div className={styles.search_wrap} id="search_left">
            <Select
              defaultValue="精确"
              bordered={false}
              style={{
                height: '100%',
                border: 'none',
                backgroundColor: '#f7f7f7',
              }}
              onChange={(value: any) => {
                setInquirePara({ ...inquirePara, termQuery: value });
                refresh();
              }}
            >
              <Option value="true">精确</Option>
              <Option value="false">批量</Option>
            </Select>
            <TextArea
              className={styles.text_area}
              id="search_text"
              // style={{
              //   border: 'none',
              //   resize: 'none',
              //   height: '77px',
              //   borderRadius: '0px',
              //   paddingTop: '27px',
              // }}
              onChange={(e: any) => setKeyword(e.target.value)}
              placeholder="请输入ID,运单号，客户代码，客户名称，业务员搜索"
              onPressEnter={() => {
                setInquirePara({ ...inquirePara, keyword: keyword });
                refresh();
              }}
            />
            <Button
              style={{ height: '100%', color: '#537ffd', border: 'none' }}
              className={styles.search_back}
              onClick={() => {
                setInquirePara({ ...inquirePara, keyword: keyword });
                refresh();
              }}
            >
              搜索
            </Button>
          </div>
          <div>
            <span>销售产品：</span>
            <Select
              showSearch
              placeholder="请选择"
              style={{ width: '250px' }}
              options={prodData}
              filterOption={(input, option: any) => true}
              allowClear
              mode={'multiple'}
              onSearch={(e) => getProductAntiShake(e)}
              onChange={(e) => {
                setProductId(e);
                getSubmit();
              }}
            />
          </div>
          <div>
            <span>预估体积：</span>
            <Input
              placeholder="请输入"
              step="1"
              type="number"
              style={{
                border: 'none',
                backgroundColor: 'rgb(251, 251, 251)',
                width: '250px',
              }}
              onChange={(e) => {
                handleInputChange(e?.target?.value);
              }}
            />
          </div>
          <div>
            <span>所在仓库：</span>
            <Select
              showSearch
              // filterOption={(input, option: any) => (option?.label ?? '').includes(input)}
              filterOption={(input, option: any) => true}
              options={warehouse}
              style={{ width: '250px' }}
              allowClear
              className={styles.waybillNumber}
              placeholder="请选择"
              onSearch={getWarehouseKeyword}
              onChange={(e) => {
                setWarehouseId(e);
                getSubmit();
              }}
            />
          </div>
          <div>
            <ProFormDateRangePicker
              label={'到货时间'}
              placeholder={['起始', '截至']}
              labelCol={{ span: 4 }}
              colon={false}
              wrapperCol={{ span: 20 }}
              fieldProps={{
                className: styles.search_back,
              }}
              onChange={(time: any) => {
                setStartArriveTime(
                  time ? formatDate(time[0]) + '00:00:00' : '',
                );
                setEndArriveTime(time ? formatDate(time[1]) + '23:59:59' : '');
              }}
              showTime
            />
          </div>
          <div>
            <ProFormDateRangePicker
              label={'签入时间'}
              placeholder={['起始', '截至']}
              labelCol={{ span: 4 }}
              colon={false}
              wrapperCol={{ span: 20 }}
              fieldProps={{
                className: styles.search_back,
              }}
              onChange={(time: any) => {
                setSignInBeginTime(
                  time ? formatDate(time[0]) + '00:00:00' : '',
                );
                setSignInEndTime(time ? formatDate(time[1]) + '23:59:59' : '');
              }}
              showTime
            />
          </div>
          <div style={{ minWidth: '400px' }}>
            状态:
            <KeywordsSelect
              getKeywords={getKeywords}
              width={'calc(100% - 104px)'}
              options={[
                {
                  label: '仓库已完成阶段',
                  options: [
                    {
                      label: '入库成功',
                      value: '1-1',
                    },
                    {
                      label: '业务确认',
                      value: '1-2',
                    },
                    {
                      label: '验货通过',
                      value: '1-3',
                    },
                    {
                      label: '已确认渠道',
                      value: '1-4',
                    },
                    {
                      label: '预报成功',
                      value: '1-5',
                    },
                    {
                      label: '签出成功',
                      value: '1-6',
                    },
                  ],
                },
                {
                  label: '运单状态',
                  options: [
                    {
                      label: '草稿',
                      value: '2-0',
                    },
                    {
                      label: '待签入',
                      value: '2-10',
                    },
                    {
                      label: '已签入',
                      value: '2-20',
                    },
                    {
                      label: '已确认',
                      value: '2-40',
                    },
                    {
                      label: '已配舱',
                      value: '2-60',
                    },
                    {
                      label: '已签出',
                      value: '2-90',
                    },
                    {
                      label: '已签收',
                      value: '2-300',
                    },
                    {
                      label: '异常结束',
                      value: '2-500',
                    },
                  ],
                },
                {
                  label: '配仓状态',
                  options: [
                    {
                      label: '未配仓',
                      value: '7-0',
                    },
                    {
                      label: '已配仓',
                      value: '7-1',
                    },
                  ],
                },
                {
                  label: '目的地关键词',
                  options: [
                    {
                      label: 'FBA',
                      value: '3-fba',
                    },
                    {
                      label: '非FBA',
                      value: '3-notFba',
                    },
                    {
                      label: '美国',
                      value: '3-US',
                    },
                    {
                      label: '英国',
                      value: '3-GB',
                    },
                    {
                      label: '加拿大',
                      value: '3-CA',
                    },
                  ],
                },
                {
                  label: '是否拦截',
                  options: [
                    {
                      label: '拦截审批中',
                      value: '4-10',
                    },
                    {
                      label: '拦截成功',
                      value: '4-20',
                    },
                    {
                      label: '拦截驳回',
                      value: '4-30',
                    },
                  ],
                },
                {
                  label: '产品类目',
                  options: [
                    {
                      label: '专线-自营',
                      value: '5-1',
                    },
                    {
                      label: '专线-同行',
                      value: '5-2',
                    },
                    {
                      label: '快递',
                      value: '5-3',
                    },
                  ],
                },
                {
                  label: '其他常用关键字',
                  options: [
                    {
                      label: '扣件',
                      value: '6-1',
                    },
                    {
                      label: '未扣件',
                      value: '6-0',
                    },
                  ],
                },
              ]}
            />
          </div>
        </div>
        <ProTable<any>
          scroll={{ x: 1600 }}
          columns={columns}
          actionRef={actionRef}
          formRef={formRef}
          rowKey="id"
          pagination={{
            pageSizeOptions: [10, 20, 100, 500],
            defaultPageSize:
              Number(localStorage.getItem('selectPreplanCabinPageSize')) || 20,
            showSizeChanger: true,
            onShowSizeChange: (current, size) => {
              localStorage.setItem('selectPreplanCabinPageSize', String(size));
            },
          }}
          request={async (params: any) => {
            const { current: start, pageSize: len } = params;
            const param: any = {
              start: (start - 1) * len,
              len,
              keyword,
              state,
              volume,
              productIds: productId.join(','),
              warehouseId,
            };
            if (startArriveTime) {
              param.startArriveTime = startArriveTime;
            }
            if (endArriveTime) {
              param.endArriveTime = endArriveTime;
            }
            if (signInnEndTime) {
              param.signInnEndTime = signInnEndTime;
            }
            if (signInBeginTime) {
              param.signInBeginTime = signInBeginTime;
            }
            const msg = await waybillListAPI({ ...param, ...inquirePara });
            const newData = processedData(msg?.data?.list || []);
            setWaybillData(newData);
            return {
              data: newData || [],
              success: msg.status,
              total: msg.data.total,
            };
          }}
          params={{
            startArriveTime,
            endArriveTime,
            signInBeginTime,
            signInnEndTime,
            ...inquirePara,
          }}
          editable={{
            type: 'multiple',
          }}
          search={{
            labelWidth: 'auto',
            collapsed: false,
            optionRender: false,
          }}
          options={{
            fullScreen: true,
          }}
          dateFormatter="string"
          style={{ marginBottom: 20 }}
          rowSelection={{ ...rowSelection, checkStrictly: false }}
          toolbar={{
            subTitle: (
              <div style={{ display: 'flex', lineHeight: '32px' }}>
                已选
                <span style={{ marginRight: '15px', color: '#4071FF' }}>
                  &nbsp;{tableTitleData?.ticket || 0}&nbsp;
                  <span style={{ color: '#333' }}>票</span>
                </span>
                <span style={{ marginRight: '15px', color: '#4071FF' }}>
                  {tableTitleData?.number || 0}&nbsp;
                  <span style={{ color: '#333' }}>件</span>
                </span>
                <span style={{ marginRight: '15px', color: '#4071FF' }}>
                  {tableTitleData?.volume || 0}&nbsp;
                  <span style={{ color: '#333' }}>m³</span>
                </span>
                <Button onClick={() => moveArrive()}>配舱</Button>
                <AccessCard
                  accessible={[':Waybill:TransferInnerFullAccess']}
                >
                  <Button loading={loading}>仓库转运</Button>
                </AccessCard>
                <Button loading={loading}>签出</Button>
              </div>
            ),
          }}
        />
      </div>
      {/* 选择舱位 */}
      <SelectShipping ref={seleShippingRef} moveMatch={moveMatch} />
    </>
  );
};
export default memo(Waybill);
