
.inland_waybill {

    .search_wrap {
      border: 1px solid #e8e8e8;
      height: 40px;
      display: flex;
      width: 440px;
      margin-right: 24px;
      border-radius: 8px;
      margin-bottom: 16px;
      .text_area {
        padding-top: 0;
        overflow-y: hidden;
        height: 38px;
        border: none;
        resize: none;
        border-radius: 0;
        // border: none,
        // resize: 'none',
        // // height: '77px',
        // borderRadius: '0px',
        // paddingTop: '27px',
      }

      :global(.ant-select-selector) {
        height: 100%;

        :global(.ant-select-selection-item) {
          display: flex;

          align-items: center;
        }
      }
    }

    .clientele {
      width: 440px !important;
      height: 38px;
      border-radius: 4px;
      border: 1px solid #E5E5E5;
      display: flex;
      margin-bottom: 16px;
      margin-right: 24px;

      .organization {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 104px;
        height: 38px;
        border-radius: 4px 0px 0px 4px;
        border-right: 1px solid #E5E5E5;
        font-weight: 400;
        font-size: 14px;
        color: #707070;
      }

      :global {
        .ant-select {
          width: calc(100% - 104px) !important;
          border: none;
        }

        .ant-select-single {
          width: 100% !important;
          height: 100% !important;
          .ant-select-selector {
            border: none;
          }
        }

        .ant-picker,
        .ant-input {
          border: none !important;
        }

      }
    }
    :global {
        .ant-form {
            display: none !important;
        }
        .ant-pro-card-border {
            border: none !important;
        }
        .ant-pro-table-list-toolbar-title {
            font-size: 14px !important;
        }
        .ant-btn-default {
            border-color: #4071FF !important;
            color: #4071FF;
            margin-right: 10px;
        }
    }
    .waybill_search {
            display: flex;
            // height: 80px;
            background-color: #fff;
            flex-wrap: wrap;
            box-sizing: border-box;
            padding: 20px 20px 0 20px;
            border-radius: 0 0 10px 10px;
            >div  {
                margin-bottom: 20px;
            }
            :global {
                .ant-select  {
                    height: 32px !important;
                }
                .ant-select-selector{
                    background-color: #FBFBFB !important;
                    border: none !important;
                }
                .ant-picker.ant-picker-range {
                    border: none !important;
                }
            }
           >div {
           margin-left: 30px;
           }
    }
    .tab_title {
        display: flex;
        justify-content: space-between;
        width: 200px;
    }
}