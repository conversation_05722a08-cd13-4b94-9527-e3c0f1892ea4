import MrTable from "@/components/MrTable";
import { REQUESTADDRESS_W } from "@/globalData";
import { exportInfoList, getWaybillLiInfoListAPI } from "@/services/productAndPrice/api";
import { formatTime } from "@/utils/format";
import { message, Tag } from "antd";
import { useRef, useState } from "react";
import { saveAs } from 'file-saver';

const TagColor: any = {
    '预报': 'gray',
    '出库': 'blue',
    '完成': 'green'
}
const WaybillBackEnd = () => {
    const actionRef = useRef<any>();
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const columns = [
        {
            title: '运单号',
            dataIndex: 'waybillNo',
            width: 200,
            ellipsis: true,
        },
        {
            title: '状态',
            dataIndex: 'stateDesc',
            width: 200,
            ellipsis: true,
            render: (_: any, recode: any) => {
                return <Tag color={TagColor[recode?.stateDesc]}>{recode?.stateDesc}</Tag>
            }
        },
        {
            title: 'ISA',
            dataIndex: 'isa',
            width: 200,
            ellipsis: true,
        },
        {
            title: '预约时间',
            dataIndex: 'appTime',
            width: 200,
            ellipsis: true,
            render: (text: any, record: any) => {
                return record?.appTime ? formatTime(record?.appTime) : ''
            },
        },
        {
            title: '仓库代码',
            dataIndex: 'fbaCode',
            width: 200,
            ellipsis: true,
            // render: (text: any, record: any) => {
            //     return record?.appTime ? formatTime(record?.appTime) : ''
            // },
        },
        {
            title: '柜号',
            dataIndex: 'containerNo',
            width: 200,
            ellipsis: true,
            // render: (text: any, record: any) => {
            //     return record?.appTime ? formatTime(record?.appTime) : ''
            // },
        },
        {
            title: 'FBA编号',
            dataIndex: 'fbaNo',
            width: 200,
            ellipsis: true,
        },
        {
            title: 'FBA跟踪号',
            dataIndex: 'referenceNo',
            width: 200,
            ellipsis: true,

        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 200,
            ellipsis: true,
            render: (text: any, record: any) => {
                return record?.createTime ? formatTime(record?.createTime) : ''
            },
        },
        {
            title: '出库时间',
            dataIndex: 'outboardTime',
            width: 200,
            ellipsis: true,
            render: (text: any, record: any) => {
                return record?.outboardTime ? formatTime(record?.outboardTime) : ''
            },
        },
        {
            title: '完成时间',
            dataIndex: 'finishTime',
            width: 200,
            ellipsis: true,
            render: (text: any, record: any) => {
                return record?.finishTime ? formatTime(record?.finishTime) : ''
            },
        },
        {
            title: 'pod文件',
            dataIndex: 'podPath',
            width: 200,
            ellipsis: true,
            render: (text: any, record: any) => {
                return record?.podPath ? <a onClick={() => {
                    window.open(`${REQUESTADDRESS_W}/${record?.podPath}`)
                }}>查看</a> : ''
            },
        },
        {
            title: '出库包裹数',
            dataIndex: 'checkoutPcs',
            width: 200,
            ellipsis: true,

        },
        {
            title: '出库板数',
            dataIndex: 'boardNums',
            width: 200,
            ellipsis: true,

        },
        {
            title: '备注',
            dataIndex: 'remark',
            width: 200,
            ellipsis: true,

        },
    ]
    /* 打印面单 */
    const printSurfaceSheet = async (value: any) => {
        try {
            const response: any = await exportInfoList(value);
            const fileName = decodeURI(
                response.headers['content-disposition'].match(/filename=(.*)/)[1],
            );
            const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, newFileName);
            message.success('导出成功');
            actionRef?.current.reload();
        } catch (err) {
            message.warning('服务器异常');
        }
    };
    return <>   <MrTable
        keyID="BOL/WaybillBackEnd"
        columns={columns}
        request={async (params: any, action: any) => {
            actionRef.current = action;
            // console.log('tableactiveKey',activeKey);
            let msg = await getWaybillLiInfoListAPI({
                ...params,
            });
            return {
                data: msg?.data?.list || [],
                success: msg?.status,
                total: msg?.data?.total,
            };
        }}
        filters={{
            keyword: {
                type: 'search',
                value: '',
                termQuery: false,
            },
            appTime: {
                desc: '预约时间',
                type: 'dateTimeRange',
                startTime: '',
                endTime: '',
            },
        }}
        exportCallback={(value: any) =>
            printSurfaceSheet(
                Array.isArray(value)
                    ? { ids: value?.map((item: any) => item?.id) }
                    : value,
            )
        }
        rowSelectionCablck={(value: any) => {
            //   setSelectedRowKeys(value);
        }}
    /></>
}
export default WaybillBackEnd