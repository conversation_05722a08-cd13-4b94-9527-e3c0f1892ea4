import {
  getAddFeeAP<PERSON>,
  getCalinetIndent,
  getCreateOrder,
} from '@/services/booking';
import MrTable from '@/components/MrTable';
import styles from './index.less';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, DatePicker, Input, message, Modal, Space } from 'antd';
import { ProFormSelect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { getCabinBlno, getFeeTypeAPI } from '@/services/financeApi';
import LogDashboard from '@/components/LogDashboard';
import _ from 'lodash';
import { getClientListAPI } from '@/services/carriers';
import dayjs from 'dayjs';
import { formatTime } from '@/utils/format';
const ClientORder = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [orderOpen, setOrderOpen] = useState(false);
  const actionRef = useRef<any>(null);
  const [action, setAction] = useState<any>({});
  const [billNameList, setBillNameList] = useState([]);
  const [feeData, setFeeData] = useState<any>({});
  const [params, setParams] = useState<any>({
    blno: '',
    shipNameVoyageCode: '',
    vesselName: '',
    estimatedDepartTime: '',
    sealNo: '',
    containerNo: '',
    departsCity: '',
    arrivesCity: '',
    clientId: '',
    counterpartyType: '',
  });
  const partyType: any = {
    ShipTransport: '订舱',
    Clearance: '清关',
    AbroadOperate: '后端',
  };
  const options = useMemo(() => {
    return [
      // { label: '空闲', value: '1' },
      // { label: '已装柜', value: '2' },
      // { label: '已开船', value: '3' },
      // { label: '已转卖', value: '4' },
      // { label: '已退订', value: '5' },
      { label: '未完成', value: '1' },
      { label: '部分完成', value: '2' },
      { label: '已完成', value: '3' },
      { label: '已撤销', value: '4' },
    ];
  }, []);
  const columns = useMemo(() => {
    return [
      {
        title: '提单号',
        // align: 'center',
        dataIndex: 'blno',
        width: 200,
        hideInSearch: true,
      },
      {
        title: '航线',
        dataIndex: 'state',
        // hideInTable: true,
        hideInSearch: true,
        // align: 'center',
        width: 100,
        render: (_: any, recode: any) => {
          return recode?.vesselName;
        },
      },
      {
        title: '业务类型',
        dataIndex: 'counterpartyType',
        // hideInTable: true,
        hideInSearch: true,
        // align: 'center',
        width: 100,
        render: (_: any, recode: any) => {
          return <span>{partyType[recode?.counterpartyType]}</span>;
        },
      },
      {
        title: '船名航次',
        hideInSearch: true,
        dataIndex: 'voyageProperties',
        // align: 'center',
        width: 260,
        render: (text: any, recode: any) => {
          return (
            <div>
              {recode?.shipNameVoyageCode
                ? recode?.shipNameVoyageCode
                : `${recode?.shipName}/${recode?.voyageCode}`}
            </div>
          );
        },
      },
      {
        title: '规格',
        dataIndex: 'provider',
        hideInSearch: true,
        // align: 'center',
        width: 160,
        render: (_: any, text: any) => {
          return text?.model;
        },
      },
      {
        title: '客户',
        hideInSearch: true,
        dataIndex: 'vesselName',
        // align: 'center',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.clientName;
        },
      },
      {
        title: '出发地',
        hideInSearch: true,
        dataIndex: 'voyage',
        // align: 'center',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.departsCity;
        },
      },

      {
        title: '到达地',
        hideInSearch: true,
        // align: 'center',
        width: 120,
        render: (_: any, recode: any) => {
          return recode?.arrivesCity;
        },
      },
      {
        hideInSearch: true,
        title: '开船时间',
        // align: 'center',
        width: 160,
        render: (_: any, recode: any) => {
          return recode?.estimatedDepartTime;
        },
      },

      {
        title: '操作',
        valueType: 'option',
        key: 'option',
        // width: 200,
        // align: 'center',
        fixed: 'right',
        render: (_: any, recode: any) => {
          return (
            <Space>
              <a
                onClick={() => {
                  history.push(`/BOL/ClientORder/Detail`, {
                    sellCabinetId: recode?.id,
                  });
                }}
              >
                查看
              </a>
              <a
                onClick={() => {
                  setAction({
                    blon: recode?.blno,
                    clientName: recode?.clientName,
                    clientId: recode?.clientId,
                    entityId: recode?.id,
                  });
                  setIsModalOpen(true);
                }}
              >
                添加费用
              </a>
              <LogDashboard
                extraId_1={recode?.id}
                btnType="link"
                btnText="日志"
                key="journal"
              />
            </Space>
          );
        },
      },
    ];
  }, []);
  const filters = {
    keyword: {
      type: 'search',
      value: '',
    },
    clientId: {
      desc: '客户',
      type: 'client',
      value: '',
    },
    counterpartyType: {
      desc: '业务',
      type: 'select',
      multi: true,
      range: [
        {
          label: '订舱',
          value: 'ShipTransport',
        },
        {
          label: '清关',
          value: 'Clearance',
        },
        {
          label: '后端',
          value: 'AbroadOperate',
        },
      ],
    },
  };
  useEffect(() => {
    getBillName();
  }, []);
  const getBillName = async (counterpartyType?: string, keyWords?: string) => {
    const { status, data } = await getFeeTypeAPI({
      keyword: keyWords,
      // counterpartyType: 'ShipTransport'
      counterpartyType,
    });
    if (status) {
      return setBillNameList(
        data?.map((item: any) => {
          return {
            label: Object.values(item)[0],
            value: Object.keys(item)[0],
          };
        }) || [],
      );
    }
  };
  const getAddFee = async () => {
    if (!feeData?.billName) return message.warning('费用名称不能为空');
    if (!feeData?.exchangeRate) return message.warning('汇率不能为空');
    if (!feeData?.currencyTypeCode) return message.warning('币种不能为空');
    if (!feeData?.amount) return message.warning('金额不能为空');
    if (!feeData?.counterpartyType) return message.warning('业务类型不能为空');
    if (!feeData?.importType) return message.warning('方式不能为空');
    try {
      const { status } = await getAddFeeAPI({
        ...feeData,
        clientId: action?.clientId,
        entityId: action?.entityId,
      });
      if (status) {
        message.success('添加成功');
        setIsModalOpen(false);
        actionRef.current?.reload();
      }
    } catch (error) {}
  };
  const getBillDetail = async (blno: string) => {
    const { status, data } = await getCabinBlno({
      blno,
    });
    if (status) {
      setParams(data ? { ...data } : { ...params, blno: blno });
    }
  };
  // 创建客户订单
  const createClientOrder = async () => {
    if (!params?.blno) return message.warning('提单号不能为空');
    if (!params?.clientId) return message.warning('请选择客户');
    if (!params?.counterpartyType) return message.warning('请选择业务类型');
    delete params?.id;
    try {
      const { status } = await getCreateOrder({ ...params });
      if (status) {
        message.success('创建成功');
        setOrderOpen(false);
        actionRef.current?.reload();
        setParams({
          blno: '',
          shipNameVoyageCode: '',
          vesselName: '',
          estimatedDepartTime: '',
          sealNo: '',
          containerNo: '',
          departsCity: '',
          arrivesCity: '',
          clientId: '',
          counterpartyType: '',
        });
      }
    } catch (error) {}
  };
  return (
    <>
      <MrTable
        columns={columns}
        keyID={'Booking/cabin/bookingPlanss'}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          const res = await getCalinetIndent({
            ...params,
          });
          return {
            data: res.data.list || [],
            success: res.status,
            total: res.data?.total,
          };
        }}
        filters={filters}
        toolBarRender={
          <Button type={'primary'} onClick={() => setOrderOpen(true)}>
            创建订单
          </Button>
        }
      />
      <Modal
        title="添加费用"
        open={isModalOpen}
        destroyOnClose
        onCancel={() => setIsModalOpen(false)}
        width={800}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              key="back"
              onClick={() => setIsModalOpen(false)}
              style={{ marginRight: '12px' }}
            >
              取消
            </Button>
            <Button key="submit" type="primary" onClick={getAddFee}>
              确认
            </Button>
          </div>,
        ]}
      >
        <div className={styles.billNumber}>
          <div>
            <span>提单号</span>
            <span>{action?.blon}</span>
            <span>客户</span>
            <span>{`${action?.clientName}`}</span>
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName}>
            <ProFormSelect
              label="业务类型"
              showSearch
              colon={false}
              // mode="multiple"
              options={[
                { label: '订舱', value: 'ShipTransport' },
                { label: '清关', value: 'Clearance' },
                { label: '后端', value: 'AbroadOperate' },
              ]}
              onChange={(e: any) => {
                setFeeData({ ...feeData, counterpartyType: e, billName: '' });
                getBillName(e);
              }}
            />
            <div className={styles.money}>
              <ProFormSelect
                label="方式"
                showSearch
                colon={false}
                options={[
                  { label: '追加', value: '1' },
                  { label: '覆盖', value: '2' },
                  { label: '总值', value: '3' },
                ]}
                onChange={(e) => {
                  setFeeData({ ...feeData, importType: e });
                }}
              />
            </div>
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName}>
            <ProFormSelect
              label="费用名称"
              showSearch
              colon={false}
              options={billNameList}
              onChange={(e) => {
                setFeeData({ ...feeData, billName: e });
              }}
            />
            <div className={styles.money}>
              <span>汇率</span>
              <Input
                placeholder="请输入"
                type="number"
                onChange={(e) => {
                  setFeeData({ ...feeData, exchangeRate: e?.target?.value });
                }}
              />
            </div>
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName}>
            <ProFormSelect
              name="clientName"
              label="币种"
              colon={false}
              onChange={(e) => {
                setFeeData({ ...feeData, currencyTypeCode: e });
              }}
              options={[
                {
                  label: '人民币',
                  value: 10,
                },
                {
                  label: '美元',
                  value: 20,
                },
                {
                  label: '加币',
                  value: 30,
                },
                {
                  label: '欧元',
                  value: 40,
                },
                {
                  label: '日元',
                  value: 50,
                },
              ]}
            />
            <div className={styles.money}>
              <span>金额</span>
              <Input
                placeholder="请输入"
                type="number"
                onChange={(e) => {
                  setFeeData({ ...feeData, amount: e?.target?.value });
                }}
              />
            </div>
          </div>
        </div>
        <div className={styles.remark}>
          <span>备注</span>
          <Input
            placeholder="请输入"
            onChange={(e) => {
              setFeeData({ ...feeData, remark: e?.target?.value });
            }}
          />
        </div>
      </Modal>
      <Modal
        title="创建订单"
        open={orderOpen}
        destroyOnClose
        onCancel={() => setOrderOpen(false)}
        width={800}
        afterClose={() => {
          setParams({
            blno: '',
            shipNameVoyageCode: '',
            vesselName: '',
            estimatedDepartTime: '',
            sealNo: '',
            containerNo: '',
            departsCity: '',
            arrivesCity: '',
            clientId: '',
            counterpartyType: '',
          });
        }}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              key="back"
              onClick={() => setOrderOpen(false)}
              style={{ marginRight: '12px' }}
            >
              取消
            </Button>
            <Button key="submit" type="primary" onClick={createClientOrder}>
              确认
            </Button>
          </div>,
        ]}
      >
        <div style={{ width: '100%', marginTop: '20px' }}>
          <div className={styles.clientName} style={{ marginBottom: '24px' }}>
            <div className={styles.money}>
              <span>提单号</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  getBillDetail(e?.target?.value);
                  // setParams({ ...params, blno: e?.target?.value })
                  // setFeeData({ ...feeData, exchangeRate: e?.target?.value })
                }}
              />
            </div>
            <div className={styles.money}>
              <span>船名航次</span>
              <Input
                placeholder="请输入"
                value={params?.shipNameVoyageCode}
                onChange={(e) => {
                  setParams({
                    ...params,
                    shipNameVoyageCode: e?.target?.value,
                  });
                }}
              />
            </div>
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName} style={{ marginBottom: '24px' }}>
            <div className={styles.money}>
              <span>航线</span>
              <Input
                placeholder="请输入"
                value={params?.vesselName}
                onChange={(e) => {
                  setParams({ ...params, vesselName: e?.target?.value });
                }}
              />
            </div>
            <div className={styles.money}>
              <span>开船时间</span>
              <DatePicker
                value={
                  params?.estimatedDepartTime
                    ? dayjs(params?.estimatedDepartTime)
                    : ''
                }
                onChange={(e) => {
                  setParams({
                    ...params,
                    estimatedDepartTime: e ? formatTime(e) : '',
                  });
                }}
              />
            </div>
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName} style={{ marginBottom: '24px' }}>
            <div className={styles.money}>
              <span>柜号</span>
              <Input
                placeholder="请输入"
                // type='number'
                value={params?.sealNo}
                onChange={(e) => {
                  setParams({ ...params, sealNo: e?.target?.value });
                }}
              />
            </div>
            <div className={styles.money}>
              <span>箱型</span>
              <Input
                placeholder="请输入"
                // type='number'
                value={params?.model}
                onChange={(e) => {
                  setParams({ ...params, model: e?.target?.value });
                }}
              />
            </div>
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName} style={{ marginBottom: '24px' }}>
            <div className={styles.money}>
              <span>出发港</span>
              <Input
                placeholder="请输入"
                value={params?.departsCity}
                onChange={(e) => {
                  setParams({ ...params, departsCity: e?.target?.value });
                }}
              />
            </div>
            <div className={styles.money}>
              <span>目的港</span>
              <Input
                placeholder="请输入"
                value={params?.arrivesCity}
                onChange={(e) => {
                  setParams({ ...params, arrivesCity: e?.target?.value });
                }}
              />
            </div>
          </div>
        </div>

        <div className={styles.remark}>
          <div className={styles.clientName} style={{ width: '100%' }}>
            <div className={styles.money}>
              <ProFormSelect
                label="选择客户"
                showSearch
                colon={false}
                // options={billNameList}
                onChange={(e: any) => {
                  setParams({ ...params, clientId: e });
                }}
                request={async ({ keyWords }) => {
                  const { status, data } = await getClientListAPI({
                    keyword: keyWords,
                    start: 0,
                    len: 20,
                  });
                  if (status) {
                    return data?.list?.map((item: any) => ({
                      value: item?.id,
                      label: item?.name,
                    }));
                  }
                  return [];
                }}
              />
              {/* <Select
                  showSearch
                  filterOption={(input, option: any) => true}
                  options={warehouse}
                  allowClear
                  className={styles.waybillNumber}
                  placeholder="请选择"
                  onSearch={getWarehouseKeyword}
                  onChange={(e) => {
                    setParams({ ...params, warehouseId: e })
                  }}
                /> */}
            </div>
            <div className={styles.money}>
              <ProFormSelect
                label="业务类型"
                showSearch
                mode="multiple"
                colon={false}
                options={[
                  { label: '舱位', value: 'ShipTransport' },
                  { label: '清关', value: 'Clearance' },
                  { label: '后端', value: 'AbroadOperate' },
                ]}
                onChange={(e: any) => {
                  setParams({ ...params, counterpartyType: e?.join(',') });
                }}
              />
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default ClientORder;
