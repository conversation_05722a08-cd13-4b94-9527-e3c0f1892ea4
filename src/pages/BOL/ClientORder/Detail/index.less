.cabinet_indent {
    display: flex;

    >div {
        display: flex;
        flex-direction: column;
        width: 460px;
        height: 120px;

        >div {
            display: flex;
            width: 100%;
            height: 44px;

            >span {
                display: inline-block;
                width: 140px;
                height: 44px;
                background: #FBFBFD;
                border: 1px solid #EFEFEF;
                padding-left: 16px;
                line-height: 44px;
            }

            >div {
                flex: 1;
                border: 1px solid #EFEFEF;
                border-left: none;
                display: flex;
                align-items: center;
                padding-left: 16px;
            }


        }
    }

    margin-bottom: 40px;
}

.inquire {
    display: flex;
    width: 100%;
    height: 180px;

    :global {
        .ant-input {
            border-radius: 0 6px 6px 0;
        }
    }

    // :first-child,
    // :nth-child(2) {
    //     width: 25%;
    //     height: 180px;
    // }

    // :nth-child(3) {
    //     width: 50%;
    //     height: 180px;
    // }
    .inquire_left,
    .inquire_center {
        width: 25%;
        height: 100%;

        >div {
            display: flex;
            width: 100%;
            height: 40px;
            margin-bottom: 16px;


            >span {
                display: inline-block;
                width: 104px;
                height: 40px;
                border-radius: 4px 0px 0px 4px;
                border: 1px solid #E5E5E5;
                line-height: 40px;
                padding-left: 12px;
                box-sizing: border-box;
                font-weight: 400;
                font-size: 14px;
                color: #707070;
                border-right: none;

            }

        }


    }

    .inquire_left {
        margin-right: 16px;
    }

    .inquire_center {
        margin-right: 40px;
    }

    .inquire_right {
        width: 50%;
        height: 100%;

        >div {
            display: flex;
            width: 100%;
            height: 40px;
            margin-bottom: 16px;

            >span {
                display: inline-block;
                width: 104px;
                height: 40px;
                border-radius: 4px 0px 0px 4px;
                border: 1px solid #E5E5E5;
                line-height: 40px;
                padding-left: 12px;
                box-sizing: border-box;
                font-weight: 400;
                font-size: 14px;
                color: #707070;
                border-right: none;
            }
        }
    }
}

.billNumber {
    width: 754px;
    height: 66px;
    background: url('../../../../assets/images/addFee.png') no-repeat;
    background-size: cover;
    padding-left: 25px;
    box-sizing: border-box;

    >div {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;

        :first-child,
        :nth-child(3) {
            font-weight: 400;
            font-size: 16px;
            color: #707070;
            margin-right: 16px;
        }

        :nth-child(2),
        :nth-child(4) {
            font-weight: 600;
            font-size: 20px;
            color: #333333;
            margin-right: 64px;
        }

    }

    margin-bottom: 31px;
}

.clientName {
    display: flex;
    justify-content: space-between;

    :global {
        .ant-select-selector {
            border: none !important;
        }

        .ant-form-item {
            width: 340px;
            // margin-right: 20px;
        }

        .ant-btn {
            background: #fff;
            color: #4071ff;
            border: 1px solid #d9d9d9;
        }


        .ant-form-item-label {
            display: flex;
            border: 1px solid #d9d9d9;
            padding-left: 10px;
            border-radius: 4px 0 0 4px;
            border-right: none;
            width: 81px;
        }

        .ant-form-item-no-colon {
            color: #707070;
        }

        .ant-form-item-control {
            border: 1px solid #d9d9d9;
            border-radius: 0 4px 4px 0;
        }

        .ant-select-selector {
            border: none;
        }

        .ant-picker-outlined {
            // border: none;
        }

        .ant-input-outlined {
            // border: none;
        }

    }

    .money {
        display: flex;
        width: 340px;
        height: 100%;

        >span {
            display: inline-block;
            align-items: center;
            width: 105px;
            line-height: 32px;
            border: 1px solid #d9d9d9;
            padding-left: 10px;
            border-radius: 4px 0 0 4px;
            border-right: none;

        }

        :global {
            .ant-input {
                border-radius: 0 6px 6px 0;
            }
        }
    }
}

.remark {
    display: flex;
    width: 752px;
    height: 100%;

    >span {
        display: inline-block;
        align-items: center;
        width: 81px;
        line-height: 32px;
        border: 1px solid #d9d9d9;
        padding-left: 10px;
        border-radius: 4px 0 0 4px;
        border-right: none;

    }

    :global {
        .ant-input {
            border-radius: 0 6px 6px 0;
        }
    }
}