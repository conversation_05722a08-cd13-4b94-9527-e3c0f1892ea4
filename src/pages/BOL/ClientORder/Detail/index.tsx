import { ProCard, ProFormSelect } from "@ant-design/pro-components"
import styles from './index.less'
import { Avatar, Button, Dropdown, Input, message, Modal, Popconfirm, Space } from "antd"
import { useEffect, useRef, useState } from "react"
import { useLocation } from "@umijs/max"
import { getCalinetSaveAPI, getResaleOrderDeleteFile, getResaleOrderDetail, getResaleOrderFile, uploadReResaleOrder, downloadFileResaleOrderAPI, getAddFeeAPI } from "@/services/booking"
import { formatTime } from "@/utils/format"
import _ from 'lodash'
import MrTable from "@/components/MrTable"
import dayjs from "dayjs"
import { saveAs } from 'file-saver';
import { EllipsisOutlined } from "@ant-design/icons"
import ExcelReader from "@/components/ExcelReader"
import { getReceivableBillListAPI } from "@/services/productAndPrice/api"
import { getFeeTypeAPI } from "@/services/financeApi"
import { AVATA_URL } from "@/shared/Enumeration"
const SellCabinetDetail = () => {
    const { state }: any = useLocation()
    const [detailData, setDetailData] = useState<any>({})
    const editFlagRef = useRef<any>(false)
    const actionRef = useRef<any>(null)
    const actionDealRef = useRef<any>(null)
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [billNameList, setBillNameList] = useState([])
    const [action, setAction] = useState<any>({})
    const [feeData, setFeeData] = useState<any>({})
    const fileTypeEnum: any = {
        // 1: '提货单DO',
        1: '参考文件',
        2: '提箱单S/O',
        3: '提单草稿',
        4: '电放件/SW件',
        5: '配仓清单',
    };
    const partyType: any = {
        'ShipTransport': '订舱',
        'Clearance': '清关',
        'AbroadOperate': '后端'
    }
    useEffect(() => {
        if (state?.sellCabinetId) {
            getOrderDetail(state?.sellCabinetId)
            actionRef?.current?.reload()
            actionDealRef?.current?.reload()
        }
    }, [state?.sellCabinetId])
    useEffect(() => {
        if (editFlagRef.current) {
            getCalinetSave(detailData)
        }
    }, [detailData?.containerNo,
    detailData?.sealNo,
    detailData?.containerSkin,
    detailData?.pieceNum,
    detailData?.weight,
    detailData?.volume,
    detailData?.receivePerson,
    detailData?.sendPerson,
    detailData?.notifyPerson,
    detailData?.shipNameVoyageCode
    ])
    const getOrderDetail = async (id: string) => {
        try {
            const { status, data } = await getResaleOrderDetail({
                id
            })
            if (status) {
                setDetailData({
                    ...data,
                    shipNameVoyageCode: data?.shipNameVoyageCode ? data?.shipNameVoyageCode : `${data?.shipName}/${data?.voyageCode}`
                })
            }
        } catch (error) {

        }
    }
    const getEditValue = (e: any, keys: any) => {
        editFlagRef.current = true
        setDetailData({
            ...detailData,
            [keys]: e?.target?.value
        })
    }
    // 修改
    const getCalinetSave = async (data: any) => {
        try {
            const { status } = await getCalinetSaveAPI({
                ...data
            })
            if (status) {
                // message.success('修改成功')
                // getOrderDetail(state?.sellCabinetId)
            }
        } catch (error) {

        }
    }
    const columns: any = [
        {
            title: '文件',
            dataIndex: 'name',
            selectable: 'none',
            className: 'nonSelectable',
            render: (text: any, record: any) => {
                return (
                    <>
                        <a
                            onClick={() => {
                                downloadFile(record?.url);
                            }}
                        >
                            {record?.name}
                        </a>
                    </>
                );
            },
        },
        {
            title: '文件类型',
            dataIndex: 'type',
            render: (text: any, record: any) => {
                return record?.typeDesc
            },
        },
        {
            title: '上传人',
            dataIndex: 'userName',
            render: (text: any, record: any) => {
                return (
                    <Space>
                        <Avatar
                            src={`${record?.avatar?.includes('http')
                                ? record?.avatar
                                : `https://static.kmfba.com/${record?.avatar}`
                                }`}
                        />
                        <div>{record?.userName}</div>
                    </Space>
                );
            },
        },
        {
            title: '上传时间',
            dataIndex: 'createTime',
            render: (text: any, record: any) => {
                return dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss');
            },
        },
        {
            title: '操作',
            width: 250,
            key: 'option',
            valueType: 'option',
            render: (text: any, record: any) => [
                <Popconfirm
                    key={1}
                    title="删除确认"
                    description="确认要删除次文件吗?"
                    onConfirm={() => {
                        deleteRelatedFile(record?.id);
                    }}
                    okText="删除"
                    cancelText="取消"
                    disabled={record?.auto === 1}
                >
                    <Button
                        type="link"
                        danger

                    >
                        删除
                    </Button>
                </Popconfirm>,
            ],
        },
    ];
    // 序号、流水号、账目、状态、本位币、原币、币种、汇率、已收款、待核销、创建人、创建时间
    const feeColumns: any = [
        {
            title: '流水号',
            dataIndex: 'id',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '账目',
            dataIndex: 'feeName',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '状态',
            dataIndex: 'writeOffStateDesc',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '本位币/元',
            dataIndex: 'shouldPayAmount', //汇率currentRate
            width: 120,
            hideInSearch: true,
            render: (text: any, record: any) => {
                return (
                    (
                        Number(record?.shouldPayAmount) * Number(record?.currentRate || 1)
                    ).toFixed(2) || record?.shouldPayAmount
                );
            },
        },
        {
            title: '原币',
            dataIndex: 'shouldPayAmount',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '币种',
            dataIndex: 'currencyTypeDesc',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '汇率',
            dataIndex: 'currentRate',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '已收款/元',
            dataIndex: 'paidAmountCny',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '待核销/元',
            dataIndex: 'unpaidAmountCny',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '创建人',
            dataIndex: 'creatorUserInfo',
            width: 200,
            hideInSearch: true,
            render: (text: any, record: any) => {
                return (
                    <Space>
                        <Avatar
                            src={
                                record?.creatorUserInfo?.avatar?.includes('http')
                                    ? record?.creatorUserInfo?.avatar
                                    : `${AVATA_URL}${record?.creatorUserInfo?.avatar}`
                            }
                        />
                        <span>{record?.creatorUserInfo?.name}</span>
                    </Space>
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 200,
            hideInSearch: true,
            valueType: 'dateTime',
        },
    ];
    /* 下载文件 */
    const downloadFile = async (url: string) => {
        try {
            const response: any = await downloadFileResaleOrderAPI({
                url,
            });
            const fileName = decodeURI(
                response.headers['content-disposition'].match(/filename=(.*)/)[1],
            );
            const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, newFileName);
        } catch (err) {
            console.log('下载失败: ', err);
        }
    };
    /* 删除相关文件 */
    const deleteRelatedFile = async (id: string) => {
        try {
            const { status } = await getResaleOrderDeleteFile({
                outId: state?.sellCabinetId,
                fileId: id,
            });
            if (status) {
                message.success('删除成功');
                actionRef.current?.reload();
            }
        } catch (e) {
            console.error('删除相关文件失败', e);
        }
    };
    /* 上传文件 */
    const uploadFile = async (type: any, files: any) => {
        const { status } = await uploadReResaleOrder({
            outId: state?.sellCabinetId,
            type: type,
            files: files,
        });
        if (status) {
            message.success('上传成功');
            actionRef?.current?.reload()
        }
    };
    useEffect(() => {
        getBillName()
    }, [])
    const getBillName = async (keyWords?: string) => {
        const { status, data } = await getFeeTypeAPI({
            keyword: keyWords,
            counterpartyType: 'ShipTransport'
        });
        if (status) {
            return setBillNameList(data?.map((item: any) => {
                return {
                    label: Object.values(item)[0],
                    value: Object.keys(item)[0],
                };
            }) || [])
        }
    }
    const getAddFee = async () => {
        if (!feeData?.billName) return message.warning('费用名称不能为空')
        if (!feeData?.exchangeRate) return message.warning('汇率不能为空')
        if (!feeData?.currencyTypeCode) return message.warning('币种不能为空')
        if (!feeData?.amount) return message.warning('金额不能为空')
        if (!feeData?.counterpartyType) return message.warning('业务类型不能为空')
        if (!feeData?.importType) return message.warning('方式不能为空')
        try {
            const { status } = await getAddFeeAPI({ ...feeData, clientId: detailData?.clientId, entityId: detailData?.id })
            if (status) {
                message.success('添加成功')
                setIsModalOpen(false)
                setTimeout(() => {
                    actionDealRef.current?.reload();
                }, 1000)

            }
        } catch (error) {

        }
    }

    return <>
        <ProCard
            style={{ marginBottom: '10px', height: '503px' }}
            gutter={24}
            title={<span style={{ fontWeight: '700' }}>卖柜订单</span>}

        >
            <div className={styles.cabinet_indent}>
                <div>
                    <div>  <span>提单号</span>
                        <div>{detailData?.blno}</div></div>
                    <div>  <span>订单号</span>
                        <div>{detailData?.id}</div></div>
                    <div>  <span>业务类型</span>
                        <div>{partyType[detailData?.counterpartyType]}</div></div>

                    {/* <div>  <span>航线</span>
                        <div>{detailData?.vesselName}</div></div> */}
                </div>
                {/* <div>
                    <div>  <span>船名/航次</span>
                        <div>{`${detailData?.shipName}/${detailData?.voyageCode}`}</div></div>
                    <div>  <span>规格</span>
                        <div>{detailData?.model}</div></div>
                    <div>  <span>出发港</span>
                        <div>{detailData?.departsCity}</div></div>
                    <div>  <span>到达港</span>
                        <div>{detailData?.arrivesCity}</div></div>
                    <div>  <span>开船时间</span>
                        <div>{formatTime(detailData?.estimatedDepartTime)}</div></div>
                </div> */}
                <div>
                    <div>  <span>客户</span>
                        <div>{detailData?.clientName}</div></div>
                    <div>  <span>订单时间</span>
                        <div>{detailData?.createTime && formatTime(detailData?.createTime)}</div></div>
                </div>
            </div>
            <div className={styles.inquire}>
                <div className={styles.inquire_left}>
                    <div>
                        <span>箱号</span>
                        <Input placeholder="请输入" value={detailData?.containerNo} onChange={(e: any) => getEditValue(e, 'containerNo')} />
                    </div>
                    <div>
                        <span>封号</span>
                        <Input placeholder="请输入" value={detailData?.sealNo} onChange={(e: any) => getEditValue(e, 'sealNo')} />
                    </div>
                    <div>
                        <span>箱皮</span>
                        <Input placeholder="请输入" value={detailData?.containerSkin} onChange={(e: any) => getEditValue(e, 'containerSkin')} />
                    </div>
                    <div>
                        <span>航线</span>
                        <Input placeholder="请输入" value={detailData?.vesselName} onChange={(e: any) => getEditValue(e, 'vesselName')} />
                    </div>
                    <div>
                        <span>船名/航次</span>
                        <Input placeholder="请输入" value={`${detailData?.shipNameVoyageCode}`} onChange={(e: any) => getEditValue(e, 'shipNameVoyageCode')} />
                    </div>
                </div>
                <div className={styles.inquire_center}>
                    <div>
                        <span>件数</span>
                        <Input placeholder="请输入" type="number" value={detailData?.pieceNum} onChange={(e: any) => getEditValue(e, 'pieceNum')} />
                    </div>
                    <div>
                        <span>实重(kg)</span>
                        <Input placeholder="请输入" type="number" value={detailData?.weight} onChange={(e: any) => getEditValue(e, 'weight')} />
                    </div>
                    <div>
                        <span>体积(m³)</span>
                        <Input placeholder="请输入" type="number" value={detailData?.volume} onChange={(e: any) => getEditValue(e, 'volume')} />
                    </div>
                    <div>
                        <span>规格</span>
                        <Input placeholder="请输入" type="number" value={detailData?.model} onChange={(e: any) => getEditValue(e, 'model')} />
                    </div>
                    <div>
                        <span>出发港</span>
                        <Input placeholder="请输入" value={detailData?.departsCity} onChange={(e: any) => getEditValue(e, 'departsCity')} />
                    </div>
                </div>
                <div className={styles.inquire_right}>
                    <div>
                        <span>接收人</span>
                        <Input placeholder="请输入" value={detailData?.receivePerson} onChange={(e: any) => getEditValue(e, 'receivePerson')} />
                    </div>
                    <div>
                        <span>发送人</span>
                        <Input placeholder="请输入" value={detailData?.sendPerson} onChange={(e: any) => getEditValue(e, 'sendPerson')} />
                    </div>
                    <div>
                        <span>通知人</span>
                        <Input placeholder="请输入" value={detailData?.notifyPerson} onChange={(e: any) => getEditValue(e, 'notifyPerson')} />
                    </div>
                    <div>
                        <span>到达港</span>
                        <Input placeholder="请输入" value={detailData?.arrivesCity} onChange={(e: any) => getEditValue(e, 'arrivesCity')} />
                    </div>
                    <div>
                        <span>开船时间</span>
                        <Input placeholder="请输入" value={formatTime(detailData?.estimatedDepartTime)} onChange={(e: any) => getEditValue(e, 'estimatedDepartTime')} />
                    </div>
                </div>

            </div>
        </ProCard>
        <MrTable
            columns={columns}
            options={false}
            // detailsTable
            // style={{ height: '400px' }}
            keyID={'Booking/cabin/sellCabinet/detail'}
            request={async (params: any, action: any) => {
                actionRef.current = action
                // shippingPlanAPI
                // getCalinetIndent
                const res = await getResaleOrderFile({
                    ...params,
                    outId: state?.sellCabinetId,
                    // outId: 'tc1d9a3d2404045',
                    // type: 30
                })

                return {
                    data: res.data.list || [],
                    success: res.status,
                    total: res.data?.total,
                };
            }}
            // filters={}
            toolBarRender={<Dropdown
                key="menu"
                // disabled={!isAccessibleFlag}
                menu={{
                    items: [
                        {
                            label: (
                                <ExcelReader
                                    onChange={(e: any, file: any) => {
                                        uploadFile(1, file);
                                    }}
                                >
                                    提货单DO
                                </ExcelReader>
                            ),
                            key: '1',
                        },
                        {
                            label: (
                                <ExcelReader
                                    onChange={(e: any, file: any) => {
                                        uploadFile(2, file);
                                    }}
                                >
                                    提箱单S/O
                                </ExcelReader>
                            ),
                            key: '2',
                        },
                        {
                            label: (
                                <ExcelReader
                                    onChange={(e: any, file: any) => {
                                        uploadFile(3, file);
                                    }}
                                >
                                    提单草稿
                                </ExcelReader>
                            ),
                            key: '3',
                        },
                        {
                            label: (
                                <ExcelReader
                                    onChange={(e: any, file: any) => {
                                        uploadFile(33, file);
                                    }}
                                >
                                    电放件/SW件
                                </ExcelReader>
                            ),
                            key: '4',
                        },
                        {
                            label: (
                                <ExcelReader
                                    onChange={(e: any, file: any) => {
                                        uploadFile(5, file);
                                    }}
                                >
                                    配舱清单
                                </ExcelReader>
                            ),
                            key: '5',
                        },
                        {
                            label: (
                                <ExcelReader
                                    onChange={(e: any, file: any) => {
                                        uploadFile(36, file);
                                    }}
                                >
                                    参考文件
                                </ExcelReader>
                            ),
                            key: '6',
                        },
                    ],
                }}
            >
                <Button >
                    上传
                    <EllipsisOutlined />
                </Button>
            </Dropdown>} />
        <MrTable
            columns={feeColumns}
            options={false}
            // detailsTable
            // style={{ height: '400px' }}
            keyID={'Booking/cabin/sellCabinet/detail/fee'}
            request={async (params: any, action: any) => {
                actionDealRef.current = action
                // shippingPlanAPI
                // getCalinetIndent
                const res = await getReceivableBillListAPI({
                    ...params,
                    // outId: state?.sellCabinetId,
                    // outId: 'tc1d9a3d2404045',

                    // type: 30
                    "condition": {
                        "entityId": {
                            "desc": "关联ID",
                            "type": "entityId",
                            "value": state?.sellCabinetId,
                            "userSoleId": "entityId"
                        },
                        "entityType": {
                            "desc": "关联类型",
                            "type": "entityType",
                            "value": "TransportSpace",
                            "userSoleId": "entityType"
                        }
                    }
                })

                return {
                    data: res.data.list || [],
                    success: res.status,
                    total: res.data?.total,
                };
            }}
            // filters={}
            toolBarRender={<Button onClick={() => setIsModalOpen(true)}>
                添加费用
                {/* <EllipsisOutlined /> */}
            </Button>} />
        {/* <Modal title='添加费用' open={isModalOpen}
            destroyOnClose
            onCancel={() => setIsModalOpen(false)}
            width={800}
            footer={[
                <div key='btn' style={{ display: 'flex', justifyContent: 'center', }}>
                    <Button key="back" onClick={() => setIsModalOpen(false)} style={{ marginRight: '12px' }}>取消</Button>
                    <Button key="submit" type="primary" onClick={getAddFee} >确认</Button>
                </div>
            ]}>
            <div className={styles.billNumber}>
                <div><span>提单号</span><span>{detailData?.blno}</span><span>客户</span><span>{`${detailData?.clientName}`}</span></div>
            </div>
            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        name="billName"
                        label="费用名称"
                        showSearch
                        colon={false}
                        options={billNameList}
                        onChange={(e) => {
                            setFeeData({ ...feeData, billName: e })
                            // setBlno({ ...blno, clientId: e })
                        }}
                    // request={async ({ keyWords }) => {
                    //     const { status, data } = await getClientListAPI({
                    //         type: 0,
                    //         len: 100,
                    //         keyword: keyWords,
                    //     });
                    //     if (status) {
                    //         return data.list.map((item: any) => {
                    //             return {
                    //                 label: `【${item.name}】 - ${item.code}`,
                    //                 value: item.id,
                    //             };
                    //         });
                    //     }
                    //     return [];
                    // }}
                    />
                    <div className={styles.money}>
                        <span>汇率</span>
                        <Input
                            placeholder='请输入'
                            type='number'
                            onChange={(e) => {
                                setFeeData({ ...feeData, exchangeRate: e?.target?.value })

                                // setBlno({ ...blno, clientId: e })
                            }}
                        />
                    </div>
                </div>
            </div>
            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        name="clientName"
                        label="币种"
                        colon={false}
                        onChange={(e) => {
                            setFeeData({ ...feeData, currencyTypeCode: e })

                        }}
                        options={[
                            {
                                label: '人民币',
                                value: 10,
                            },
                            {
                                label: '美元',
                                value: 20,
                            },
                            {
                                label: '加币',
                                value: 30,
                            },
                            {
                                label: '欧元',
                                value: 40,
                            },
                            {
                                label: '日元',
                                value: 50,
                            },
                        ]}
                    />
                    <div className={styles.money}>
                        <span>金额</span>
                        <Input
                            placeholder='请输入'
                            type='number'
                            onChange={(e) => {
                                setFeeData({ ...feeData, amount: e?.target?.value })
                                // setBlno({ ...blno, clientId: e })
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className={styles.remark}>
                <span>备注</span>
                <Input
                    placeholder='请输入'
                    onChange={(e) => {
                        setFeeData({ ...feeData, remark: e?.target?.value })
                    }}
                />
            </div>
        </Modal> */}
        <Modal title='添加费用' open={isModalOpen}
            destroyOnClose
            onCancel={() => setIsModalOpen(false)}
            width={800}
            footer={[
                <div key='btn' style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button key="back" onClick={() => setIsModalOpen(false)} style={{ marginRight: '12px' }}>取消</Button>
                    <Button key="submit" type="primary" onClick={getAddFee} >确认</Button>
                </div>
            ]}>
            <div className={styles.billNumber}>
                <div><span>提单号</span><span>{detailData?.blno}</span><span>客户</span><span>{`${detailData?.clientName}`}</span></div>
            </div>
            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        label="业务类型"
                        showSearch
                        colon={false}
                        // mode="multiple"
                        options={[
                            { label: '订舱', value: 'ShipTransport' },
                            { label: '清关', value: 'Clearance' },
                            { label: '后端', value: 'AbroadOperate' },
                        ]}
                        onChange={(e: any) => {
                            setFeeData({ ...feeData, counterpartyType: e, billName: '' })
                            getBillName(e)
                        }}

                    />
                    <div className={styles.money}>
                        <ProFormSelect
                            label="方式"
                            showSearch
                            colon={false}
                            options={[
                                { label: '追加', value: '1' },
                                { label: '覆盖', value: '2' },
                                { label: '总值', value: '3' },
                            ]}
                            onChange={(e) => {
                                setFeeData({ ...feeData, importType: e })
                            }}

                        />
                    </div>
                </div>
            </div>
            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        label="费用名称"
                        showSearch
                        colon={false}
                        options={billNameList}
                        // valueEnum={feeData?.billName}
                        onChange={(e) => {
                            setFeeData({ ...feeData, billName: e })
                            // setBlno({ ...blno, clientId: e })
                        }}
                    // request={async ({ keyWords }) => {
                    //     const { status, data } = await getClientListAPI({
                    //         type: 0,
                    //         len: 100,
                    //         keyword: keyWords,
                    //     });
                    //     if (status) {
                    //         return data.list.map((item: any) => {
                    //             return {
                    //                 label: `【${item.name}】 - ${item.code}`,
                    //                 value: item.id,
                    //             };
                    //         });
                    //     }
                    //     return [];
                    // }}
                    />
                    <div className={styles.money}>
                        <span>汇率</span>
                        <Input
                            placeholder='请输入'
                            type='number'
                            onChange={(e) => {
                                setFeeData({ ...feeData, exchangeRate: e?.target?.value })

                                // setBlno({ ...blno, clientId: e })
                            }}
                        />
                    </div>
                </div>
            </div>

            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        name="clientName"
                        label="币种"
                        colon={false}
                        onChange={(e) => {
                            setFeeData({ ...feeData, currencyTypeCode: e })

                        }}
                        options={[
                            {
                                label: '人民币',
                                value: 10,
                            },
                            {
                                label: '美元',
                                value: 20,
                            },
                            {
                                label: '加币',
                                value: 30,
                            },
                            {
                                label: '欧元',
                                value: 40,
                            },
                            {
                                label: '日元',
                                value: 50,
                            },
                        ]}
                    />
                    <div className={styles.money}>
                        <span>金额</span>
                        <Input
                            placeholder='请输入'
                            type='number'
                            onChange={(e) => {
                                setFeeData({ ...feeData, amount: e?.target?.value })
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className={styles.remark}>
                <span>备注</span>
                <Input
                    placeholder='请输入'
                    onChange={(e) => {
                        setFeeData({ ...feeData, remark: e?.target?.value })
                    }}
                />
            </div>
        </Modal>
    </>
}
export default SellCabinetDetail