.tableWarp {
  :global {
    .ant-input-affix-wrapper {
      border-color: #fafafa;
      background: #fafafa;
    }

    .ant-input-affix-wrapper:hover {
      border-color: #fafafa;
      background: #fafafa;
    }

    .ant-input {
      background: #fafafa;
      border: 1px solid #fafafa;
    }

    .ant-select-selector {
      background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-picker-range {
      background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }
  }
}

.modal_overseas {
  :global {
    .ant-modal-title {
      position: relative;
      z-index: 99;
    }
  }

  .modal_title {
    position: absolute;
    left: 0;
    top: 0;
    width: 1200px;
    height: 60px;
    background: url('../../../assets/images/hwcyb.png') no-repeat;
    background-position: center;
    border-radius: 10px 10px 0 0;
  }
}

.clientele,
.remark {
  width: 280px;
  height: 40px !important;
  border-radius: 4px;
  border: 1px solid #E5E5E5;
  display: flex;
  float: left;
  margin-bottom: 16px;
  margin-right: 16px;

  .organization {
    display: flex;
    // justify-content: center;
    justify-content: space-between;
    align-items: center;
    width: 100px !important;
    height: 38px;
    border-radius: 4px 0px 0px 4px;
    border-right: 1px solid #E5E5E5;
    font-weight: 400;
    font-size: 14px;
    color: #707070;
    box-sizing: border-box;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :global {
    .ant-form-item {
      width: calc(100% - 100px) !important;
    }

    .ant-pro-table {
      height: calc(100% - 32px);
    }

    .ant-select {
      width: calc(100% - 100px);
      border: none;

      .ant-select-selector {
        width: 100%;
        height: 36px !important;
        border: none !important;

        .ant-select-selection-overflow {
          flex-wrap: nowrap;
          white-space: nowrap;
          /* 防止内容换行 */
          overflow: hidden;
          /* 隐藏超出盒子的内容 */
          text-overflow: ellipsis;
          /* 超出部分显示省略号 */
        }
      }
    }

    .ant-cascader {
      width: 100% !important;
    }

    .ant-picker,
    .ant-input {
      // width: 100%;
      width: calc(100% - 100px) !important;
      height: 100%;
      border: none !important;
    }

  }


}

.remark {
  width: 800px !important;
}