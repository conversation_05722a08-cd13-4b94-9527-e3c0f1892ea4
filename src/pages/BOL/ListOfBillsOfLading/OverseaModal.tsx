import { Button, Input, Modal, Select } from "antd"
import { useState } from "react";
import styles from './index.less'
const OverseaModal = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const handleOk = () => {
        setIsModalOpen(false)
    }
    const handleCancel = () => {
        setIsModalOpen(false)
    }
    return <>
        <Button onClick={() => setIsModalOpen(true)}
        >海外仓预报</Button>
        <Modal okText='确认预报' className={styles.modal_overseas} width={1200} title="海外仓预报" open={isModalOpen} onOk={handleOk} onCancel={handleCancel}
        >
            <div className={styles.modal_title}>

            </div>
            <div style={{ display: 'flex', marginTop: '30px' }}>
                <div
                    className={styles.clientele}
                //   style={{
                //     width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
                //   }}
                >
                    <div className={styles.organization}>渠道名称</div>
                    {/* {cutRender(item)} */}
                    <Input
                        placeholder="请输入关键字"
                        type="text"
                        // defaultValue={item?.value}
                        onChange={(e: any) => {
                            //   getTextValue(e, item);
                        }}
                    />
                </div>
                <div
                    className={styles.clientele}
                //   style={{
                //     width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
                //   }}
                >
                    <div className={styles.organization}>服务商</div>
                    {/* {cutRender(item)} */}
                    <Input
                        placeholder="请输入关键字"
                        type="text"
                        // defaultValue={item?.value}
                        onChange={(e: any) => {
                            //   getTextValue(e, item);
                        }}
                    />
                </div>
                <div
                    className={styles.clientele}
                //   style={{
                //     width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
                //   }}
                >
                    <div className={styles.organization}>仓库名称</div>
                    {/* {cutRender(item)} */}
                    <Input
                        placeholder="请输入关键字"
                        type="text"
                        // defaultValue={item?.value}
                        onChange={(e: any) => {
                            //   getTextValue(e, item);
                        }}
                    />
                </div>
                <Button type="primary">查询</Button>
            </div>
            <div
                className={styles.clientele}
            //   style={{
            //     width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
            //   }}
            >
                <div className={styles.organization} style={{ width: '136px' }}>
                    交货方式
                </div>
                <Select
                    showSearch
                    // mode={item?.multi ? 'multiple' : (undefined as any)}
                    filterOption={(input, option: any) => true}
                    options={[]}
                    placeholder="请选择"
                    // defaultValue={item?.defaultValue}
                    // value={item?.value}
                    allowClear
                    onChange={(e) => {
                        //   editParameter(e, item);
                    }}
                />
            </div>
            <div
                className={styles.remark}
            //   style={{
            //     width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
            //   }}
            >
                <div className={styles.organization}>整单备注</div>
                {/* {cutRender(item)} */}
                <Input
                    placeholder="请输入关键字"
                    type="text"
                    // defaultValue={item?.value}
                    onChange={(e: any) => {
                        //   getTextValue(e, item);
                    }}
                />
            </div>
        </Modal>
    </>
}
export default OverseaModal