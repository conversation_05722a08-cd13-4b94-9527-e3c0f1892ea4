import KeywordsSelect from '@/components/KeywordsSelect';
import LogDashboard from '@/components/LogDashboard';
import { SwapRightOutlined, createFromIconfontCN } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Space, Tag } from 'antd';
import dayjs from 'dayjs';
const MyIcon = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3877009_cm2626uyk4s.js',
});

const spaceStateType: any = {
  1: {
    text: '空闲',
    color: '#dfe4ea',
  },
  10: {
    text: '待出库',
    color: '#ff9f43',
  },
  20: {
    text: '已提柜',
    color: '#ff6b6b',
  },
  30: {
    text: '已出库',
    color: '#48dbfb',
  },
  40: {
    text: '已进港',
    color: '#1dd1a1',
  },
  50: {
    text: '已开船',
    color: '#3742fa',
  },
  60: {
    text: '到达目的港',
    color: '#2ed573',
  },
  65: {
    text: ' 已下船',
    color: '#00a8ff',
  },
  70: {
    text: '清关查验',
    color: '#ff6348',
  },
  80: {
    text: '海外提柜',
    color: '#8e44ad',
  },
  90: {
    text: '已还柜',
    color: '#00b894',
  },
  '-1': {
    text: '已转卖',
    color: '#ff4757',
  },
  '-2': {
    text: '已退订',
    color: '#ff4757',
  },
  0: {
    text: '未知',
    color: '#95a5a6',
  },
};
// const columns1: any = [
//   {
//     title: '查询',
//     dataIndex: 'keyword',
//     hideInTable: true,
//     fieldProps: {
//       placeholder: '请输入要查询的内容',
//     },
//   },
//   {
//     title: '关键词',
//     dataIndex: 'state',
//     hideInTable: true,
//     renderFormItem: (_1: any, a2: any, a3: any) => {
//       const getKeywords = (value: any) => {
//         a3.setFieldsValue({
//           state: value
//         });
//       };
//       return (
//         <KeywordsSelect
//           getKeywords={getKeywords}
//           options={[
//             {
//               label: '提单状态',
//               options: [
//                 {
//                   label: '已出库',
//                   value: '30',
//                 },
//                 {
//                   label: '已进港',
//                   value: '40',
//                 },
//                 {
//                   label: '已开船',
//                   value: '50',
//                 },
//                 {
//                   label: '已还柜',
//                   value: '90',
//                 },
//                 {
//                   label: '海外提柜',
//                   value: '80',
//                 },
//                 {
//                   label: '到达目的港',
//                   value: '60',
//                 },
//                 {
//                   label: '清关查验',
//                   value: '70',
//                 },
//               ],
//             },
//             {
//               label: '头程运输方式',
//               options: [
//                 {
//                   label: '空运',
//                   value: '1',
//                 },
//                 {
//                   label: '海运',
//                   value: '2',
//                 },
//               ],
//             },
//             // {
//             //   label: '常用出发港/机场',
//             //   options: [
//             //     {
//             //       label: '宁波',
//             //       value: '宁波',
//             //     },
//             //     {
//             //       label: '上海',
//             //       value: '上海',
//             //     },
//             //     {
//             //       label: '厦门',
//             //       value: '厦门',
//             //     },
//             //     {
//             //       label: '深圳盐田',
//             //       value: '深圳盐田',
//             //     },
//             //     {
//             //       label: '青岛',
//             //       value: '青岛',
//             //     },
//             //     {
//             //       label: '杭州',
//             //       value: '杭州',
//             //     },
//             //   ],
//             // },
//             // {
//             //   label: '常用目的港/机场',
//             //   options: [
//             //     {
//             //       label: '洛杉矶',
//             //       value: '洛杉矶',
//             //     },
//             //     {
//             //       label: '芝加哥',
//             //       value: '芝加哥',
//             //     },
//             //     {
//             //       label: '纽约',
//             //       value: '纽约',
//             //     },
//             //     {
//             //       label: '奥克兰',
//             //       value: '奥克兰',
//             //     },
//             //   ],
//             // },
//           ]}
//         />
//       );
//     },
//   },
//   {
//     title: '基础信息',
//     hideInSearch: true,
//     children: [
//       {
//         title: '提单号',
//         dataIndex: 'blno',
//         width: 200,
//         ellipsis: true,
//         render: (text: any, record: any) => {
//           return record?.info?.blno;
//         },
//       },
//       {
//         title: '航程',
//         dataIndex: 'departsCity',
//         width: 200,
//         render: (text: any, record: any) => {
//           return (
//             <>
//               <Space>
//                 <div>
//                   {record?.info?.mode === 1 ? (
//                     <MyIcon type="icon-kongyun" />
//                   ) : (
//                     <MyIcon type="icon-haihang" />
//                   )}
//                 </div>
//                 <div>{record?.info?.departsCity}</div>
//                 <SwapRightOutlined />
//                 <div>{record?.info?.deliveryCity}</div>
//               </Space>
//             </>
//           );
//         },
//       },
//       {
//         title: '提单状态',
//         dataIndex: 'status',
//         width: 200,
//         render: (text: any, record: any) => {
//           return (
//             <>
//               <Tag color={spaceStateType[record.state]?.color}>
//                 {spaceStateType[record?.state]?.text}
//               </Tag>
//             </>
//           );
//         },
//       },
//       {
//         title: '航线',
//         dataIndex: 'vesselName',
//         width: 200,
//         render: (text: any, record: any) => {
//           return (
//             <Space>
//               <div>{record?.info?.vesselName}</div>
//               <div>{record?.info?.vesselCode}</div>
//             </Space>
//           );
//         },
//       },
//       {
//         title: '航次/航班',
//         dataIndex: 'shipName',
//         width: 220,
//         render: (text: any, record: any) => {
//           return (
//             <>
//               {
//                 record?.info?.mode === 2 ? <span>
//                   {record?.info?.shipName}/{record?.info?.voyageCode}
//                 </span> : record?.info?.airCode
//               }
//             </>
//           );
//         },
//       },
//       {
//         title: '预计启航时间',
//         dataIndex: 'estimatedDepartTime',
//         width: 200,
//         render: (text: any, record: any) => {
//           return dayjs(record?.info?.estimatedDepartTime).format(
//             'YYYY-MM-DD HH:mm:ss',
//           );
//         },
//       },
//     ],
//   },
//   {
//     title: '配仓情况',
//     hideInSearch: true,
//     children: [
//       {
//         title: '配仓率/m³',
//         dataIndex: 'volume/info.volume',
//         width: 200,
//         render: (text: any, record: any) => {
//           return (
//             <span>
//               {record?.volume}/{record?.info?.volume}
//             </span>
//           );
//         },
//       },
//       {
//         title: '装柜地',
//         dataIndex: 'warehouseList',
//         width: 200,
//         render: (text: any, record: any) => {
//           return (
//             <>
//               {record?.warehouseList?.map((item: any, index: any) => {
//                 return <div key={index}>{item?.name}</div>;
//               })}
//             </>
//           );
//         },
//       },
//       {
//         title: '型号',
//         dataIndex: 'info.model',
//         width: 200,
//         render: (text: any, record: any) => {
//           return record?.info?.model;
//         },
//       },
//       {
//         title: '箱号(海运)',
//         dataIndex: 'info.containerNo',
//         width: 200,
//         render: (text: any, record: any) => {
//           return record?.info?.containerNo;
//         },
//       },
//       {
//         title: '配仓票数',
//         dataIndex: 'waybillNum',
//         width: 200,
//         render: (text: any, record: any) => {
//           return <>{record?.waybillNum}</>;
//         },
//       },
//       {
//         title: '配仓件数',
//         dataIndex: 'pieceNum',
//         width: 200,
//       },
//       {
//         title: '出货实重/KG',
//         dataIndex: 'weight',
//         width: 200,
//       },
//       {
//         title: '出货体积重/KG',
//         dataIndex: 'volumeWeight',
//         width: 200,
//       },
//       {
//         title: '出货计费重/KG',
//         dataIndex: 'chargeWeight',
//         width: 200,
//       },
//     ],
//   },
//   {
//     title: '时间',
//     hideInSearch: true,
//     children: [
//       {
//         title: '最晚预报',
//         dataIndex: 'forecastLimitTime',
//         width: 200,
//         render: (text: any, record: any) => {
//           return dayjs(record?.info?.forecastLimitTime).format(
//             'YYYY-MM-DD HH:mm:ss',
//           );
//         },
//       },
//       {
//         title: '最晚装柜',
//         dataIndex: 'cargoLimitTime',
//         width: 200,
//         render: (text: any, record: any) => {
//           return dayjs(record?.info?.cargoLimitTime).format(
//             'YYYY-MM-DD HH:mm:ss',
//           );
//         },
//       },
//       {
//         title: '预计到达时间',
//         dataIndex: 'estimatedArrivalTime',
//         width: 200,
//         render: (text: any, record: any) => {
//           return dayjs(record?.info?.estimatedArrivalTime).format(
//             'YYYY-MM-DD HH:mm:ss',
//           );
//         },
//       },
//       {
//         title: '预计提柜时间',
//         dataIndex: 'estimatedDeliveryTime',
//         width: 200,
//         render: (text: any, record: any) => {
//           return dayjs(record?.info?.estimatedDeliveryTime).format(
//             'YYYY-MM-DD HH:mm:ss',
//           );
//         },
//       },
//       {
//         title: '备注',
//         dataIndex: '20',
//         width: 200,
//       },
//     ],
//   },
//   {
//     title: '操作',
//     width: 160,
//     key: 'option',
//     valueType: 'option',
//     fixed: 'right',
//     render: (text: any, record: any) => [
//       <a
//         key="link"
//         onClick={() => {
//           history.push(
//             `/BOL/listOfBillsOfLading/billOfLadingDetails?spaceId=${record?.spaceId}`,
//           );
//         }}
//       >
//         详情
//       </a>,
//       <LogDashboard key="2" extraId_1={record?.id} btnType="link" btnText="日志" />
//     ],
//   },
// ];
const columns1: any = [
  {
    title: '查询',
    dataIndex: 'keyword',
    hideInTable: true,
    fieldProps: {
      placeholder: '请输入要查询的内容',
    },
  },
  {
    title: '关键词',
    dataIndex: 'state',
    hideInTable: true,
    renderFormItem: (_1: any, a2: any, a3: any) => {
      const getKeywords = (value: any) => {
        a3.setFieldsValue({
          state: value
        });
      };
      return (
        <KeywordsSelect
          getKeywords={getKeywords}
          options={[
            {
              label: '提单状态',
              options: [
                {
                  label: '已出库',
                  value: '30',
                },
                {
                  label: '已进港',
                  value: '40',
                },
                {
                  label: '已开船',
                  value: '50',
                },
                {
                  label: '已还柜',
                  value: '90',
                },
                {
                  label: '海外提柜',
                  value: '80',
                },
                {
                  label: '到达目的港',
                  value: '60',
                },
                {
                  label: '清关查验',
                  value: '70',
                },
              ],
            },
            {
              label: '头程运输方式',
              options: [
                {
                  label: '空运',
                  value: '1',
                },
                {
                  label: '海运',
                  value: '2',
                },
              ],
            },
            // {
            //   label: '常用出发港/机场',
            //   options: [
            //     {
            //       label: '宁波',
            //       value: '宁波',
            //     },
            //     {
            //       label: '上海',
            //       value: '上海',
            //     },
            //     {
            //       label: '厦门',
            //       value: '厦门',
            //     },
            //     {
            //       label: '深圳盐田',
            //       value: '深圳盐田',
            //     },
            //     {
            //       label: '青岛',
            //       value: '青岛',
            //     },
            //     {
            //       label: '杭州',
            //       value: '杭州',
            //     },
            //   ],
            // },
            // {
            //   label: '常用目的港/机场',
            //   options: [
            //     {
            //       label: '洛杉矶',
            //       value: '洛杉矶',
            //     },
            //     {
            //       label: '芝加哥',
            //       value: '芝加哥',
            //     },
            //     {
            //       label: '纽约',
            //       value: '纽约',
            //     },
            //     {
            //       label: '奥克兰',
            //       value: '奥克兰',
            //     },
            //   ],
            // },
          ]}
        />
      );
    },
  },
  // {
  //   title: '基础信息',
  //   hideInSearch: true,
  //   children: [
  {
    title: '提单号',
    dataIndex: 'blno',
    width: 200,
    ellipsis: true,
    render: (text: any, record: any) => {
      return record?.info?.blno;
    },
  },
  {
    title: '航程',
    dataIndex: 'departsCity',
    width: 200,
    render: (text: any, record: any) => {
      return (
        <>
          <Space>
            <div>
              {record?.info?.mode === 1 ? (
                <MyIcon type="icon-kongyun" />
              ) : (
                <MyIcon type="icon-haihang" />
              )}
            </div>
            <div>{record?.info?.departsCity}</div>
            <SwapRightOutlined />
            <div>{record?.info?.deliveryCity}</div>
          </Space>
        </>
      );
    },
  },
  {
    title: '提单状态',
    dataIndex: 'status',
    width: 200,
    render: (text: any, record: any) => {
      return (
        <>
          <Tag color={spaceStateType[record.state]?.color}>
            {spaceStateType[record?.state]?.text}
          </Tag>
        </>
      );
    },
  },
  {
    title: '航线',
    dataIndex: 'vesselName',
    width: 200,
    render: (text: any, record: any) => {
      return (
        <Space>
          <div>{record?.info?.vesselName}</div>
          <div>{record?.info?.vesselCode}</div>
        </Space>
      );
    },
  },
  {
    title: '航次/航班',
    dataIndex: 'shipName',
    width: 220,
    render: (text: any, record: any) => {
      return (
        <>
          {
            record?.info?.mode === 2 ? <span>
              {record?.info?.shipName}/{record?.info?.voyageCode}
            </span> : record?.info?.airCode
          }
        </>
      );
    },
  },
  {
    title: '预计启航时间',
    dataIndex: 'estimatedDepartTime',
    width: 200,
    render: (text: any, record: any) => {
      return dayjs(record?.info?.estimatedDepartTime).format(
        'YYYY-MM-DD HH:mm:ss',
      );
    },
  },
  //   ],
  // },
  // {
  //   title: '配仓情况',
  //   hideInSearch: true,
  //   children: [
  {
    title: '配仓率/m³',
    dataIndex: 'volume/info.volume',
    width: 200,
    render: (text: any, record: any) => {
      return (
        <span>
          {record?.volume}/{record?.info?.volume}
        </span>
      );
    },
  },
  {
    title: '装柜地',
    dataIndex: 'warehouseList',
    width: 200,
    render: (text: any, record: any) => {
      return (
        <>
          {record?.warehouseList?.map((item: any, index: any) => {
            return <div key={index}>{item?.name}</div>;
          })}
        </>
      );
    },
  },
  {
    title: '型号',
    dataIndex: 'info.model',
    width: 200,
    render: (text: any, record: any) => {
      return record?.info?.model;
    },
  },
  {
    title: '箱号(海运)',
    dataIndex: 'info.containerNo',
    width: 200,
    render: (text: any, record: any) => {
      return record?.info?.containerNo;
    },
  },
  {
    title: '配仓票数',
    dataIndex: 'waybillNum',
    width: 200,
    render: (text: any, record: any) => {
      return <>{record?.waybillNum}</>;
    },
  },
  {
    title: '配仓件数',
    dataIndex: 'pieceNum',
    width: 200,
  },
  {
    title: '出货实重/KG',
    dataIndex: 'weight',
    width: 200,
  },
  {
    title: '出货体积重/KG',
    dataIndex: 'volumeWeight',
    width: 200,
  },
  {
    title: '出货计费重/KG',
    dataIndex: 'chargeWeight',
    width: 200,
  },
  //   ],
  // },
  // {
  //   title: '时间',
  //   hideInSearch: true,
  //   children: [
  {
    title: '最晚预报',
    dataIndex: 'forecastLimitTime',
    width: 200,
    render: (text: any, record: any) => {
      return dayjs(record?.info?.forecastLimitTime).format(
        'YYYY-MM-DD HH:mm:ss',
      );
    },
  },
  {
    title: '最晚装柜',
    dataIndex: 'cargoLimitTime',
    width: 200,
    render: (text: any, record: any) => {
      return dayjs(record?.info?.cargoLimitTime).format(
        'YYYY-MM-DD HH:mm:ss',
      );
    },
  },
  {
    title: '预计到达时间',
    dataIndex: 'estimatedArrivalTime',
    width: 200,
    render: (text: any, record: any) => {
      return dayjs(record?.info?.estimatedArrivalTime).format(
        'YYYY-MM-DD HH:mm:ss',
      );
    },
  },
  {
    title: '预计提柜时间',
    dataIndex: 'estimatedDeliveryTime',
    width: 200,
    render: (text: any, record: any) => {
      return dayjs(record?.info?.estimatedDeliveryTime).format(
        'YYYY-MM-DD HH:mm:ss',
      );
    },
  },
  {
    title: '备注',
    dataIndex: '20',
    width: 200,
  },
  //   ],
  // },
  {
    title: '操作',
    width: 160,
    key: 'option',
    valueType: 'option',
    fixed: 'right',
    render: (text: any, record: any) => [
      <a
        key="link"
        onClick={() => {
          history.push(
            `/BOL/listOfBillsOfLading/billOfLadingDetails?spaceId=${record?.spaceId}`,
          );
        }}
      >
        详情
      </a>,
      <LogDashboard key="2" extraId_1={record?.id} btnType="link" btnText="日志" />
    ],
  },
];

export { columns1 };
