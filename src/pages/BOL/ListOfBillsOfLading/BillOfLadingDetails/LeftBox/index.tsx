import styles from '../index.less';
import classNames from 'classnames';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useScroll } from 'ahooks';
import { KeepAliveTabContext } from '@/layouts/context';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
interface LeftBoxProps {
  leftBoxW: boolean; //左侧盒子是否显示
}
const LeftBox = ({}: LeftBoxProps) => {
  const { MyScroll }: any = useContext(KeepAliveTabContext);
  // console.log('MyScroll: ', MyScroll);
  const [toList] = usePermissionFiltering()
  const [itemList, setItemList] = useState(toList([
    {
      lable: '基本信息',
      active: true,
      idM: 'ACE01',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|a/.test(data.parts.value)
      },
    },
    {
      lable: '服务商',
      active: true,
      idM: 'ACE00fws',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|b/.test(data.parts.value)
      },
    },
    {
      lable: '分票统计数据',
      active: false,
      idM: 'ACE02',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|c/.test(data.parts.value)
      },
    },
    {
      lable: '配仓情况',
      active: false,
      idM: 'ACE03',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|d/.test(data.parts.value)
      },
    },
    {
      lable: '轨迹',
      active: false,
      idM: 'ACE004',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|e/.test(data.parts.value)
      },
    },
    {
      lable: '拖车/转运',
      active: false,
      idM: 'ACE04',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|f/.test(data.parts.value)
      },
    },
    {
      lable: '报关清关',
      active: false,
      idM: 'ACE05',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|g/.test(data.parts.value)
      },
    },
    {
      lable: '提单应付',
      active: false,
      idM: 'ACE060',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|h/.test(data.parts.value)
      },
    },
    {
      lable: '提单成本',
      active: false,
      idM: 'ACE06',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|i/.test(data.parts.value)
      },
    },
    {
      lable: '相关文件',
      active: false,
      idM: 'ACE07',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|j/.test(data.parts.value)
      },
    },
    {
      lable: '抬头/收发通',
      active: false,
      idM: 'ACE08',
      accessible:[':BL:DetailAccess'],
      dataValidator: (path: any, data: any) => {
        return /0|k/.test(data.parts.value)
      },
    },
  ]));

  // const box2 = document.getElementById('contentBodyBox');
  const box2 = useMemo(() => {
    return document.getElementById('contentBodyBox');
  }, [document.getElementById('contentBodyBox')]);

  const scroll = useScroll(box2, (val) => val.top > 1);
  const documents: any = document;
  useEffect(() => {
    itemList.forEach((item: any) => {
      if (
        documents.getElementById(item?.idM)?.getBoundingClientRect().top < 95
      ) {
        itemList.forEach((item2: any) => (item2.active = false));
        item.active = true;
      } else {
        item.active = false;
      }
      setItemList([...itemList]);
    });
  }, [scroll, MyScroll]);

  

  return (
    <>
      {/* {!leftBoxW && ( */}
      <div className="p-10px ml-10px fixed">
        {itemList.map((item: any, index: number) => {
          return (
            <div
              className={classNames(
                'color-#7F8BAC text-13px line-height-36px cursor-pointer',
                item.active ? styles['active'] : '',
              )}
              key={index}
              onClick={() => {
                itemList.forEach((item: any) => {
                  item.active = false;
                });
                item.active = true;
                setItemList([...itemList]);
                document?.querySelector(`#${item?.idM}`)?.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start',
                  inline: 'nearest',
                });
              }}
            >
              {item.lable}
            </div>
          );
        })}
      </div>
      {/* )} */}
    </>
  );
};

export default LeftBox;
