import AccessCard from '@/AccessCard';
import { KeepAliveTabContext } from '@/layouts/context';
import { VerticalAlignTopOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useContext, useState } from 'react';
import BillOfLadingCost from './BillOfLadingCost';
import BillOfLadingDetails from './BillOfLadingDetails';
import BillOfLadingPayable from './BillOfLadingPayable';
import CustomsClearance from './CustomsClearance';
import HeadUpTransceiver from './HeadUpTransceiver';
import RelatedDocuments from './RelatedDocuments';
import ServiceProviderCard from './ServiceProviderCard';
import TicketDistributionStatistics from './TicketDistributionStatistics';
import TrailerTransfer from './TrailerTransfer';
import Trajectory from './Trajectory';
import WarehouseSituation from './WarehouseSituation';

interface Props {
  detailsList: any; // 提单详情数据
  spaceId: string; // 提单id
  refreshDetails?: () => void; // 刷新详情
}
const RightBox = ({
  detailsList,
  spaceId: newSpaceId,
  refreshDetails,
}: Props) => {
  const [spaceId] = useState<any>(newSpaceId);
  const { MyScroll, scrollRef }: any = useContext(KeepAliveTabContext);

  return (
    <>
      {/* 提单详情 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|a/.test(data.parts.value);
        }}
      >
        <BillOfLadingDetails detailsList={detailsList} />
      </AccessCard>
      {/* 服务商 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|b/.test(data.parts.value);
        }}
      >
        {detailsList?.info?.mode === 2 && (
          <ServiceProviderCard
            detailsList={detailsList}
            spaceId={spaceId}
            refreshDetails={refreshDetails}
          />
        )}
      </AccessCard>
      {/* 分票统计数据 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|c/.test(data.parts.value);
        }}
      >
        <TicketDistributionStatistics spaceId={spaceId} />
      </AccessCard>
      {/* 配仓情况 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|d/.test(data.parts.value);
        }}
      >
        <WarehouseSituation detailsList={detailsList} />
      </AccessCard>

      {/* 轨迹 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|e/.test(data.parts.value);
        }}
      >
        <Trajectory detailsList={detailsList} spaceId={spaceId} />
      </AccessCard>
      {/* 拖车/转运 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|f/.test(data.parts.value);
        }}
      >
        <TrailerTransfer detailsList={detailsList} />
      </AccessCard>
      {/* 报关清关 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|g/.test(data.parts.value);
        }}
      >
        <CustomsClearance detailsList={detailsList} />
      </AccessCard>
      {/* 提单应付 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|h/.test(data.parts.value);
        }}
      >
        <BillOfLadingPayable
          detailsList={detailsList}
          spaceId={spaceId}
          refreshDetails={refreshDetails}
        />
      </AccessCard>
      {/* 提单成本 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|i/.test(data.parts.value);
        }}
      >
        <BillOfLadingCost detailsList={detailsList} />
      </AccessCard>
      {/* 相关文件 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|j/.test(data.parts.value);
        }}
      >
        <RelatedDocuments spaceId={spaceId} />
      </AccessCard>
      {/* 抬头/收发通 */}
      <AccessCard
        accessible={[':BL:DetailAccess']}
        dataValidator={(path: any, data: any) => {
          return /0|j/.test(data.parts.value);
        }}
      >
        <HeadUpTransceiver detailsList={detailsList} spaceId={spaceId} />
      </AccessCard>

      {MyScroll?.top >= 400 && (
        <div
          className="fixed bottom-30px right-50px z-999"
          onClick={() => {
            scrollRef.current.scrollTo(0, 0, 500);
          }}
        >
          <Button
            type="primary"
            shape="circle"
            icon={<VerticalAlignTopOutlined />}
          />
        </div>
      )}
    </>
  );
};

export default RightBox;
