import MyIcon from '@/components/MyIcon';
import { ProCard } from '@ant-design/pro-components';
import { Button, Divider, Space, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import { history } from '@umijs/max';
interface Props {
  detailsList: any; // 详情数据
}
/* 运单状态 枚举 */
const waybillStatusEnum: any = {
  0: {
    text: '草稿',
    color: '#dfe4ea',
  },
  10: {
    text: '待签入',
    color: '#ff9f43',
  },
  20: {
    text: '已签入',
    color: '#48dbfb',
  },
  40: {
    text: '已确认',
    color: '#1dd1a1',
  },
  60: {
    text: '已配仓',
    color: '#1e90ff',
  },
  90: {
    text: '已签出',
    color: '#ff6b81',
  },
  200: {
    text: '运输中',
    color: '#ff9f43',
  },
  300: {
    text: '已签收',
    color: '#1dd1a1',
  },
  500: {
    text: '异常结束',
    color: '#ff4757',
  },
};
const WarehouseSituation = ({ detailsList }: Props) => {
  const columns: any = [
    {
      title: '运单号',
      dataIndex: 'waybillNO',
      width: 200,
      fixed: 'left',
    },
    {
      title: '件数',
      dataIndex: 'pieceNum',
      width: 100,
    },
    {
      title: '出货体积/m³',
      dataIndex: 'volume',
      width: 150,
    },
    // {
    //   title: '原则',
    //   dataIndex: 'waybillNo',
    //   width: 100,
    // },
    {
      title: '运单状态',
      dataIndex: 'state',
      width: 100,
      render: (text: any, record: any) => {
        return (
          <Tag color={waybillStatusEnum[record?.state]?.color}>
            {waybillStatusEnum[record?.state]?.text}
          </Tag>
        );
      },
    },
    {
      title: '品名',
      dataIndex: 'name',
      width: 100,
      render: (text: any, record: any) => {
        return record?.pieceList[0]?.name || '-';
      }
    },
    {
      title: '销售产品',
      dataIndex: 'productName',
      width: 220,
    },
    {
      title: '签入时间',
      dataIndex: 'signInTime',
      width: 200,
      render: (text: any, record: any) => {
        return dayjs(record.signInTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '目的地',
      dataIndex: 'recipientAddress',
      width: 260,
      render: (text: any, record: any) => {
        return `${record?.recipientAddress?.city} ${record?.recipientAddress?.street}`;
      },
    },
    {
      title: '客户',
      dataIndex: 'client',
      width: 120,
      render: (text: any, record: any) => {
        return record?.client?.name;
      },
    },
    {
      title: '业务员',
      dataIndex: 'salesman',
      width: 120,
      render: (text: any, record: any) => {
        return record?.salesman?.name;
      },
    },
    // {
    //   title: '操作员',
    //   dataIndex: 'waybillNo',
    //   width: 120,
    // },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      fixed: 'right',
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                history.push(`/Waybilladministration/detail`, record);
              }}
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];
  const columns2: any = [
    {
      title: '子运单号',
      dataIndex: 'subWaybillNO',
      width: 200,
    },
    // {
    //   title: '件数',
    //   dataIndex: 'pieceNum',
    //   width: 100,
    // },
    {
      title: '出货体积/m³',
      dataIndex: 'volume',//signActualWeight
      // width: 150,
    },
    {
      title: '原则',
      dataIndex: 'reason',
      // width: 100,
    },
    {
      title: '运单状态',
      dataIndex: 'state',
      // width: 100,
      render: (text: any, record: any) => {
        return (
          <Tag color={waybillStatusEnum[record?.state]?.color}>
            {waybillStatusEnum[record?.state]?.text}
          </Tag>
        );
      },
    },
  ];
  return (
    <>
      <ProCard title="配仓情况" className="mb-20px" id="ACE03">
        <div>
          <Space size={130}>
            <div>
              <div className="line-height-32px">
                <span className="color-#AEAEAE">配仓率/m³:</span>
                <span>
                  {' '}
                  {detailsList?.volume}/{detailsList?.info?.volume}
                </span>
              </div>
              <div className="line-height-32px">
                <span className="color-#AEAEAE">总票数:</span>
                <span> {detailsList?.waybillNum}</span>
              </div>
              <div className="line-height-32px">
                <span className="color-#AEAEAE">总件数:</span>
                <span> {detailsList?.pieceNum}</span>
              </div>
            </div>
            <div>
              <div className="line-height-32px">
                <span className="color-#AEAEAE">出货总实重/kg:</span>
                <span>{detailsList?.weight}</span>
              </div>
              {/* <div className="line-height-32px">
                <span className="color-#AEAEAE">出货总体积重/kg:</span>
                <span> {detailsList?.volumeWeight}</span>
              </div> */}
              <div className="line-height-32px">
                <span className="color-#AEAEAE">出货总计费重/kg:</span>
                <span> {detailsList?.chargeWeight}</span>
              </div>
            </div>
          </Space>
        </div>
        <Divider dashed />
        {/* 配仓列表 */}
        {detailsList?.list?.map((item: any, index: number) => {
          return (
            <div className="mb-10px" key={index}>
              <div className="mb-5px">
                <Space>
                  <MyIcon type="icon-gailan-copy" />
                  <div className="font-600">{item?.warehouse?.name} 仓库</div>
                  <div>
                    <span className="color-#4071FF">
                      {item?.waybillList?.length}
                    </span>{' '}
                    票
                  </div>
                  <div>
                    <span className="color-#4071FF">
                      {item?.waybillList?.reduce(
                        (total: any, currentValue: any) => {
                          return total + currentValue?.pieceNum;
                        },
                        0,
                      )}
                    </span>{' '}
                    件
                  </div>
                  <div>
                    <span className="color-#4071FF">
                      {' '}
                      {item?.waybillList?.reduce(
                        (total: any, currentValue: any) => {
                          return total + Number(currentValue?.volume);
                        },
                        0,
                      )?.toFixed(3) || '-'}
                    </span>{' '}
                    m³
                  </div>
                </Space>
              </div>
              <Table
                dataSource={item?.waybillList}
                columns={columns}
                pagination={false}
                rowKey="id"
                scroll={{ x: 800 }}
                expandable={{
                  expandedRowRender: (record: any) => {
                    return (
                      <>
                        <Table
                          columns={columns2}
                          dataSource={record?.pieceList}
                          pagination={false}
                          rowKey="id"
                        />
                      </>
                    );
                  },
                  // rowExpandable: (record) => record.name !== 'Not Expandable',
                }}
              />
            </div>
          );
        })}
      </ProCard>
    </>
  );
};

export default WarehouseSituation;
