import { ProTable } from '@ant-design/pro-components';
import { Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';

interface Props {
  detailsList: any;
}

const BillOfLadingCost = ({ detailsList }: Props) => {
  const [activeKey, setActiveKey] = useState<React.Key>('tab1');
  const [tableListDataSource, setTableListDataSource] = useState<any>([]);
  useEffect(() => {
    if (activeKey === 'tab1') {
      setTableListDataSource(detailsList?.blnoCostList);
    } else {
      setTableListDataSource(detailsList?.subBlnoCostList);
    }
  }, [activeKey, detailsList]);
  /* 本位币金额总计 */
  const totalAmout = useMemo(()=>{
    //计算本位币金额总数
    let total = 0;
    tableListDataSource?.forEach((item:any)=>{
      total +=( Number(item?.amount) * Number(item?.currentRate))
    })
    return total.toFixed(2)
  },[tableListDataSource])

  const columns: any = [
    {
      title: '子提单号',
      dataIndex: 'waybillNo',
      hideInTable: activeKey === 'tab1',
    },
    {
      title: '费用名称',
      dataIndex: 'reason',
    },
    {
      title: '渠道商',
      dataIndex: 'counterpartyName',
    },
    {
      title: '本位币金额',
      dataIndex: 'amount',
      render:(text:any,record:any)=>{
        return ((Number(record?.amount) * Number(record?.currentRate))?.toFixed(2)) || '-'
      }
    },
    {
      title: '原位币金额',
      dataIndex: 'amount',
    },
    {
      title: '币种',
      dataIndex: 'currencyTypeDesc',
    },
    {
      title: '费率',
      dataIndex: 'currentRate',
    },
    // {
    //   title: '价格/元',
    //   dataIndex: 'amount',
    // },
    // {
    //   title: '成本来源(应付账单号)',
    //   dataIndex: 'remark',
    // },
    // {
    //   title: '添加人',
    //   dataIndex: 'waybillNo',
    // },
    {
      title: '创建时间',
      dataIndex: 'time',
      valueType: 'dateTime',
    },
    {
      title: '备注',
      dataIndex: 'reason',
    },
  ];
  return (
    <>
      <ProTable
        id="ACE06"
        className="mb-20px"
        headerTitle="提单成本"
        columns={columns}
        dataSource={tableListDataSource}
        // request={(params, sorter, filter) => {
        //   console.log(params, sorter, filter);
        //   return Promise.resolve({
        //     data: [],
        //     success: true,
        //   });
        // }}
        toolbar={{
          filter: (
            <>
              {activeKey === 'tab2' && (
                <>
                  <div>子提单号：</div>
                  <Select
                    defaultValue="all"
                    style={{ width: 220 }}
                    options={[
                      {
                        label: '全部',
                        value: 'all',
                      },
                    ]}
                  />
                </>
              )}
            </>
          ),
          menu: {
            type: 'tab',
            activeKey: activeKey,
            items: [
              {
                key: 'tab1',
                label: '主提单成本',
              },
              {
                key: 'tab2',
                label: '子提单成本',
                disabled:true
              },
            ],
            onChange: (key) => {
              setActiveKey(key as string);
            },
          },
          actions: [],
        }}
        rowKey="id"
        pagination={false}
        search={false}
        dateFormatter="string"
        options={false}
        footer={()=>{
          return <>
            本位币总计：{totalAmout}
          </>
        }}
      />
    </>
  );
};

export default BillOfLadingCost;
