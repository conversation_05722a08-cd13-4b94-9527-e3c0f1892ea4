import { ProCard } from '@ant-design/pro-components';
import { Col, Row, Space, Image, Button, message, Flex } from 'antd';
import React, { useMemo } from 'react';
import styles from './index.less';
import haiyunImg from '@/assets/images/provide/p-64.png';
import tuoche from '@/assets/images/provide/p-128.png';
import kongyun from '@/assets/images/provide/p-32.png';
import weicheng from '@/assets/images/provide/p-2.png';
import baoguan from '@/assets/images/provide/p-256.png';
import qinguan from '@/assets/images/provide/p-512.png';
import houduan from '@/assets/images/provide/p-2048.png';
import zhuangui from '@/assets/images/provide/p-4096.png';
import { getWaybillOperationProviderAPI } from '@/services/financeApi';
import { useRequest } from 'ahooks';
import { ProviderType } from '@/shared/ProviderTypeFn';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import AddProxyModal from '../components/AddProxyModal';
interface Props {
  detailsList: any;
  spaceId?: any;
  refreshDetails?: () => void;
}
const style = {
  padding: '0 12px',
  border: '1px solid #E2E8EB',
  borderRadius: '10px',
  minHeight: '70px',
  display: 'flex',
  alignItems: 'center',
};
const ServiceProviderCard = ({
  detailsList,
  spaceId,
  refreshDetails,
}: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { agents } = detailsList;
  // console.log('agents: ', agents);
  // console.log(agents[ProviderType.ShipTransport.getCode()]);
  // console.log('船司shipperAgent: ', shipperAgent);

  // console.log('拖车trailerAgent: ', trailerAgent);
  const { loading, run } = useRequest(getWaybillOperationProviderAPI, {
    debounceWait: 300,
    manual: true,
    onSuccess: (res) => {
      if (res?.status) {
        messageApi.success('操作成功');
        if (refreshDetails) {
          refreshDetails();
        }
      }
    },
    onError: (err) => {
      console.error('configure/triggerProviderBlnoOperation: ', err);
    },
  });

  const serviceProviderList = useMemo(() => {
    return [
      {
        name: '海运',
        code: ProviderType.ShipTransport.getCode(),
        img: haiyunImg,
        list: agents[ProviderType.ShipTransport.getCode()],
        spaceId:spaceId
      },
      {
        name: '拖车',
        code: ProviderType.Trailer.getCode(),
        img: tuoche,
        list: agents[ProviderType.Trailer.getCode()],
        spaceId:spaceId
      },
      {
        name: '空运',
        code: ProviderType.AirTransport.getCode(),
        img: kongyun,
        list: agents[ProviderType.AirTransport.getCode()],
        spaceId:spaceId
      },
      {
        name: '尾程卡派',
        code: ProviderType.TailTruck.getCode(),
        img: weicheng,
        list: agents[ProviderType.TailTruck.getCode()],
        spaceId:spaceId
      },
      {
        name: '报关',
        code: ProviderType.Declaration.getCode(),
        img: baoguan,
        list: agents[ProviderType.Declaration.getCode()],
        spaceId:spaceId
      },
      {
        name: '清关',
        code: ProviderType.Clearance.getCode(),
        img: qinguan,
        list: agents[ProviderType.Clearance.getCode()],
        spaceId:spaceId
      },
      {
        name: '后端操作',
        code: ProviderType.AbroadOperate.getCode(),
        img: houduan,
        list: agents[ProviderType.AbroadOperate.getCode()],
        spaceId:spaceId
      },
      {
        name: '装柜',
        code: ProviderType.ContainerLoading.getCode(),
        img: zhuangui,
        list: agents[ProviderType.ContainerLoading.getCode()],
        spaceId:spaceId
      },
    ];
  }, [detailsList?.id,agents]);

  return (
    <div className="mb-20px" id="ACE00fws">
      {contextHolder}
      <ProCard title="服务商">
        <Row gutter={16}>{
          serviceProviderList?.map((item)=>{
            return  <Col span={12} className={styles['gutter-box']} key={item.code}>
            {/* 海运 */}
            {item?.list ? (
              <>
                {item?.list?.map(
                  (
                    item2: {
                      name: any;
                      cabinetPrice: {
                        priceModelArr: {
                          operationName: string;
                          triggered: boolean;
                          id: string | number;
                        }[];
                        id: any;
                      };
                    },
                    index2: React.Key | null | undefined,
                  ) => {
                    return (
                      <div style={style} key={index2}>
                        <div>
                          <Space>
                            <div>
                              <Image
                                width={50}
                                height={50}
                                src={item?.img}
                                preview={false}
                              />
                            </div>
                            <div className="color-#333 font-500 min-w-80px">
                              {item2?.name || '-'}
                            </div>
                          </Space>
                        </div>
                        <Flex wrap="wrap" gap="small">
                          {item2?.cabinetPrice?.priceModelArr?.map(
                            (
                              item3: {
                                operationName: string;
                                triggered: boolean;
                                id: string | number;
                              },
                              key3: React.Key | number | null | undefined,
                            ) => {
                              return (
                                <Button
                                  loading={loading}
                                  key={key3}
                                  size="small"
                                  type="link"
                                  disabled={item3?.triggered}
                                  onClick={() => {
                                    run({
                                      spaceId,
                                      priceId: item2?.cabinetPrice?.id,
                                      feeId: item3?.id,
                                    });
                                  }}
                                >
                                  {item3?.operationName}
                                </Button>
                              );
                            },
                          )}
                        </Flex>
                      </div>
                    );
                  },
                )}
              </>
            ) : (
              <div style={style}>
              <div>
                <Space>
                  <div>
                    <Image
                      width={50}
                      height={50}
                      src={item?.img}
                      preview={false}
                    />
                  </div>
                  <div key={item?.code}>
                    <div className='color-#707070 text-12px '> <ExclamationCircleOutlined className='color-orange ' /> 暂无代理</div>
                    {
                      (item.code ===2 || item.code ===2048 || item.code === 1 || item.code ===4096 || item.code === 256) && <AddProxyModal modalList={item} detailsList={detailsList} refreshDetails={refreshDetails}  />
                    }
                    
                  </div>
                </Space>
              </div>
            </div>
            )}
          </Col>
          })
        }</Row>
      </ProCard>
    </div>
  );
};

export default React.memo(ServiceProviderCard);
