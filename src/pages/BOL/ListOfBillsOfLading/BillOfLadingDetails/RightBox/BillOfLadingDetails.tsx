import { ProCard } from '@ant-design/pro-components';
import { Space, Tag } from 'antd';
import classNames from 'classnames';
import styles from './index.less';
import arrow from '@/assets/images/arrow.png';
import aircraft from '@/assets/images/aircraft.png';
import chuan from '@/assets/images/chuan.png';
import dayjs from 'dayjs';

interface Props {
  detailsList: any;
}
const spaceStateType: any = {
  1: {
    text: '空闲',
    color: '#dfe4ea',
  },
  10: {
    text: '待出库',
    color: '#ff9f43',
  },
  20: {
    text: '已提柜',
    color: '#ff6b6b',
  },
  30: {
    text: '已出库',
    color: '#48dbfb',
  },
  40: {
    text: '已进港',
    color: '#1dd1a1',
  },
  50: {
    text: '已起航',
    color: '#3742fa',
  },
  60: {
    text: '到达目的地',
    color: '#2ed573',
  },
  70: {
    text: '清关查验',
    color: '#ff6348',
  },
  80: {
    text: '海外提柜',
    color: '#8e44ad',
  },
  90: {
    text: '已还柜',
    color: '#00b894',
  },
  '-1': {
    text: '已转卖',
    color: '#ff4757',
  },
  '-2': {
    text: '已退订',
    color: '#ff4757',
  },
  0: {
    text: '未知',
    color: '#95a5a6',
  },
};

const BillOfLadingDetails = ({ detailsList }: Props) => {
  const { info } = detailsList;
  return (
    <ProCard title="提单详情" className="mb-20px" id="ACE01">
      <div className="w-100% border-1 border-solid border-#E2E8EB ">
        <div
          className={classNames('w-100% h-50px bg-#4A577E', styles['top-bar'])}
        >
          <Space>
            <div className="color-white line-height-50px font-500 ml-20px">
              提单号: {info?.blno}
            </div>
            <Tag color={spaceStateType[detailsList?.state]?.color}>
              {spaceStateType[detailsList?.state]?.text}
            </Tag>
          </Space>
        </div>
        <div className="bg-#F8F8FA p-40px">
          <Space size={120}>
            <div>
              <div className="mb-10px">
                <img
                  src={info?.mode === 1 ? aircraft : chuan}
                  width={25}
                  height={25}
                  alt="Example"
                />
              </div>
              <div className="color-#333 text-14px">
                {info?.vesselName} {info?.vesselCode}
              </div>
              <div className="color-#333 font-600">
                {info?.mode === 2 ? (
                  <span>
                    {info?.shipName}/{info?.voyageCode}
                  </span>
                ) : (
                  info?.airCode
                )}
              </div>
            </div>
            <div className="flex">
              <div className="text-center">
                <div className="color-#333 text-22px font-600 mb-6px">
                  {info?.departsCity}
                </div>
                <Tag color="#4A577E">始发</Tag>
              </div>
              <div className="text-center m-10px relative top--28px">
                <Tag color="blue">{info?.period}天</Tag>
                <div>
                  <img width={120} height={8} src={arrow} alt="Example" />
                </div>
              </div>
              <div className="text-center">
                <div className="color-#333 text-22px font-600 mb-6px">
                  {info?.deliveryCity}
                </div>
                <Tag color="#4A577E">目的</Tag>
              </div>
            </div>
            {
              /* 空运 */
              info?.mode === 1 && (
                <div>
                  <div className="line-height-30px mt-14px">
                    <span className="color-#707070">预计启航：</span>
                    <span className="color-#333333 font-600">
                      {info?.airDepartTime
                        ? dayjs(info?.airDepartTime)?.format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : '-'}
                    </span>
                  </div>
                  {/* <div className="line-height-30px">
                <span className="color-#707070">预计到达：</span>
                <span className="color-#333333 font-600">
                  {info?.estimatedArrivalTime
                    ? dayjs(info?.estimatedArrivalTime).format(
                        'YYYY-MM-DD HH:mm:ss',
                      )
                    : '-'}
                </span>
              </div> */}
                </div>
              )
            }
            {
              /* 海运 */
              info?.mode === 2 && (
                <div>
                  <div className="line-height-30px mt-14px">
                    <span className="color-#707070">预计启航：</span>
                    <span className="color-#333333 font-600">
                      {info?.estimatedDepartTime
                        ? dayjs(info?.estimatedDepartTime)?.format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : '-'}
                    </span>
                  </div>
                  <div className="line-height-30px">
                    <span className="color-#707070">预计到达：</span>
                    <span className="color-#333333 font-600">
                      {info?.estimatedArrivalTime
                        ? dayjs(info?.estimatedArrivalTime).format(
                            'YYYY-MM-DD HH:mm:ss',
                          )
                        : '-'}
                    </span>
                  </div>
                </div>
              )
            }
          </Space>
        </div>
        <div className="bg-#FBFBFC p-20px text-12px">
          <Space size={30}>
            {info?.mode === 2 && (
              <>
                <div>
                  <span className="color-#707070">最晚预报：</span>
                  <span className="font-600 color-#333">
                    {info?.forecastLimitTime
                      ? dayjs(info?.forecastLimitTime).format(
                          'YYYY-MM-DD HH:mm:ss',
                        )
                      : '-'}
                  </span>
                </div>
                <div>
                  <span className="color-#707070">最晚到货：</span>
                  <span className="font-600 color-#333">
                    {info?.cargoLimitTime
                      ? dayjs(info?.cargoLimitTime).format(
                          'YYYY-MM-DD HH:mm:ss',
                        )
                      : '-'}
                  </span>
                </div>
                <div>
                  <span className="color-#707070">预计提柜：</span>
                  <span className="font-600 color-#333">
                    {info?.estimatedDeliveryTime
                      ? dayjs(info?.estimatedDeliveryTime).format(
                          'YYYY-MM-DD HH:mm:ss',
                        )
                      : '-'}
                  </span>
                </div>
              </>
            )}
            <div>
              <span className="color-#707070">航司：</span>
              <span className="font-600 color-#333">{info?.providerName}</span>
            </div>
            <div>
              <span className="color-#707070">装柜地点：</span>
              {detailsList?.info?.mode === 2 ? (
                <span className="font-600 color-#333">
                  {info?.warehouses?.map((item: any) => item.name).join(',')}
                </span>
              ) : (
                <span className="font-600 color-#333">
                  {detailsList?.warehouseList
                    ?.map((item: any) => item.name)
                    .join(',')}
                </span>
              )}
            </div>
          </Space>
        </div>

        {detailsList?.info?.mode === 2 && (
          <div className="bg-#fff p-20px text-12px">
            <Space size={50}>
              <div>
                <span className="color-#707070">箱号：</span>
                <span className="font-600 color-#333">
                  {detailsList?.trailerPlan?.containerNo}
                </span>
              </div>
              <div>
                <span className="color-#707070">封号：</span>
                <span className="font-600 color-#333">
                  {detailsList?.trailerPlan?.sealNo}
                </span>
              </div>
              <div>
                <span className="color-#707070">箱型：</span>
                <span className="font-600 color-#333">{info?.model}</span>
              </div>
              <div>
                <span className="color-#707070">箱皮：</span>
                <span className="font-600 color-#333">
                  {detailsList?.trailerPlan?.containerSkin}
                </span>
              </div>
            </Space>
          </div>
        )}
      </div>
    </ProCard>
  );
};

export default BillOfLadingDetails;
