import AccessCard from '@/AccessCard';
import { getSplitStatisticsListAPI } from '@/services/productAndPrice/api';
import { ProCard } from '@ant-design/pro-components';
import { Table } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import DivideVoteModal from '../components/DivideVoteModal';

interface Props {
  spaceId: string; // 提单id
}

const typeEnum: any = {
  10: '非单独报关',
  20: '单独报关',
  30: '合并报关',
  0: '未知',
};

const TicketDistributionStatistics = ({ spaceId }: Props) => {
  // console.log('spaceId: ', spaceId);
  const [tickeList, setTickeList] = useState<any>([]);
  /* 查询分票统计 */
  const getTicketDistributionStatistics = async () => {
    try {
      const { status, data } = await getSplitStatisticsListAPI({
        spaceId: spaceId,
      });
      if (status) {
        setTickeList(data?.list);
      }
    } catch (err) {
      console.error('查询分票统计接口出错: ', err);
    }
  };

  useEffect(() => {
    if (spaceId) {
      // console.log(11);
      // console.log(spaceId);
      getTicketDistributionStatistics();
    }
  }, [spaceId]);

  /* 刷新 */
  const refresh = () => {
    getTicketDistributionStatistics();
  };

  const columns: any = [
    {
      title: '报关ID',
      dataIndex: 'declarationId',
      width: 200
    },
    {
      title: '报关类型',
      dataIndex: 'type',
      width: 100,
      render: (text: any, record: any) => {
        return typeEnum[record?.declaration?.type];
      },
    },
    {
      title: '子提单号',
      dataIndex: 'subBlno',
      width: 200,
      render: (text: any, record: any) => {
        return record?.declaration?.subBlno;
      },
    },
    {
      title: '推荐英文品名',
      dataIndex: 'enName',
      width: 200,
    },
    {
      title: '唛头',
      dataIndex: 'shippingMark',
      width: 100,
    },
    {
      title: '件数',
      dataIndex: 'num',
      width: 100,
    },
    {
      title: '包装单位',
      dataIndex: 'skinUnit',
      width: 100,
    },
    {
      title: '毛重/kg',
      dataIndex: 'weight',
      width: 100,
    },
    {
      title: '体积/m³',
      dataIndex: 'volume',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
      render: (text: any, record: any) => {
        return dayjs(record?.declaration?.createTime).format(
          'YYYY-MM-DD HH:mm:ss',
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      fixed: 'right',
      render: (text: any, record: any) => {
        return (
          <AccessCard accessible={[':BL:FullAccess']}>
            <DivideVoteModal
              btnText="编辑"
              record={record}
              spaceId={spaceId}
              refresh={refresh}
            />
          </AccessCard>
        );
      },
    },
  ];
  return (
    <>
      <ProCard title="分票统计数据" className="mb-20px" id="ACE02">
        <Table
          dataSource={tickeList}
          columns={columns}
          pagination={false}
          rowKey="id"
          scroll={{ x: 1200 }}
        />
      </ProCard>
    </>
  );
};

export default TicketDistributionStatistics;
