import TrackList from "@/components/TrackList";
import usePermissionFiltering from "@/hooks/usePermissionFiltering";
import { ProCard } from "@ant-design/pro-components";

interface Props {
  detailsList: any; // 提单详情数据
  spaceId?: string; // 提单id
}
const Trajectory = ({detailsList,spaceId}:Props)=>{
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [toList,IsAccessible] = usePermissionFiltering()
  return <>
    <ProCard title="轨迹" className="mb-20px"  id='ACE004'>
      <TrackList trackList={detailsList?.trackList} spaceId={spaceId} accessible={IsAccessible([':BL:TrackFullAccess'])} />
    </ProCard>
  </>
}

export default Trajectory;