import MyIcon from '@/components/MyIcon';
import { spaceStateType } from '@/shared/Enumeration';
import { ProCard } from '@ant-design/pro-components';
import { Empty, Space, Tag } from 'antd';

interface Props {
  detailsList: any; // 提单详情数据
}
const TrailerTransfer = ({ detailsList }: Props) => {
  return (
    <>
      <ProCard title="拖车/转运" className="mb-20px" id="ACE04">
        {/* 海运拖车 */}
        {detailsList?.info?.mode === 2 && (
          <div className="flex h-320px">
            {/* <div className="w-50% bg-#FBFCFD overflow-auto p-20px">
              <Timeline
                items={[
                  {
                    children: (
                      <div className="color-#4071FF">
                        <Space>
                          <div className="text-16px font-600">已入港</div>
                          <div>2023-08-10 12:30</div>
                        </Space>
                      </div>
                    ),
                  },
                  {
                    children: (
                      <div className="color-#333333">
                        <Space>
                          <div className="text-16px font-600">已入港</div>
                          <div>2023-08-10 12:30</div>
                        </Space>
                      </div>
                    ),
                  },
                ]}
              />
            </div> */}
            <div className="w-50% bg-white  p-20px flex items-center ">
              <Space direction="vertical" size="large">
                <div className="border border-#171717 border-rd border-solid flex line-height-24px">
                  <div className="w-25% text-center">
                    <MyIcon type="icon-huoche" className="color-#6ba91a" />
                  </div>
                  <div className="w-75% text-12px color-white  bg-gradient-to-b from-#171717 to-#707070 text-center">
                    Freight vehicles
                  </div>
                </div>
                <div>
                  <span className="color-#AEAEAE">拖车代理：</span>
                  <span className="color-#333333">{detailsList?.trailerPlan?.trailerAgent?.name}</span>
                </div>
                <div>
                  <span className="color-#AEAEAE">司机：</span>
                  <span className="color-#333333">
                    {detailsList?.trailerPlan?.driverName}
                  </span>
                </div>
                <div>
                  <span className="color-#AEAEAE">车牌号：</span>
                  <span className="color-#333333">
                    {detailsList?.trailerPlan?.licensePlateNo}
                  </span>
                </div>
                <div>
                  <span className="color-#AEAEAE">电话：</span>
                  <span className="color-#333333">
                    {detailsList?.trailerPlan?.driverPhone}
                  </span>
                </div>
                <div>
                  <span className="color-#AEAEAE">装柜地点：</span>
                  <span className="color-#333333">
                    {detailsList?.trailerPlan?.warehouseArr?.map(
                      (item: any) => item.name,
                    )}
                  </span>
                </div>
              </Space>
            </div>
          </div>
        )}

        {/* 空运托车 */}
        {detailsList?.info?.mode === 1 && (
          <div>
            {detailsList?.agentInlandPointIntermodalForms?.length ?detailsList?.agentInlandPointIntermodalForms?.map(
              (item: any, index: any) => {
                return (
                  <div key={index}>
                    <div className="bg-#FBFCFD h-40px text-17px font-600 flex items-center">
                      <Space>
                        <div>转运单号: {item?.id || '-'}</div>
                        <div className="mt--3px">
                          <Tag color={spaceStateType[item.state]?.color}>
                            {spaceStateType[item?.state]?.text}
                          </Tag>
                        </div>
                      </Space>
                    </div>
                    <div className="flex h-320px">
                      <div className="w-50% flex  items-center p-20px">
                        <Space direction="vertical" size="large">
                          <div className="border border-#171717 border-rd border-solid flex line-height-24px">
                            <div className="w-25% text-center">
                              <MyIcon
                                type="icon-gailan-copy"
                                className="color-#6ba91a"
                              />
                            </div>
                            <div className="w-75% text-12px color-white  bg-gradient-to-b from-#171717 to-#707070 text-center">
                              Warehouse
                            </div>
                          </div>
                          <div>
                            <span className="color-#AEAEAE">
                              目的仓库代理：
                            </span>
                            <span className="color-#333333">
                              {item?.agentName}
                            </span>
                          </div>
                          <div>
                            <span className="color-#AEAEAE">收件人：</span>
                            <span className="color-#333333">
                              {item?.contactPhone}
                            </span>
                          </div>
                          <div>
                            <span className="color-#AEAEAE">联系电话：</span>
                            <span className="color-#333333">
                              {item?.contactName}
                            </span>
                          </div>
                          <div>
                            <span className="color-#AEAEAE">地址：</span>
                            <span className="color-#333333 w-40px">
                              {item?.agentWarehouse?.county}{' '}
                              {item?.agentWarehouse?.contactPhone}
                            </span>
                          </div>
                        </Space>
                      </div>
                    </div>
                  </div>
                );
              },
            ):<div className="text-center"><Empty image={Empty.PRESENTED_IMAGE_SIMPLE} /></div>}
          </div>
        )}
      </ProCard>
    </>
  );
};

export default TrailerTransfer;
