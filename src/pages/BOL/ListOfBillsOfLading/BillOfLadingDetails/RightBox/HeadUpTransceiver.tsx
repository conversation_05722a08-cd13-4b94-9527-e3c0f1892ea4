import AccessCard from '@/AccessCard';
import { addressFuzzySearchAPI } from '@/services/home/<USER>';
import {
  updateNotifyInfoAPI,
  updateReceiverInfoAPI,
  updateSenderInfoAPI,
} from '@/services/productAndPrice/api';
import { ProCard } from '@ant-design/pro-components';
import { Button, Input, message, Select, Table } from 'antd';
import { useEffect, useState } from 'react';

interface Props {
  detailsList: any;
  spaceId: string;
}

const HeadUpTransceiver = ({ detailsList, spaceId }: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { sendAddress, recipientAddress, notificationAddress } = detailsList;
  // console.log('sendAddress: ', sendAddress);
  const [addressList, setAddressList] = useState<any[]>([]);
  /* 获取地址 */
  const getAddress = async (value: any) => {
    try {
      const { status, data } = await addressFuzzySearchAPI({ token: value });
      if (status) {
        const list = data.list.map((item: any) => {
          return {
            label: `${item.country.replaceAll('CN', '中国')} - ${
              item.provinceShortName
            } / ${item.province} - ${item.city} ${item.county ? '-' : ''} ${
              item.county
            } / ${item.zipCode}`,
            value: JSON.stringify(item),
          };
        });

        setAddressList(list);
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  const [dataList, setDataList] = useState<any>([]);
  useEffect(() => {
    setDataList([
      {
        id: 1,
        title: '发件人',
        sendAddress: sendAddress,
      },
      { id: 2, title: '收件人', sendAddress: recipientAddress },
      { id: 3, title: '通知人', sendAddress: notificationAddress },
    ]);
  }, [sendAddress]);

  /* 修改发件人信息 */
  const changeSendAddress = async (value: any) => {
    try {
      const { status } = await updateSenderInfoAPI({
        spaceId: spaceId,
        address: value,
      });
      if (status) {
        messageApi.success('修改成功');
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  /* 修改收件人信息 */
  const changeRecipientAddress = async (value: any) => {
    try {
      const { status } = await updateReceiverInfoAPI({
        spaceId: spaceId,
        address: value,
      });
      if (status) {
        messageApi.success('修改成功');
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  /* 修改通知人信息 */
  const changeNotificationAddress = async (value: any) => {
    try {
      const { status } = await updateNotifyInfoAPI({
        spaceId: spaceId,
        address: value,
      });
      if (status) {
        messageApi.success('修改成功');
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  const columns: any = [
    {
      title: '',
      dataIndex: 'title',
      width: 110,
      fixed: 'left',
    },
    {
      title: '地区',
      dataIndex: 'province',
      width: 280,
      render: (text: any, record: any) => {
        return (
          <>
            <Select
              showSearch
              value={`${record?.sendAddress?.province || ''}${
                record?.sendAddress?.city || ''
              }${record?.sendAddress?.county || ''}`}
              options={addressList}
              onFocus={() => {
                getAddress('');
              }}
              style={{ width: '100%' }}
              onChange={(value: any) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  ...JSON.parse(value),
                };
                setDataList([...dataList]);
              }}
              onSearch={(value: any) => {
                getAddress(value);
              }}
            />
          </>
        );
      },
    },
    {
      title: '详细地址',
      dataIndex: 'street',
      width: 220,
      render: (text: any, record: any) => {
        return (
          <>
            <Input
              value={record?.sendAddress?.street}
              onChange={(e: any) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  street: e.target.value,
                };
                setDataList([...dataList]);
              }}
            />
          </>
        );
      },
    },
    {
      title: '公司名字',
      dataIndex: 'companyName',
      width: 220,
      render: (text: any, record: any) => {
        return (
          <>
            <Input
              value={record?.sendAddress?.companyName}
              onChange={(e) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  companyName: e.target.value,
                };
                setDataList([...dataList]);
              }}
            />
          </>
        );
      },
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      width: 220,
      render: (text: any, record: any) => {
        return (
          <>
            <Input
              value={record?.sendAddress?.contactName}
              onChange={(e) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  contactName: e.target.value,
                };
                setDataList([...dataList]);
              }}
            />
          </>
        );
      },
    },
    {
      title: '电话',
      dataIndex: 'contactPhone',
      width: 220,
      render: (text: any, record: any) => {
        return (
          <>
            <Input
              value={record?.sendAddress?.contactPhone}
              onChange={(e) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  contactPhone: e.target.value,
                };
                setDataList([...dataList]);
              }}
            />
          </>
        );
      },
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
      width: 220,
      render: (text: any, record: any) => {
        return (
          <>
            <Input
              value={record?.sendAddress?.email}
              onChange={(e) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  email: e.target.value,
                };
                setDataList([...dataList]);
              }}
            />
          </>
        );
      },
    },
    {
      title: '传真',
      dataIndex: 'fax',
      width: 220,
      render: (text: any, record: any) => {
        return (
          <>
            <Input
              value={record?.sendAddress?.fax}
              onChange={(e) => {
                record.sendAddress = {
                  ...record.sendAddress,
                  fax: e.target.value,
                };
                setDataList([...dataList]);
              }}
            />
          </>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 100,
      fixed: 'right',
      render: (text: any, record: any) => [
        <AccessCard key={1} accessible={[':BL:FullAccess']}>
          <Button
            key="1"
            type="link"
            onClick={() => {
              // console.log('record: ', record);
              if (record?.title === '发件人') {
                changeSendAddress(JSON.stringify(record?.sendAddress));
              }
              if (record?.title === '收件人') {
                changeRecipientAddress(JSON.stringify(record?.sendAddress));
              }
              if (record?.title === '通知人') {
                changeNotificationAddress(JSON.stringify(record?.sendAddress));
              }
            }}
          >
            更新
          </Button>
        </AccessCard>,
      ],
    },
  ];
  return (
    <>
      {contextHolder}
      <ProCard
        title="抬头/收发通"
        className="mb-40px"
        id="ACE08"
        gutter={8}
        style={{ marginBlockStart: 8 }}
      >
        <Table
          dataSource={dataList}
          columns={columns}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1200 }}
        />
      </ProCard>
    </>
  );
};

export default HeadUpTransceiver;
