import { cleanDeclarationType, declarationType } from "@/utils/constant";
import { ProCard } from "@ant-design/pro-components";
import { Divider, Table } from "antd";

interface Props{
  detailsList?:any;
}

const CustomsClearance = ({detailsList}:Props)=>{
  /* 报关 */
  const columns1:any = [
    {
      title: '子提单号',
      dataIndex: 'subBlno',
    },
    {
      title: '报关ID',
      dataIndex: 'id',
    },
    {
      title: '报关单号',
      dataIndex: 'no',
    },
    {
      title: '报关类型',
      dataIndex: 'type',
      render:(text:any,record:any)=>{
        return declarationType[record?.type] || '-'
      }
    },
    {
      title: '报关状态',
      dataIndex: 'state',
      render:(text:any,record:any)=>{
        return declarationType[record?.state] || '-'
      }
    },
    {
      title: '运单号',
      dataIndex: 'waybillNo',
    },
    {
      title: '经营单位',
      dataIndex: 'businessName',
    },
    {
      title: '报关行',
      dataIndex: 'waybillNo',
    },
  ]
  /* 清关单 */
  const columns2:any = [
    {
      title: '清关单ID',
      dataIndex: 'id',
    },
    {
      title: '清关单号',
      dataIndex: 'no',
    },
    {
      title: '清关状态',
      dataIndex: 'state',
      render:(text:any,record:any)=>{
        return cleanDeclarationType[record?.state] || '-'
      }
    },
    {
      title: '清关行',
      dataIndex: 'customsBroker.name',
      render:(text:any,record:any)=>{
        return record?.customsBroker?.name || '-'
      }
      
    },
  ]
  return <>
  <ProCard title="报关清关" className="mb-20px" id="ACE05">
    {/* 报关单 */}
    <div className="font-600">报关单</div>
    <Table dataSource={detailsList?.declarationList || []} columns={columns1} pagination={false} rowKey="id" />
    <Divider dashed />
    {/* 清关单 */}
    <div className="font-600">清关单</div>
    <Table dataSource={detailsList?.clearanceList || []} columns={columns2} pagination={false} rowKey="id" />
  </ProCard>
  </>
}

export default CustomsClearance;