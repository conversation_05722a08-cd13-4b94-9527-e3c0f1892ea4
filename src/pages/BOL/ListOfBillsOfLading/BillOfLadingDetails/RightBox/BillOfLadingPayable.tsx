import AccessCard from '@/AccessCard';
import { getAvataUrl } from '@/shared/Enumeration';
import { addedWaybillState, costReconciliationType } from '@/utils/constant';
import { ProTable } from '@ant-design/pro-components';
import { Space, Image, Avatar } from 'antd';
import ModifyWaybillModal from '../components/ModifyWaybillModal';

interface Props {
  detailsList: any;
  spaceId: string;
  refreshDetails?: () => void;
}
const BillOfLadingPayable = ({
  detailsList,
  spaceId,
  refreshDetails,
}: Props) => {
  const columns: any = [
    {
      title: '账单号',
      dataIndex: 'id',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '费用名称',
      dataIndex: 'feeReason',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '费用分类',
      dataIndex: 'counterpartyTypeDesc',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '服务商',
      dataIndex: 'counterpartyName',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '结算状态',
      dataIndex: 'writeOffStateDesc',
      width: 130,
      hideInSearch: true,
    },
    {
      title: '原币应付',
      dataIndex: 'shouldPayAmount',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '币种',
      dataIndex: 'currencyTypeDesc',
      width: 150,
      hideInSearch: true,
      // render: (text: any) => {
      //   return currencyType[text];
      // }
    },
    {
      title: '汇率',
      dataIndex: 'currentRate',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '本位币已付',
      dataIndex: 'paidAmountCny',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '已结算金额',
      dataIndex: 'paidAmount',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '未结算金额',
      dataIndex: 'unpaidAmount',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '成本分摊',
      dataIndex: 'costReconciliationType',
      width: 150,
      hideInSearch: true,
      render: (text: any) => {
        return costReconciliationType[text];
      },
    },
    {
      title: '运单加收',
      dataIndex: 'addedWaybillState',
      width: 120,
      hideInSearch: true,
      render: (text: any) => {
        return addedWaybillState[text];
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorUserInfo',
      width: 120,
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Space>
            <Avatar src={getAvataUrl(record?.creatorUserInfo?.avatar)} />
            <span>{text?.name}</span>
          </Space>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '附件',
      dataIndex: 'attachmentFiles',
      width: 200,
      hideInSearch: true,
      render: (text: any) => {
        const imgList = text?.split(',');
        return imgList?.map((item: any, index: any) => {
          if (item.length > 5) {
            return (
              <Image
                key={index}
                src={getAvataUrl(item)}
                width={50}
                height={50}
              />
            );
          } else {
            return '-';
          }
        });
      },
    },
    {
      title: '费用备注',
      dataIndex: 'remark',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
    },
  ];
  return (
    <>
      <ProTable
        id="ACE060"
        className="mb-20px"
        headerTitle="提单应付"
        columns={columns}
        dataSource={detailsList?.paymentsList || []}
        // request={(params, sorter, filter) => {
        //   console.log(params, sorter, filter);
        //   return Promise.resolve({
        //     data: [],
        //     success: true,
        //   });
        // }}
        rowKey="id"
        pagination={false}
        search={false}
        dateFormatter="string"
        options={false}
        scroll={{ x: 1200 }}
        toolBarRender={() => [
          <div key="1">
            {Number(detailsList?.info?.mode) === 1 && (
              <Space>
                <div>单价 {detailsList?.price || '-'} 元/kg</div>
                <div>计费重 {detailsList?.paymentChargeWeight || '-'} kg</div>
                <AccessCard accessible={[':BL:FullAccess']}>
                  <ModifyWaybillModal
                    detailsList={detailsList}
                    spaceId={spaceId}
                    refreshDetails={refreshDetails}
                  />
                </AccessCard>
              </Space>
            )}
          </div>,
        ]}
      />
    </>
  );
};

export default BillOfLadingPayable;
