import ExcelReader from '@/components/ExcelReader';
import {
  deleteRelatedFileAPI,
  downloadFileAPI,
  getRelatedFileListAPI,
  uploadReferenceFileAPI,
} from '@/services/productAndPrice/api';
import { EllipsisOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { Avatar, Button, Dropdown, message, Popconfirm, Space } from 'antd';
import dayjs from 'dayjs';
import { useRef } from 'react';
import { saveAs } from 'file-saver';
import AccessCard from '@/AccessCard';
import FileList from '../components/FileList'


interface Props {
  spaceId: string;
}
/* 文件类型枚举 */
const fileTypeEnum: any = {
  1: '提货单DO',
  2: '提箱单S/O',
  3: '提单草稿',
  4: '电放件/SW件',
  5: '配仓清单',
};
const RelatedDocuments = ({ spaceId }: Props) => {
  const actionRef = useRef<any>();
  const [messageApi, contextHolder] = message.useMessage();
  /* 删除相关文件 */
  const deleteRelatedFile = async (id: string) => {
    try {
      const { status } = await deleteRelatedFileAPI({
        spaceId: spaceId,
        fileId: id,
      });
      if (status) {
        messageApi.success('删除成功');
        actionRef.current?.reload();
      }
    } catch (e) {
      console.error('删除相关文件失败', e);
    }
  };
  /* 下载文件 */
  const downloadFile = async (url: string) => {
    try {
      const response: any = await downloadFileAPI({
        url,
      });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
    } catch (err) {
      console.log('下载失败: ', err);
    }
  };
  const columns: any = [
    {
      title: '文件',
      dataIndex: 'name',
      render: (text: any, record: any) => {
        if(record?.name ==='舱单'){
          return <FileList spaceId={spaceId} name='舱单' />
        }
        if(record?.name ==='拆柜清单'){
          return <FileList spaceId={spaceId} name='拆柜清单' />
        }
        return (
          <>
            <a
              onClick={() => {
                downloadFile(record?.url);
              }}
            >
              {record?.name}
            </a>
          </>
        );
      },
    },
    {
      title: '文件类型',
      dataIndex: 'type',
      render: (text: any, record: any) => {
        return <span>{fileTypeEnum[record?.type]}</span>;
      },
    },
    {
      title: '上传人',
      dataIndex: 'userName',
      render: (text: any, record: any) => {
        return (
          <Space>
            <Avatar
              src={`${record?.avatar?.includes('http')
                ? record?.avatar
                : `https://static.kmfba.com/${record?.avatar}`
                }`}
            />
            <div>{record?.userName}</div>
          </Space>
        );
      },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      render: (text: any, record: any) => {
        return dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      width: 250,
      key: 'option',
      valueType: 'option',
      render: (text: any, record: any) => [
        <AccessCard key={1} accessible={[':BL:FullAccess']}>
          <Popconfirm
            key={1}
            title="删除确认"
            description="确认要删除次文件吗?"
            onConfirm={() => {
              deleteRelatedFile(record?.id);
            }}
            okText="删除"
            cancelText="取消"
            disabled={record?.auto === 1}
          >
            <Button type="link" danger disabled={record?.auto === 1}>
              删除
            </Button>
          </Popconfirm>
        </AccessCard>,
      ],
    },
  ];
  /* 刷新 */
  const refresh = () => {
    actionRef.current?.reload();
  };
  /* 上传文件 */
  const uploadFile = async (type: any, files: any) => {
    const { status } = await uploadReferenceFileAPI({
      spaceId: spaceId,
      type: type,
      files: files,
    });
    if (status) {
      messageApi.success('上传成功');
      refresh();
    }
  };
  return (
    <>
      {contextHolder}
      <ProTable
        id="ACE07"
        className="mb-20px"
        rowKey="id"
        actionRef={actionRef}
        pagination={{
          showQuickJumper: true,
        }}
        options={false}
        request={async (params: any = {}) => {
          const msg = await getRelatedFileListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            // keyword: params.keyword,
            spaceId: spaceId,
          });
          /* 剔除无id的数据 报错看的心烦  */
          msg?.data?.list?.forEach((item: any, index: number) => {
            if (!item.id) {
              msg?.data?.list?.splice(index, 1);
            }
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg.data?.total,
          };
        }}
        columns={columns}
        search={false}
        dateFormatter="string"
        headerTitle="相关文件"
        toolBarRender={() => [
          <AccessCard key={1} accessible={[':BL:FullAccess']}>
            <Dropdown
              key="menu"
              menu={{
                items: [
                  {
                    label: (
                      <ExcelReader
                        onChange={(e: any, file: any) => {
                          uploadFile(1, file);
                        }}
                      >
                        提货单DO
                      </ExcelReader>
                    ),
                    key: '1',
                  },
                  {
                    label: (
                      <ExcelReader
                        onChange={(e: any, file: any) => {
                          uploadFile(2, file);
                        }}
                      >
                        提箱单S/O
                      </ExcelReader>
                    ),
                    key: '2',
                  },
                  {
                    label: (
                      <ExcelReader
                        onChange={(e: any, file: any) => {
                          uploadFile(3, file);
                        }}
                      >
                        提单草稿
                      </ExcelReader>
                    ),
                    key: '3',
                  },
                  {
                    label: (
                      <ExcelReader
                        onChange={(e: any, file: any) => {
                          uploadFile(4, file);
                        }}
                      >
                        电放件/SW件
                      </ExcelReader>
                    ),
                    key: '4',
                  },
                  {
                    label: (
                      <ExcelReader
                        onChange={(e: any, file: any) => {
                          uploadFile(5, file);
                        }}
                      >
                        配仓清单
                      </ExcelReader>
                    ),
                    key: '5',
                  },
                ],
              }}
            >
              <Button>
                上传
                <EllipsisOutlined />
              </Button>
            </Dropdown>
          </AccessCard>,
        ]}
      />
    </>
  );
};

export default RelatedDocuments;
