import React, { useEffect, useMemo, useState } from 'react';
import { Button, Form, Modal } from 'antd';
import { ProFormCascader, ProFormSelect } from '@ant-design/pro-components';
// import { CounterpartyType1 } from '@/shared/EnumerationTypes';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import { useRequest } from 'ahooks';
import {
  addServiceProviderAPI,
  getServiceProviderAPI,
} from '@/services/financeApi';
import { getBrokerList } from '@/services/customer/CustomerHome';
import { updateBroker } from '@/services/bol';

interface Props {
  modalList: any;
  detailsList?: any;
  refreshDetails?: () => void;
}

const AddProxyModal: React.FC<Props> = ({
  modalList,
  refreshDetails,
}: Props) => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [toList] = usePermissionFiltering();
  const newCounterpartyType = useMemo(() => {
    return toList([
      {
        value: 'Declaration',
        label: '报关',
        isLeaf: false,
        accessible: [':Finance:PayablesCheckoutFullAccess'],
        dataValidator: (path: any, data: any) => {
          return /all|Declaration/.test(data.payableType.value);
        },
      },
      // {
      //   value: 'Clearance',
      //   label: '清关',
      //   isLeaf: false,
      //   accessible: [':Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|Clearance/.test(data.payableType.value);
      //   },
      // },
      // {
      //   value: 'Trailer',
      //   label: '拖车',
      //   isLeaf: false,
      //   accessible: [':Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|Trailer/.test(data.payableType.value);
      //   },
      // },
      // {
      //   value: 'AirTransport',
      //   label: '空运',
      //   isLeaf: false,
      //   accessible: [':Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|AirTransport/.test(data.payableType.value);
      //   },
      // },
      // {
      //   value: 'ShipTransport',
      //   label: '海运',
      //   isLeaf: false,
      //   accessible: [':Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|ShipTransport/.test(data.payableType.value);
      //   },
      // },
      {
        value: 'TailChannel',
        label: '尾程渠道',
        isLeaf: false,
        accessible: [':Finance:PayablesCheckoutFullAccess'],
        dataValidator: (path: any, data: any) => {
          return /all|TailChannel/.test(data.payableType.value);
        },
      },
      // {
      //   value: 'TransChannel',
      //   label: '卖货应付',
      //   isLeaf: false,
      //   accessible: [':Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|TransChannel/.test(data.payableType.value);
      //   },
      // },
      {
        value: 'AbroadOperate',
        label: '后端应付',
        isLeaf: false,
        accessible: [':Finance:PayablesCheckoutFullAccess'],
        dataValidator: (path: any, data: any) => {
          return /all|AbroadOperate/.test(data.payableType.value);
        },
      },
      {
        value: 'ContainerLoading',
        label: '装柜应付',
        isLeaf: false,
        accessible: [':Finance:PayablesCheckoutFullAccess'],
        dataValidator: (path: any, data: any) => {
          return /all|ContainerLoading/.test(data.payableType.value);
        },
      },
      // {
      //   value: 'Truck',
      //   label: '卡派应付',
      //   isLeaf: false,
      //   accessible: [':Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|Truck/.test(data.payableType.value);
      //   },
      // },
    ]);
  }, []);
  const [options, setOptions] = useState(newCounterpartyType);

  const { runAsync: addProxy } = useRequest(addServiceProviderAPI, {
    manual: true,
  });
  const { runAsync: updateBrokerFn } = useRequest(updateBroker, {
    manual: true,
  });

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    form.validateFields().then((values) => {
      console.log('values: ', values);
      if (modalList?.code === 256) {
        updateBrokerFn({
          spaceId: modalList?.spaceId,
          brokerId: values?.brokerId,
        }).then((res) => {
          if (res.status) {
            setIsModalOpen(false);
            form.resetFields();
            if (refreshDetails) {
              refreshDetails();
            }
          }
        });
      } else {
        addProxy({
          spaceId: modalList?.spaceId,
          providerId: values?.providerId[1],
          type: modalList?.code,
        }).then((res) => {
          if (res.status) {
            setIsModalOpen(false);
            form.resetFields();
            if (refreshDetails) {
              refreshDetails();
            }
          }
        });
      }
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (isModalOpen) {
      // console.log('modalList: ', modalList);
      // console.log('detailsList', detailsList);
    }
  }, [isModalOpen]);

  const { runAsync } = useRequest(getServiceProviderAPI, {
    manual: true,
  });
  const loadData = (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    runAsync({ counterpartyType: targetOption.value }).then((res: any) => {
      targetOption.children = res?.data?.list?.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      setOptions([...options]);
    });
  };
  return (
    <>
      <Button type="link" size="small" className="ml-8px" onClick={showModal}>
        添加代理
      </Button>
      <Modal
        title="添加代理"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={form} autoComplete="off">
          {modalList?.code === 256 ? (
            <ProFormSelect
              name="brokerId"
              showSearch
              label="报关行"
              // rules={[{ required: true, message: '请输入站点' }]}
              style={{ width: '100%' }}
              request={async () => {
                const { status, data } = await getBrokerList({
                  type: 80,
                  len: 999,
                });
                if (status) {
                  return data?.list?.map((item: any) => {
                    return {
                      label: item.name,
                      value: item.id,
                    };
                  });
                }
                return [];
              }}
              fieldProps={{
                filterOption: false,
              }}
            />
          ) : (
            <ProFormCascader
              fieldProps={{
                options: options,
                // onChange: onChange,
                // changeOnSelect:true,
                loadData: loadData,
              }}
              name="providerId"
              label="代理商"
              rules={[
                {
                  required: true,
                  message: '不能为空',
                },
              ]}
            />
          )}
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(AddProxyModal);
