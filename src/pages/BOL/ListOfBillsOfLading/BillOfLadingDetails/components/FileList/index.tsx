import React, { useEffect, useState } from 'react';
import { Modal, Table } from 'antd';
import { downloadWaybillAPI, downloadWaybillAPI2 } from '@/services/financeApi';
import { useRequest } from 'ahooks';
import { downloadFileAPI } from '@/services/productAndPrice/api';
import { saveAs } from 'file-saver'
const FileList = (spaceId: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [fileList, setFileList] = useState<any>([]);

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const downloadFile = async (url: string) => {
    try {
      const response: any = await downloadFileAPI({
        url,
      });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
    } catch (err) {
      console.log('下载失败: ', err);
    }
  };
  const { runAsync } = useRequest(spaceId?.name ==='舱单' ? downloadWaybillAPI : downloadWaybillAPI2, {
    manual: true,
  })

  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '下载',
      dataIndex: 'url',
      render: (text: any, record: any) => {
        return <a onClick={() => downloadFile(record?.url)}>下载</a>;
      }
    }
  ]

  useEffect(() => {
    if(isModalOpen) {
      runAsync({
        spaceId: spaceId?.spaceId,
      }).then((res) => {
        setFileList(res?.data?.list || [])
      })
    }

  }, [isModalOpen]);

  return (
    <>
      <a onClick={showModal}>{spaceId?.name}</a>
      <Modal
        title={spaceId?.name}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Table columns={columns} dataSource={fileList} pagination={false} />
      </Modal>
    </>
  );
};
export default React.memo(FileList);
