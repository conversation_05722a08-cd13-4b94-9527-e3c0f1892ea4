import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Space, Table } from 'antd';
import {
  getSplitDetailAPI,
  updateSplitInfoAPI,
} from '@/services/productAndPrice/api';

interface Props {
  btnText?: string;
  btnType?: string;
  record?: any;
  spaceId?: string; // 提单id
  refresh?: () => void;
}

const DivideVoteModal = ({ btnText, record, spaceId, refresh }: Props) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [subWaybillList, setSubWaybillList] = useState<any>([]);
  /* 获取提单详情 */
  const getSpaceDetail = async () => {
    try {
      const { status, data } = await getSplitDetailAPI({
        declarationId: record?.declarationId,
        spaceId: spaceId,
      });
      if (status) {
        setSubWaybillList(data);
      }
    } catch (err) {
      console.error('获取提单详情接口出错: ', err);
    }
  };

  useEffect(() => {
    if (open && record) {
      getSpaceDetail();
    }
  }, [open, record]);

  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };

  /* 更新分票信息 */
  const updateSplitInfo = async (values: any) => {
    try {
      const { status } = await updateSplitInfoAPI({
        ...values,
        spaceId: spaceId,
      });
      if (status) {
        onClose();
        if (refresh) {
          refresh();
        }
      }
    } catch (err) {
      console.error('更新分票信息接口出错: ', err);
    }
  };

  const getFormValue = () => {
    form.validateFields().then((values) => {
      updateSplitInfo(values);
    });
  };

  const columns: any = [
    {
      title: '运单号',
      dataIndex: 'waybillNO',
    },
    {
      title: '产品',
      dataIndex: 'productName',
    },
    {
      title: '运单状态',
      dataIndex: 'stateDesc',
    },
    {
      title: '报关类型',
      dataIndex: 'declarationTypeDesc',
    },
    {
      title: '箱数',
      dataIndex: 'pieceNum',
    },
    {
      title: '出库实重/kg',
      dataIndex: 'outerActualWeight',
    },
    {
      title: '出库体积/m³',
      dataIndex: 'outerVolume',
    },
    {
      title: '品名',
      dataIndex: 'goodsNames',
    },
    {
      title: '备注',
      dataIndex: 'comment',
      render: (text: any, record: any) => {
        return record?.comment?.content || '-';
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 120,
      fixed: 'right',
      render: () => {
        return (
          <>
            <Button type="link">详情</Button>
          </>
        );
      },
    },
  ];
  return (
    <>
      <Button type="link" onClick={showDrawer}>
        {btnText}
      </Button>
      <Drawer
        title="子单详情"
        placement="right"
        onClose={onClose}
        open={open}
        destroyOnClose
        width="100%"
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={getFormValue}>
              确认
            </Button>
          </Space>
        }
      >
        <div>
          <Form
            form={form}
            name="basic"
            initialValues={{
              shippingMark: 'N/M',
              declarationId: record?.declarationId,
            }}
          >
            <Row>
              <Col span={8}>
                <Form.Item
                  label="子提单号"
                  name="declarationId"
                  rules={[
                    {
                      required: true,
                      message: '必填项不能为空!',
                    },
                  ]}
                >
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="唛头" name="shippingMark">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="件数" name="num">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="包装单位" name="skinUnit">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="毛重/kg" name="weight">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="体积/m³" name="volume">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="英文品名" name="enName">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="hsfCode" name="hsfCode">
                  <Input style={{ width: '90%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <div>
          <Table
            dataSource={subWaybillList?.waybillList || []}
            columns={columns}
            pagination={false}
            rowKey="waybillNO"
          />
        </div>
      </Drawer>
    </>
  );
};
export default DivideVoteModal;
