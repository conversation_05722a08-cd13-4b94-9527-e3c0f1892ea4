import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Modal } from 'antd';
import { updateBillAPI } from '@/services/financeApi';
const { TextArea } = Input;

interface Props {
  detailsList?: any;
  spaceId?: string;
  refreshDetails?: () => void;
}
const ModifyWaybillModal = ({
  detailsList,
  spaceId,
  refreshDetails,
}: Props) => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);

  /* 修改提单 */
  const modifyWaybill = async (val: any) => {
    try {
      const { status } = await updateBillAPI({
        ...val,
        spaceId: spaceId,
      });
      if (status) {
        setIsModalOpen(false);
        if (refreshDetails) {
          refreshDetails();
        }
      }
    } catch (err) {
      console.log(err);
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    form.validateFields().then((val) => {
      modifyWaybill(val);
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (detailsList) {
      form.setFieldsValue({
        price: detailsList.price,
        paymentChargeWeight: detailsList.paymentChargeWeight,
        reason: detailsList.reason,
      });
    }
  }, [detailsList]);

  return (
    <>
      <Button type="link" onClick={showModal}>
        修改
      </Button>
      <Modal
        title="修改空运单价"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={form}>
          <Form.Item
            label="单价"
            name="price"
            rules={[
              {
                required: true,
                message: '必填不能为空',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="重量"
            name="paymentChargeWeight"
            rules={[
              {
                required: true,
                message: '必填不能为空',
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="说明"
            name="reason"
            rules={[
              {
                required: true,
                message: '必填不能为空',
              },
            ]}
          >
            <TextArea rows={2} placeholder="说明" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default ModifyWaybillModal;
