import { getWaybillListDetailAPI } from '@/services/productAndPrice/api';
// import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons';
import { useSearchParams } from '@umijs/max';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import LeftBox from './LeftBox';
import RightBox from './RightBox';
const BillOfLadingDetails = () => {
  const [leftBoxW] = useState(false);
  const [searchParams] = useSearchParams();
  const spaceId:any = useMemo(() => searchParams.get('spaceId'), [searchParams])
  // const [spaceId] = useState<any>(searchParams.get('spaceId'));
  const [detailsList, setDetailsList] = useState<any>({});
  /* 获取提单详情 */
  const getBillOfLadingDetails = async () => {
    try {
      const { status, data } = await getWaybillListDetailAPI({
        spaceId,
      });

      if (status) {
        setDetailsList(data);

      }
    } catch (err) {
      console.error('获取提单详情接口出错: ', err);
    }
  };

  useEffect(() => {
    if (spaceId) {
      getBillOfLadingDetails();
    }
  }, [spaceId]);

  /* 刷新详情 */
  const refreshDetails = () => {
    getBillOfLadingDetails();
  };

  return (
    <div className={classNames('flex', styles['sidebar'])}>
      <div
        className={classNames(
          styles['left-box'],
          leftBoxW ? styles['left-box-display'] : '',
        )}
      >
        <LeftBox leftBoxW={leftBoxW} />
      </div>
      <div className={styles['right-box']} id='boxRight'  >
        {/* <div
          className={styles.btnIcon}
          onClick={() => {
            setLeftBoxW(!leftBoxW);
          }}
        >
          {leftBoxW ? <RightCircleOutlined /> : <LeftCircleOutlined />}
        </div> */}
        <RightBox detailsList={detailsList} spaceId={spaceId} refreshDetails={refreshDetails} />
      </div>

    </div>
  );
};
export default BillOfLadingDetails;
