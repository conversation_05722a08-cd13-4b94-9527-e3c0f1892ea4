.sidebar{
  .left-box-display{
    width: 6px !important;
    transition: all 0.9s;
  }

  .left-box{
    width:130px;
    background: #FBFBFD;
    min-height: 500px;
    border-radius: 0 0 0 8px;
  }

  .right-box{
    width: calc(100% - 130px);
    position: relative;
    border-radius: 0 8px 8px 0;
  }

  .btnIcon{
    color:#00195B;
    margin-top: 6px;
    cursor: pointer;
    position: absolute;
    top: -4px;
    left: 6px;
    z-index: 99999;
  }
  
  .btnIcon:hover{
    color:#1890FF;
    transition: all 0.9s;
  }

  .active{
    color: #00195B;
    font-weight: 500;
    position: relative;
  }

  .active::after{
    content: '';
    width: 3px;
    height: 14px;
    background: #00195B;
    position: absolute;
    top: 12px;
    left: -18px;
  }
}

