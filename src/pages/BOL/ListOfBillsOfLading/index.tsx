// import {
//   // ProFormDateTimeRangePicker,
//   ProTable,
// } from '@ant-design/pro-components';
import {
  Button,
  DatePicker,
  Dropdown,
  Form,
  message,
  Modal,
  Space,
} from 'antd';
import { useRef, useState } from 'react';
// import styles from './index.less';
import { columns1 } from './columns';
import {
  getWaybillListByBillNoAPI,
  modifyBillOfLadingStatusAPI,
} from '@/services/productAndPrice/api';
import { DownOutlined } from '@ant-design/icons';
// import RemarksModal from '@/components/RemarksModal';
// import SearchCard from '@/components/SearchCard';
// import dayjs from 'dayjs';
// import KeywordsSelect from '@/components/KeywordsSelect';
// import AccessCard from '@/AccessCard';
import MrTable from '@/components/MrTable';
import dayjs from 'dayjs';
import { history } from '@umijs/max';
import AccessCard from '@/AccessCard';
import OverseaModal from './OverseaModal';
const ListOfBillsOfLading = () => {
  const actionRef = useRef<any>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [from] = Form.useForm();
  const [menuPropsKey, setMenuPropsKey] = useState<any>('');
  // const [messageApi, contextHolder] = message.useMessage();
  // const [keyword, setKeyword] = useState<string>('');
  // const [searchParams, setSearchParams] = useState<any>({});
  const items = [
    {
      label: '已进港',
      key: 40,
    },
    {
      label: '已启航',
      key: 50,
    },
    {
      label: '已到目的机场/港口',
      key: 60,
    },
    {
      label: '已下船（海运）',
      key: 65,
    },
    {
      label: '清关查验',
      key: 70,
    },
    {
      label: '海外提柜',
      key: 80,
    },
    {
      label: '已还柜',
      key: 90,
    },
  ];

  /* 刷新 */
  // const refresh = () => {
  //   actionRef?.current?.reload();
  // };
  const handleCancel = () => {
    setIsModalOpen(false);
    from.resetFields();
  };
  /* 修改提单状态 */
  const updateWaybillState = async (state: any, values: any) => {
    try {
      let params;
      if (state === '65' && values?.fetchTime) {
        params = {
          state: state,
          spaceId: selectedRowKeys
            ?.map((item: any) => item?.info?.id)
            ?.join(','),
          time: values?.time
            ? dayjs(values.time).format('YYYY-MM-DD HH:mm:ss')
            : '',
          fetchTime: values?.fetchTime
            ? dayjs(values.fetchTime).format('YYYY-MM-DD HH:mm:ss')
            : '',
        };
      } else {
        params = {
          state: state,
          spaceId: selectedRowKeys
            ?.map((item: any) => item?.info?.id)
            ?.join(','),
          time: values?.time
            ? dayjs(values.time).format('YYYY-MM-DD HH:mm:ss')
            : '',
        };
      }
      const { status } = await modifyBillOfLadingStatusAPI(params);
      if (status) {
        message.success('修改提单状态成功');
        actionRef?.current?.reload();
        handleCancel();
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  const handleOk = () => {
    from.validateFields().then((values) => {
      updateWaybillState(menuPropsKey, values);
    });
  };

  const handleMenuClick = (e: any) => {
    // updateWaybillState(e.key);
    if (!selectedRowKeys?.length) return message.warning('请选择运单');
    setIsModalOpen(true);
    setMenuPropsKey(e.key);
  };
  const menuProps = {
    items,
    onClick: handleMenuClick,
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
      tabs: true,
    },
    state: {
      desc: '状态',
      type: 'select',
      range: [
        {
          label: '已出库',
          value: '30',
        },
        {
          label: '已进港',
          value: '40',
        },
        {
          label: '已开船',
          value: '50',
        },
        {
          label: '已还柜',
          value: '90',
        },
        {
          label: '海外提柜',
          value: '80',
        },
        {
          label: '到达目的港',
          value: '60',
        },
        {
          label: '清关查验',
          value: '70',
        },
        {
          label: '已提柜',
          value: '20',
        },
        {
          label: '已下船',
          value: '65',
        },
      ],
    },
    mode: {
      desc: '类型',
      type: 'select',
      range: [
        {
          label: '空运',
          value: '1',
        },
        {
          label: '海运',
          value: '2',
        },
      ],
    },
    departTime: {
      desc: '开船时间',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
      showTime: true,
    },
  };
  return (
    <>
      <Modal
        title="确认时间"
        open={isModalOpen}
        width={500}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={from}>
          <Form.Item
            label="发生时间"
            name="time"
            rules={[{ required: true, message: '请选择发生时间' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>

          {menuPropsKey == 65 && (
            <Form.Item label="预计提柜时间" name="fetchTime">
              <DatePicker style={{ width: '100%' }} showTime />
            </Form.Item>
          )}
        </Form>
      </Modal>
      <MrTable
        keyID="BOL/listOfBillsOfLading"
        columns={columns1}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getWaybillListByBillNoAPI({
            ...params,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.total,
          };
        }}
        filters={filters}
        rowSelectionCablck={(value: any) => {
          setSelectedRowKeys(value);
        }}
        toolBarRender={
          <Space>
            <Dropdown menu={menuProps}>
              <Button>
                <Space>
                  修改提单状态
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
            <AccessCard accessible={[':Waybill:TrackFullAccess']}>
              <Button
                onClick={() => {
                  history.push(
                    `/Waybilladministration/MaintenanceTrajectory?type=1`,
                  );
                }}
              >
                轨迹维护记录
              </Button>
            </AccessCard>
            {/* <OverseaModal /> */}
          </Space>
        }
      />
    </>
  );
};

export default ListOfBillsOfLading;
