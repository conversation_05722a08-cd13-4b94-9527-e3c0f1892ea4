import { Avatar, Space, Table } from 'antd';
import React, { useEffect } from 'react';
import { addedWaybillState, costReconciliationType } from '@/utils/constant';
import { formatTime } from '@/utils/format';
import AddFee from '@/components/AddFee';
import AddCleanFee from '@/components/AddCleanFee';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import { getClearanceList } from '@/services/bol';
import { AVATA_URL } from '@/shared/Enumeration';
import FeesDetail from '@/components/FeesDetail';
import { getListProviderBills } from '@/services/financeApi';
import ViewCost from '@/components/ViewCost';
import ViewWaybillAdd from '@/components/ViewWaybillAdd';

const FeeDetails = (prop: any) => {
  const { details } = prop;

  const columns = [
    {
      title: '账单号',
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: '费用名称',
      dataIndex: 'feeName',
      key: 'feeName',
      width: 200,
    },
    {
      title: '应付/元',
      dataIndex: 'shouldPayAmount',
      key: 'shouldPayAmount',
      width: 200,
    },
    {
      title: '结算状态',
      dataIndex: 'writeOffStateDesc',
      key: 'writeOffStateDesc',
      width: 200,
    },
    {
      title: '成本分摊',
      dataIndex: 'costReconciliationType',
      key: 'costReconciliationType',
      width: 200,
      render: (text: any, row: any) => {
        return costReconciliationType[text];
      },
    },
    {
      title: '影响主提单号',
      dataIndex: ['extraData', 'blno'],
      key: 'blno',
      width: 200,
    },

    {
      title: '运单加收',
      dataIndex: 'addedWaybillState',
      key: 'addedWaybillState',
      width: 300,
      render: (text: any, row: any) => {
        return addedWaybillState[text];
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorUserInfo',
      key: 'creatorUserInfo',
      width: 300,
      render: (text: any, record: any) => {
        return (
          <Space>
            <Avatar
              src={
                record?.creatorUserInfo?.avatar?.includes('http')
                  ? record?.creatorUserInfo?.avatar
                  : `${AVATA_URL}${record?.creatorUserInfo?.avatar}`
              }
            />
            <span>{record?.creatorUserInfo?.name}</span>
          </Space>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 200,
      render: (text: any) => {
        return <span>{formatTime(text)}</span>;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 390,
      fixed: 'right',
      render: (text: any, row: any) => {
        return (
          <>
            <FeesDetail
              title={'费用详情'}
              type="link"
              id={row?.id}
              details={prop?.details}
            />
            <ViewCost title={'查看成本'} type={'link'} id={row?.id} row={row} />
            <ViewWaybillAdd
              title={'运单加收'}
              type={'link'}
              id={row?.id}
              row={row}
            />
          </>
        );
      },
    },
  ];

  return (
    <ProTable
      dataSource={prop?.fees}
      // @ts-ignore
      columns={columns}
      search={false}
      pagination={{
        defaultPageSize: 10,
      }}
      request={async (params: any) => {
        const msg = await getListProviderBills({
          entityId: details?.id,
          counterpartyType: 'Clearance',
          entityType: 'TransportSpace',
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
        });
        return {
          data: msg?.data?.list || [],
          success: msg.status,
          total: msg.data?.total,
        };
      }}
    />
  );
};
export default FeeDetails;
