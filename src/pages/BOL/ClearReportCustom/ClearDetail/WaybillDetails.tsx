import { Table, Tag, Tooltip } from 'antd';
import React from 'react';
import { declarationType, waybillStateMap } from '@/utils/constant';
import { GenerateTab } from '@/utils/format';

const WaybillDetails = (prop: any) => {
  const columns = [
    {
      title: '运单号',
      dataIndex: 'waybillNo',
      key: 'waybillNo',
      width: 200,
    },
    {
      title: '产品',
      align: 'center',
      width: 240,
      dataIndex: 'productName',
      hideInSearch: true,
    },
    {
      title: '运单状态',
      dataIndex: 'state',
      key: 'state',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <Tag color={GenerateTab(waybillStateMap[record?.state])}>
            {waybillStateMap[record?.state]}
          </Tag>
        );
      },
    },
    {
      title: '报关类型',
      dataIndex: 'declarationType',
      key: 'declarationType',
      width: 200,
      render: (text: any, record: any) => {
        return declarationType[text];
      },
    },
    {
      title: '客服',
      dataIndex: 'customerServiceInfo',
      key: 'customerServiceInfo',
      width: 200,
      render: (text: any, row: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-evenly',
            }}
          >
            <img
              style={{ width: 40, height: 40, borderRadius: '50%' }}
              src={`https://static.kmfba.com/${text?.avatar}`}
            />
            <span>{text?.name}</span>
          </div>
        );
      },
    },
    {
      title: '业务员',
      dataIndex: 'salesManInfo',
      key: 'salesmanInfo',
      width: 150,
      render: (_: any, text: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-evenly',
            }}
          >
            <img
              style={{ width: 40, height: 40, borderRadius: '50%' }}
              src={`https://static.kmfba.com/${_?.avatar}`}
            />
            <span>{_?.name}</span>
          </div>
        );
      },
    },
    {
      title: '箱数',
      dataIndex: 'pieceNum',
      key: 'pieceNum',
      width: 200,
    },
    {
      title: '品名',
      dataIndex: 'pieceList',
      key: 'pieceList',
      width: 200,
      ellipsis: true,
      render: (test: any, row: any) => {
        return (
          <Tooltip
            title={test.map((item: any) =>
              item.boxDetailList.map((i: any) => {
                return (
                  <span>
                    {i.name || i.nameEn} {i.totalAmount}
                  </span>
                );
              }),
            )}
            placement="topLeft"
          >
            {test?.map((item: any) =>
              item.boxDetailList.map((i: any) => {
                return (
                  <span>
                    {i.name || i.nameEn} {i.totalAmount}
                  </span>
                );
              }),
            )}
          </Tooltip>
        );
      },
    },
    /*    {
      title: '预配信息预留字段1',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },
    {
      title: '预配信息预留字段2',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },
    {
      title: '预配信息预留字段3',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },*/
  ];
  return (
    <Table dataSource={prop.waybills} columns={columns} scroll={{ x: 1100 }} />
  );
};
export default WaybillDetails;
