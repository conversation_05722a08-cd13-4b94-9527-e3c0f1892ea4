import {
  ProCard,
  ProDescriptions,
  ProFormSelect,
} from '@ant-design/pro-components';
import React, { useEffect, useRef, useState } from 'react';
import {
  Select,
  Button,
  Table,
  Tabs,
  Space,
  Avatar,
  Upload,
  message,
  Modal,
} from 'antd';
import { useLocation } from 'umi';
import WaybillDetails from '@/pages/BOL/ClearReportCustom/ClearDetail/WaybillDetails';
import FeeDetails from '@/pages/BOL/ClearReportCustom/ClearDetail/FeeDetails';
import styles from './index.less';
import {
  delClearanceReceiptFile,
  getClearanceDetail,
  GetCustomGroup,
  SaveCustom,
} from '@/services/bol';
import {
  cleanDeclarationType,
  convertObjectToArray,
  declarationState,
} from '@/utils/constant';
import { formatTime } from '@/utils/format';
import { REQUESTADDRESS_W } from '@/globalData';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { calculateSum } from '@/utils/utils';
import RemarksModal from '@/components/RemarksModal';
import AddCleanFee from '@/components/AddCleanFee';
import { getBrokerList } from '@/services/customer/CustomerHome';
import { ProviderType } from '@/shared/ProviderTypeFn';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import AccessCard from '@/AccessCard';
const { confirm } = Modal;

const Details = () => {
  const location = useLocation();
  const { state }: any = location;
  const actionRef = useRef();
  const [details, setDetails] = useState<any>({});
  console.log('deta', details);
  const [tab, setTab] = useState<any>(1);
  const [toList,IsAccessible] = usePermissionFiltering();
  const isAccessibleFlag = IsAccessible([':Customs:ClearanceFullAccess'])

  const getDetail = async () => {
    try {
      const { status, data } = await getClearanceDetail({
        id: state?.id,
      });
      if (status) {
        console.log(data);
        setDetails(data);
      }
    } catch (err) {
      console.log('获取余额等信息抛出异常: ', err);
    }
  };
  /* 删除 附加费 */
  const handleDelete = (id: any) => {
    delClearanceReceiptFile({ id }).then((res) => {
      if (res.status) {
        message.success('删除成功');
        getDetail();
      }
    });
  };

  const columns = [
    {
      title: '文件',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text: any, record: any) => {
        return (
          <div>
            <a
              href={`https://static.kmfba.com/${record.url}`}
              target="_blank"
              rel="noreferrer"
            >
              {record.fileName}
            </a>
          </div>
        );
      },
    },
    {
      title: '上传人',
      dataIndex: 'uploader',
      key: 'uploader',
      render: (text: any) => {
        return (
          <Space>
            <Avatar src={`https://static.kmfba.com/${text?.avatar}`} />
            <div>{text?.name}</div>
          </Space>
        );
      },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: any) => {
        return <span>{formatTime(text)}</span>;
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 120,
      align: 'center',
      valueType: 'option',
      fixed: 'right',
      hideInSearch: true,
      render: (text: any, record: any) => (
        <a
          style={{ color: 'red' }}
          onClick={() => {
            confirm({
              title: '确认删除该文件吗',
              icon: <ExclamationCircleFilled rev={undefined} />,
              content: `确认删除吗？`,
              onOk() {
                handleDelete(record.id);
              },
              onCancel() {
                //message.info('已取消删除');
              },
            });
          }}
        >
          删除
        </a>
      ),
    },
  ];
  const items: any = [
    {
      key: '1',
      label: `运单详情`,
      children: <WaybillDetails waybills={details?.waybills} />,
    },
    {
      key: '2',
      label: `费用`,
      children: <FeeDetails fees={details?.fees} details={details} />,
    },
  ];

  const onChange = (key: string) => {
    setTab(key);
  };

  const handleEdit = async (params: any) => {
    try {
      const { status } = await SaveCustom({
        ...params,
        id: state?.id,
      });
      if (status) {
        /*修改直接更新*/
        await getDetail();
      }
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };

  const GetSlot = () => {
    if (tab === '2') {
      return (
        <div>
          <span style={{ marginRight: '10px' }}>
            <AccessCard accessible={[':Customs:ClearanceFullAccess']}>
              <RemarksModal
                btnText="批量备注运单"
                btnType="primary"
                refresh={getDetail}
                batch={true}
                record={{
                  id: details?.waybills?.map((item: any) => item.id).join(','),
                }}
              />
            </AccessCard>
          </span>
          <AddCleanFee details={details} getDetail={getDetail} />
        </div>
      );
    } else {
      return (
        <div>
          <AccessCard accessible={[':Customs:ClearanceFullAccess']}>
            <RemarksModal
              btnText="批量备注运单"
              btnType="primary"
              refresh={getDetail}
              batch={true}
              record={{
                id: details?.waybills?.map((item: any) => item.id).join(','),
              }}
            />
          </AccessCard>
          运单出货总实重:
          {details?.waybills
            ? calculateSum(details?.waybills, 'outerActualWeight')
            : '-'}
          kg
        </div>
      );
    }
  };

  useEffect(() => {
    getDetail();
  }, []);
  return (
    <div className={styles.wrap}>
      <ProCard gutter={8} title="基本信息">
        <ProDescriptions
          actionRef={actionRef}
          column={4}
          editable={{
            onSave: async (keypath: any, newInfo) => {
              handleEdit(newInfo);
              return true;
            },
          }}
        >
          <ProDescriptions.Item
            dataIndex={['id']}
            label="状态"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            <Select
              size={'small'}
              disabled={!isAccessibleFlag}
              defaultValue={cleanDeclarationType[details.state]}
              key={details.state}
              options={convertObjectToArray(cleanDeclarationType)}
              onChange={(value) => {
                handleEdit({ state: value });
              }}
            />
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['legalPerson']}
            label="清关行"
            valueType="text"
            editable={false}
          >
            <ProFormSelect
              name="brokerId"
              showSearch
              // rules={[{ required: true, message: '请输入站点' }]}
              noStyle={true}
              disabled={!isAccessibleFlag}
              labelCol={{ span: 5 }}
              wrapperCol={{ span: 12 }}
              style={{ width: '200px' }}
              key={details?.brokerId}
              request={async () => {
                const { status, data } = await getBrokerList({
                  type: ProviderType.Clearance.getCode(),
                  len: 999,
                });
                if (status) {
                  return data?.list?.map((item: any) => {
                    return {
                      label: item.name,
                      value: item.id,
                    };
                  });
                }
                return [];
              }}
              fieldProps={{
                onChange: (e) => {
                  handleEdit({ brokerId: e });
                },
                defaultValue: details?.brokerId,
                size: 'small',
                filterOption: false,
              }}
            ></ProFormSelect>
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="blno"
            label="主提单号"
            valueType="text"
            className={styles.require}
            editable={false}
          >
            {details.blno}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="no"
            label="清关单号"
            valueType="text"
            editable={isAccessibleFlag?null:false}
          >
            {details.no}
          </ProDescriptions.Item>
        </ProDescriptions>
      </ProCard>
      <ProCard
        style={{ marginBlockStart: 8 }}
        gutter={8}
        title={'清关回传文件'}
        extra={
          <Upload
            key={'upload'}
            showUploadList={false}
            disabled={!isAccessibleFlag}
            name="receiptFiles"
            action={`${REQUESTADDRESS_W}/customs/clearance/receiptFile/upload?service_id=TMS`}
            headers={{ token: localStorage.getItem('token') || '' }}
            data={{ id: state?.id }}
            multiple={true}
            onChange={() => {
              getDetail();
            }}
          >
            <Button>上传文件</Button>
          </Upload>
        }
      >
        <Table dataSource={details.receiptFiles} columns={columns} />
      </ProCard>
      <ProCard style={{ marginBlockStart: 8 }}>
        <Tabs
          defaultActiveKey="1"
          onChange={onChange}
          items={items}
          tabBarExtraContent={GetSlot()}
        />
      </ProCard>
    </div>
  );
};
export default Details;
