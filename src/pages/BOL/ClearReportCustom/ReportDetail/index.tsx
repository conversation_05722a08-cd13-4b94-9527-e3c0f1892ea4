import {
  ProCard,
  ProDescriptions,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useEffect, useRef, useState } from 'react';
import { Select, Button, Table, Tabs, message, Modal } from 'antd';
import { useLocation } from 'umi';
import WaybillDetails from '@/pages/BOL/ClearReportCustom/ReportDetail/WaybillDetails';
import FeeDetails from '@/pages/BOL/ClearReportCustom/ReportDetail/FeeDetails';
import { formatTime } from '@/utils/format';
import styles from './index.less';
import AddFee from '@/components/AddFee';
import {
  delPrepareFileFile,
  delReceiptFileFile,
  GetCustomGroup,
  getDeclarationAPI,
  GetDeclarationDetail,
  getDeclarationList,
  GetDeclarationList,
  ReadyFileApprove,
  SaveDeclaration,
} from '@/services/bol';
import {
  convertObjectToArray,
  declarationState,
  declarationType,
  prepareFileState,
} from '@/utils/constant';
import MyUpload from '@/components/MyUpload';
import { addRemarkAPI } from '@/services/productAndPrice/api';
import RemarksModal from '@/components/RemarksModal';
import { calculateSum } from '@/utils/utils';
import { getBrokerList } from '@/services/customer/CustomerHome';
import { ProviderType } from '@/shared/ProviderTypeFn';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import AccessCard from '@/AccessCard';

const Details = () => {
  const location = useLocation();
  const { state }: any = location;
  const actionRef = useRef();
  const [details, setDetails] = useState<any>({});
  const [tab, setTab] = useState<any>(1);
  const [toList, IsAccessible] = usePermissionFiltering();
  const isAccessibleFlag = IsAccessible([':Customs:DeclarationFullAccess'])
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [clearance, setClearance] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([])
  const getDetail = async () => {
    try {
      const { status, data } = await GetDeclarationDetail({
        id: state?.id,
      });
      if (status) {
        setDetails(data);
      }
    } catch (err) {
      console.log('获取信息抛出异常: ', err);
    }
  };
  const handleDelete = (id: any, type: number) => {
    /*删除回传文件*/
    if (type === 2) {
      delReceiptFileFile({ id, type: 2 }).then((res) => {
        if (res.status) {
          message.success('删除成功');
          getDetail();
        }
      });
    } else {
      /* 删除 准备文件 */
      delPrepareFileFile({ id, type: 1 }).then((res) => {
        if (res.status) {
          message.success('删除成功');
          getDetail();
        }
      });
    }
  };

  const BackhaulColumns: any = [
    {
      title: '文件',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text: any, record: any) => {
        return (
          <div>
            <a
              href={`https://static.kmfba.com/${record.url}`}
              target="_blank"
              rel="noreferrer"
            >
              {record.fileName}
            </a>
          </div>
        );
      },
    },
    {
      title: '上传人',
      dataIndex: ['uploader', 'name'],
      key: 'uploaderId',
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: any) => {
        return <div>{formatTime(text)}</div>;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 200,
      fixed: 'right',
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              danger
              onClick={() => {
                handleDelete(record.id, 2);
              }}
            >
              删除
            </Button>
          </>
        );
      },
    },
  ];

  const columns = [
    {
      title: '文件',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text: any, record: any) => {
        return (
          <div>
            <a
              href={`https://static.kmfba.com/${record.url}`}
              target="_blank"
              rel="noreferrer"
            >
              {record.fileName}
            </a>
          </div>
        );
      },
    },
    {
      title: '审批状态',
      dataIndex: 'state',
      key: 'state',
      render: (text: any, row: any) => {
        console.log('text', text);
        return (
          <Select
            options={convertObjectToArray(prepareFileState, 'number')}
            onChange={(value: any) => {
              ReadyFileApprove({ id: row?.id, type: value }).then(() =>
                getDetail(),
              );

              console.log('value', value);
            }}
            value={text}
            style={{ minWidth: '80px' }}
          />
        );
      },
    },
    {
      title: '上传人',
      dataIndex: ['uploader', 'name'],
      key: 'uploaderId',
      render: (text: any, row: any) => {
        console.log('row', row);
        //1客户2员工
        return (
          <div>
            {row?.source === '1' ? '客户' : '客服'}-{text}
          </div>
        );
      },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: any) => {
        return <div>{formatTime(text)}</div>;
      },
    },

    {
      title: '最新审批人',
      dataIndex: ['auditor', 'name'],
      key: 'auditor',
    },
    {
      title: '最新审批时间',
      dataIndex: 'auditTime',
      key: 'auditTime',
    },
  ];

  const items: any = [
    {
      key: '1',
      label: `运单详情`,
      children: <WaybillDetails waybills={details?.waybills} getDetail={getDetail} />,
    },
    {
      key: '2',
      label: `费用`,
      children: <FeeDetails fees={details?.fees} details={details} />,
    },
  ];

  const onChange = (key: string) => {
    setTab(key);
  };

  const handleEdit = async (params: any) => {
    try {
      const { status } = await SaveDeclaration({
        ...params,
        id: state?.id,
      });
      if (status) {
        await getDetail();
      }
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };
  const GetSlot = () => {
    if (tab === '2') {
      return (
        <div>
          <span style={{ marginRight: '10px' }}>
            <AccessCard accessible={[':Customs:DeclarationFullAccess']}>
              <RemarksModal
                btnText="批量备注运单"
                btnType="primary"
                refresh={getDetail}
                batch={true}
                record={{
                  id: details?.waybills?.map((item: any) => item.id).join(','),
                }}
              />
            </AccessCard>
          </span>
          <AddFee details={details} getDetail={getDetail} />
        </div>
      );
    } else {
      return (
        <div>
          {details?.waybills?.map((item: any) => (item?.declarationType)).includes(30) && <Button style={{ marginRight: '10px' }} onClick={() => {
            setIsModalOpen(true)
          }}>添加报关单</Button>}
          <AccessCard accessible={[':Customs:DeclarationFullAccess']}>
            <RemarksModal
              btnText="批量备注运单"
              btnType="primary"
              refresh={getDetail}
              batch={true}
              record={{
                id: details?.waybills?.map((item: any) => item.id).join(','),
              }}
            />
          </AccessCard>
          <span style={{ marginLeft: '10px' }}>
            运单出货总实重
            {details?.waybills
              ? calculateSum(details?.waybills, 'outerActualWeight')
              : ''}
            kg
          </span>

        </div>
      );
    }
  };

  useEffect(() => {
    getDetail();
  }, []);
  useEffect(() => {
    if (isModalOpen) {
      GetDeclaration()
    }
  }, [isModalOpen])
  const GetDeclaration = async () => {
    try {
      const { status, data } = await getDeclarationList({
        condition: {
          states: {
            "value": "1,2,3,4",
          },
          type: {
            value: '30'
          }
        },
        excludeId: {
          value: state?.id
        },
        len: 100,
        start: 0,

      })
      if (status) {
        setClearance(data?.list)
        // console.log(data, 'datadatadata');
      }
    } catch (error) {

    }
  }
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (Keys: any, selectedRows: any) => {
      setSelectedRowKeys(Keys)
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },

  };
  // 合并报关
  const combineClearance = async () => {
    // if (selectedRowKeys?.length < 2) return message.warning('至少勾选两个报关单')
    const { status } = await getDeclarationAPI({
      ids: selectedRowKeys?.join(','),
      mainId: state?.id
    })
    if (status) {
      message.success('操作成功')
      setIsModalOpen(false)
      setSelectedRowKeys([])
      getDetail();
      // actionRef.current?.reload()

    }
  }
  return (
    <div className={styles.wrap}>
      <ProCard
        gutter={8}
        title="基本信息"
      /* extra={<Button type={'primary'}>更新</Button>}*/
      >
        <ProDescriptions
          actionRef={actionRef}
          column={3}
          editable={{
            onSave: async (keypath: any, newInfo) => {
              handleEdit(newInfo);
              return true;
            },
          }}
        >
          <ProDescriptions.Item
            dataIndex="state"
            label="状态"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            <Select
              size={'small'}
              disabled={!isAccessibleFlag}
              defaultValue={declarationState[details.state]}
              key={details.state}
              options={convertObjectToArray(declarationState)}
              onChange={(value) => {
                handleEdit({ state: value });
              }}
            />
          </ProDescriptions.Item>

          <ProDescriptions.Item
            dataIndex="blno"
            label="主提单号"
            valueType="text"
            className={styles.require}
            editable={false}
          >
            {details?.blno}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['legalPerson']}
            label="报关行"
            valueType="text"
            editable={false}
          >
            <ProFormSelect
              name="brokerId"
              showSearch
              // rules={[{ required: true, message: '请输入站点' }]}
              labelCol={{ span: 5 }}
              wrapperCol={{ span: 12 }}
              style={{ width: 200 }}
              noStyle={true}
              key={details?.brokerId}
              disabled={!isAccessibleFlag}
              request={async () => {
                const { status, data } = await getBrokerList({
                  type: ProviderType.Declaration.getCode(),
                  len: 999,
                });
                if (status) {
                  return data?.list?.map((item: any) => {
                    return {
                      label: item.name,
                      value: item.id,
                    };
                  });
                }
                return [];
              }}
              fieldProps={{
                onChange: (e) => {
                  handleEdit({ brokerId: e });
                },
                defaultValue: details?.brokerId,
                size: 'small',
                filterOption: false,
              }}
            ></ProFormSelect>
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['declarationType']}
            label="报关类型"
            valueType="text"
            className={styles.require}
            editable={false}
          >
            {declarationType[details.type]}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['subBlno']}
            label="子提单号"
            valueType="text"
            editable={false}
          >
            {details.subBlno}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="businessName"
            label="经营单位"
            valueType="text"
            editable={isAccessibleFlag ? null : false}
          >
            {details?.businessName}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['no']}
            label="报关单号"
            valueType="text"
            editable={isAccessibleFlag ? null : false}
          >
            {details.no}
          </ProDescriptions.Item>
        </ProDescriptions>
      </ProCard>
      <ProCard
        style={{ marginBlockStart: 8 }}
        gutter={8}
        title={'报关准备文件'}
        extra={
          <MyUpload
            disabled={!isAccessibleFlag}
            name="prepareFiles"
            action={'/customs/declaration/prepareFile/upload'}
            id={state?.id}
            getDetail={getDetail}
          />
        }
      >
        <Table dataSource={details?.prepareFiles} columns={columns} />
      </ProCard>
      <ProCard
        style={{ marginBlockStart: 8 }}
        gutter={8}
        title={'报关回传文件'}
        extra={
          <MyUpload
            disabled={!isAccessibleFlag}
            name="receiptFiles"
            action={'/customs/declaration/receiptFile/upload'}
            id={state?.id}
            getDetail={getDetail}
          />
        }
      >
        <Table dataSource={details?.receiptFiles} columns={BackhaulColumns} />
      </ProCard>
      <ProCard style={{ marginBlockStart: 8 }}>
        <Tabs
          defaultActiveKey="1"
          onChange={onChange}
          items={items}
          tabBarExtraContent={GetSlot()}
        />
      </ProCard>
      <Modal title='添加报关单' onOk={combineClearance} width={800} onCancel={() => setIsModalOpen(false)} open={isModalOpen}>
        <Table rowSelection={{
          ...rowSelection
        }}
          rowKey={'id'} dataSource={clearance} columns={[{
            title: 'id',
            dataIndex: 'id',
            width: 200,
            // hideInSearch: true,
          },
          {
            title: '报关单号',
            dataIndex: 'no',
            // hideInSearch: true,
            width: 120,
          },
          {
            title: '运单号',
            dataIndex: 'waybills',
            // hideInSearch: true,
            width: 120,
            render: (text: any, record: any) => {
              return record?.waybills.length > 0
                ? record?.waybills
                  .map((item: any) => item?.waybillNo) // 单行箭头函数会自动返回结果
                  .join(',')
                : '-';
            },
          },]} />
      </Modal>
    </div>
  );
};
export default Details;
