import { message, Modal, Radio, Space, Table, Tag, Tooltip } from 'antd';
import React, { useState } from 'react';
import { GenerateTab } from '@/utils/format';
import { declarationType, waybillStateMap } from '@/utils/constant';
import { history } from '@@/core/history';
import { removeWaybillAPI } from '@/services/bol';

const WaybillDetails = (props: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [declaration, setDeclaration] = useState('')
  const [waybillRecordId, setWaybillRecordId] = useState('')
  const columns = [
    {
      title: '运单号',
      dataIndex: 'waybillNo',
      key: 'waybillNo',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <a
            onClick={() =>
              history.push(`/Waybilladministration/detail`, record)
            }
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '产品',
      align: 'center',
      width: 240,
      dataIndex: 'productName',
      hideInSearch: true,
    },
    {
      title: '运单状态',
      dataIndex: 'state',
      key: 'state',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <Tag color={GenerateTab(waybillStateMap[record?.state])}>
            {waybillStateMap[record?.state]}
          </Tag>
        );
      },
    },
    {
      title: '报关类型',
      dataIndex: 'declarationType',
      key: 'declarationType',
      width: 200,
      render: (text: any, record: any) => {
        return declarationType[text];
      },
    },
    {
      title: '客服',
      dataIndex: 'customerServiceInfo',
      key: 'customerServiceInfo',
      align: 'center',
      width: 120,
      render: (text: any, row: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-evenly',
            }}
          >
            <img
              style={{ width: 20, height: 20, borderRadius: '50%' }}
              src={`https://static.kmfba.com/${text?.avatar}`}
            />
            <span>{text?.name}</span>
          </div>
        );
      },
    },
    {
      title: '业务员',
      dataIndex: 'salesManInfo',
      key: 'salesmanInfo',
      align: 'center',
      width: 120,
      render: (_: any, text: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-evenly',
            }}
          >
            <img
              style={{ width: 20, height: 20, borderRadius: '50%' }}
              src={`https://static.kmfba.com/${_?.avatar}`}
            />
            <span>{_?.name}</span>
          </div>
        );
      },
    },
    {
      title: '箱数',
      dataIndex: 'pieceNum',
      key: 'pieceNum',
      width: 200,
    },
    {
      title: '品名',
      dataIndex: 'pieceList',
      key: 'pieceList',
      width: 200,
      ellipsis: true,
      render: (test: any, row: any) => {
        return (
          <Tooltip
            title={test?.map((item: any) =>
              item.boxDetailList.map((i: any) => {
                return (
                  <span>
                    {i.name || i.nameEn} {i.totalAmount}
                  </span>
                );
              }),
            )}
            placement="topLeft"
          >
            {test?.map((item: any) =>
              item.boxDetailList.map((i: any) => {
                return (
                  <span>
                    {i.name || i.nameEn} {i.totalAmount}
                  </span>
                );
              }),
            )}
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      width: 180,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_: any, recode: any) => {
        setWaybillRecordId(recode?.id)
        return <>{recode?.declarationType == 30 && <a onClick={() => {
          setIsModalOpen(true)
        }}>删除</a>}</>
      }
    }
    /*    {
      title: '预配信息预留字段1',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },
    {
      title: '预配信息预留字段2',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },
    {
      title: '预配信息预留字段3',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },*/
  ];
  const handleOk = async () => {
    if (!declaration) return message.warning('请选择报关类型')
    try {
      const { status } = await removeWaybillAPI({
        waybillRecordId,
        declarationType: declaration
      })
      if (status) {
        message.success('操作成功')
        setIsModalOpen(false)
        props?.getDetail()
      }
    } catch (error) {

    }
  }
  return (
    <>
      <Table dataSource={props.waybills} columns={columns} scroll={{ x: 1100 }} />
      <Modal destroyOnClose afterClose={() => setDeclaration('')} title="选择报关类型" open={isModalOpen} onOk={handleOk} onCancel={() => {
        setIsModalOpen(false)
      }}>
        <Radio.Group style={{ marginTop: '10px' }} onChange={(e) => {
          setDeclaration(e?.target?.value)
        }} >
          <Space direction="vertical">
            <Radio value={20}>单独报关</Radio>
            <Radio value={10}>非单独报关</Radio>
          </Space>

        </Radio.Group>
      </Modal>
    </>
  );
};
export default WaybillDetails;
