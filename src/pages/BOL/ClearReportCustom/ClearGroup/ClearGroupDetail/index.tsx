import {
  ProCard,
  ProDescriptions,
  ProFormSelect,
} from '@ant-design/pro-components';
import React, { useEffect, useRef, useState } from 'react';
import { Select, Button, Row, Col, Form, InputNumber, message } from 'antd';

import { useLocation } from 'umi';
import styles from './index.less';
import {
  delCustomFile,
  getClearanceDetail,
  GetCustomGroupDetail,
  saveBrokerDetail,
} from '@/services/bol';
import { convertObjectToArray, currency_map } from '@/utils/constant';
import { airLineListAPI } from '@/services/booking';
const ClearGroupDetail = (props: any) => {
  const { OutTableData } = props;
  const [form] = Form.useForm();
  const location = useLocation();
  const { state }: any = location;
  /*详情数据*/
  const [detail, setDetail] = useState<any>();
  /*第一次提交以后新增变成修改*/
  const [editId, setEditId] = useState<any>();
  const getDetail = async () => {
    console.log('OutTableData', OutTableData);
    setDetail(OutTableData);
  };

  const columns = [
    {
      title: '文件',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '上传人',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: '上传时间',
      dataIndex: 'address',
      key: 'address',
    },
  ];
  /*  const handleEdit = async (params: any) => {
    console.log(params);
    try {
      const { status, data } = await saveBrokerDetail({
        ...params,
        id: OutTableData?.id || editId,
        type: 1,
      });
      if (status) {
        message.success('保存成功');
        setEditId(data?.id);
      }
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  }*/ /*保存修改*/
  const saveEdit = async (params: any) => {
    try {
      let param = { ...params };
      console.log(param);
      if (param.clearanceFee) {
        param.clearanceFee.id = detail?.clearanceFee?.id;
        param.clearanceFee.brokerId = OutTableData?.id || editId;
      }
      const { status, data } = await saveBrokerDetail({
        ...param,
      });
      if (status) {
        message.success('保存成功');
      }
      getDetail();
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };
  /*选择后缀*/
  const selectAfter = (label: any, name: any) => {
    return (
      <Form.Item noStyle name={['clearanceFee', 'fee', name]}>
        <Select
          defaultValue={currency_map[label]}
          key={currency_map[label]}
          style={{ width: '85px' }}
          options={convertObjectToArray(currency_map, 'number')}
        ></Select>
      </Form.Item>
    );
  };
  /*  useEffect(() => {
    if (editId || state?.id) {
      getDetail();
    }
  }, [state?.id, editId]);*/
  useEffect(() => {
    getDetail();
  }, []);
  return (
    <div className={styles.wrap}>
      <Form onFinish={saveEdit} form={form} initialValues={{ ...OutTableData }}>
        <ProCard
          style={{ marginBlockStart: 8 }}
          gutter={8}
          title={<strong>费用</strong>}
          extra={
            <Button type={'primary'} htmlType="submit">
              保存
            </Button>
          }
        >
          <Row>
            <Col span={6}>
              <Form.Item
                name={['clearanceFee', 'fee', 'fixedFee']}
                label="固定清关费"
              >
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.fixedFeeType,
                    'fixedFeeType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.fixedFee}
                  key={detail?.clearanceFee?.fee?.fixedFee}
                  step="0.01"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={['clearanceFee', 'fee', 'multiBrandNameFee']}
                label="多品名费"
              >
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.multiBrandNameFeeType,
                    'multiBrandNameFeeType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.multiBrandNameFee}
                  key={detail?.clearanceFee?.fee?.multiBrandNameFee}
                  step="0.01"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name={['clearanceFee', 'fee', 'fda']} label="FDA认证">
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.fdaType,
                    'fdaType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.fda}
                  key={detail?.clearanceFee?.fee?.fda}
                  step="0.01"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={['clearanceFee', 'fee', 'isf']}
                label="ISF申报费用"
              >
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.isfType,
                    'isfType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.isf}
                  key={detail?.clearanceFee?.fee?.isf}
                  step="0.01"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item
                name={['clearanceFee', 'fee', 'destructionFee']}
                label="销毁费"
              >
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.destructionFeeType,
                    'destructionFeeType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.destructionFee}
                  key={detail?.clearanceFee?.fee?.destructionFee}
                  step="0.01"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={['clearanceFee', 'fee', 'inspectionFee']}
                label="查验费"
              >
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.inspectionFeeType,
                    'inspectionFeeType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.inspectionFee}
                  key={detail?.clearanceFee?.fee?.inspectionFee}
                  step="0.01"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={['clearanceFee', 'fee', 'surcharge']}
                label="清关杂费"
              >
                <InputNumber
                  addonAfter={selectAfter(
                    detail?.clearanceFee?.fee?.surchargeType,
                    'surchargeType',
                  )}
                  style={{ width: '200px' }}
                  //defaultValue={detail?.clearanceFee?.fee?.surcharge}
                  key={detail?.clearanceFee?.fee?.surcharge}
                  step="0.01"
                />
              </Form.Item>
            </Col>
          </Row>
        </ProCard>
      </Form>
    </div>
  );
};
export default ClearGroupDetail;
