.wrap{
  .title{
    :global(.ant-descriptions-item-container::before){
      background: #0068bf;
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: white;
      content: "清" !important;
      margin-right: 5px;
    }
  }
  .switch{
    position: relative;
  }
  :global(.ant-descriptions-item-label){
    color: #aeaeae !important;
  }
  .require{
    :global( .ant-descriptions-item-label::before){
      display: inline-block !important;
      margin-inline-end: 4px !important;
      color: #ff4d4f !important;
      font-size: 14px !important;
      font-family: SimSun,sans-serif;
      line-height: 1;
      content: "*" !important;
    }
  }
}
