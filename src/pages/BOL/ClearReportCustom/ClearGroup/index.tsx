import { ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';
import { history } from '@umijs/max';
import service from '@/services/home';
import { GenerateTab } from '@/utils/format';
import { Avatar, Button, Row, Switch, Tag } from 'antd';
import MySearch from '@/components/MyProTable/MySearch';
import {
  editBrokerState,
  getBrokerList,
} from '@/services/customer/CustomerHome';
import {
  isEnabled,
  reconciliationPeriod,
  servicePeriod,
} from '@/utils/constant';
import ServiceProvider from '@/pages/ProductAndPrice/ChannelAgent/ServiceProvider';
import { ProviderType } from '@/shared/ProviderTypeFn';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import AccessCard from '@/AccessCard';
import MrTable from '@/components/MrTable';
const { getClientListAPI } = service.UserHome;

const ClearGroup = () => {
  const actionRef = useRef<any>();
  const [toList, IsAccessible] = usePermissionFiltering();
  /* 刷新列表 */
  const refresh = () => {
    actionRef.current?.reload();
  };
  const handleSwitch = async (checked: any, record: any) => {
    await editBrokerState({
      id: record.id,
      enabled: checked ? 1 : 0,
    });
  };
  const columns: any = [
    /*   {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <MySearch
            placeholder="请输入客户名称,ID搜索"
            allowClear
            enterButton="搜索"
            style={{ width: '300px', background: '#FBFBFB' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },*/
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '电话',
      dataIndex: 'contactPhone',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'contactEmail',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '结算周期',
      dataIndex: 'reconciliationPeriod',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return servicePeriod[record?.reconciliationPeriod] || '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return (
          <Switch
            defaultChecked={text}
            disabled={!IsAccessible([':Provider:UpdateAccess'])}
            onChange={(checked) => {
              handleSwitch(checked, record);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      align: 'left',
      width: 220,
      fixed: 'right',
      hideInSearch: true,
      render: (text: any, record: any) => [
        <ServiceProvider
          btnType="link"
          btnText="详情"
          activeKey={record.type}
          refresh={refresh}
          record={record}
          key="detail"
        />,
      ],
    },
  ];

  return (
    // <ProTable<any>
    //   columns={columns}
    //   actionRef={actionRef}
    //   // cardBordered
    //   request={async (params: any) => {
    //     const msg = await getBrokerList({
    //       start: (params.current - 1) * params.pageSize,
    //       len: params.pageSize,
    //       keyword: params.keyword,
    //       type: ProviderType.Clearance.getCode(),
    //     });
    //     return {
    //       data: msg?.data?.list || [],
    //       success: msg.status,
    //       total: msg.data?.total,
    //     };
    //   }}
    //   rowKey="id"
    //   search={{
    //     labelWidth: 'auto',
    //     optionRender: false,
    //   }}
    //   options={{
    //     fullScreen: true,
    //   }}
    //   pagination={{
    //     pageSize: 10,
    //   }}
    //   scroll={{ x: 1200 }}
    //   toolBarRender={() => [
    // <AccessCard key={1} accessible={[':Provider:UpdateAccess']}>
    //   <ServiceProvider
    //     btnText="创建清关行"
    //     key="create"
    //     providerType={ProviderType.Clearance as any}
    //   />
    // </AccessCard>,
    //   ]}
    // />
    <MrTable
      keyID="BOL/ClearReportCustom/clear"
      columns={columns}
      request={async (params: any, action: any) => {
        actionRef.current = action;
        // console.log('tableactiveKey',activeKey);
        let msg = await getBrokerList({
          ...params,
          type: ProviderType.Clearance.getCode(),
        });
        return {
          data: msg?.data?.list || [],
          success: msg?.status,
          total: msg?.data?.total,
        };
      }}
      filters={{}}
      toolBarRender={
        <AccessCard key={1} accessible={[':Provider:UpdateAccess']}>
          <ServiceProvider
            btnText="创建清关行"
            key="create"
            providerType={ProviderType.Clearance as any}
          />
        </AccessCard>
      }
    />
  );
};
export default ClearGroup;
