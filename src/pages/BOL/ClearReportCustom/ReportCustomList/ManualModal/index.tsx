import {
  ModalForm,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Checkbox, Form, message, Select } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import { rePushWaybill } from '@/services/Waybilladministration';
import { updateBroker } from '@/services/bol';
import { getBrokerList } from '@/services/customer/CustomerHome';
import styles from '@/pages/DataManagement/TagManagement/AddTags/index.less';
const ManualModal = (props: any) => {
  const { record, btnText, myList, type, refresh } = props;

  const ids = record?.map((item: any) => item?.id);
  console.log('ids', ids);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const showModal = () => {
    /*   if (!myList?.length) {
      console.log('由于myList为空而关闭弹窗');
      setModalVisit(false);
      message.error(`请选择需要${btnText}的运单`);
    } else {
      setModalVisit(true);
    }*/
    setModalVisit(true);
  };

  /* 提交 */
  const onFinish = async (values: any) => {
    //退件退款
    const { data, status } = await updateBroker({
      brokerId: values.brokerId,
      ids: ids?.join(','),
    });
    if (status) {
      message.success('操作成功');
      setModalVisit(false);
      props.refresh();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };

  return (
    <>
      <Button onClick={showModal} type={'primary'}>
        {btnText}
      </Button>
      <ModalForm
        title={btnText}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        open={modalVisit}
        layout="horizontal"
        submitTimeout={2000}
        width={'528px'}
        className={styles['warp-from']}
        onFinish={onFinish}
      >
        <div style={{ marginTop: '40px', marginBottom: '40px' }}>
          <ProFormSelect
            name="brokerId"
            showSearch
            label={'报关行'}
            rules={[{ required: true, message: '请输入报关行' }]}
            colon={false}
            request={async () => {
              const { status, data } = await getBrokerList({
                type: 80,
                len: 999,
                enabled: 1
              });
              if (status) {
                return data?.list?.map((item: any) => {
                  return {
                    label: item.name,
                    value: item.id,
                  };
                });
              }
              return [];
            }}
            fieldProps={{
              filterOption: false,
            }}
          ></ProFormSelect>
        </div>
      </ModalForm>
    </>
  );
};
export default ManualModal;
