import { ProFormSelect, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';
import { history } from '@umijs/max';
import service from '@/services/home';
import { Button, message } from 'antd';
import MySearch from '@/components/MyProTable/MySearch';
import MySelect from '@/components/MyProTable/MySelect';
import MyInput from '@/components/MyProTable/MyInput';
import { getDeclarationAPI, GetDeclarationDetail, getDeclarationList, getDownloadAPI } from '@/services/bol';
import {
  convertObjectToArray,
  declarationState,
  declarationType,
} from '@/utils/constant';
import _ from 'lodash';
import { calculateSum } from '@/utils/utils';
import { getBrokerList } from '@/services/customer/CustomerHome';
import AccessCard from '@/AccessCard';
import MrTable from '@/components/MrTable';
import ManualModal from '@/pages/BOL/ClearReportCustom/ReportCustomList/ManualModal';
import MyUpload from '@/components/MyUpload';
const { getClientListAPI } = service.UserHome;
import { saveAs } from 'file-saver';
import LogDashboard from '@/components/LogDashboard';

const ReportCustomList = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const actionRef = useRef<any>();
  /*查询参数*/
  const [params, setParams] = useState<any>({});
  const rowSelectionCablck = (data: any) => {
    setSelectedRowKeys(data);
  };
  // const getDetail = async () => {
  //   try {
  //     const { status, data } = await GetDeclarationDetail({
  //       id: 'state?.id',
  //     });
  //     if (status) {
  //       // setDetails(data);
  //     }
  //   } catch (err) {
  //     console.log('获取信息抛出异常: ', err);
  //   }
  // };
  // 报关行
  const getDepartment = async (keyword: any) => {
    // const { status, data } = await getModuleList({
    //   keyword,
    // });
    // if (status) {
    //   return data?.list?.map((item: any) => ({
    //     label: item?.desc,
    //     value: item?.name,
    //   }));
    // }
    const { status, data } = await getBrokerList({
      type: 256,
      keyword,
      len: 999,
    });
    if (status) {
      return data?.list?.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    }
  };
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <MySearch
            placeholder="请输入客户名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px', background: '#FBFBFB' }}
            onSearch={(value: any) => setParams({ ...params, keyword: value })}
          />
        );
      },
    },
    {
      dataIndex: 'type',
      hideInTable: true,
      title: '报关类型',
      renderFormItem: () => {
        return (
          <MySelect
            placeholder="请选择"
            allowClear
            style={{ width: '200px' }}
            onChange={(value: any) => {
              setParams({ ...params, type: value });
            }}
            options={convertObjectToArray(declarationType, 'number')}
          />
        );
      },
    },
    {
      dataIndex: 'state',
      hideInTable: true,
      title: '报关状态',
      renderFormItem: () => {
        return (
          <MySelect
            placeholder="请选择"
            allowClear
            style={{ width: '200px' }}
            onChange={(value: any) => {
              setParams({ ...params, state: value });
            }}
            options={convertObjectToArray(declarationState, 'number')}
          />
        );
      },
    },

    {
      dataIndex: 'brokerId',
      hideInTable: true,
      title: '报关行',
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="brokerId"
            showSearch
            // rules={[{ required: true, message: '请输入站点' }]}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 12 }}
            style={{ width: 200 }}
            request={async () => {
              const { status, data } = await getBrokerList({
                type: 80,
                len: 999,
              });
              if (status) {
                return data?.list?.map((item: any) => {
                  return {
                    label: item.name,
                    value: item.id,
                  };
                });
              }
              return [];
            }}
            fieldProps={{
              onChange: (value) => {
                setParams({ ...params, brokerId: value });
              },
              filterOption: false,
            }}
          ></ProFormSelect>
        );
      },
    },
    {
      dataIndex: 'blno',
      hideInTable: true,
      title: '主提单号',
      renderFormItem: () => {
        return <MyInput placeholder="请输入" allowClear />;
      },
    },
    {
      title: 'id',
      dataIndex: 'id',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '报关单号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报关类型',
      dataIndex: 'type',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return declarationType[record?.type];
      },
    },
    {
      title: '报关状态',
      dataIndex: 'state',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return declarationState[record?.state];
      },
    },
    {
      title: '客户名称',
      dataIndex: 'clientName',
      width: 160,
    },
    {
      title: '运单号',
      dataIndex: 'waybills',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return record?.waybills.length > 0
          ? record?.waybills
            .map((item: any) => item?.waybillNo) // 单行箭头函数会自动返回结果
            .join(',')
          : '-';
      },
    },
    {
      title: '运单详情',
      dataIndex: 'deposit',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return (
          <div>
            <div>总件数：{calculateSum(record?.waybills, 'pieceNum')}</div>
            <div>
              总实重：{calculateSum(record?.waybills, 'signActualWeight')}kg
            </div>
            <div>
              总计费重：{calculateSum(record?.waybills, 'chargeWeight')}kg
            </div>
          </div>
        );
      },
    },
    {
      title: '主提单号',
      dataIndex: 'blno',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '子提单号',
      dataIndex: 'subBlno',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '预配信息预留字段',
      dataIndex: 'salesman',
      hideInSearch: true,
      width: 150,
      ellipsis: true,
      render: (text: any, record: any) => {
        return (
          <div>
            <div>仓位:{record?.spaceId}</div>
            <div>航班:{record?.vesselCode}</div>
            <div>箱号:{record?.containerNo}</div>
          </div>
        );
      },
    },
    {
      title: '修改人员',
      dataIndex: 'prePaymentAmount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '经营单位',
      dataIndex: 'businessName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报关行',
      dataIndex: 'brokerName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报关草案等文件',
      dataIndex: 'prepareFiles',
      hideInSearch: true,
      width: 120,
      ellipsis: true,
      render: (text: any, record: any) => {
        if (record?.prepareFiles?.length > 1) {
          return <a>{record?.prepareFiles[record?.prepareFiles?.length - 1]?.fileName}</a>
        } else {
          return record?.prepareFiles?.map((item: any) => (
            <div>
              <a>{item.fileName}</a>
            </div>
          )); // 单行箭头函数会自动返回结果
        }
      },
    },
    {
      title: '报关返回文件',
      dataIndex: 'receiptFiles',
      hideInSearch: true,
      width: 120,
      ellipsis: true,
      render: (text: any, record: any) => {
        if (record?.receiptFiles?.length > 1) {
          return <a>{record?.receiptFiles[record?.receiptFiles?.length - 1]?.fileName}</a>
        } else {
          return record?.receiptFiles?.map((item: any) => (
            <div>
              <a>{item.fileName}</a>
            </div>
          )); // 单行箭头函数会自动返回结果
        }
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'option',
      align: 'left',
      width: 220,
      fixed: 'right',
      hideInSearch: true,
      render: (text: any, record: any) => [
        <a
          key={1}
          style={{ marginRight: '10px' }}
          onClick={() =>
            history.push(`/BOL/ClearReportCustom/ReportDetail`, record)
          }
        >
          详情
        </a>,
        <AccessCard key={2} accessible={[':Customs:DeclarationFullAccess']}>
          {/* <a
            key={2}
            style={{ marginRight: '10px' }}
            onClick={() => history.push(`/customer/ChannelManagement`, record)}
          >
            回传报关文件
          </a> */}
          <MyUpload
            // disabled={!isAccessibleFlag}
            type
            name="receiptFiles"
            action={'/customs/declaration/receiptFile/upload'}
            id={record?.id}
            getDetail={() => {
              actionRef.current?.reload()
            }}
          >回传报关文件</MyUpload>
        </AccessCard>,
        // <a
        //   key={3}
        //   style={{ marginRight: '10px' }}
        //   onClick={() => history.push(`/customer/Details`, record)}
        // >
        //   日志
        // </a>,
        <LogDashboard
          extraId_1={record?.id}
          btnType="link"
          btnText="日志"
          holdFlag={record?.holdFlag}
        />
      ],
    },
  ];

  // 合并报关
  const combineClearance = async () => {
    if (selectedRowKeys?.length < 2) return message.warning('至少勾选两个报关单')
    const { status } = await getDeclarationAPI({
      ids: selectedRowKeys.map((item: any) => item?.id)?.join(',')
    })
    if (status) {
      message.success('操作成功')
      actionRef.current?.reload()
      setSelectedRowKeys([])
    }
  }
  // 批量下载报关文件
  const batchDownload = async () => {
    try {
      const response: any = await getDownloadAPI({ ids: selectedRowKeys.map((item: any) => item?.id)?.join(',') });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      console.log(fileName, 'fileName');

      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
      message.success('批量下载报关文件成功')
      actionRef?.current.reload()
    } catch (err) {
      message.warning('服务器异常')
    }
  }
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
      tabs: true
    },
    type: {
      desc: '报关类型',
      type: 'select',
      range: convertObjectToArray(declarationType, 'number'),
    },
    blnos: {
      desc: '提单号',
      type: 'text',
      value: ''
    },
    states: {
      desc: '状态',
      type: 'select',
      multi: true,
      range: [
        { label: '待处理', value: 1 },
        { label: '已配舱', value: 2 },
        { label: '查验中', value: 3 },
        { label: '已结关', value: 4 },
        { label: '合并做废', value: 5 },

      ],
      value: ''
    },
    brokerIds: {
      desc: '报关行',
      type: 'custom',
      multi: true,
      value: '',
      generate: (value: any) => getDepartment(value),
    },
    //   1: '待处理',
    // 2: '已配舱',
    // 3: '查验中',
    // 4: '已结关',
    // "state": {
    //   "desc": "报关状态",
    //   "type": "select",
    //   "range": convertObjectToArray(declarationState, 'number'),
    // },
  };

  return (
    // <ProTable<any>
    //   columns={columns}
    //   actionRef={actionRef}
    //   params={params}
    //   // cardBordered
    //   request={async (params: any) => {
    //     console.log(params);
    //     const msg = await getDeclarationList({
    //       start: (params.current - 1) * params.pageSize,
    //       len: params.pageSize,
    //       keyword: params.keyword,
    //       type: params.type,
    //       blno: params.blno,
    //       state: params.state,
    //       brokerId: params.brokerId,
    //     });
    //     return {
    //       data: msg?.data?.list || [],
    //       success: msg.status,
    //       total: msg.data?.total,
    //     };
    //   }}
    //   rowKey="id"
    //   search={{
    //     labelWidth: 'auto',
    //   }}
    //   options={{
    //     fullScreen: true,
    //   }}
    //   pagination={{
    //     pageSize: 10,
    //   }}
    //   scroll={{ x: 1200 }}
    //   toolBarRender={() => [
    // <Button
    //   key={'hai2'}
    //   href={'http://online.customs.gov.cn/'}
    //   target="_blank"
    // >
    //   中国海关
    // </Button>,
    //   ]}
    // />

    <MrTable
      keyID="BOL/ClearReportCustom/clearance"
      columns={columns}
      rowSelectionCablck={rowSelectionCablck}
      request={async (params: any, action: any) => {
        actionRef.current = action;
        // console.log('tableactiveKey',activeKey);
        let msg = await getDeclarationList({
          ...params,
        });
        return {
          data: msg?.data?.list || [],
          success: msg?.status,
          total: msg?.data?.total,
        };
      }}
      filters={filters}
      toolBarRender={
        <>
          <Button
            key={'hai2'}
            href={'http://online.customs.gov.cn/'}
            target="_blank"
          >
            中国海关
          </Button>
          <ManualModal
            btnText={'修改报关行'}
            record={selectedRowKeys}
            refresh={() => actionRef.current?.reload()}
          />
          <Button onClick={batchDownload}>批量下载报关文件</Button>
          <Button onClick={combineClearance}>合并报关</Button>
          {/* <Button type={'primary'}>修改报关类型</Button> */}
        </>
      }
    />
  );
};
export default ReportCustomList;
