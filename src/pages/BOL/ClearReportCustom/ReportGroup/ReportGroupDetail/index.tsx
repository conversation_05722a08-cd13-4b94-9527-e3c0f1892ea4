import {
  ProCard,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useEffect, useRef, useState } from 'react';
import { Select, Button, Table, Form } from 'antd';
import { useLocation } from 'umi';
import styles from './index.less';
import { airLineListAPI } from '@/services/booking';
import {
  changeServiceFee,
  getBrokerDetail,
  saveBrokerDetail,
} from '@/services/bol';
import {
  convertObjectToArray,
  currency_map,
  isEnabled,
} from '@/utils/constant';
import { formatTime } from '@/utils/format';

const ReportGroupDetail = (props: any) => {
  const { OutTableData, id, refresh } = props;
  console.log('OutTableData', OutTableData);
  const location = useLocation();
  const { state }: any = location;
  const [form] = Form.useForm();
  /*详情数据*/
  const [detail, setDetail] = useState<any>();
  /*table数据*/
  const [TableData, setTableData] = useState<any>();
  const [editId, setEditId] = useState<any>();
  const getDetail = async () => {
    const res = await getBrokerDetail({ id: state?.id || editId });
    setDetail(res.data);
    setTableData(res?.data?.declarationFees);
    form.setFieldsValue(res.data);
  };
  useEffect(() => {
    setTableData(OutTableData);
  }, [OutTableData]);
  /*修改基本信息*/
  /*  const handleEdit = async (params: any) => {
    try {
      const { status, data } = await saveBrokerDetail({
        ...params,
        id: state?.id || editId,
        type: 2,
      });
      if (status) {
        setEditId(data?.id);
      }
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };*/
  /*添加航路*/
  const addRoute = () => {
    const newData: any = {
      fixedFee: '',
      fixedFeeType: '',
      multiBrandNameFee: '',
      multiBrandNameFeeType: '',
      fda: '',
      fdaType: '',
      isf: '',
      isfType: '',
      destructionFee: '',
      destructionFeeType: '',
      inspectionFee: '',
      inspectionFeeType: '',
      surcharge: '',
      surchargeType: '',
      isNew: true,
    };
    if (TableData) {
      setTableData([...TableData, newData]);
    } else {
      setTableData([newData]);
    }
    console.log(TableData);
  };

  useEffect(() => {
    if (editId || state?.id) {
      getDetail();
    }
  }, [state?.id, editId]);
  /*选择后缀*/
  const selectAfter = (value: any, name: any, isNew: any, index: any) => {
    return (
      <Form.Item noStyle name={[index, name]}>
        <Select
          defaultValue={currency_map[value]}
          key={currency_map[value]}
          style={{ width: '85px' }}
          disabled={!isNew}
          options={convertObjectToArray(currency_map)}
        ></Select>
      </Form.Item>
    );
  };
  /*保存修改*/
  const saveEdit = async (params: any) => {
    try {
      let param = {
        ...params,
        id: state?.id || editId,
        type: 2,
      };
      if (param.declarationFees) {
        param.declarationFees.id = detail?.declarationFees?.id;
      }

      const { status } = await saveBrokerDetail({
        ...param,
      });
      refresh();
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };

  /*新增服务地及费用*/
  const addFee = async (params: any) => {
    console.log('params', params);
    // 使用Object.keys()和filter()方法来过滤掉vesselId为空的对象
    const filteredData = Object.keys(params)
      .filter(
        (key) =>
          params[key].hasOwnProperty('vesselId') &&
          params[key].vesselId !== undefined,
      )
      .reduce((acc: any, key) => {
        acc[key] = params[key];
        return acc;
      }, {});
    try {
      let param = {
        declarationFees: Object.values(filteredData).map((item: any) => {
          return {
            fee: item,
            vesselId: item.vesselId,
            id: editId || '',
            brokerId: id,
            enabled: true,
          };
        }),
      };
      console.log('param', params);
      const { status } = await saveBrokerDetail({
        ...param,
      });
      if (status) {
        refresh();
        form.resetFields();
        setTableData(OutTableData);
      }
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };

  const columns = [
    {
      title: '航线',
      dataIndex: 'vesselId',
      key: 'vesselId',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormSelect
            style={{ width: '140px' }}
            name={[index, 'vesselId']}
            disabled={!record.isNew}
            // @ts-ignore
            value={record?.vesselId}
            request={async () => {
              const { status, data } = await airLineListAPI({
                len: 9999,
                type: 2,
              });
              if (status) {
                return data.list.map((item: any) => {
                  return {
                    label: item.name,
                    value: item.id,
                  };
                });
              }
              return [];
            }}
          ></ProFormSelect>
        );
      },
    },
    {
      title: <div className={styles.require}>状态</div>,
      dataIndex: ['enabled'],
      key: 'state',
      width: 200,
      align: 'center',
      render: (text: any, record: any) => {
        return <div style={{ marginBottom: 24 }}>{isEnabled[text]}</div>;
      },
    },
    {
      title: <div className={styles.require}>非单独报关</div>,
      dataIndex: ['fee'],
      key: 'deputedDeclarationFee',
      width: 200,
      render: (text: any, record: any, index: any) => {
        console.log(record);
        return (
          <ProFormText
            addonAfter={selectAfter(
              text?.deputedDeclarationFeeType,
              'deputedDeclarationFeeType',
              record.isNew,
              index,
            )}
            disabled={!record.isNew}
            style={{ width: '180px' }}
            name={[index, 'deputedDeclarationFee']}
            // @ts-ignore
            value={text?.deputedDeclarationFee}
          />
        );
      },
    },
    {
      title: <div className={styles.require}>退税报关</div>,
      dataIndex: 'fee',
      key: 'taxRefundFee',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.taxRefundFeeType,
              'taxRefundFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '150px' }}
            name={[index, 'taxRefundFee']}
            // @ts-ignore
            value={text?.taxRefundFee}
          />
        );
      },
    },
    {
      title: '联单费',
      dataIndex: 'fee',
      key: 'sheetPatchUpFee',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.sheetPatchUpFeeType,
              'sheetPatchUpFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '150px' }}
            name={[index, 'sheetPatchUpFee']}
            // @ts-ignore
            value={text?.sheetPatchUpFee}
          />
        );
      },
    },
    {
      title: '刻章费',
      dataIndex: 'fee',
      key: 'carvingFee',
      width: 300,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.carvingFeeType,
              'carvingFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '200px' }}
            name={[index, 'carvingFee']}
            // @ts-ignore
            value={text?.carvingFee}
          />
        );
      },
    },
    {
      title: '查验代理费',
      dataIndex: 'fee',
      key: 'inspectionAgentFee',
      width: 300,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.inspectionAgentFeeType,
              'inspectionAgentFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '200px' }}
            name={[index, 'inspectionAgentFee']}
            // @ts-ignore
            value={text?.inspectionAgentFee}
          />
        );
      },
    },
    {
      title: '查验处理费',
      dataIndex: 'fee',
      key: 'inspectionHandingFee',
      width: 300,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.inspectionHandingFeeType,
              'inspectionHandingFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '200px' }}
            name={[index, 'inspectionHandingFee']}
            // @ts-ignore
            value={text?.inspectionHandingFee}
          />
        );
      },
    },
    {
      title: '商检费',
      dataIndex: 'fee',
      key: 'commercialInspectFee',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.commercialInspectFeeType,
              'commercialInspectFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '150px' }}
            name={[index, 'commercialInspectFee']}
            // @ts-ignore
            value={text?.commercialInspectFee}
          />
        );
      },
    },
    {
      title: '查验铅封费',
      dataIndex: 'fee',
      key: 'inspectionSealFee',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            name={[index, 'inspectionSealFee']}
            addonAfter={selectAfter(
              text?.inspectionSealFeeType,
              'inspectionSealFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '150px' }}
            // @ts-ignore
            value={text?.inspectionSealFee}
          />
        );
      },
    },
    {
      title: '报关退税费',
      dataIndex: 'fee',
      key: 'taxRefund',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.taxRefundType,
              'taxRefundType',
              record.isNew,
              index,
            )}
            style={{ width: '130px' }}
            name={[index, 'taxRefund']}
            // @ts-ignore
            value={text?.taxRefund}
          />
        );
      },
    },
    {
      title: '退关费',
      dataIndex: 'fee',
      key: 'shutoutFee',
      width: 200,
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormText
            disabled={!record.isNew}
            addonAfter={selectAfter(
              text?.shutoutFeeType,
              'shutoutFeeType',
              record.isNew,
              index,
            )}
            style={{ width: '130px' }}
            name={[index, 'shutoutFee']}
            // @ts-ignore
            value={text?.shutoutFee}
          />
        );
      },
    },
    {
      title: '生效时间',
      dataIndex: 'enabledTime',
      key: 'enabledTime',
      width: 200,
      render: (text: any, record: any) => {
        console.log('text', text);
        return (
          <ProFormText
            disabled
            style={{ width: '130px' }}
            name={[record.name, 'enabledTime']}
            // @ts-ignore
            value={text ? formatTime(text) : '--'}
          />
        );
      },
    },
    {
      title: '失效时间',
      dataIndex: 'disabledTime',
      key: 'disabledTime',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <ProFormText
            disabled
            style={{ width: '130px' }}
            name={[record.name, 'disabledTime']}
            // @ts-ignore
            value={text ? formatTime(text) : '--'}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      align: 'left',
      width: 220,
      fixed: 'right',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Button
            danger
            type={'link'}
            key={'stop'}
            onClick={() => {
              changeServiceFee({ id: record.id, enabled: 0 });
              refresh();
            }}
            style={{ marginBottom: 24 }}
          >
            停用
          </Button>
        );
      },
    },
  ];

  return (
    <div className={styles.wrap}>
      <Form onFinish={addFee} form={form}>
        <ProCard
          style={{ marginBlockStart: 8 }}
          gutter={8}
          title={<strong>服务地及费用</strong>}
          extra={
            <>
              <Button onClick={() => addRoute()}>增加航线</Button>{' '}
              <Button type={'primary'} htmlType="submit">
                保存
              </Button>
            </>
          }
        >
          <Table
            dataSource={TableData}
            columns={columns}
            scroll={{ x: 1100 }}
          />
        </ProCard>
      </Form>
      <ProCard
        style={{ marginBlockStart: 8 }}
        gutter={8}
        layout="center"
      ></ProCard>
    </div>
  );
};
export default ReportGroupDetail;
