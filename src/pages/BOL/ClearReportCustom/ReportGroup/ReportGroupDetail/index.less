.wrap {
  :global(.ant-descriptions-item-label) {
    color: #aeaeae !important;
  }

  .title {
    :global(.ant-descriptions-item-container::before) {
      background: #7947FF;
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      color: white;
      content: "报" !important;
      margin-right: 5px;
    }
  }

  .switch {
    position: relative;
  }
  .require::before {
    display: inline-block !important;
    margin-inline-end: 4px !important;
    color: #ff4d4f !important;
    font-size: 14px !important;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*" !important;
  }
}
