// import { DownOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import Customer from '@/pages/customer/CustomerList';
import GroupList from '@/pages/customer/GroupList';
import { useModel } from 'umi';
import LabelList from '@/pages/customer/LabelList';
import ClearCustomList from '@/pages/BOL/ClearReportCustom/ClearCustomList';
import ReportCustomList from '@/pages/BOL/ClearReportCustom/ReportCustomList';
import ReportGroup from '@/pages/BOL/ClearReportCustom/ReportGroup';
import ClearGroup from '@/pages/BOL/ClearReportCustom/ClearGroup';
import TabsType from '@/components/TabsType';
import TableComponent from '@/pages/Waybilladministration/WaybillCheckIn/TableComponent';
import MachineCheckIn from '@/pages/Waybilladministration/WaybillCheckIn/MachineCheckIn';
import { ProviderType } from '@/shared/ProviderTypeFn';
import { inc } from 'semver';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';

export default () => {
  const [toList] = usePermissionFiltering();
  // @ts-ignore
  const customer = useModel('customer');
  const [activeKey, setActiveKey] = useState<number>(1);
  const { getLabelList } = customer;
  const items = [
    {
      label: '报关管理',
      key: 1,
      //children: <ReportCustomList />,
      accessible: [':Customs:Declaration'],
    },
    {
      label: '清关管理',
      key: 2,
      //children: <ClearCustomList />,
      accessible: [':Customs:Clearance'],
    },

    {
      label: '报关行',
      key: ProviderType.Declaration.getCode(),
      //children: <ReportGroup />,
      accessible: [':Provider:ReadAccess'],
      dataValidator: (path: any, data: any) => {
        return (data.type.value ===
          'all' ||
          data.type.value.indexOf(ProviderType.Declaration.getCode()) > -1);
      },
    },
    {
      label: '清关行',
      key: ProviderType.Clearance.getCode(),
      //children: <ClearGroup />,
      accessible: [':Provider:ReadAccess'],
      dataValidator: (path: any, data: any) => {
        return (data.type.value ===
          'all' ||
          data.type.value.indexOf(ProviderType.Clearance.getCode()) > -1);
      },
    },
  ];
  const init = async () => {
    getLabelList();
    console.log('(toList(items)[0]?.key', toList(items)[0]?.key);
    setActiveKey(toList(items)[0]?.key);
  };
  useEffect(() => {
    init();
  }, []);
  console.log('ssss', ProviderType.Declaration.getCode());
  const onChange = (key: any) => {
    setActiveKey(key);
  };

  const tabsProps = useMemo(
    () => ({
      className: styles.tabBox,
      activeKey,
      items: toList(items),
      onChange,
    }),
    [activeKey],
  );
  return (
    <div>
      <TabsType {...tabsProps} />
      <div>
        {activeKey === 1 && <ReportCustomList />}
        {activeKey === 2 && <ClearCustomList />}
        {activeKey === ProviderType.Declaration.getCode() && <ReportGroup />}
        {activeKey === ProviderType.Clearance.getCode() && <ClearGroup />}
      </div>
    </div>
  );
};
