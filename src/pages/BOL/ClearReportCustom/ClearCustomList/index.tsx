import { ProFormSelect, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';
import { history } from '@umijs/max';
import service from '@/services/home';
import MySearch from '@/components/MyProTable/MySearch';
import MySelect from '@/components/MyProTable/MySelect';
import MyInput from '@/components/MyProTable/MyInput';
import { getClearanceList } from '@/services/bol';
import {
  cleanDeclarationType,
  convertObjectToArray,
  declarationType,
} from '@/utils/constant';
import { getBrokerList } from '@/services/customer/CustomerHome';
import { calculateSum } from '@/utils/utils';
import { ProviderType } from '@/shared/ProviderTypeFn';
import MrTable from '@/components/MrTable';

const ClearCustomList = () => {
  const actionRef = useRef<any>();
  /*查询参数*/
  const [params, setParams] = useState<any>({});
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <MySearch
            placeholder="请输入客户名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px', background: '#FBFBFB' }}
            onSearch={(value: any) => setParams({ ...params, keyword: value })}
          />
        );
      },
    },
    {
      dataIndex: 'state',
      hideInTable: true,
      title: '清关类型',
      renderFormItem: () => {
        return (
          <MySelect
            placeholder="请选择"
            allowClear
            style={{ width: '200px' }}
            onChange={(value: any) => {
              setParams({ ...params, state: value });
            }}
            options={convertObjectToArray(cleanDeclarationType, 'number')}
          />
        );
      },
    },
    {
      dataIndex: 'brokerId',
      hideInTable: true,
      title: '清关行',
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="brokerId"
            showSearch
            // rules={[{ required: true, message: '请输入站点' }]}
            noStyle={true}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 12 }}
            style={{ width: '200px' }}
            request={async () => {
              const { status, data } = await getBrokerList({
                type: ProviderType.Clearance.getCode(),
                len: 999,
              });
              if (status) {
                return data?.list?.map((item: any) => {
                  return {
                    label: item.name,
                    value: item.id,
                  };
                });
              }
              return [];
            }}
            fieldProps={{
              onChange: (value) => {
                setParams({ ...params, brokerId: value });
              },
              size: 'small',
              filterOption: false,
            }}
          ></ProFormSelect>
        );
      },
    },
    {
      dataIndex: 'blno',
      hideInTable: true,
      title: '主提单号',
      renderFormItem: () => {
        return <MyInput placeholder="请输入" allowClear />;
      },
    },
    {
      title: 'id',
      dataIndex: 'id',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '清关单号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '清关状态',
      dataIndex: 'state',
      hideInSearch: true,
      width: 120,
      render: (_: any, text: any) => {
        return cleanDeclarationType[text?.state];
      },
    },
    {
      title: '运单号',
      dataIndex: 'waybills',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        console.log(record?.waybills);
        return record?.waybills
          .map((item: any) => item?.waybillNo) // 单行箭头函数会自动返回结果
          .join(',');
      },
    },
    {
      title: '运单详情',
      dataIndex: 'detail',
      hideInSearch: true,
      width: 120,
      render: (text: any, record: any) => {
        return (
          <div>
            <div>
              总件数
              {record?.waybills && calculateSum(record?.waybills, 'pieceNum')}
            </div>
            <div>
              总实重
              {record?.waybills &&
                calculateSum(record?.waybills, 'signActualWeight')}
              kg
            </div>
            <div>
              总计费重
              {record?.waybills &&
                calculateSum(record?.waybills, 'chargeWeight')}
              kg
            </div>
          </div>
        );
      },
    },
    {
      title: '主提单号',
      dataIndex: 'blno',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '预配信息（预留）',
      dataIndex: 'deposit',
      hideInSearch: true,
      width: 150,
      render: (text: any, record: any) => {
        return (
          <div>
            <div>仓位:{record?.configure?.spaceId}</div>
            <div>航班:{record?.configure?.info?.vesselCode}</div>
            <div>箱号:{record?.configure?.info?.containerNo}</div>
          </div>
        );
      },
    },
    {
      title: '修改人员',
      dataIndex: 'prePaymentAmount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '清关行',
      dataIndex: 'brokerName',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '提单（预留）',
      dataIndex: 'salesman',
      hideInSearch: true,
      width: 120,
    },
    {
      title: 'do（预留）',
      dataIndex: 'salesman',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '清关文件',
      dataIndex: 'receiptFiles',
      hideInSearch: true,
      width: 160,
      render: (text: any, record: any) => {
        return record?.receiptFiles.map((item: any) => (
          <div>
            <a>{item.fileName}</a>
          </div>
        )); // 单行箭头函数会自动返回结果
      },
    },
    {
      title: '操作',
      key: 'option',
      align: 'left',
      width: 220,
      fixed: 'right',
      hideInSearch: true,
      render: (text: any, record: any) => [
        <a
          key={1}
          style={{ marginRight: '10px' }}
          onClick={() =>
            history.push(`/BOL/ClearReportCustom/ClearDetail`, record)
          }
        >
          详情
        </a>,
      ],
    },
  ];
  const filters = {
    "keyword": {
      "type": "keyword",
      "value": '',
      tabs: true
    },
    type: {
      desc: '清关类型',
      type: 'select',
      range: convertObjectToArray(cleanDeclarationType, 'number'),
    },
    // "state": {
    //   "desc": "清关行",
    //   "type": "select",
    //   "range": convertObjectToArray(declarationState, 'number'),
    // },
  };
  return (
    // <ProTable<any>
    //   columns={columns}
    //   actionRef={actionRef}
    //   // cardBordered
    //   request={async (params: any) => {
    //     console.log('params', params);
    //     const msg = await getClearanceList({
    //       start: (params.current - 1) * params.pageSize,
    //       len: params.pageSize,
    //       keyword: params.keyword,
    //       blno: params.blno,
    //       state: params.state,
    //       brokerId: params.brokerId,
    //     });
    //     return {
    //       data: msg?.data?.list || [],
    //       success: msg.status,
    //       total: msg.data?.amount,
    //     };
    //   }}
    //   params={params}
    //   rowKey="id"
    //   search={{
    //     labelWidth: 'auto',
    //   }}
    //   options={{
    //     fullScreen: true,
    //   }}
    //   pagination={{
    //     pageSize: 10,
    //   }}
    //   scroll={{ x: 1200 }}
    //   toolBarRender={() => []}
    // />
    <MrTable
      keyID="BOL/ClearReportCustom/customs"
      columns={columns}
      request={async (params: any, action: any) => {
        actionRef.current = action;
        // console.log('tableactiveKey',activeKey);
        let msg = await getClearanceList({
          ...params,
        });
        return {
          data: msg?.data?.list || [],
          success: msg?.status,
          total: msg?.data?.total,
        };
      }}
      filters={filters}
    />
  );
};
export default ClearCustomList;
