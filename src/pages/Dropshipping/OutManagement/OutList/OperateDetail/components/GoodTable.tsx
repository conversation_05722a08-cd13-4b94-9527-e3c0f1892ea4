import { Button, Form, Input, message, Radio, Select, Table } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { getWarehouseMap } from '@/services/ConfiguredCar';
import { collectApi } from '@/services/Waybilladministration';
import _ from 'lodash';

const GoodTable = forwardRef((props: any, ref) => {
  const { Detail } = props;
  const { avaliableGood, skuInfoList } = Detail;
  const [SkuInfoListData, setSkuInfoListData] = useState<any>([]);

  const [form] = Form.useForm();

  useEffect(() => {
    const skuInfoListData = skuInfoList?.map((item: any) => {
      return { ...item, children: avaliableGood[item.skuId] };
    });
    setSkuInfoListData(skuInfoListData);
  }, [Detail]);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
      console.log('提交22');
    },
  }));
  const handleSubmit = async (value: any) => {
    console.log('value', value);

    const items: any = [];

    // 遍历原始数据，将每个SKU及其数量添加到newData的items数组中
    for (const key in value) {
      const skuObj = value[key];
      for (const sku in skuObj) {
        items.push({
          skuId: sku,
          quatity: skuObj[sku].quatity,
          slotId: skuObj[sku].slotId,
        });
      }
    }
    const res = await collectApi({
      outboundId: Detail?.id,
      details: items,
    });
    if (res.status) {
      message.success('操作成功');
    }
    /*   //对象变成数组
    const arr = Object.keys(value).map((key) => {
      console.log('key', key);
      return { ...value[key] };
    });*/
    // console.log('arr', arr);
  };
  const columns: any = [
    {
      title: 'SKU',
      dataIndex: 'id',
      key: 'id',
      render: (_: any, record: any, index: any) => {
        return record?.sku?.code;
      },
    },
    {
      title: '产品名称',
      dataIndex: 'id22',
      key: 'id222',
      render: (_: any, record: any, index: any) => {
        return record?.sku?.cnName;
      },
    },
    {
      title: 'FNSKU',
      dataIndex: 'FNSKU',
      key: 'FNSKU',
    },
    {
      title: '待拣货数量',
      dataIndex: 'quatity',
      key: 'quatity',
    },
    {
      title: '已拣货数量',
      dataIndex: 'collectedQuatity',
      key: 'collectedQuatity',
    },
    {
      title: '拣货库位',
      dataIndex: 'slotId',
      key: 'slotId',
      render: (_2: any, record: any, index: any) => {
        console.log('record', record);
        return (
          <Form.Item
            name={[index, record?.skuId, 'slotId']}
            style={{ margin: 0 }}
          >
            <Select
              //defaultValue={record.children[0]?.slotId}
              style={{ width: 120 }}
              onChange={(e: any) => {
                console.log('e', e);

                console.log('copyData', copyData);
                const selectItem = record?.children?.find(
                  (item: any) => item.slotId === e,
                );
                //修改库存
                const copyData = _.cloneDeep(SkuInfoListData);
                copyData[index]['quatity2'] = selectItem.quatity;
                setSkuInfoListData(copyData);
              }}
              options={record?.children?.map((item: any) => {
                return {
                  value: item.slotId,
                  label: item.slotId,
                };
              })}
            />
          </Form.Item>
        );
      },
    },
    {
      title: '库位可用库存',
      dataIndex: 'quatity2',
      key: 'quatity2',
    },
    {
      title: '拣货数量',
      dataIndex: 'quatit',
      key: 'quatit',
      render: (_: any, record: any, index: any) => {
        console.log('record', record);
        return (
          <Form.Item
            name={[index, record?.skuId, 'quatity']}
            style={{ margin: 0 }}
          >
            <Input style={{ width: 100 }} />
          </Form.Item>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'op',
      key: 'op',
      /*     render: (_: any, record: any, index: any) => {
        return (
          <Button type={'link'} style={{ color: 'red' }}>
            添加库位
          </Button>
        );
      },*/
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Form onFinish={handleSubmit} form={form}>
        <Table
          columns={columns}
          dataSource={SkuInfoListData}
          scroll={{ x: 1200 }}
          childrenColumnName={'skuList'}
        />
      </Form>
    </div>
  );
});
export default GoodTable;
