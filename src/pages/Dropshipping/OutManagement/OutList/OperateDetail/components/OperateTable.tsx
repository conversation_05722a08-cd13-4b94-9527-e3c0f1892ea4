import { Avatar, Input, Radio, Space, Table } from 'antd';
import React from 'react';
import { formatTime } from '@/utils/format';
import { getAvataUrl } from '@/shared/Enumeration';

const OperateTable = (props: any) => {
  const { Detail } = props;
  const columns: any = [
    {
      title: 'SKU',
      dataIndex: ['sku', 'code'],
      key: 'id',
    },
    {
      title: '产品名称',
      dataIndex: ['sku', 'cnName'],
      key: 'cnName',
    },
    {
      title: 'FNSKU',
      dataIndex: ['sku', 'code22'],
      key: 'code22',
    },
    {
      title: '拣货库位',
      dataIndex: 'slotId',
      key: 'slotId',
    },
    {
      title: '拣货数量',
      dataIndex: 'quatity',
      key: 'quatity',
    },
    {
      title: '拣货完成时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: any, record: any) => {
        return <span>{formatTime(record?.createTime)}</span>;
      },
    },
    {
      title: '拣货员',
      dataIndex: 'code',
      key: 'code',
      render: (text: any, record: any) => {
        return (
          <Space>
            <Avatar src={getAvataUrl(record?.user?.avatar)} />
            <span>{record?.user?.name}</span>
          </Space>
        );
      },
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Table
        columns={columns}
        dataSource={Detail?.recordList}
        scroll={{ x: 1200 }}
        childrenColumnName={'skuList'}
      />
    </div>
  );
};
export default OperateTable;
