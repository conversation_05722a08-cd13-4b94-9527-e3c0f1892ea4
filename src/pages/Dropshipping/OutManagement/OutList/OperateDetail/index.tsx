// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import styles from './index.less';

import { getOutboundDetail } from '@/services/Waybilladministration';
import { useLocation } from 'umi';
import OperateTable from './components/OperateTable';
import { deliveryMethod, getLabelByValue } from '@/utils/constant';
import { Button, Space, Tabs } from 'antd';
import GoodTable from '@/pages/Dropshipping/OutManagement/OutList/OperateDetail/components/GoodTable';

const ScanStore = () => {
  const [Keyword, setKeyword] = useState('');
  const [Detail, setDetail] = useState({});
  const [State, setState] = useState(0);
  const addRef = useRef<any>(null);
  const location = useLocation();
  useEffect(() => {
    const { state }: any = location;
    console.log('state', state);
    setKeyword(state?.id);
  }, []);
  const handleSubmit = () => {
    addRef?.current?.handleSubmit();
  };
  const getInboundDetailApi = async (id: any) => {
    const res = await getOutboundDetail({ id });
    setDetail(res?.data);
    console.log('结果', res);
  };
  /*获取*/

  useEffect(() => {
    console.log('keyword', Keyword);
    if (Keyword) {
      getInboundDetailApi(Keyword);
    }
  }, [Keyword]);
  return (
    <div className={styles.container}>
      <div className={styles.top}>
        <div className={styles.title}>出库单拣货</div>
        <div>
          <Space>
            <Button>打印拣货单</Button>
          </Space>
        </div>
      </div>
      <div>
        <>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={
              <div style={{ fontWeight: 600 }}>出库单号：{Detail?.no}</div>
            }
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['warehouse', 'name']}
                label="出库仓库"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.warehouse?.name}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="运输类型"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {getLabelByValue(Detail?.deliveryMethod, deliveryMethod)}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流承运商"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.express}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['trackNo']}
                label="物流单号"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.trackNo}
              </ProDescriptions.Item>

              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流面单"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>

          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            className={styles.fill}
          >
            <Tabs
              defaultActiveKey="1"
              type="card"
              className={styles.tabBox}
              style={{ marginBottom: 32 }}
              items={[
                {
                  label: `拣货列表`,
                  key: '1',
                  children: (
                    <GoodTable
                      Detail={Detail}
                      ref={addRef}
                      getInboundDetailApi={getInboundDetailApi}
                    />
                  ),
                },
                {
                  label: `拣货记录`,
                  key: '2',
                  children: <OperateTable Detail={Detail} />,
                },
              ]}
            />
          </ProCard>
        </>
        <div
          style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
        ></div>
      </div>
      <ProCard
        gutter={8}
        className={styles.footer}
        style={{ textAlign: 'center' }}
        /* extra={<Button type={'primary'}>更新</Button>}*/
      >
        <Space>
          <Button>取消</Button>
          <Button type={'primary'} onClick={handleSubmit}>
            确定拣货
          </Button>
        </Space>
      </ProCard>
    </div>
  );
};
export default ScanStore;
