// import { Access, useAccess } from '@@/exports';
import React, { useRef, useState } from 'react';
import { Button, Input, Space, Tag } from 'antd';
import MrTable from '@/components/MrTable';
import { history } from '@@/core/history';

import {
  deliveryMethod,
  getColorByValue,
  getLabelByValue,
  OutGoodtype,
} from '@/utils/constant';
import { getOutboundList, printOrder } from '@/services/Waybilladministration';
import { REQUESTADDRESS_W } from '@/globalData';

const { Search } = Input;

const InventoryForecast = () => {
  const actionRef = useRef<any>();
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);

  const refreshTable = () => {
    actionRef.current.reload();
  };
  const getMenu = (record) => {
    return [
      {
        key: '1',
        label: (
          <Button
            key="edit3"
            type="link"
            onClick={async () => {
              const res = await printOrder({ id: record?.id });
              if (res?.data?.url) {
                window.open(REQUESTADDRESS_W + res?.data?.url, '_blank');
              } else {
                //message.error('暂未上传免单');
              }

              console.log('res', res);
            }}
          >
            打印面单
          </Button>
        ),
      },
      {
        key: '2',
        label: (
          <Button
            key="edit3"
            type="link"
            onClick={() => {
              history.push(`/OutManagement/OutList/Detail`, record);
            }}
          >
            详情
          </Button>
        ),
      },
    ];
  };
  /* 刷新表格 */
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Search
            placeholder="请输入标签名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },
    {
      title: '出库单号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 150,
      render: (text: any, record: any) => {
        return (
          <Button
            type={'link'}
            onClick={() =>
              history.push(`/OutManagement/OutList/Detail`, record)
            }
          >
            {record?.no}
          </Button>
        );
      },
    },
    {
      title: '打印次数',
      dataIndex: 'printTimes',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '客户',
      hideInSearch: true,
      dataIndex: 'clientName',
    },
    {
      title: '订单类型',
      hideInSearch: true,
      dataIndex: 'clientName',
    },
    {
      title: '运输服务',
      hideInSearch: true,
      dataIndex: 'clientName',
      render: (text: any, record: any) => {
        return getLabelByValue(record?.deliveryMethod, deliveryMethod);
      },
    },
    {
      title: '物流产品',
      hideInSearch: true,
      dataIndex: 'clientName',
    },
    {
      title: '物流单号',
      hideInSearch: true,
      dataIndex: 'trackNo',
    },
    {
      title: '物流服务商',
      hideInSearch: true,
      dataIndex: 'providerName',
    },
    {
      title: '产品总数',
      hideInSearch: true,
      dataIndex: 'clientName',
    },
    {
      title: 'SKU明细',
      hideInSearch: true,
      dataIndex: 'skuSummary',
      width: 150,
    },

    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, OutGoodtype)}>
            {getLabelByValue(record?.state, OutGoodtype)}
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 300,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => {
        return (
          <Space>
            {record?.state === 1 && (
              <Button
                key="edit"
                type="link"
                onClick={() => {
                  history.push(
                    `/dropshipping/OutManagement/OutList/OperateDetail`,
                    record,
                  );
                }}
              >
                拣货
              </Button>
            )}
            {record?.state === 3 && (
              <Button
                key="edit"
                type="link"
                onClick={() => {
                  history.push(
                    `/dropshipping/OutManagement/OutList/PackDetail`,
                    record,
                  );
                }}
              >
                打包
              </Button>
            )}
            <Button
              key="edit3"
              type="link"
              onClick={async () => {
                const res = await printOrder({ id: record?.id });
                console.log('res', res);
              }}
            >
              打印面单
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <>
      <MrTable
        keyID="system/setting"
        columns={columns}
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getOutboundList({ ...params });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        toolbar={{
          menu: {
            type: 'tab',
            //activeKey: activeKey,
            items: [
              {
                key: '0',
                label: '全部',
              },
              {
                key: '1',
                label: '待入库',
              },
              {
                key: '2',
                label: '收货中',
              },
              {
                key: '3',
                label: '已收货',
              },
              {
                key: '4',
                label: '已上架',
              },
              {
                key: '5',
                label: '已取消',
              },
            ],
            onChange: (key: any) => {
              //setActiveKey(key as string);
              //refresh();
            },
          },
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            termQuery: false,
            // tabs: true,
          },
          state: {
            desc: '状态',
            type: 'select',
            range: [],
          },
          oversea: {
            desc: '海外仓',
            type: 'select',
            range: [],
          },
          arriveTime: {
            desc: '创建日期',
            type: 'dateTimeRange',
            startTime: '',
            endTime: '',
          },
        }}
        /* toolBarRender={<Button
          key="primary"
          type="primary"
          onClick={() => setIsModalOpen(true)}
        >
          新建规则
        </Button>}*/
      />
    </>
  );
};
export default InventoryForecast;
