import { Input, Radio, Table } from 'antd';
import React from 'react';

const OperateTable = (props: any) => {
  const { Detail } = props;
  const columns: any = [
    {
      title: '记录时间',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '操作',
      dataIndex: 'id22',
      key: 'id222',
    },
    {
      title: '状态',
      dataIndex: 'quatity',
      key: 'quatity',
    },
    {
      title: '操作人',
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Table
        columns={columns}
        dataSource={Detail?.boxList}
        scroll={{ x: 1200 }}
        childrenColumnName={'skuList'}
      />
    </div>
  );
};
export default OperateTable;
