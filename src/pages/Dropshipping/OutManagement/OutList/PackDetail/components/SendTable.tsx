import { Form, Input, Radio, Table } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { receiveGoodApi } from '@/services/Waybilladministration';

const SendTable = forwardRef((props: any, ref) => {
  const { Detail } = props;
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRow, setSelectedRow] = useState<any>([]);
  console.log('Detail', Detail);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
      console.log('提交');
    },
  }));
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    checkStrictly: false,
    defaultExpandAllRows: true,
    renderCell: (checked: any, record: any, index: any, originNode: any) => {
      if (record?.boxNo) {
        return originNode;
      } else {
        return <div></div>;
      }
    },
    onChange: (Keys: any, selectedRows: any) => {
      console.log('Keys', Keys);
      setSelectedRowKeys(Keys);
      setSelectedRow(selectedRows);
      console.log('selectedRows', selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
  };
  const onFinish = async (values: any) => {
    console.log('收到提交', values);
    console.log('selectedRowKeys', selectedRowKeys);
    /*过滤主单数据*/
    const boxList = selectedRow
      .filter((item: any) => item?.boxNo)
      .map((item: any, index: any) => {
        return {
          boxId: item?.id,
          receivedBox: values[index].receivedBox,
          receivedSkuList: item.skuList.map((item: any) => {
            return { skuId: item.id, ...values['number'][item?.id] };
          }),
        };
      });
    console.log('boxList', boxList);
    const params = {
      inboundId: Detail?.id,
      boxList: boxList,
    };
    const res = await receiveGoodApi(params);
    console.log('adasdsad', res);
  };
  const columns: any = [
    {
      title: 'SKU',
      dataIndex: ['sku', 'code'],
      key: 'id',
      width: 200,
    },
    {
      title: '产品名称',
      dataIndex: ['sku', 'cnName'],
      width: 100,
      key: 'name',
    },
    {
      title: 'FNSKU',
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: '发货数量',
      width: 100,
      dataIndex: 'quatity',
      key: 'quatity',
    },
    {
      title: '尺寸',
      dataIndex: 'boxNo',
      key: 'boxNo',
      width: 200,
      render: (text: any, record: any) => {
        return `${record?.sku?.length}*${record?.sku?.width}*${record?.sku?.height}`;
      },
    },
    {
      title: '重量',
      dataIndex: ['sku', 'weight'],
      key: 'weight',
      width: 200,
    },
  ];

  return (
    <div style={{ background: '#fff' }}>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Form form={form} onFinish={onFinish}>
        <Table
          size={'small'}
          rowKey={'id'}
          columns={columns}
          dataSource={Detail}
          scroll={{ x: 'max-content' }}
          //childrenColumnName={'skuList'}
        />
      </Form>
    </div>
  );
});
export default SendTable;
