import { Button, Form, Input, message, Radio, Select, Table } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { getRemarkListAPI } from '@/services/productAndPrice/api';
import { packOutboundApi } from '@/services/Waybilladministration';
import { ProFormSelect } from '@ant-design/pro-components';
import { airLineListAPI } from '@/services/booking';
import { getSummarizedList } from '@/services/ConfiguredCar';

const GoodTable = forwardRef((props: any, ref) => {
  const { Detail } = props;
  const [form] = Form.useForm<any>();
  console.log('Detail', Detail);
  const [DateSourse, setDateSourse] = useState([]);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
    },
  }));
  useEffect(() => {
    setDateSourse([{ skuInfoList: Detail?.skuInfoList }]);
  }, [Detail]);
  const onFinish = async (value: any) => {
    const packInfoList = value;
    console.log('packInfoList', packInfoList);
    const TransData = Object.keys(packInfoList).map(
      (item) => packInfoList[item],
    );
    const res = await packOutboundApi({
      packInfoList: TransData,
      outboundId: Detail?.id,
    });
    if (res?.status) {
      message.success('打包成功');
    }
  };
  const columns: any = [
    {
      title: '包裹',
      dataIndex: 'id',
      key: 'id',
      render: (text: any, record: any, index: number) => `${index + 1}`,
    },
    {
      title: '包材',
      dataIndex: 'id22',
      key: 'id222',
      render: (text: any, record: any, index: number) => {
        return (
          //<Form.Item name={[index, 'packMaterialId']} style={{ margin: 0 }}>
          <ProFormSelect
            style={{ width: '140px' }}
            name={['packMaterialId']}
            noStyle={true}
            params={Detail}
            // @ts-ignore
            request={async (v) => {
              console.log('v', v);
              if (Detail) {
                const { status, data } = await getSummarizedList({
                  self: 1,
                  usage: '包装',
                  warehouseId: Detail?.warehouseId,
                });
                if (status) {
                  return data.list.map((item: any) => {
                    return {
                      label: item?.sku?.cnName,
                      value: item?.skuId,
                    };
                  });
                }
                return [];
              }
            }}
          />
          //</Form.Item>
        );
      },
    },
    {
      title: '尺寸',
      dataIndex: 'quatity',
      key: 'quatity',
      render: (text: any, record: any, index: number) => {
        return (
          <div style={{ display: 'flex' }}>
            <Form.Item
              name={[index, 'length']}
              style={{ width: 80, marginRight: 5, marginBottom: 0 }}
            >
              <Input placeholder={'长'} />
            </Form.Item>
            <Form.Item
              name={[index, 'width']}
              style={{ width: 80, marginRight: 5, marginBottom: 0 }}
            >
              <Input placeholder={'宽'} />
            </Form.Item>
            <Form.Item
              name={[index, 'height']}
              style={{ width: 80, margin: 0, marginBottom: 0 }}
            >
              <Input placeholder={'高'} />
            </Form.Item>
          </div>
        );
      },
    },
    {
      title: '重量',
      dataIndex: 'weight',
      key: 'weight',
      render: (text: any, record: any, index: number) => {
        return (
          <div style={{ display: 'flex' }}>
            <Form.Item name={[index, 'weight']} style={{ margin: 0 }}>
              <Input />
            </Form.Item>
          </div>
        );
      },
    },
    {
      title: '包裹内SKU*数量',
      dataIndex: 'skuInfoList',
      key: 'boxNo',
      render: (text: any, record: any, index: number) => {
        console.log('record', record);
        return (
          <div>
            {record?.skuInfoList?.map((item: any) => {
              return (
                <Form.Item
                  name={[index, 'skuDetails', item?.sku?.id]}
                  label={<div>{item?.sku?.code} * </div>}
                  key={item.code}
                  colon={false}
                  style={{ marginBottom: 5 }}
                >
                  <Input />
                </Form.Item>
              );
            })}
          </div>
        );
      },
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Form form={form} onFinish={onFinish}>
        <Table columns={columns} dataSource={DateSourse} scroll={{ x: 1200 }} />
      </Form>
      <Button style={{ marginTop: 10 }} type={'primary'} ghost>
        添加包裹
      </Button>
    </div>
  );
});
export default GoodTable;
