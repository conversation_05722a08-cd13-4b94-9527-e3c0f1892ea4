// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import styles from './index.less';

import { getOutboundDetail } from '@/services/Waybilladministration';
import { useLocation } from 'umi';
import GoodTable from './components/GoodTable';
import SendTable from './components/SendTable';
import { deliveryMethod, getLabelByValue } from '@/utils/constant';
import { Button, Space } from 'antd';

const ScanStore = () => {
  const [Keyword, setKeyword] = useState('');
  const [Detail, setDetail] = useState({});
  const addRef = useRef<any>(null);
  const location = useLocation();
  useEffect(() => {
    const { state }: any = location;
    console.log('state', state);
    setKeyword(state?.id);
  }, []);
  const handleSubmit = () => {
    addRef?.current?.handleSubmit();
  };
  const getInboundDetailApi = async (id: any) => {
    const res = await getOutboundDetail({ id });
    setDetail(res?.data);
    console.log('结果', res);
  };
  /*获取*/

  useEffect(() => {
    console.log('keyword', Keyword);
    if (Keyword) {
      getInboundDetailApi(Keyword);
    }
  }, [Keyword]);
  return (
    <div className={styles.container}>
      <div className={styles.top}>
        <div className={styles.title}>出库单打包</div>
        <div>
          <Space>
            <Button>打印拣货单</Button>
          </Space>
        </div>
      </div>
      <div className={styles.fill}>
        <>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={
              <div style={{ fontWeight: 600 }}>出库单号：{Detail?.no}</div>
            }
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['warehouse', 'name']}
                label="出库仓库"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.warehouse?.name}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="运输类型"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {getLabelByValue(Detail?.deliveryMethod, deliveryMethod)}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流承运商"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.express}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['trackNo']}
                label="物流单号"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.trackNo}
              </ProDescriptions.Item>

              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流面单"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>发货列表</div>}
            extra={
              <div>
                <span>SKU种类：2</span>
                <span>发货产品总数：3</span>
              </div>
            }
          >
            <SendTable Detail={Detail?.skuInfoList} />
          </ProCard>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>打包信息</div>}
            extra={<div>包裹数量：1</div>}
          >
            <GoodTable Detail={Detail} ref={addRef} />
          </ProCard>
          <ProCard
            gutter={8}
            className={styles.footer}
            style={{ textAlign: 'center' }}
            /* extra={<Button type={'primary'}>更新</Button>}*/
          >
            <Space>
              <Button>取消</Button>
              <Button type={'primary'} onClick={handleSubmit}>
                确定打包
              </Button>
            </Space>
          </ProCard>
        </>
      </div>
    </div>
  );
};
export default ScanStore;
