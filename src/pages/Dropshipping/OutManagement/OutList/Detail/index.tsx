// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import styles from './index.less';

import { getOutboundDetail } from '@/services/Waybilladministration';
import { useLocation } from 'umi';
import GoodTable from './components/GoodTable';
import SendTable from './components/SendTable';
import OperateTable from './components/OperateTable';
import { deliveryMethod, getLabelByValue } from '@/utils/constant';
import { Button, message, Space, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { REQUESTADDRESS, REQUESTADDRESS_W } from '@/globalData';

const ScanStore = () => {
  const [Keyword, setKeyword] = useState('');
  const [Detail, setDetail] = useState({});
  const [State, setState] = useState(0);
  const addRef = useRef<any>(null);
  const location = useLocation();
  useEffect(() => {
    const { state }: any = location;
    console.log('state', state);
    setKeyword(state?.id);
  }, []);
  const handleSubmit = () => {
    addRef?.current?.handleSubmit();
  };
  const getInboundDetailApi = async (id: any) => {
    const res = await getOutboundDetail({ id });
    setDetail(res?.data);
    console.log('结果', res);
  };
  /*获取*/
  const props: any = {
    name: 'file',
    action: `${REQUESTADDRESS_W}/outbound/uploadLabel?service_id=WMS`,
    headers: {
      token: localStorage.getItem('token'),
    },
    data: {
      outboundId: Detail?.id,
    },
    onChange(info: any) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        getInboundDetailApi(Keyword);
        message.success(`${info.file.name} file uploaded successfully`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  useEffect(() => {
    console.log('keyword', Keyword);
    if (Keyword) {
      getInboundDetailApi(Keyword);
    }
  }, [Keyword]);
  return (
    <div className={styles.container}>
      <div className={styles.top}>
        <div className={styles.title}>出库单详情</div>
        <div>
          <Space>
            <Button>打印拣货单</Button>
            <Button>打印拣货单</Button>
          </Space>
        </div>
      </div>
      <div className={styles.fill}>
        <>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={
              <div style={{ fontWeight: 600 }}>出库单号：{Detail?.no}</div>
            }
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['warehouse', 'name']}
                label="出库仓库"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.warehouse?.name}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="运输类型"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {getLabelByValue(Detail?.deliveryMethod, deliveryMethod)}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流承运商"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.express}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['trackNo']}
                label="物流单号"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.trackNo}
              </ProDescriptions.Item>

              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流面单"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.labelUrl ? (
                  <a>下载面单</a>
                ) : (
                  <Upload {...props}>
                    <Button
                      size={'small'}
                      style={{ marginRight: '20px' }}
                      icon={<UploadOutlined />}
                    >
                      更新面单
                    </Button>
                  </Upload>
                )}
              </ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>订单信息</div>}
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="销售平台"
                editable={false}
                valueType="text"
              >
                {Detail?.clientOrder?.platform}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="销售店铺"
                editable={false}
                valueType="text"
              >
                {Detail?.clientOrder?.shopName}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="平台单号"
                editable={false}
                valueType="text"
              >
                {Detail?.clientOrder?.orderId}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="参考单号"
                editable={false}
                valueType="text"
              >
                {Detail?.clientOrder?.referenceNo}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="订单销售金额"
                editable={false}
                valueType="text"
              >
                {Detail?.clientOrder?.amount}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="VAT税号"
                editable={false}
                valueType="text"
              >
                {Detail?.clientOrder?.vat}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="电池类型"
                editable={false}
                valueType="text"
              ></ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>收货信息</div>}
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['recipientAddress', 'contactName']}
                label="姓名"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.contactName}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['recipientAddress', 'contactPhone']}
                label="电话"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.contactName}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="公司"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.companyName}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="邮箱"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.email}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="国家"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.country}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="州/省"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.province}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="城市"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.city}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="邮编"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.zipCode}
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="街道门牌"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                {Detail?.recipientAddress?.street}
              </ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>包裹信息</div>}
            /*extra={<div>包裹数量：1</div>}*/
          >
            <GoodTable />
          </ProCard>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>发货信息</div>}
            /*    extra={
              <div>
                <span>SKU种类：2</span>
                <span>发货产品总数：3</span>
              </div>
            }*/
          >
            <SendTable Detail={Detail?.skuInfoList} />
          </ProCard>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div style={{ fontWeight: 600 }}>操作记录</div>}
            /*       extra={
              <div>
                <span>SKU种类：2</span>
                <span>发货产品总数：3</span>
              </div>
            }*/
          >
            <OperateTable />
          </ProCard>
        </>
      </div>
    </div>
  );
};
export default ScanStore;
