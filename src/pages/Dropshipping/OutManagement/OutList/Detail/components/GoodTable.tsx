import { Input, Radio, Table } from 'antd';
import React from 'react';

const GoodTable = (props: any) => {
  const { Detail } = props;
  const columns: any = [
    {
      title: '物流单号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '物流商',
      dataIndex: 'id22',
      key: 'id222',
    },
    {
      title: '物流服务',
      dataIndex: 'quatity',
      key: 'quatity',
    },
    {
      title: '包裹尺寸',
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
    },
    {
      title: '包裹重量',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '使用包材',
      dataIndex: 'boxNo',
      key: 'boxNo',
    },
    {
      title: '物流面单',
      dataIndex: 'code',
      key: 'code',
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Table
        columns={columns}
        dataSource={Detail?.boxList}
        scroll={{ x: 1200 }}
        childrenColumnName={'skuList'}
      />
    </div>
  );
};
export default GoodTable;
