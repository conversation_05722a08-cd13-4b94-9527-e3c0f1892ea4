import { Form, Input, Radio, Table } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { receiveGoodApi } from '@/services/Waybilladministration';

const UnTable = forwardRef((props: any, ref) => {
  const { Detail } = props;
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRow, setSelectedRow] = useState<any>([]);
  console.log('Detail', Detail);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
      console.log('提交');
    },
  }));
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    checkStrictly: false,
    defaultExpandAllRows: true,
    renderCell: (checked: any, record: any, index: any, originNode: any) => {
      if (record?.boxNo) {
        return originNode;
      } else {
        return <div></div>;
      }
    },
    onChange: (Keys: any, selectedRows: any) => {
      console.log('Keys', Keys);
      setSelectedRowKeys(Keys);
      setSelectedRow(selectedRows);
      console.log('selectedRows', selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
  };
  const onFinish = async (values: any) => {
    console.log('收到提交', values);
    console.log('selectedRowKeys', selectedRowKeys);
    /*过滤主单数据*/
    const boxList = selectedRow
      .filter((item: any) => item?.boxNo)
      .map((item: any, index: any) => {
        return {
          boxId: item?.id,
          receivedBox: values[index].receivedBox,
          receivedSkuList: item.skuList.map((item: any) => {
            return { skuId: item.id, ...values['number'][item?.id] };
          }),
        };
      });
    console.log('boxList', boxList);
    const params = {
      inboundId: Detail?.id,
      boxList: boxList,
    };
    const res = await receiveGoodApi(params);
    console.log('adasdsad', res);
  };
  const columns: any = [
    {
      title: '箱型号',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (_: any, record: any) => {
        if (!record?.boxNo) {
          return <></>;
        } else {
          return record?.id;
        }
      },
    },
    {
      title: '预报箱数',
      dataIndex: 'quatity',
      width: 100,
      key: 'quatity',
    },
    {
      title: '已接收',
      width: 100,
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
    },
    {
      title: '本次接收',
      width: 200,
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
      render: (_: any, record: any, index: any) => {
        if (record?.boxNo) {
          return (
            <Form.Item
              name={[index, 'receivedBox']}
              style={{ margin: 0 }}
              rules={[
                {
                  required: true,
                  message: '请输入实收数量（不良品）',
                },
              ]}
            >
              <Input />
            </Form.Item>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
    },
    {
      title: '自定义箱条码',
      dataIndex: 'boxNo',
      key: 'boxNo',
      width: 200,
    },
  ];
  const expandColumns: any = [
    {
      title: '箱内SKU',
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: '中文品名',
      dataIndex: 'cnName',
      key: 'cnName',
      width: 150,
    },
    {
      title: '实收（良品）',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (_: any, record: any, index: any) => {
        console.log('records', record);
        console.log('record', record?.id);
        if (!record?.boxNo) {
          return (
            <Form.Item
              name={['number', record?.id, 'normalQuatity']}
              style={{ margin: 0 }}
              rules={[
                {
                  required: selectedRowKeys.includes(record?.id),
                  message: '请输入实收数量（良品）',
                },
              ]}
            >
              <Input />
            </Form.Item>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      title: '实收（不良品）',
      dataIndex: 'name',
      width: 200,
      key: 'name',
      render: (_: any, record: any, index: any) => {
        if (!record?.boxNo) {
          return (
            <Form.Item
              name={['number', record?.id, 'defectiveQuatity']}
              style={{ margin: 0 }}
              rules={[
                {
                  required: true,
                  message: '请输入实收数量（不良品）',
                },
              ]}
            >
              <Input />
            </Form.Item>
          );
        } else {
          return <></>;
        }
      },
    },
    {
      title: '预报总数',
      dataIndex: 'number',
      key: 'number',
      width: 150,
    },
    {
      title: '单箱数量',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    // {
    //   title: 'OMS预报尺寸',
    //   dataIndex: 'name',
    //   key: 'name',
    // },
    {
      title: '预报材积（W*H*L-W）',
      dataIndex: 'name',
      width: 200,
      key: 'name',
    },
    // {
    //   title: 'WMS测量尺寸/cm',
    //   dataIndex: 'name',
    //   key: 'name',
    // },
    {
      title: 'WMS材积（W*H*L-W）',
      dataIndex: 'name',
      width: 200,
      key: 'name',
    },
  ];

  const expandedRowRender = (index: any) => {
    console.log('index', index);
    return (
      <Table<any>
        columns={expandColumns}
        dataSource={index?.skuList}
        pagination={false}
      />
    );
  };

  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>预报箱子总数：{Detail?.boxQuatity}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>待接收箱型：{Detail?.boxList?.length}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>
            待接收箱子数量：{Detail?.boxQuatity - Detail?.receivedBoxQuatity}
          </span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Form form={form} onFinish={onFinish}>
        <Table
          rowSelection={{
            ...rowSelection,
          }}
          expandable={{
            expandedRowRender,
            defaultExpandAllRows: true,
            showExpandColumn: false,
          }}
          size={'small'}
          rowKey={'id'}
          columns={columns}
          dataSource={Detail?.boxList}
          scroll={{ x: 'max-content' }}
          //childrenColumnName={'skuList'}
        />
      </Form>
    </div>
  );
});
export default UnTable;
