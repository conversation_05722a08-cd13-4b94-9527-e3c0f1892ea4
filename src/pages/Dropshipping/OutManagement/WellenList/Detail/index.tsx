// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { Tabs, Tag } from 'antd';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import styles from './index.less';
import UnTable from '@/pages/Dropshipping/InManagement/ScanStore/components/UnTable';
import HasTable from '@/pages/Dropshipping/InManagement/ScanStore/components/HasTable';
import { getInboundDetail } from '@/services/Waybilladministration';
import { useLocation } from 'umi';

const ScanStore = () => {
  const [Keyword, setKeyword] = useState('');
  const [Detail, setDetail] = useState({});
  const addRef = useRef<any>(null);
  const location = useLocation();
  useEffect(() => {
    const { state }: any = location;
    console.log('state', state);
    setKeyword(state?.id);
  }, []);

  const getInboundDetailApi = async (id: any) => {
    const res = await getInboundDetail({ id });
    setDetail(res?.data);
    console.log('结果', res);
  };
  /*获取*/

  useEffect(() => {
    console.log('keyword', Keyword);
    if (Keyword) {
      getInboundDetailApi(Keyword);
    }
  }, [Keyword]);
  return (
    <div className={styles.container}>
      <div className={styles.fill}>
        <>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={
              <div>
                入库单：{Detail?.no}
                <Tag style={{ marginLeft: 10 }} color={'orange'}>
                  收货中
                </Tag>
              </div>
            }
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="海外仓库"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="服务产品"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="送仓形式"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="创建时间"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="附件"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="备注"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>
          <ProCard
            gutter={8}
            style={{ marginBottom: 12 }}
            title={<div>交货信息</div>}
          >
            <ProDescriptions column={3}>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="交货方式"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                仓库取货
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="预估取货时间"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="物流跟踪号"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
            </ProDescriptions>
            <div style={{ marginBottom: 12 }}>取件地址</div>
            <ProDescriptions column={4}>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="姓名"
                editable={false}
                valueType="text"
                className={styles.require}
              >
                ada
              </ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="电话"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="公司"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="邮箱"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="国家"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="州/省"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="城市"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="邮编"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
              <ProDescriptions.Item
                dataIndex={['id']}
                label="街道门牌"
                editable={false}
                valueType="text"
                className={styles.require}
              ></ProDescriptions.Item>
            </ProDescriptions>
          </ProCard>
          <div
            style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
          ></div>
          <Tabs
            defaultActiveKey="1"
            type="card"
            className={styles.tabBox}
            style={{ marginBottom: 32 }}
            items={[
              {
                label: `待收货`,
                key: '1',
                children: <UnTable Detail={Detail} ref={addRef} />,
              },
              {
                label: `已收货`,
                key: '2',
                children: <HasTable Detail={Detail} />,
              },
            ]}
          />
        </>
      </div>
    </div>
  );
};
export default ScanStore;
