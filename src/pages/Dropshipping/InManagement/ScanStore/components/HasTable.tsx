import { Form, Input, Radio, Table } from 'antd';
import React from 'react';
import { calculateSum } from '@/utils/utils';

const HasTable = (props: any) => {
  const { Detail } = props;
  const Datasource: any = [];
  console.log('Detail', Detail);
  Detail?.receiveRecordList?.map((item: any) => {
    const { skuDetails, ...exit } = item;
    Datasource.push({
      ...exit,
      ...skuDetails[0],
      boxId: exit.id,
      ...skuDetails[0].sku,
    });
    const newArr = skuDetails.slice(1).map((item: any) => {
      return { ...item, myboxNo: exit.boxNo, boxId: exit.id, ...item.sku };
    });
    Datasource.push(...newArr);
    return true;
  });
  const columns: any = [
    {
      title: '箱信息',
      children: [
        /*    {
          title: '序号',
          dataIndex: 'boxNo',
          key: 'boxNo',
          width: 60,
          onCell: (record: any, index: number) => {
            if (record?.boxNo) {
              const select = Detail?.receiveRecordList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              // console.log('select', select);
              return { rowSpan: select?.skuDetails?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
          render: (_: any, record: any, index: any) => {
            return index + 1;
          },
        },*/
        {
          title: '箱条码',
          dataIndex: 'boxNo',
          key: 'boxNo',
          width: 140,
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.receiveRecordList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuDetails?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
        {
          title: '预报箱数',
          dataIndex: 'quatity',
          width: 100,
          key: 'quatity',
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.receiveRecordList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuDetails?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
        {
          title: '已接收',
          width: 100,
          dataIndex: 'receivedQuatity',
          key: 'receivedQuatity',
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.receiveRecordList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuDetails?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
      ],
    },
    {
      title: '箱内货物',
      children: [
        {
          title: 'SKU',
          dataIndex: 'code',
          key: 'code',
          width: 200,
        },
        {
          title: '中文品名',
          dataIndex: 'cnName',
          key: 'cnName',
          width: 150,
        },
        {
          title: '实收（良品）',
          dataIndex: 'name',
          key: 'name',
          width: 130,
          render: (_: any, record: any, index: any) => {
            return record?.normalQuatity;
          },
        },
        {
          title: '实收（不良品）',
          dataIndex: 'name',
          width: 150,
          key: 'name',
          render: (_: any, record: any, index: any) => {
            return record?.defectiveQuatity;
          },
        },
        {
          title: '预报总数',
          dataIndex: 'number',
          key: 'number',
          width: 150,
          render: (_: any, record: any) => {
            const quatity = Detail?.receiveRecordList.find(
              (item: any) =>
                item?.boxNo === record?.boxNo ||
                item?.boxNo === record?.myboxNo,
            )?.quatity;
            return quatity * record?.number;
          },
        },
        {
          title: '单箱数量',
          dataIndex: 'number',
          key: 'number',
          width: 150,
        },
        {
          title: 'WMS（L*W*H-W）',
          dataIndex: 'name',
          width: 200,
          key: 'name',
          render: (_: any, record: any) => {
            return `${record?.length} * ${record.width} * ${record.height} - ${record.weight}`;
          },
        },
        {
          title: '是否新品',
          dataIndex: 'measured',
          width: 200,
          key: 'measured',
          render: (_: any, record: any) => {
            return record?.measured ? '否' : '是';
          },
        },
      ],
    },
  ];

  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>预报箱子总数：{Detail?.boxQuatity}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>
            已收货箱数：
            {calculateSum(Detail?.receiveRecordList, 'receivedQuatity')}
          </span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Table
        columns={columns}
        dataSource={Datasource}
        scroll={{ x: 'max-content' }}
        childrenColumnName={'skuList'}
      />
    </div>
  );
};
export default HasTable;
