import { Form, Table } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { receiveGoodApi } from '@/services/Waybilladministration';

const UnTable = forwardRef((props: any, ref) => {
  /*先保留以免后面要按这个*/
  const { Detail } = props;
  const Datasource: any = [];
  Detail?.boxList?.map((item: any) => {
    const { skuList, ...exit } = item;
    Datasource.push({
      ...exit,
      ...skuList[0],
      boxId: exit.id,
      ...skuList[0].sku,
    });
    const newArr = skuList.slice(1).map((item: any) => {
      return { ...item, myboxNo: exit.boxNo, boxId: exit.id, ...item.sku };
    });
    Datasource.push(...newArr);
    console.log('Datasource', Datasource);
    return true;
  });
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRow, setSelectedRow] = useState<any>([]);
  console.log('Detail', Detail);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
      console.log('提交');
    },
  }));
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    checkStrictly: false,
    defaultExpandAllRows: true,
    renderCell: (checked: any, record: any, index: any, originNode: any) => {
      if (record?.boxNo) {
        return originNode;
      } else {
        return <div></div>;
      }
    },
    onChange: (Keys: any, selectedRows: any) => {
      console.log('Keys', Keys);
      setSelectedRowKeys(Keys);
      setSelectedRow(selectedRows);
      console.log('selectedRows', selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
  };
  const columns: any = [
    {
      title: '箱型号',
      dataIndex: 'id',
      key: 'id',
      width: 200,
      render: (_: any, record: any) => {
        if (!record?.boxNo) {
          return <></>;
        } else {
          return record?.id;
        }
      },
      onCell: (record: any) => {
        if (record?.boxNo) {
          const select = Detail?.boxList.find(
            (item: any) => item?.boxNo === record?.boxNo,
          );
          console.log('select', select);
          //console.log('select', select);
          return { rowSpan: select?.skuList?.length };
        } else {
          return { rowSpan: 0 };
        }
      },
    },
    {
      title: '预报箱数',
      dataIndex: 'quatity',
      width: 100,
      key: 'quatity',
      onCell: (record: any) => {
        if (record?.boxNo) {
          const select = Detail?.boxList.find(
            (item: any) => item?.boxNo === record?.boxNo,
          );
          console.log('select', select);
          //console.log('select', select);
          return { rowSpan: select?.skuList?.length };
        } else {
          return { rowSpan: 0 };
        }
      },
    },
    {
      title: '已接收',
      width: 100,
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
      onCell: (record: any) => {
        if (record?.boxNo) {
          const select = Detail?.boxList.find(
            (item: any) => item?.boxNo === record?.boxNo,
          );
          console.log('select', select);
          //console.log('select', select);
          return { rowSpan: select?.skuList?.length };
        } else {
          return { rowSpan: 0 };
        }
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
      onCell: (record: any) => {
        if (record?.boxNo) {
          const select = Detail?.boxList.find(
            (item: any) => item?.boxNo === record?.boxNo,
          );
          console.log('select', select);
          //console.log('select', select);
          return { rowSpan: select?.skuList?.length };
        } else {
          return { rowSpan: 0 };
        }
      },
    },
    {
      title: '自定义箱条码',
      dataIndex: 'boxNo',
      key: 'boxNo',
      width: 200,
      onCell: (record: any) => {
        if (record?.boxNo) {
          const select = Detail?.boxList.find(
            (item: any) => item?.boxNo === record?.boxNo,
          );
          console.log('select', select);
          //console.log('select', select);
          return { rowSpan: select?.skuList?.length };
        } else {
          return { rowSpan: 0 };
        }
      },
    },
    {
      title: 'SKU',
      dataIndex: 'code',
      key: 'code',
      width: 200,
    },
    {
      title: '中文品名',
      dataIndex: 'cnName',
      key: 'cnName',
      width: 150,
    },
    {
      title: '实收（良品）',
      dataIndex: 'name',
      key: 'name',
      width: 130,
      render: (_: any, record: any, index: any) => {
        return record?.normalQuatity;
      },
    },
    {
      title: '实收（不良品）',
      dataIndex: 'name',
      width: 150,
      key: 'name',
      render: (_: any, record: any, index: any) => {
        return record?.defectiveQuatity;
      },
    },
    {
      title: '预报总数',
      dataIndex: 'number',
      key: 'number',
      width: 150,
      render: (_: any, record: any) => {
        const quatity = Detail?.boxList.find(
          (item: any) =>
            item?.boxNo === record?.boxNo || item?.boxNo === record?.myboxNo,
        )?.quatity;
        return quatity * record?.number;
      },
    },
    {
      title: '单箱数量',
      dataIndex: 'number',
      key: 'number',
      width: 150,
    },
    {
      title: 'WMS（L*W*H-W）',
      dataIndex: 'name',
      width: 200,
      key: 'name',
      render: (_: any, record: any) => {
        return `${record?.length} * ${record.width} * ${record.height} - ${record.weight}`;
      },
    },
    {
      title: '是否新品',
      dataIndex: 'measured',
      width: 200,
      key: 'measured',
      render: (_: any, record: any) => {
        return record?.measured ? '否' : '是';
      },
    },
  ];

  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>预报箱子总数：{Detail?.boxQuatity}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>待接收箱型：{Detail?.boxList?.length}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>
            待接收箱子数量：{Detail?.boxQuatity - Detail?.receivedBoxQuatity}
          </span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      {Detail?.boxList && (
        <Table
          columns={columns}
          dataSource={Datasource}
          scroll={{ x: 'max-content' }}
          childrenColumnName={'skuList'}
        />
      )}
    </div>
  );
});
export default UnTable;
