import {
  Col,
  Form,
  Input,
  message,
  Popconfirm,
  Radio,
  Row,
  Select,
  Table,
} from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import {
  measureSkuApi,
  receiveGoodApi,
} from '@/services/Waybilladministration';
import styles from './untable2.less';
const UnTable2 = forwardRef((props: any, ref) => {
  const { Detail, getInboundDetailApi } = props;
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRow, setSelectedRow] = useState<any>([]);
  const [CheckBox, setCheckBox] = useState<any>({});
  const Datasource: any = [];
  console.log('Detail', Detail);
  Detail?.boxList?.map((item: any) => {
    const { skuList, ...exit } = item;
    Datasource.push({ ...exit, ...skuList[0], boxId: exit.id });
    const newArr = skuList.slice(1).map((item: any) => {
      return { ...item, myboxNo: exit.boxNo, boxId: exit.id };
    });
    Datasource.push(...newArr);
    return true;
  });

  console.log('Datasource', Datasource);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
      console.log('提交');
    },
  }));
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    checkStrictly: false,
    defaultExpandAllRows: true,
    renderCell: (checked: any, record: any, index: any, originNode: any) => {
      if (record?.boxNo) {
        return originNode;
      } else {
        return <div></div>;
      }
    },
    onChange: (Keys: any, selectedRows: any) => {
      console.log('Keys', Keys);
      setSelectedRowKeys(Keys);
      setSelectedRow(selectedRows);
      console.log('selectedRows', selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
  };
  const onCheck = async (e: any, record: any) => {
    const boxNumber = record.boxId;
    const allForm = form.getFieldsValue();
    const receivedSkuList = allForm[boxNumber]?.receivedSkuList;
    console.log('boxNumber', boxNumber);
    console.log('allForm', allForm[boxNumber]);
    /* if (!!e.target.value) {
      setCheckBox({ ...CheckBox, [record.boxNo]: true });
    }*/
    //填了没有
    let flag = true;
    for (const allFormKey in receivedSkuList) {
      if (!!receivedSkuList[allFormKey].normalQuatity) {
        flag = true;
      } else {
        flag = false;
        break;
      }
    }
    if (!!allForm[boxNumber].receivedBox) {
      if (flag) {
        console.log('全部都填了');
        setCheckBox({ ...CheckBox, [boxNumber]: true });
      } else {
        console.log('不必填');
        setCheckBox({ ...CheckBox, [boxNumber]: true });
      }
    } else {
      if (flag) {
        //2个都没填也不要验证
        console.log('必填');
        setCheckBox({ ...CheckBox, [boxNumber]: true });
      } else {
        console.log('2个都没不必填');
        setCheckBox({ ...CheckBox, [boxNumber]: false });
      }
    }
    form.validateFields();
  };
  const selectAfter = (
    <Form.Item
      name="measureType"
      initialValue={1}
      noStyle={true}
      rules={[{ required: true, message: '必填项不能为空!' }]}
    >
      <Select
        options={[
          { value: 0, label: 'cm / kg' },
          { value: 1, label: 'in / lb' },
        ]}
      />
    </Form.Item>
  );

  const onFinish = async (values: any) => {
    console.log('收到提交', values);
    console.log('CheckBox', CheckBox);
    /*过滤主单数据*/
    let arr = [];
    for (let key in values) {
      if (values.hasOwnProperty(key)) {
        //空的直接过滤
        if (!values[key].receivedBox) {
          console.log('空的');
          continue;
        }
        arr.push({
          boxId: key,
          remark: values[key].remark,
          receivedBox: values[key].receivedBox,
          receivedSkuList: Object.keys(values[key].receivedSkuList).map(
            (item) => {
              return { skuId: item, ...values[key].receivedSkuList[item] };
            },
          ),
        });
      }
    }
    if (arr.length === 0) {
      message.error('请填写数据');
      return;
    }
    console.log('arr', arr);
    const params = {
      inboundId: Detail?.id,
      boxList: arr,
    };
    const res = await receiveGoodApi(params);
    console.log('adasdsad', res);
    if (res.status) {
      message.success('提交成功！');
      getInboundDetailApi(Detail?.id);
    }
  };
  const columns: any = [
    {
      title: '箱信息',
      children: [
        {
          title: '序号',
          dataIndex: 'boxNo',
          key: 'boxNo',
          width: 50,
          className: styles.top,
          onCell: (record: any, index: number) => {
            if (record?.boxNo) {
              const select = Detail?.boxList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuList?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
          render: (_: any, record: any, index: any) => {
            return index + 1;
          },
        },
        {
          title: '箱条码',
          dataIndex: 'boxNo',
          key: 'boxNo',
          width: 140,
          className: styles.top,
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.boxList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuList?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
        {
          title: '预报箱数',
          dataIndex: 'quatity',
          width: 100,
          className: styles.top,
          key: 'quatity',
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.boxList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuList?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
        {
          title: '已接收',
          width: 100,
          dataIndex: 'receivedQuatity',
          key: 'receivedQuatity',
          className: styles.top,
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.boxList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              //console.log('select', select);
              return { rowSpan: select?.skuList?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
        {
          title: '本次接收',
          width: 100,
          dataIndex: 'receivedQuatity',
          key: 'receivedQuatity',
          className: styles.top,
          render: (_: any, record: any, index: any) => {
            console.log(
              '本次接收record?.boxNo]',
              CheckBox[record?.boxNo] || CheckBox[record?.myboxNo],
            );
            if (record?.boxNo) {
              return (
                <Form.Item
                  name={[record.boxId, 'receivedBox']}
                  style={{ margin: 0 }}
                  rules={[
                    {
                      required: CheckBox[record?.boxId],
                      message: '请输入本次接收',
                    },
                  ]}
                >
                  <Input onChange={(e: any) => onCheck(e, record)} />
                </Form.Item>
              );
            } else {
              return <></>;
            }
          },
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.boxList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              return { rowSpan: select?.skuList?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
          className: styles.top,
          width: 100,
          onCell: (record: any) => {
            if (record?.boxNo) {
              const select = Detail?.boxList.find(
                (item: any) => item?.boxNo === record?.boxNo,
              );
              return { rowSpan: select?.skuList?.length };
            } else {
              return { rowSpan: 0 };
            }
          },
          render: (_: any, record: any, index: any) => {
            if (record?.boxNo) {
              return (
                <Form.Item
                  name={[record.boxId, 'remark']}
                  style={{ margin: 0 }}
                  rules={[
                    {
                      required: false,
                      message: '请输入备注',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              );
            } else {
              return <></>;
            }
          },
        },
      ],
    },
    {
      title: '箱内货物',
      children: [
        {
          title: 'SKU',
          dataIndex: 'code',
          key: 'code',
          width: 200,
        },
        {
          title: '中文品名',
          dataIndex: 'cnName',
          key: 'cnName',
          width: 150,
        },
        {
          title: '实收（良品）',
          dataIndex: 'name',
          key: 'name',
          width: 130,
          render: (_: any, record: any, index: any) => {
            console.log(
              '实收数量boxNo',
              CheckBox[record?.boxNo] || CheckBox[record?.myboxNo],
            );
            return (
              <Form.Item
                name={[
                  record?.boxId,
                  'receivedSkuList',
                  record?.id,
                  'normalQuatity',
                ]}
                style={{ margin: 0 }}
                rules={[
                  {
                    required: CheckBox[record?.boxId],
                    message: '请输入数量',
                  },
                ]}
              >
                <Input onChange={(e: any) => onCheck(e, record)} />
              </Form.Item>
            );
          },
        },
        {
          title: '实收（不良品）',
          dataIndex: 'name',
          width: 150,
          key: 'name',
          render: (_: any, record: any) => {
            return (
              <Form.Item
                name={[
                  record?.boxId,
                  'receivedSkuList',
                  record?.id,
                  'defectiveQuatity',
                ]}
                initialValue={0}
                style={{ margin: 0 }}
                rules={[
                  {
                    required: false,
                    message: '请输入数量',
                  },
                ]}
              >
                <Input defaultValue={0} />
              </Form.Item>
            );
          },
        },
        {
          title: '预报总数',
          dataIndex: 'number',
          key: 'number',
          width: 150,
          render: (_: any, record: any) => {
            const quatity = Detail?.boxList.find(
              (item: any) =>
                item?.boxNo === record?.boxNo ||
                item?.boxNo === record?.myboxNo,
            )?.quatity;
            return quatity * record?.number;
          },
        },
        {
          title: '单箱数量',
          dataIndex: 'number',
          key: 'number',
          width: 150,
        },
        {
          title: 'WMS（L*W*H-W）',
          dataIndex: 'name',
          width: 200,
          key: 'name',
          render: (_: any, record: any) => {
            return `${record?.length} * ${record.width} * ${record.height} - ${record.weight}`;
          },
        },
        {
          title: '是否新品',
          dataIndex: 'measured',
          width: 200,
          key: 'measured',
          render: (_: any, record: any) => {
            return record?.measured ? '否' : '是';
          },
        },
        {
          title: '操作',
          dataIndex: 'measured',
          width: 200,
          key: 'measured',
          fixed: 'right',
          render: (_: any, record: any) => {
            return record?.measured ? (
              ''
            ) : (
              <Popconfirm
                title="新品测量"
                description={
                  <Form
                    form={form2}
                    onFinish={async (v: any) => {
                      console.log('record', record);
                      const res = await measureSkuApi({
                        ...v,
                        skuId: record.id,
                      });
                      console.log('res', res);
                    }}
                  >
                    <Row>
                      <Col>
                        <Form.Item style={{ width: '100px' }} name={'length'}>
                          <Input placeholder={'长'} />
                        </Form.Item>
                      </Col>
                      <Col>
                        <Form.Item style={{ width: '100px' }} name={'width'}>
                          <Input placeholder={'宽'} />
                        </Form.Item>
                      </Col>
                      <Col>
                        <Form.Item style={{ width: '100px' }} name={'height'}>
                          <Input placeholder={'高'} />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={24}>
                        <Form.Item name={'weight'}>
                          <Input
                            placeholder={'重量'}
                            addonAfter={selectAfter}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                }
                onConfirm={() => form2.submit()}
                //onCancel={cancel}
                okText="确认"
                cancelText="取消"
              >
                <a>新品测量</a>
              </Popconfirm>
            );
          },
        },
      ],
    },
  ];

  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>预报箱子总数：{Detail?.boxQuatity}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>待接收箱型：{Detail?.boxList?.length}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>
            待接收箱子数量：{Detail?.boxQuatity - Detail?.receivedBoxQuatity}
          </span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Form form={form} onFinish={onFinish}>
        {Detail?.boxList && (
          <Table
            size={'small'}
            rowKey={'id'}
            columns={columns}
            dataSource={Datasource}
            scroll={{ x: 'max-content' }}
            //childrenColumnName={'skuList'}
          />
        )}
      </Form>
    </div>
  );
});
export default UnTable2;
