// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Button, Empty, Input, Space, Tabs, Tag } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import styles from './index.less';
import HasTable from '@/pages/Dropshipping/InManagement/ScanStore/components/HasTable';
import {
  getInboundDetail,
  getInboundList,
} from '@/services/Waybilladministration';
import { formatTime } from '@/utils/format';
import { deliverytype } from '@/utils/constant';
import UnTable2 from '@/pages/Dropshipping/InManagement/ScanStore/components/UnTable2';
import { useLocation } from 'umi';

const { Search } = Input;
const ScanStore = () => {
  const { state }: any = useLocation();
  const [Keyword, setKeyword] = useState('');
  const [Detail, setDetail] = useState({});
  const [State, setState] = useState(0);
  const addRef = useRef<any>(null);

  const handleSubmit = () => {
    addRef?.current?.handleSubmit();
  };
  const getInboundDetailApi = async (id: any) => {
    setKeyword(id);
    const res = await getInboundDetail({ id });
    setDetail(res?.data);
    console.log('结果', res);
  };
  /*获取*/
  const getInboundDetailList = async (id: any) => {
    if (!id) return;
    const res = await getInboundList({ code: id });
    console.log('res', res.data);
    if (res?.data?.total === 1) {
      console.log('res?.list', res?.data?.list);
      const id = res?.data?.list[0].id;
      getInboundDetailApi(id);
      setState(1);
    } else if (res?.data?.total === 0) {
      setState(0);
      console.log('没有数据');
    } else {
      setState(2);
      console.log('多条数据');
    }
    console.log('结果', res);
  };
  useEffect(() => {
    if (state) {
      getInboundDetailList(state?.no);
      console.log('详情', state);
    }
  }, [state]);
  const onSearch = (v: any) => {
    console.log('search', v);
    //setKeyword(v);
    getInboundDetailList(v);
  };
  /*  useEffect(() => {
    console.log('keyword', Keyword);
    getInboundDetailList(Keyword);
  }, [Keyword]);*/
  return (
    <div className={styles.container}>
      <ProCard
        gutter={8}
        className={styles.header}
        style={{ marginBottom: 12, textAlign: 'center' }}
      >
        <Search
          placeholder="请扫描或输入入库单号、客户箱唛"
          allowClear
          enterButton="搜索"
          size="large"
          style={{ width: 600 }}
          onSearch={onSearch}
        />
      </ProCard>
      <div className={styles.fill}>
        {State === 0 && (
          <>
            {Keyword && (
              <Alert
                message={`未能在系统中找到可识别的记录：${Keyword}，请使用入库单号查询待收货数据。若在入库单中无法找到此记录，请创建入库认领单。`}
                type="error"
                showIcon
              />
            )}
            <Empty
              style={{ marginTop: '200px' }}
              description={State === 0 ? '找不到该记录' : '请扫描收货'}
            />
          </>
        )}
        {State === 1 && (
          <>
            <ProCard
              gutter={8}
              style={{ marginBottom: 12 }}
              className={styles.card}
              title={
                <div>
                  入库单：{Detail?.no}
                  <Tag style={{ marginLeft: 10 }} color={'orange'}>
                    收货中
                  </Tag>
                </div>
              }
            >
              <div style={{ display: 'flex', color: '#707070' }}>
                <div>
                  <span>海外仓库：{Detail?.warehouse?.name}</span>
                </div>
                <div style={{ marginLeft: 10 }}>
                  <span>
                    送仓形式：
                    <Tag color={deliverytype[Detail?.deliveryForm]?.color}>
                      {deliverytype[Detail?.deliveryForm]?.text}
                    </Tag>
                  </span>
                </div>
                <div style={{ marginLeft: 10 }}>
                  <span>客户：{Detail?.clientName}</span>
                </div>
                <div style={{ marginLeft: 10 }}>
                  <span>创建时间：{formatTime(Detail?.createTime)}</span>
                </div>
              </div>
            </ProCard>
            <div
              style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
            ></div>
            <Tabs
              defaultActiveKey="1"
              type="card"
              className={styles.tabBox}
              style={{ marginBottom: 32 }}
              items={[
                {
                  label: `待收货`,
                  key: '1',
                  children: (
                    <UnTable2
                      Detail={Detail}
                      ref={addRef}
                      getInboundDetailApi={getInboundDetailApi}
                    />
                  ),
                },
                {
                  label: `已收货`,
                  key: '2',
                  children: <HasTable Detail={Detail} />,
                },
              ]}
            />
          </>
        )}
      </div>
      <ProCard
        gutter={8}
        className={styles.footer}
        style={{ textAlign: 'center' }}
        /* extra={<Button type={'primary'}>更新</Button>}*/
      >
        <Space>
          <Button>取消</Button>
          <Button type={'primary'} onClick={handleSubmit}>
            确定收货
          </Button>
        </Space>
      </ProCard>

      {/*  <Table dataSource={dataSource} columns={columns} scroll={{ x: 1200 }} />;*/}
    </div>
  );
};
export default ScanStore;
