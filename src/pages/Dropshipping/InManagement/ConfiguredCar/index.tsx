// import { Access, useAccess } from '@@/exports';
import React, { useRef, useState } from 'react';
import { Button, Input, Space, Tag } from 'antd';
import MrTable from '@/components/MrTable';
import { history } from '@@/core/history';

import {
  deliverytype,
  getColorByValue,
  getLabelByValue,
  goodsType,
  InWareHouseMap,
} from '@/utils/constant';
import { getInboundList } from '@/services/Waybilladministration';

const { Search } = Input;

const InventoryForecast = () => {
  const actionRef = useRef<any>();
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);

  const refreshTable = () => {
    actionRef.current.reload();
  };

  /* 刷新表格 */
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Search
            placeholder="请输入标签名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },
    {
      title: '入库单号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '已收/预报箱数',
      dataIndex: 'no',
      hideInSearch: true,
      width: 150,
      render: (text: any, record: any) => {
        return record?.receivedBoxQuatity + '/' + record?.boxQuatity;
      },
    },

    {
      title: '客户',
      hideInSearch: true,
      dataIndex: 'clientName',
    },
    {
      title: 'SKU明细',
      hideInSearch: true,
      dataIndex: 'skuSummary',
      width: 150,
    },
    {
      title: '海外仓库',
      hideInSearch: true,
      dataIndex: 'warehouse',
      width: 150,
      render: (text: any, record: any) => {
        return record?.warehouse?.name;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, InWareHouseMap)}>
            {getLabelByValue(record?.state, InWareHouseMap)}
          </Tag>
        );
      },
    },
    {
      title: '提单号/货柜号',
      hideInSearch: true,
      width: 300,
      dataIndex: 'blno/container',
      render: (text: any, record: any) => {
        return (record?.blno || '-') + '/' + (record?.containerNo || '-');
      },
    },
    {
      title: '物流跟踪号',
      hideInSearch: true,
      dataIndex: 'trackNo',
    },
    {
      title: '到仓方式',
      hideInSearch: true,
      dataIndex: 'isa',
      render: (text: any, record: any) => {
        return (
          <Tag color={deliverytype[record?.deliveryForm]?.color}>
            {deliverytype[record?.deliveryForm]?.text}
          </Tag>
        );
      },
    },
    {
      title: '交货方式',
      hideInSearch: true,
      dataIndex: 'isa',
      render: (text: any, record: any) => {
        return (
          <Tag color={goodsType[record?.deliveryForm]?.color}>
            {goodsType[record?.deliveryForm]?.text}
          </Tag>
        );
      },
    },
    {
      title: '预计到达时间',
      hideInSearch: true,
      dataIndex: 'estimateArriveTime',
      valueType: 'dateTime',
      width: 200,
    },
    {
      title: '提交时间',
      hideInSearch: true,
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 200,
    },

    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => {
        return (
          <Space>
            <Button
              key="edit"
              type="link"
              onClick={() => {
                history.push(`/dropshipping/InManagement/ScanStore`, record);
              }}
            >
              收货
            </Button>
            <Button
              key="edit"
              type="link"
              onClick={() => {
                history.push(
                  `/dropshipping/InManagement/ConfiguredCar/Detail`,
                  record,
                );
              }}
            >
              详情
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <>
      <MrTable
        keyID="system/setting"
        columns={columns}
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getInboundList({ ...params });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        toolbar={{
          menu: {
            type: 'tab',
            //activeKey: activeKey,
            items: [
              {
                key: '0',
                label: '全部',
              },
              {
                key: '1',
                label: '待入库',
              },
              {
                key: '2',
                label: '收货中',
              },
              {
                key: '3',
                label: '已收货',
              },
              {
                key: '4',
                label: '已上架',
              },
              {
                key: '5',
                label: '已取消',
              },
            ],
            onChange: (key: any) => {
              //setActiveKey(key as string);
              //refresh();
            },
          },
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            termQuery: false,
            // tabs: true,
          },
          state: {
            desc: '状态',
            type: 'select',
            range: [],
          },
          oversea: {
            desc: '海外仓',
            type: 'select',
            range: [],
          },
          arriveTime: {
            desc: '创建日期',
            type: 'dateTimeRange',
            startTime: '',
            endTime: '',
          },
        }}
        /* toolBarRender={<Button
          key="primary"
          type="primary"
          onClick={() => setIsModalOpen(true)}
        >
          新建规则
        </Button>}*/
      />
    </>
  );
};
export default InventoryForecast;
