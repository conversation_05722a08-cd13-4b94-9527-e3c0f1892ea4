import { Input, Radio, Table } from 'antd';
import React from 'react';

const HasTable = (props: any) => {
  const { Detail } = props;
  const columns: any = [
    {
      title: '箱型号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '客户箱唛',
      dataIndex: 'id22',
      key: 'id222',
    },
    {
      title: '预报箱数',
      dataIndex: 'quatity',
      key: 'quatity',
    },
    {
      title: '已接收',
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '自定义箱条码',
      dataIndex: 'boxNo',
      key: 'boxNo',
    },
    {
      title: '箱内SKU',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '中文品名',
      dataIndex: 'cnName',
      key: 'cnName',
    },
    {
      title: '实收（良品）',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '实收（不良品）',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '预报总数',
      dataIndex: 'number',
      key: 'number',
    },
    {
      title: '单箱数量',
      dataIndex: 'name',
      key: 'name',
    },
    // {
    //   title: 'OMS预报尺寸',
    //   dataIndex: 'name',
    //   key: 'name',
    // },
    {
      title: '预报材积（W*H*L-W）',
      dataIndex: 'name',
      key: 'name',
    },
    // {
    //   title: 'WMS测量尺寸/cm',
    //   dataIndex: 'name',
    //   key: 'name',
    // },
    {
      title: 'WMS材积（W*H*L-W）',
      dataIndex: 'name',
      key: 'name',
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>预报箱子总数：{Detail?.boxQuatity}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>待接收箱型：{Detail?.boxList?.length}</span>
        </div>
        <div style={{ marginLeft: 10 }}>
          <span>
            待接收箱子数量：{Detail?.boxQuatity - Detail?.receivedBoxQuatity}
          </span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Table
        columns={columns}
        dataSource={Detail?.boxList}
        scroll={{ x: 1200 }}
        childrenColumnName={'skuList'}
      />
    </div>
  );
};
export default HasTable;
