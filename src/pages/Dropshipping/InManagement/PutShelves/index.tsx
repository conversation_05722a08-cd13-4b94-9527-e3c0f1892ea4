// import { Access, useAccess } from '@@/exports';
import React, { useRef, useState } from 'react';
import { Button, Input, Space, Tag } from 'antd';
import MrTable from '@/components/MrTable';
import { getReceiveList } from '@/services/ConfiguredCar';
import { history } from '@@/core/history';
import { DispatchMap } from '@/constants';
import { getColorByValue, getLabelByValue, PutMap } from '@/utils/constant';

const { Search } = Input;

const PutShelves = () => {
  // const access = useAccess();
  const actionRef = useRef<any>();
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);

  const refreshTable = () => {
    actionRef.current.reload();
  };

  /* 刷新表格 */
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Search
            placeholder="请输入标签名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },
    {
      title: '上架单号',
      dataIndex: 'id',
      hideInSearch: true,
    },
    {
      title: '入库单号',
      dataIndex: 'no',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return record?.inbound?.no;
      },
    },
    {
      title: '客户',
      hideInSearch: true,
      dataIndex: 'clientName',
      render: (text: any, record: any) => {
        return record?.inbound?.clientName;
      },
    },
    {
      title: '入库SKU编码*数量',
      hideInSearch: true,
      dataIndex: 'summary',
    },

    {
      title: '创建时间',
      hideInSearch: true,
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },

    {
      title: '上架时间',
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      valueType: 'dateTime',
    },
    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, PutMap)}>
            {getLabelByValue(record?.state, PutMap)}
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => {
        return (
          <Space>
            <Button
              key="edit"
              type="link"
              onClick={() => {
                history.push(
                  `/dropshipping/InManagement/PutShelves/PutOn`,
                  record,
                );
              }}
            >
              上架
            </Button>
            <Button key="edit" type="link" onClick={() => {}}>
              打印
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <>
      <MrTable
        keyID="system/setting"
        columns={columns}
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getReceiveList({
            ...params,
            type: 0,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        toolbar={{
          menu: {
            type: 'tab',
            //activeKey: activeKey,
            items: [
              {
                key: '0,1,-1',
                label: '全部',
              },
              {
                key: '0',
                label: '待上架',
              },
              {
                key: '1',
                label: '已上架',
              },
            ],
            onChange: (key: any) => {
              //setActiveKey(key as string);
              //refresh();
            },
          },
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          fbaCode: {
            desc: 'FBA仓库',
            type: 'FBA',
            value: '',
          },
          shipmentMethod: {
            desc: '派送方式',
            type: 'select',
            range: DispatchMap,
            value: '',
          },
          appointmentTime: {
            desc: '预约送货时间',
            type: 'dateTimeRange',
            value: '',
          },
          warehouseId: {
            desc: '交货仓库',
            type: 'overseas',
            value: '',
          },
          /* outboardTime: {
            desc: '出库时间',
            type: 'dateTimeRange',
            value: '',
          },*/
          deliveriedTime: {
            desc: '送达时间',
            type: 'dateTimeRange',
            value: '',
          },
        }}
        /* toolBarRender={<Button
          key="primary"
          type="primary"
          onClick={() => setIsModalOpen(true)}
        >
          新建规则
        </Button>}*/
      />
    </>
  );
};
export default PutShelves;
