import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Select,
  Table,
} from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { inboundStore, receiveGoodApi } from '@/services/Waybilladministration';
import { ProFormSelect } from '@ant-design/pro-components';
import { getWarehouseMap } from '@/services/ConfiguredCar';
import _ from 'lodash';

const UnTable = forwardRef((props: any, ref) => {
  const { Detail, getInboundDetailApi, Keyword, setDetail } = props;
  const [form] = Form.useForm();
  const [CheckBox, setCheckBox] = useState<any>({});
  const [UnstoredList, setUnstoredList] = useState<any>([]);
  useImperativeHandle(ref, () => ({
    handleSubmit() {
      form.submit();
      console.log('提交');
    },
  }));
  useEffect(() => {
    setUnstoredList(Detail?.unstoredList);
  }, [Detail]);
  const onFinish = async (values: any) => {
    console.log('收到提交', values);
    const boxList = Object.keys(values)
      //过滤完全没填的
      .filter((item) => !!values[item].slotId && !!values[item].quatity)
      .map((item: any) => {
        return {
          skuId: item.length > 18 ? item.slice(0, -14) : item.slice(0, -1),
          ...values[item],
          quality: Number(item[item.length - 1]),
        };
      });
    console.log('boxList', boxList);
    if (boxList.length === 0) {
      message.error('请填写数据');
      return;
    }
    const params = {
      receiveRecordId: Detail?.id,
      list: boxList,
    };
    const res = await inboundStore(params);
    if (res.status) {
      message.success('提交成功');
      getInboundDetailApi(Keyword);
    }
  };
  const onCheck = async (e: any, record: any) => {
    const allForm = form.getFieldsValue();
    console.log('record', record);
    console.log('allForm', allForm);
    /*    for (const allFormKey in allForm) {
      if (!!allForm[allFormKey].quatity && !allForm[allFormKey].slotId) {
        setCheckBox({ ...CheckBox, [allFormKey]: true });
      }
      if (!allForm[allFormKey].quatity && !!allForm[allFormKey].slotId) {
        setCheckBox({ ...CheckBox, [allFormKey]: true });
      }
      if (!allForm[allFormKey].quatity && !allForm[allFormKey].slotId) {
        setCheckBox({ ...CheckBox, [allFormKey]: false });
      }
    }*/
    const updatedCheckBox = { ...CheckBox };

    for (const allFormKey in allForm) {
      const { quatity, slotId } = allForm[allFormKey];
      updatedCheckBox[allFormKey] = !!quatity !== !!slotId;
    }

    setCheckBox(updatedCheckBox);
  };
  const columns: any = [
    {
      title: 'SKU编号',
      dataIndex: ['sku', 'code'],
      key: 'id',
      width: 200,
      onCell: (record: any) => {
        if (record?.copy) {
          return { rowSpan: 0 };
        } else {
          if (UnstoredList?.length > 0) {
            //计算同一个编号的数量
            const number = UnstoredList?.filter(
              (item) =>
                record?.skuId + record?.quality === item?.skuId + item?.quality,
            ).length;
            return { rowSpan: number };
          } else {
            return { rowSpan: 1 };
          }
        }
      },
    },
    {
      title: '名称',
      dataIndex: ['sku', 'cnName'],
      key: 'id',
      width: 200,
      onCell: (record: any) => {
        if (record?.copy) {
          return { rowSpan: 0 };
        } else {
          if (UnstoredList?.length > 0) {
            //计算同一个编号的数量
            const number = UnstoredList?.filter(
              (item) =>
                record?.skuId + record?.quality === item?.skuId + item?.quality,
            ).length;
            return { rowSpan: number };
          } else {
            return { rowSpan: 1 };
          }
        }
      },
    },
    {
      title: '分类',
      dataIndex: ['sku', 'category'],
      key: 'id',
      width: 200,
      onCell: (record: any) => {
        if (record?.copy) {
          return { rowSpan: 0 };
        } else {
          if (UnstoredList?.length > 0) {
            //计算同一个编号的数量
            const number = UnstoredList?.filter(
              (item) =>
                record?.skuId + record?.quality === item?.skuId + item?.quality,
            ).length;
            return { rowSpan: number };
          } else {
            return { rowSpan: 1 };
          }
        }
      },
    },
    {
      title: '库存属性',
      dataIndex: 'quality',
      width: 100,
      key: 'quality',
      render: (_: any, record: any, index: any) => {
        if (record?.quality === 1) {
          return '良品';
        } else {
          return '不良品';
        }
      },
      onCell: (record: any) => {
        if (record?.copy) {
          return { rowSpan: 0 };
        } else {
          if (UnstoredList?.length > 0) {
            //计算同一个编号的数量
            const number = UnstoredList?.filter(
              (item) =>
                record?.skuId + record?.quality === item?.skuId + item?.quality,
            ).length;
            return { rowSpan: number };
          } else {
            return { rowSpan: 1 };
          }
        }
      },
    },
    {
      title: '已上架/总数',
      width: 100,
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
      render: (_: any, record: any, index: any) => {
        return `${record?.storedQuatity}/${record?.quatity}`;
      },
      onCell: (record: any) => {
        if (record?.copy) {
          return { rowSpan: 0 };
        } else {
          if (UnstoredList?.length > 0) {
            //计算同一个编号的数量
            const number = UnstoredList?.filter(
              (item) =>
                record?.skuId + record?.quality === item?.skuId + item?.quality,
            ).length;
            return { rowSpan: number };
          } else {
            return { rowSpan: 1 };
          }
        }
      },
    },
    {
      title: '待上架',
      width: 200,
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
      render: (_: any, record: any, index: any) => {
        return record?.quatity - record?.storedQuatity;
      },
      onCell: (record: any) => {
        if (record?.copy) {
          return { rowSpan: 0 };
        } else {
          if (UnstoredList?.length > 0) {
            //计算同一个编号的数量
            const number = UnstoredList?.filter(
              (item) =>
                record?.skuId + record?.quality === item?.skuId + item?.quality,
            ).length;
            return { rowSpan: number };
          } else {
            return { rowSpan: 1 };
          }
        }
      },
    },
    {
      title: '本次上架库位',
      dataIndex: 'slotId',
      key: 'slotId',
      width: 200,
      render: (_: any, record: any, index: any) => {
        if (record?.copy) {
          return (
            <>
              <div>
                <ProFormSelect
                  rules={[
                    {
                      required: form.getFieldValue([
                        record?.skuId + record?.quality,
                        'quatity',
                      ]),
                      message: '请选择货位',
                    },
                  ]}
                  noStyle={true}
                  name={[
                    record?.skuId + record?.quality + record?.copy,
                    'slotId',
                  ]}
                  request={async () => {
                    const { status, data } = await getWarehouseMap({
                      warehouseId: record?.warehouseId,
                      type: 0,
                    });
                    if (status) {
                      return data?.list?.map((item: any) => {
                        return {
                          label: item.value,
                          value: item.value,
                        };
                      });
                    }
                    return [];
                  }}
                  onChange={(e) => {
                    onCheck(e, record);
                    /*   const updatedData = [...allData];
                    updatedData[index] = e;
                    setAllData(updatedData);*/
                  }}
                />
              </div>
            </>
          );
        } else {
          return (
            <>
              <div>
                <ProFormSelect
                  rules={[
                    {
                      required: form.getFieldValue([
                        record?.skuId + record?.quality,
                        'quatity',
                      ]),
                      message: '请选择货位',
                    },
                  ]}
                  noStyle={true}
                  name={[record?.skuId + record?.quality, 'slotId']}
                  request={async () => {
                    const { status, data } = await getWarehouseMap({
                      warehouseId: record?.warehouseId,
                      type: 0,
                    });
                    if (status) {
                      return data?.list?.map((item: any) => {
                        return {
                          label: item.value,
                          value: item.value,
                        };
                      });
                    }
                    return [];
                  }}
                  onChange={(e) => {
                    onCheck(e, record);
                    /*   const updatedData = [...allData];
                    updatedData[index] = e;
                    setAllData(updatedData);*/
                  }}
                />
              </div>
            </>
          );
        }
      },
    },
    {
      title: '本次上架数量',
      dataIndex: 'boxNo',
      key: 'boxNo',
      width: 200,
      render: (_: any, record: any, index: any) => {
        if (record?.copy) {
          return (
            <>
              <Form.Item
                name={[
                  record?.skuId + record?.quality + record?.copy,
                  'quatity',
                ]}
                style={{ margin: 0 }}
                rules={[
                  {
                    required: form.getFieldValue([
                      record?.skuId + record?.quality,
                      'slotId',
                    ]),
                    message: '请输入数量',
                  },
                ]}
              >
                <InputNumber onChange={(e: any) => onCheck(e, record)} />
              </Form.Item>
            </>
          );
        } else {
          return (
            <>
              <Form.Item
                name={[record?.skuId + record?.quality, 'quatity']}
                style={{ margin: 0 }}
                rules={[
                  {
                    required: form.getFieldValue([
                      record?.skuId + record?.quality,
                      'slotId',
                    ]),
                    message: '请输入数量',
                  },
                ]}
              >
                <InputNumber onChange={(e: any) => onCheck(e, record)} />
              </Form.Item>
            </>
          );
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'boxNo',
      key: 'boxNo',
      width: 200,
      render: (__: any, record: any, index: any) => {
        if (record?.copy) {
          return (
            <Button
              type={'link'}
              style={{ color: 'red' }}
              onClick={() => {
                setUnstoredList(
                  UnstoredList.filter((item: any) => item.copy !== record.copy),
                );
              }}
            >
              删除
            </Button>
          );
        } else {
          return (
            <a
              onClick={() => {
                //当前时间戳做索引保证不重复
                const time = Date.now();
                const newItem = { ...record, copy: time };
                console.log('newItem', newItem);
                let UnstoredListCopy = _.cloneDeep(UnstoredList);
                UnstoredListCopy.splice(index + 1, 0, newItem);
                console.log('newArray', UnstoredListCopy);
                setUnstoredList(UnstoredListCopy);
              }}
            >
              增加库位
            </a>
          );
        }
      },
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>SKU待上架总数：{Detail?.boxQuatity}</span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Form form={form} onFinish={onFinish}>
        <Table
          size={'small'}
          columns={columns}
          dataSource={UnstoredList}
          scroll={{ x: 'max-content' }}
          childrenColumnName={'skuList'}
        />
      </Form>
    </div>
  );
});
export default UnTable;
