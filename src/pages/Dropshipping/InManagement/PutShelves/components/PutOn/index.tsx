// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { Button, Empty, Input, Space, Tabs, Tag } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import styles from './index.less';

import { getReceiveDetail } from '@/services/Waybilladministration';
import UnTable from '@/pages/Dropshipping/InManagement/PutShelves/components/PutOn/UnTable';
import HasTable from '@/pages/Dropshipping/InManagement/PutShelves/components/PutOn/HasTable';
import { useLocation } from '@@/exports';
import { formatTime } from '@/utils/format';

const { Search } = Input;
const PutOn = () => {
  const [Keyword, setKeyword] = useState('');
  const [Detail, setDetail] = useState({});
  const addRef = useRef<any>(null);
  const location = useLocation();
  const handleSubmit = () => {
    addRef?.current?.handleSubmit();
  };
  /*刷新数据*/
  const getInboundDetailApi = async (id: any) => {
    const res = await getReceiveDetail({ id });
    setDetail(res?.data);
    console.log('结果', res);
  };
  useEffect(() => {
    const { state }: any = location;
    setKeyword(state?.id);
    getInboundDetailApi(state?.id);
  }, []);
  const onSearch = (v: any) => {
    setKeyword(v);
    getInboundDetailApi(Keyword);
  };

  return (
    <div className={styles.container}>
      <ProCard
        gutter={8}
        className={styles.header}
        style={{ marginBottom: 12, textAlign: 'center' }}
      >
        <Search
          placeholder="请扫描上架单"
          allowClear
          enterButton="搜索"
          size="large"
          key={Keyword}
          defaultValue={Keyword}
          style={{ width: 600 }}
          onSearch={onSearch}
        />
      </ProCard>
      <div className={styles.fill}>
        {Keyword ? (
          <>
            <ProCard
              gutter={8}
              style={{ marginBottom: 12 }}
              className={styles.card}
              title={
                <div>
                  上架单：{Detail?.id}
                  <Tag style={{ marginLeft: 10 }} color={'orange'}>
                    收货中
                  </Tag>
                </div>
              }
            >
              <div style={{ display: 'flex', color: '#707070' }}>
                <div>
                  <span>入库单号：{Detail?.inbound?.no}</span>
                </div>
                <div style={{ marginLeft: 10 }}>
                  <span>业务类型：一件代发</span>
                </div>
                <div style={{ marginLeft: 10 }}>
                  <span>客户：{Detail?.inbound?.clientName}</span>
                </div>
                <div style={{ marginLeft: 10 }}>
                  <span>创建时间：{formatTime(Detail?.createTime)}</span>
                </div>
              </div>
            </ProCard>
            <div
              style={{ background: '#f0f1f3', height: '12px', width: '100%' }}
            ></div>
            <Tabs
              defaultActiveKey="1"
              type="card"
              className={styles.tabBox}
              style={{ marginBottom: 32 }}
              items={[
                {
                  label: `待上架列表`,
                  key: '1',
                  children: (
                    <UnTable
                      Detail={Detail}
                      ref={addRef}
                      Keyword={Keyword}
                      getInboundDetailApi={getInboundDetailApi}
                      setDetail={setDetail}
                    />
                  ),
                },
                {
                  label: `上架记录`,
                  key: '2',
                  children: <HasTable Detail={Detail} />,
                },
              ]}
            />
          </>
        ) : (
          <Empty style={{ marginTop: '200px' }} description={'请扫描上架'} />
        )}
      </div>
      <ProCard
        gutter={8}
        className={styles.footer}
        style={{ textAlign: 'center' }}
        /* extra={<Button type={'primary'}>更新</Button>}*/
      >
        <Space>
          <Button>取消</Button>
          <Button type={'primary'} onClick={handleSubmit}>
            确定上架
          </Button>
        </Space>
      </ProCard>

      {/*  <Table dataSource={dataSource} columns={columns} scroll={{ x: 1200 }} />;*/}
    </div>
  );
};
export default PutOn;
