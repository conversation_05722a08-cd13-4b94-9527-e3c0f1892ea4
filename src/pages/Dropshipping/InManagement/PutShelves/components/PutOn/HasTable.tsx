import { Form, Input, Radio, Table } from 'antd';
import React from 'react';
import { formatTime } from '@/utils/format';

const HasTable = (props: any) => {
  const { Detail } = props;
  const Datasource: readonly any[] | undefined = [];
  Detail?.storedRecordList?.map((item: any) => {
    const { details, ...exit } = item;
    const addTimeData = details.map((item) => {
      return { ...item, createTime: exit.createTime };
    });
    console.log('addTimeData', addTimeData);
    Datasource.push(...addTimeData);
    return true;
  });
  const columns: any = [
    {
      title: '上架时间',
      dataIndex: ['createTime'],
      key: 'id',
      width: 200,
      valueType: 'dateTime',
      render: (text: any, rocord: any) => {
        return <div>{formatTime(rocord?.createTime)}</div>;
      },
    },
    {
      title: 'SKU编号',
      dataIndex: ['sku', 'code'],
      key: 'id',
      width: 200,
    },
    {
      title: '中文品名',
      dataIndex: ['sku', 'cnName'],
      key: 'id',
      width: 200,
    },
    /*    {
      title: '分类',
      dataIndex: ['sku', 'category'],
      key: 'id',
      width: 200,
    },*/
    {
      title: '库存属性',
      dataIndex: 'quatity',
      width: 100,
      key: 'quatity',
      render: (_: any, record: any, index: any) => {
        if (record?.quatity === 1) {
          return '良品';
        } else {
          return '不良品';
        }
      },
    },
    {
      title: '上架库位',
      dataIndex: 'slotId',
      key: 'slotId',
      width: 200,
    },
    {
      title: '上架数量',
      width: 100,
      dataIndex: 'receivedQuatity',
      key: 'receivedQuatity',
      render: (_: any, record: any) => {
        return record?.quatity;
      },
    },

    {
      title: '操作人',
      dataIndex: 'boxNo',
      key: 'boxNo',
      width: 200,
    },
  ];
  return (
    <div style={{ background: '#fff' }}>
      <div style={{ display: 'flex', color: '#707070', paddingLeft: 24 }}>
        <div>
          <span>SKU已上架总数：{Detail?.boxQuatity}</span>
        </div>
      </div>
      {/* <Radio.Group style={{ marginTop: 20, marginLeft: 24 }}>
        <Radio.Button value="large">按箱型收</Radio.Button>
        <Radio.Button value="middle">按单箱收</Radio.Button>
      </Radio.Group>*/}
      <Table
        columns={columns}
        dataSource={Datasource}
        scroll={{ x: 1200 }}
        childrenColumnName={'skuList'}
      />
    </div>
  );
};
export default HasTable;
