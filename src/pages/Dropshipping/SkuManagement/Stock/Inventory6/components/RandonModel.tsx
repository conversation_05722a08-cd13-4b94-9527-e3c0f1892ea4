import {Form, InputNumber, Modal, Select} from 'antd';
import style from './RoleModel.less';
import { useEffect } from 'react';
const RandonModel = (props: any) => {
  const { isRandonOpen, handleOk, handleCancel } = props;
  const [form] = Form.useForm();
  const layout = {
    labelCol: {
      span: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };

  //获取随机数
  const getRandon = () => {
    const length = form.getFieldValue('length');
    const params = form.getFieldValue('params');
    const RandonDate = `[随机码,${length},${params}]`;
    handleOk(RandonDate);
  };

  useEffect(() => {
    if (!isRandonOpen) {
      form.resetFields();
    }
    form.setFieldsValue({length:6})
  }, [isRandonOpen]);
  return (
    <>
      <Modal
        title="填写随机数信息"
        open={isRandonOpen}
        onOk={() => getRandon()}
        onCancel={handleCancel}
        width={500}
        okText="确定"
      >
        <div className={style.formWrapper}>
          <Form {...layout} form={form} name="control-ref">
            <Form.Item
              name="length"
              label="长度"
              rules={[
                {
                  required: true,
                  message: '请输入长度',
                },
              ]}
            >
              <InputNumber
                style={{ width: '60%' }}
                placeholder="请输入长度"
                min={1}
                max={20}
              />
            </Form.Item>
            <Form.Item
              name="params"
              label="数值内容"
              rules={[
                {
                  required: true,
                  message: '请输入数值',
                },
              ]}
              initialValue="数字"
            >
              <Select
                style={{ width: '60%' }}
                options={[
                  { value: '数字', label: '数字' },
                  { value: '数字字母', label: '数字字母' },
                ]}
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
};

export default RandonModel;
