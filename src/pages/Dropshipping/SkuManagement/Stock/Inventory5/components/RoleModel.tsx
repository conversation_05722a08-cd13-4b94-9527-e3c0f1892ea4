import { Form, Input, Modal, Button, message } from 'antd';
import style from './RoleModel.less';
import { useEffect, useState } from 'react';
import RandonModel from '@/pages/System/IdRule/components/RandonModel';
import {
  createIdRule,
  testIdRule,
  updateIdRule,
} from '@/services/system/IdRule';
import FlowModel from '@/pages/System/IdRule/components/FlowModel';
const RoleModel = (props: any) => {
  const { isModalOpen, handleCancel, value, handleOk } = props;
  const [isRandonOpen, setIsRandonOpen] = useState(false);
  const [isFlowOpen, setIsFlowOpen] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const isEdit = Object.keys(value).length > 0;
  const [form] = Form.useForm();
  const ContentModel = Form.useWatch('content', form);
  const [previewData, setPreviewData] = useState([]);
  const layout = {
    labelCol: {
      span: 4,
    },
    wrapperCol: {
      span: 20,
    },
  };
  //提交
  const onFinish = async () => {
    const FromData = form.getFieldsValue();
    if(errorMsg) return
    if (isEdit) {
      const res = await updateIdRule({
        name: FromData.name,
        content: FromData.content,
        id: value.id,
      });
      if (res.status) {
        message.success('编辑成功');
      } else {
        message.error(res.errorDesc);
      }
    } else {
      const res = await createIdRule({
        name: FromData.name,
        content: FromData.content,
      });
      if (res.status) {
        message.success('添加成功');
      } else {
        message.error(res.errorDesc);
      }
    }
    handleOk();
  };
  const handleRandonCancel = () => {
    setIsRandonOpen(false);
    setIsFlowOpen(false);
  };
  //添加参数
  const onAddParams = (v: any) => {
    let newName;
    if (ContentModel) {
      newName = ContentModel + v;
    } else {
      newName = v;
    }
    form.setFieldsValue({ content: newName });
  };
  //大小写转换
  const onTransform = (v: string) => {
    let newName;
    const bigExist =ContentModel.indexOf('[编码大写]') !== -1;
    const smallExist =ContentModel.indexOf('[编码小写]') !== -1;
    //按编码小写
    if (v === 'down') {
      if(bigExist){
        newName =  ContentModel.replace('[编码大写]','[编码小写]');
      }else {
        if(smallExist){
          newName =  ContentModel.replace('[编码小写]','');
        }else {
          newName = ContentModel.toLowerCase() + '[编码小写]';
        }
      }
    } else {
      if(smallExist){
        newName = ContentModel.replace('[编码小写]','[编码大写]');
      }else {
        if(bigExist){
          newName = ContentModel.replace('[编码大写]','');
        }else {
          newName = ContentModel.toLowerCase() + '[编码大写]';
        }
      }
    }
    form.setFieldsValue({ content: newName });
  };

  //提交
  const handleSubmit = () => {
    form.submit();
  };
  //预览生成
  const handlePreview =  async ( ) => {
    const res = await testIdRule({
      content: ContentModel,
    });
    console.log(res);
    if (res.status) {
      const data = res.data;
      const {sampleList} = data;
      setPreviewData(sampleList);
      setErrorMsg('');
    } else {
      setErrorMsg( res.errorDesc);
    }
  };
  //监听字符字段
  useEffect(()=>{
    if(isModalOpen){
      handlePreview();
    }
      },
      [ContentModel])
  const getRandon = (v: any) => {
    setIsFlowOpen(false);
    setIsRandonOpen(false);
    onAddParams(v);
  };
  useEffect(() => {
    if (isModalOpen) {
      if (isEdit) {
        form.setFieldsValue(value);
      } else {
        //form.setFieldsValue({name:'',content:''});
      }
    } else {
      setPreviewData([]);
      form.resetFields();
    }
  }, [isModalOpen,form]);
  return (
    <>
      <Modal
        title={`${isEdit ? `编辑【${value.name}】编码规则` : '新增编码规则'}`}
        open={isModalOpen}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={880}
        okText="保存"
      >
        <div className={style.formWrapper}>
          <Form {...layout} form={form} onFinish={onFinish} name="control-ref">
            <Form.Item
              name="name"
              label="名称"
              rules={[
                {
                  required: true,
                  message: '请输入名称',
                },
              ]}
            >
              <Input
                style={{ width: '90%' }}
                placeholder="请输入"
              />
            </Form.Item>
            <Form.Item
              name="content"
              label="编码规则"
              rules={[
                {
                  required: true,
                  message: '请输入表达式',
                },
              ]}
            >
              <Input
                style={{ width: '90%' }}
                placeholder="请输入表达式"
              />
            </Form.Item>
            <Form.Item name="param" label="参数">
              <div>
                <Button
                  className={style.buttonGroup}
                  onClick={() => onAddParams('[year]')}
                >
                  年
                </Button>
                <Button
                  className={style.buttonGroup}
                  onClick={() => onAddParams('[yy]')}
                >
                  年[后两位]
                </Button>
                <Button
                  className={style.buttonGroup}
                  onClick={() => onAddParams('[month]')}
                >
                  月
                </Button>
                <Button
                  className={style.buttonGroup}
                  onClick={() => onAddParams('[day]')}
                >
                  日
                </Button>
                <Button
                  className={style.buttonGroup}
                  onClick={() => onAddParams('[minute]')}
                >
                  分
                </Button>
                <Button
                  className={style.buttonGroup}
                  onClick={() => setIsRandonOpen(true)}
                >
                  随机数
                </Button>
                <Button
                  className={style.buttonGroup}
                  onClick={() => {
                    setIsFlowOpen(true);
                  }}
                >
                  流水号
                </Button>
                <Button
                    className={style.buttonGroup}
                    onClick={() => {
                      onAddParams('[公司编码]');
                    }}
                >
                  公司编码
                </Button>
                <Button
                  className={`${style.buttonGroup} ${ContentModel && ContentModel.indexOf('[编码大写]') !== -1 ? style.buttonGroupColor:''}`}
                  onClick={() => {
                    onTransform('up');
                  }}
                >
                  编码大写
                </Button>
                <Button
                    className={`${style.buttonGroup} ${ContentModel && ContentModel.indexOf('[编码小写]') !== -1 ? style.buttonGroupColor:''}`}
                  onClick={() => {
                    onTransform('down');
                  }}
                >
                  编码小写
                </Button>
              </div>
            </Form.Item>
            <Form.Item name="tips" label="说明">
              <div className={style.tips}>
                <p>
                  1.除参数内包含中文或特殊字符，其他内容不可填写中文或特殊字符，否则将导致生成编码失败或不可用。
                </p>
                <p>
                  2.参数中的编码大写或编码小写作用是将生成的编码对应的英文字符转换成全大写或全小写格式的字符。
                </p>
                <p>
                  3.编码生成规则建议不要与其他编码规则格式一致，尽量使用单独的前缀或后缀加以区分，以免产生相同的编码！
                </p>
              </div>
            </Form.Item>
            <div className={style.previewWrap}>
              <div className={style.label}></div>
              <div className={style.preview}>
                <div className={style.preview_label}>
                  预览
                </div>
                <div className={style.preview_content}>
                  {!errorMsg ? previewData.map((item, index) =>
                    index < 3 ? item + ' ' : '',
                  ):<div className={style.preview_content_error}><span className={style.preview_content_label}>语法错误:</span>{errorMsg}</div>}
                </div>
              </div>
            </div>
          </Form>
        </div>
        <RandonModel
          isRandonOpen={isRandonOpen}
          handleOk={getRandon}
          handleCancel={handleRandonCancel}
        />
        <FlowModel
          isRandonOpen={isFlowOpen}
          handleOk={getRandon}
          handleCancel={handleRandonCancel}
        />
      </Modal>
    </>
  );
};

export default RoleModel;
