// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import RoleModel from '@/pages/System/IdRule/components/RoleModel';
import { Button, Input, message, Popconfirm, Space, Tag } from 'antd';
import MrTable from '@/components/MrTable';
import { getBatchSlotGoodList, getPreList } from '@/services/ConfiguredCar';
import { history } from '@@/core/history';
import { DispatchMap } from '@/constants';
import ReservationModal from '@/pages/PreConfiguredCar/Modal/ReservationModal';
import AssignSuppliersModal from '@/pages/PreConfiguredCar/Modal/AssignSuppliersModal';
import {
  formType,
  getColorByValue,
  getLabelByValue,
  ProgressStateType,
} from '@/utils/constant';
import { TableDropdown } from '@ant-design/pro-components';
import Delivery from '@/pages/BringGroup/Delivery';
import SuperTables from '@/components/SuperTables';

const { Search } = Input;

const Inventory = () => {
  // const access = useAccess();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState({});
  const actionRef = useRef<any>();
  const handleOk = async () => {
    actionRef.current.reload();
    setIsModalOpen(false);
  };
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const handleCancel = () => {
    setIsModalOpen(false);
    setValue({});
  };
  const refreshTable = () => {
    actionRef.current.reload();
  };

  useEffect(() => {
    setValue({});
  }, []);

  /* 刷新表格 */
  const columns: any = [
    {
      title: 'SKU编码',
      dataIndex: 'no',
      hideInSearch: true,
      fieldFormat: (record: any) => {
        return record?.sku?.code;
      },
    },
    /*   {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, ProgressStateType)}>
            {getLabelByValue(record?.state, ProgressStateType)}
          </Tag>
        );
      },
    },*/
    {
      title: '中文品名',
      hideInSearch: true,
      dataIndex: 'shipmentMethods',
      fieldFormat: (record: any) => {
        return record?.sku?.cnName;
      },
    },
    {
      title: '英文品名',
      hideInSearch: true,
      dataIndex: 'form',
      fieldFormat: (record: any) => {
        return record?.sku?.enName;
      },
    },

    {
      title: '客户名称',
      hideInSearch: true,
      dataIndex: 'clientName',
      fieldFormat: (record: any) => {
        return record?.clientName;
      },
    },
    {
      title: '批次号',
      hideInSearch: true,
      dataIndex: 'batchNo',
      fieldFormat: (record: any) => {
        return record?.batchNo;
      },
    },
    {
      title: '所在仓库',
      hideInSearch: true,
      dataIndex: 'isa',
      fieldFormat: (record: any) => {
        return record?.warehouse?.name;
      },
    },
    {
      title: '库位',
      hideInSearch: true,
      dataIndex: 'slotId',
    },
    {
      title: '库存属性',
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      fieldFormat: (record: any) => {
        if (record?.quality === 1) {
          return '良品';
        } else {
          return '不良品';
        }
      },
    },
    {
      title: '实际库存',
      hideInSearch: true,
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return record?.quatity;
      },
    },
  ];
  return (
    <>
      <SuperTables
        instanceId={'SkuManagement3'}
        ref={actionRef}
        columns={columns}
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getBatchSlotGoodList({
            ...params,
            type: 0,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          clientId: {
            desc: '客户',
            type: 'client',
            value: '',
          },
          warehouseId: {
            desc: '所在仓库',
            type: 'overseas',
            value: '',
          },
          /* outboardTime: {
            desc: '出库时间',
            type: 'dateTimeRange',
            value: '',
          },*/
        }}
        /* toolBarRender={<Button
              key="primary"
              type="primary"
              onClick={() => setIsModalOpen(true)}
            >
              新建规则
            </Button>}*/
      />
      <RoleModel
        isModalOpen={isModalOpen}
        handleOk={handleOk}
        handleCancel={handleCancel}
        value={value}
      />
    </>
  );
};
export default Inventory;
