// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import RoleModel from '@/pages/System/IdRule/components/RoleModel';
import { Button, Input, message, Popconfirm, Space, Tag } from 'antd';
import MrTable from '@/components/MrTable';
import { getPreList, getSummarizedGoodLogList } from '@/services/ConfiguredCar';
import { history } from '@@/core/history';
import { DispatchMap } from '@/constants';
import ReservationModal from '@/pages/PreConfiguredCar/Modal/ReservationModal';
import AssignSuppliersModal from '@/pages/PreConfiguredCar/Modal/AssignSuppliersModal';
import {
  formType,
  getColorByValue,
  getLabelByValue,
  ProgressStateType,
} from '@/utils/constant';
import { TableDropdown } from '@ant-design/pro-components';

const { Search } = Input;

const Inventory = () => {
  // const access = useAccess();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState({});
  const actionRef = useRef<any>();
  const handleOk = async () => {
    actionRef.current.reload();
    setIsModalOpen(false);
  };
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const handleCancel = () => {
    setIsModalOpen(false);
    setValue({});
  };
  const refreshTable = () => {
    actionRef.current.reload();
  };

  useEffect(() => {
    setValue({});
  }, []);

  /* 刷新表格 */
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Search
            placeholder="请输入标签名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },
    {
      title: 'SKU编码',
      dataIndex: 'no',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return record?.sku?.code;
      },
    },
    /*   {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, ProgressStateType)}>
            {getLabelByValue(record?.state, ProgressStateType)}
          </Tag>
        );
      },
    },*/
    {
      title: '中文品名',
      hideInSearch: true,
      dataIndex: 'shipmentMethods',
      render: (text: any, record: any) => {
        return record?.sku?.cnName;
      },
    },
    {
      title: '英文品名',
      hideInSearch: true,
      dataIndex: 'form',
      render: (text: any, record: any) => {
        return record?.sku?.enName;
      },
    },
    {
      title: '客户代码',
      hideInSearch: true,
      dataIndex: 'warehouse',
      render: (text: any, record: any) => {
        return record?.warehouse?.name;
      },
    },

    {
      title: '客户名称',
      hideInSearch: true,
      dataIndex: 'xxxxx',
      render: (text: any, record: any) => {
        return record?.appointmentTime ? '是' : '否';
      },
    },
    {
      title: '所在仓库',
      hideInSearch: true,
      dataIndex: 'isa',
      render: (_: any, record: any) => {
        return record?.warehouse?.name;
      },
    },
    {
      title: '库位',
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      valueType: 'dateTime',
    },
    {
      title: '库存属性',
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      valueType: 'dateTime',
    },
    {
      title: '批次号',
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      valueType: 'dateTime',
    },
    {
      title: '上架日期',
      hideInSearch: true,
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    {
      title: '统计日期',
      hideInSearch: true,
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    {
      title: '库龄（天）',
      hideInSearch: true,
      dataIndex: 'createTime',
    },
    {
      title: 'WMS尺寸（cm）',
      hideInSearch: true,
      dataIndex: 'createTime',
    },
    {
      title: '总体积（m³）',
      hideInSearch: true,
      dataIndex: 'createTime',
    },
  ];
  return (
    <>
      <MrTable
        keyID="system/setting"
        columns={columns}
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getSummarizedGoodLogList({
            ...params,
            type: 0,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          fbaCode: {
            desc: 'FBA仓库',
            type: 'FBA',
            value: '',
          },
          shipmentMethod: {
            desc: '派送方式',
            type: 'select',
            range: DispatchMap,
            value: '',
          },
          appointmentTime: {
            desc: '预约送货时间',
            type: 'dateTimeRange',
            value: '',
          },
          warehouseId: {
            desc: '交货仓库',
            type: 'overseas',
            value: '',
          },
          /* outboardTime: {
                desc: '出库时间',
                type: 'dateTimeRange',
                value: '',
              },*/
          deliveriedTime: {
            desc: '送达时间',
            type: 'dateTimeRange',
            value: '',
          },
        }}
        /* toolBarRender={<Button
              key="primary"
              type="primary"
              onClick={() => setIsModalOpen(true)}
            >
              新建规则
            </Button>}*/
      />
      <RoleModel
        isModalOpen={isModalOpen}
        handleOk={handleOk}
        handleCancel={handleCancel}
        value={value}
      />
    </>
  );
};
export default Inventory;
