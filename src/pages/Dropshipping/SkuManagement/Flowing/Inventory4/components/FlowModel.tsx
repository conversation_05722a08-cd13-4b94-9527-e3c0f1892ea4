import { Form, Input, InputNumber, Modal, Select } from 'antd';
import style from './RoleModel.less';
import { useEffect } from 'react';
const FlowModel = (props: any) => {
  const { isRandonOpen, handleOk, handleCancel } = props;
  const [form] = Form.useForm();
  const layout = {
    labelCol: {
      span: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };

  //获取随机数
  const getRandon = () => {
    const length = form.getFieldValue('length');
    console.log(length);
    const params = form.getFieldValue('params');
    const defaultIndex = form.getFieldValue('defaultIndex');
    const RandonDate = `[流水号,${length},${params},${defaultIndex}]`;
    handleOk(RandonDate);
  };
  useEffect(() => {
    if (!isRandonOpen) {
      form.resetFields();
    }
    form.setFieldsValue({ length: 6 });
  }, [isRandonOpen]);
  return (
    <>
      <Modal
        title="填写流水号信息"
        open={isRandonOpen}
        onOk={() => getRandon()}
        onCancel={handleCancel}
        width={500}
        okText="确定"
      >
        <div className={style.formWrapper}>
          <Form {...layout} form={form} name="control-ref">
            <Form.Item
              name="length"
              label="长度"
              rules={[
                {
                  required: true,
                  message: '请输入长度',
                },
              ]}
            >
              <InputNumber
                style={{ width: '60%' }}
                placeholder="请输入长度"
                min={1}
                max={20}
              />
            </Form.Item>
            <Form.Item
              name="params"
              label="数值内容"
              rules={[
                {
                  required: true,
                  message: '请输入数值',
                },
              ]}
              initialValue="不清零"
            >
              <Select
                style={{ width: '60%' }}
                options={[
                  { value: '不清零', label: '不清零' },
                  { value: '每月清零', label: '每月清零' },
                  { value: '每日清零', label: '每日清零' },
                  { value: '每年清零', label: '每年清零' },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="defaultIndex"
              label="默认值"
              rules={[
                {
                  required: true,
                  message: '请输入数值',
                },
              ]}
              initialValue="0"
            >
              <Input style={{ width: '60%' }} />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
};

export default FlowModel;
