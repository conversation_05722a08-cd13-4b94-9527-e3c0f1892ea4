// import { DownOutlined } from '@ant-design/icons';
import { useEffect, useMemo, useState } from 'react';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import TabsType from '@/components/TabsType';
import Inventory from './Inventory';
import Inventory2 from './Inventory2';
import Inventory3 from './Inventory3';
import Inventory4 from './Inventory4';
import Inventory5 from './Inventory5';
import { useLocation } from '@@/exports';
import Inventory6 from './Inventory6';
export default () => {
  const [toList] = usePermissionFiltering();
  const location = useLocation();
  const { search }: any = location;
  console.log('渲染');
  const [activeKey, setActiveKey] = useState<string>('4');
  useEffect(() => {
    if (search.includes('type=2')) {
      console.log('执行');
      setActiveKey('2');
    }
  }, [search]);
  const onChange = (key: any) => {
    console.log('key', key);
    console.log('keytt', typeof key);
    setActiveKey(key);
  };
  const items = [
    {
      label: 'SKU批次库存流水',
      key: '4',
      // children: <LabelModel />,
      //accessible: [':System:IDRuleFullAccess'],
    },
    {
      label: '仓库作业流水',
      key: '5',
      // children: <LabelModel />,
      //accessible: [':System:IDRuleFullAccess'],
    },
  ];
  const tabsProps = {
    activeKey,
    items: toList(items),
    onChange,
    defaultActiveKey: activeKey,
  };
  const renderComponents: any = {
    '4': <Inventory4 />,
    '5': <Inventory5 />,
  };
  return (
    <>
      <TabsType {...tabsProps} />
      <div style={{ height: ' calc(100vh - 140px)' }}>
        {renderComponents[activeKey]}
      </div>
    </>
  );
};
