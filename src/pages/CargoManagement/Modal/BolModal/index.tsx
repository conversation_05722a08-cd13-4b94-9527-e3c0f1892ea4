import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import service from '@/services/home';
import React, { useState } from 'react';
import { REQUESTADDRESS_W } from '@/globalData';

const { addressFuzzyGetAPI } = service.UserHome;
const BolModal = (props: any) => {
  const { record, btnText, key, myList, type, refresh } = props;
  console.log('mylist', myList);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);

  const showModal = () => {
    if (!myList?.length) {
      console.log('由于myList为空而关闭弹窗');
      setModalVisit(false);
      message.error(`请选择需要${btnText}的运单`);
    } else {
      setModalVisit(true);
    }
  };

  /* 提交 */
  const onFinish = async (values: any) => {
    console.log('values', values.withRemark);
    console.log('myList', myList);
    const waybillIds = myList.map((item: any) => item.id).join(',');
    console.log('waybillIds', waybillIds);
    //退件退款
    try {
      /*  const res = await batchGenerateBOL({
        ids: waybillIds,
        withRemark: values.withRemark,
      });*/
      setModalVisit(false);
      window.open(
        `${REQUESTADDRESS_W}/shipment/batchGenerateBOL?ids=${waybillIds}&withRemark=${values?.withRemark}`,
        '_blank',
      );
    } catch (e) {
      console.error('打印面单失败', e);
    }
    if (status) {
      message.success('操作成功');
      setModalVisit(false);
      refresh();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };

  return (
    <>
      <Button onClick={showModal} type={'primary'} ghost>
        {btnText}
      </Button>
      <ModalForm
        title={btnText}
        //labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        width={600}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        open={modalVisit}
        layout="horizontal"
        submitTimeout={2000}
        onFinish={onFinish}
        initialValues={{ withRemark: 1 }}
      >
        <div style={{ marginTop: 20 }}>
          <ProFormSelect
            name="withRemark"
            label="备注"
            width={'md'}
            options={[
              { label: '含备注', value: 1 },
              { label: '无备注', value: 0 },
            ]}
          />
          <div style={{ color: 'red' }}>仅导出系统生成的，不包含用户上传的</div>
        </div>
      </ModalForm>
    </>
  );
};
export default BolModal;
