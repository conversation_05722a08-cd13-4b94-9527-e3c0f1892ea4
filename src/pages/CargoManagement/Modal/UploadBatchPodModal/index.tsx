import { ModalForm } from '@ant-design/pro-components';
import { Button, Form, Image, Input, message, Tooltip, Upload } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import service from '@/services/home';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import {
  getShipmentDetail,
  getWaybillList2,
  uploadPodV2,
} from '@/services/ConfiguredCar';
import { REQUESTADDRESS_W } from '@/globalData';
import { UploadOutlined } from '@ant-design/icons';

import SuperTables from '@/components/SuperTables';
import address from '@/assets/icon/address.png';
import menu from '@/assets/icon/menu.png';
import car from '@/assets/icon/car.png';

const { addressFuzzyGetAPI } = service.UserHome;
const UploadBatchPodModal = (props: any) => {
  const {
    record,
    btnText,
    key,
    type = 'link',
    refresh,
    style = { color: '#1677ff' },
  } = props;
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  /*数据源*/
  const [Datasource, setDatasource] = useState<any>([]);
  /*url*/
  const [Url, setUrl] = useState<any>('');
  /*url*/
  const [FileList, setFileList] = useState<any>([]);
  /*数据源*/
  const [OldDatasource, setOldDatasource] = useState<any>([]);

  console.log('record', record);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const [Details, setDetails] = useState({});
  const [Keyword, setKeyword] = useState('');
  const getDetail = async (id: any) => {
    const res = await getWaybillList2({ id });
    console.log('res', res);
    if (res?.status) {
      setDatasource(res?.data?.list);
      setOldDatasource(res?.data?.list);
    }
  };
  const showModal = () => {
    if (!record?.length) {
      setModalVisit(false);
      message.error(`请选择需要${btnText}的数据`);
    } else {
      setModalVisit(true);
    }
  };
  const getbaseData = async () => {
    const { status, data } = await getShipmentDetail({
      id: record?.map((item) => item?.id).join(','),
    });
    if (status) {
      setDetails(data);
    }
    console.log('data', data);
  };

  useEffect(() => {
    if (record?.length > 0 && modalVisit) {
      getDetail(record?.map((item: any) => item?.id).join(','));
      //getbaseData(record?.map((item: { id: any }) => item?.id).join(','));
    }
  }, [record?.id, modalVisit]);
  useEffect(() => {
    const datas = OldDatasource.filter(
      (item: any) =>
        item.waybillNo.includes(Keyword) ||
        item.dest.includes(Keyword) ||
        item.recipient.subject.includes(Keyword),
    );
    if (Keyword) {
      console.log('Keyword', Keyword);
      setDatasource(datas);
    } else {
      if (record?.length > 0) {
        getDetail(record?.id);
      }
    }
  }, [Keyword]);
  /* 提交 */
  const onFinish = async (values: any) => {
    console.log('values', values);
    console.log('checkData', checkData);
    console.log('FileList', FileList);
    const ids = FileList?.map((item: any) => item.response?.data?.url).join(
      ',',
    );
    console.log('ids', ids);
    console.log('Url', Url);
    console.log('Datasource?.length', Datasource?.length);
    if (!ids) {
      message.warning('请选择pod文件');
      return;
    } else if (checkData.length === 0) {
      message.warning('请选择运单号');
      return;
    }
    //退件退款
    const { data, status } = await uploadPodV2({
      podUrl: ids,
      id: record?.map((item) => item.id).join(','),
      waybillIds: checkData?.map((item) => item.id).join(','),
    });
    if (status) {
      message.success('操作成功');
      setModalVisit(false);
      refresh();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };
  const columns = [
    {
      title: '运单号',
      dataIndex: 'waybillNo',
      width: 120,
    },
    {
      title: '件数',
      dataIndex: 'pieceNum',
      width: 80,
    },
    {
      title: '派送标志',
      dataIndex: 'dest',
      width: 80,
    },
    {
      title: '收件地址',
      dataIndex: ['recipient', 'subject'],
      width: 500,
    },
  ];

  return (
    <>
      <Button onClick={showModal} type={type} style={style} ghost>
        {btnText}
      </Button>
      <ModalForm
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div>{btnText}</div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginLeft: 8,
              }}
            >
              <div>
                <Form.Item
                  name={'podUrl'}
                  labelCol={{ span: 8 }}
                  style={{ margin: 0 }}
                  rules={[{ required: true, message: '请选择附件!' }]}
                >
                  <Upload
                    name="file"
                    action={`${REQUESTADDRESS_W}/file/uploadTempFile?service_id=WMS`}
                    headers={{
                      token: localStorage.getItem('token') as any,
                    }}
                    multiple={true}
                    className={styles.required}
                    showUploadList={{ showPreviewIcon: true }}
                    onChange={(info: any) => {
                      console.log('info', info);
                      if (info?.file?.status === 'done') {
                        setFileList(info?.fileList);
                        setUrl(info?.file?.response?.data?.url);
                        //info?.file?.response?.data?.url
                        message.success('上传成功');
                      } else if (info?.file?.status === 'removed') {
                        setFileList(info?.fileList);
                        setUrl('');
                        //info?.file?.response?.data?.url
                        message.success('删除成功');
                      }
                    }}
                  >
                    <Button icon={<UploadOutlined />}>上传文件</Button>
                  </Upload>
                </Form.Item>
              </div>
              <div
                style={{
                  marginLeft: 17,
                  color: '#AEAEAE',
                  fontSize: '14px',
                  fontWeight: 300,
                }}
              >
                支持pod文件，图片，pdf，excel等多种格式
              </div>
            </div>
          </div>
        }
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 21 }}
        width={1200}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        open={modalVisit}
        layout="horizontal"
        submitTimeout={2000}
        onFinish={onFinish}
        initialValues={{ ...record }}
      >
        <div className={styles.topWrap}>
          <div className={styles.topbp}>
            <div className={styles.items}>
              <Image
                width={40}
                height={40}
                src={menu}
                preview={false}
                style={{ marginRight: 8 }}
              />
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{ marginLeft: 8 }}>派送单号：</div>
                <Tooltip
                  title={Array.from(
                    new Set(record.map((item: any) => item?.no)),
                  ).join(',')}
                >
                  <div
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: 200,
                      display: 'block',
                      marginRight: 8,
                    }}
                  >
                    {Array.from(
                      new Set(record.map((item: any) => item?.no)),
                    ).join(',')}
                  </div>
                </Tooltip>
              </div>
            </div>
            <div className={styles.items}>
              <Image
                width={12}
                height={16}
                src={address}
                preview={false}
                style={{ marginRight: 8 }}
              />
              <Tooltip
                title={Array.from(
                  new Set(record.map((item: any) => item?.dest)),
                ).join(',')}
              >
                <div
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: 200,
                    display: 'block',
                    marginRight: 8,
                  }}
                >
                  {Array.from(
                    new Set(record.map((item: any) => item?.dest)),
                  ).join(',')}
                </div>
              </Tooltip>
            </div>
            <div className={styles.items}>
              <Image
                width={16}
                height={14}
                src={car}
                preview={false}
                style={{ marginRight: 8 }}
              />
              <Tooltip
                title={Array.from(
                  new Set(record.map((item: any) => item?.providerName)),
                ).join(',')}
              >
                <div
                  style={{
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: 200,
                    display: 'block',
                    marginRight: 8,
                  }}
                >
                  {Array.from(
                    new Set(record.map((item: any) => item?.providerName)),
                  ).join(',')}
                </div>
              </Tooltip>
            </div>
          </div>
        </div>
        <div className={styles.tableWrap}>
          <SuperTables
            columns={columns}
            isHideToolBar={true}
            dataSource={Datasource}
            height={300}
            instanceId={'UploadPodModal'}
            rowSelection={(Keys) => setCheckData(Keys)}
            rowKey={'id'}
            toolBarRender={() => {
              return (
                <Input
                  style={{ width: 300, height: 32 }}
                  onChange={(e: any) => {
                    console.log('e', e);
                    setKeyword(e.target.value);
                  }}
                  placeholder={'请输入关键词搜索'}
                  allowClear={true}
                  value={Keyword}
                />
              );
            }}
            isHidePagination={true}
          />
        </div>
      </ModalForm>
    </>
  );
};
export default UploadBatchPodModal;
