
.topWrap{
  display: flex;
  justify-content: center;

  .topbp{
    background: linear-gradient( 331deg, #3A63FF 0%, #569EFF 100%), #F6F9FD;
    border-radius: 10px;
    width: 100%;
    height: 72px;
    display: flex;
    align-items: center;
    padding-left: 16px;
    .items{
      color: #fff;
      margin-right: 84px;
      font-weight: 600;
      font-size: 18px;
      display: flex;
    }
  }
}
.tableWrap{
  margin-top:8px;
  :global{
    .ant-pro-card-body{
      padding: 0 !important;
    }
  }
}
.required::before {
  content: '* ';
  color: red;
  font-weight: bold;
}