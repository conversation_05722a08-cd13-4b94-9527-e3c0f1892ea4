import { modifyRemarkAPI } from '@/services/ConfiguredCar';
import { Button, Input, message, Modal } from 'antd';
import { useState } from 'react';
const { TextArea } = Input;
const WorkingInstruction = ({ checkData, refresh }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [remark, setRemark] = useState('');
  const showModal = () => {
    if (!checkData?.length) return message.error('请勾选');
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    try {
      const { status } = await modifyRemarkAPI({
        ids: checkData
          ?.map((item: any) => item?.id)
          ?.filter(Boolean)
          ?.join(','),
        remark,
      });
      if (status) {
        message.success('操作成功');
        setRemark('');
        if (refresh) refresh();
      }
      setIsModalOpen(false);
    } catch (error) {}
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type="primary" onClick={showModal}>
        作业指令
      </Button>
      <Modal
        title="作业指令"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
      >
        <TextArea
          allowClear
          onChange={(e) => {
            setRemark(e?.target?.value);
          }}
          placeholder="请输入"
        />
      </Modal>
    </>
  );
};
export default WorkingInstruction;
