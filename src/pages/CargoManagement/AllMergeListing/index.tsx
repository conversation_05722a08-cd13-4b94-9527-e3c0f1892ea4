import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useState } from 'react';
import { getWarehouseMap, mergeListing } from '@/services/ConfiguredCar';

const AllMergeListing = (props: any) => {
  const { record, btnText, myList, ButtonType = 'primary' } = props;
  console.log('mylist', myList);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const [checkData, setCheckData] = useState([]);
  const [allData, setAllData] = useState([]);
  /*是否有重复值*/
  function hasDuplicateValues(arr, key) {
    const values = arr.map((item) => item[key]);
    const uniqueValues = new Set(values);
    console.log('uniqueValues: ', uniqueValues.size);
    console.log('values: ', values.length);
    if (values.length === 1) {
      return false;
    } else {
      return uniqueValues.size === values.length;
    }
  }
  const showModal = () => {
    if (!myList?.length) {
      console.log('由于myList为空而关闭弹窗');
      setModalVisit(false);
      message.error(`请选择需要${btnText}的运单`);
    } else {
      if (hasDuplicateValues(myList, 'warehouseId')) {
        message.error(`批量上架只支持同一仓库`);
        setModalVisit(false);
      } else {
        setModalVisit(true);
      }
    }
  };
  const columns: any = [
    {
      title: '托盘号码',
      dataIndex: 'no',
      hideInSearch: true,
    },
    {
      title: '派送方式',
      hideInSearch: true,
      dataIndex: 'shipmentMethods',
    },
    {
      title: 'FBA仓库',
      hideInSearch: true,
      dataIndex: 'dest',
    },
    {
      title: '件数',
      dataIndex: 'pieceNumber',
      hideInSearch: true,
    },
    {
      title: '总立方数/CBM',
      hideInSearch: true,
      dataIndex: 'volume',
    },
    {
      title: '总重量/Kg',
      hideInSearch: true,
      dataIndex: 'weight',
    },
    {
      title: '上架库位',
      hideInSearch: true,
      dataIndex: 'weight',
      render: (text: any, record: any, index: any) => {
        return (
          <ProFormSelect
            required={true}
            noStyle={true}
            showSearch={true}
            request={async () => {
              const { status, data } = await getWarehouseMap({
                warehouseId: record?.warehouseId,
                type: 0,
              });
              if (status) {
                return data?.list?.map((item: any) => {
                  return {
                    label: item.value,
                    value: item.value,
                  };
                });
              }
              return [];
            }}
            onChange={(e) => {
              console.log('index', index);
              console.log('e', e);
              const updatedData = [...allData];
              updatedData[index] = e;
              setAllData(updatedData);
            }}
          />
        );
      },
    },
  ];
  /* 提交 */
  const onFinish = async (values: any) => {
    console.log('allData', allData);
    const { check } = values;
    console.log('values', values);
    const list = myList.map((item: any, index: any) => {
      return { id: item?.id, slotId: values.slotId };
    });
    console.log('CheckData', checkData);
    const param = {
      list,
      warehouseId: myList[0]?.warehouseId,
    };
    const { data, status } = await mergeListing(param);
    if (status) {
      message.success('操作成功');
      setModalVisit(false);
      props.refresh();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };
  const handleChange = (key, record) => {
    setCheckData(record);
    console.log(' record', record);
  };
  return (
    <>
      <Button onClick={showModal} type={ButtonType} ghost>
        {btnText}
      </Button>
      <ModalForm
        title={btnText}
        form={form}
        autoComplete="off"
        autoFocusFirstInput
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        /*        submitter={{
          render: (props, doms) => {
            console.log(props);
            return [
              <Button type="primary" key="rest" onClick={onFinish}>
                确定
              </Button>,
              <Button
                type="default"
                key="submit"
                onClick={() => setModalVisit(false)}
              >
                取消
              </Button>,
            ];
          },
        }}*/
        open={modalVisit}
        submitTimeout={2000}
        onFinish={onFinish}
        initialValues={{ ...record }}
      >
        <ProFormSelect
          label={'上架库位'}
          required={true}
          showSearch={true}
          name={'slotId'}
          style={{ width: '180px' }}
          request={async () => {
            const { status, data } = await getWarehouseMap({
              // warehouseId: record?.warehouseId,
              type: 0,
            });
            if (status) {
              return data?.list?.map((item: any) => {
                return {
                  label: item.value,
                  value: item.value,
                };
              });
            }
            return [];
          }}
        />
        {/*  <Table columns={columns} rowKey={'id'} dataSource={myList} />*/}
      </ModalForm>
    </>
  );
};
export default AllMergeListing;
