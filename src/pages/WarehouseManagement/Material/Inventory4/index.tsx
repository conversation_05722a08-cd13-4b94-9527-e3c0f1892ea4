import React, { useEffect, useRef, useState } from 'react';
import { Input, Space } from 'antd';
import MrTable from '@/components/MrTable';
import {
  getSummarizedList,
  getSummarizedListSelf,
} from '@/services/ConfiguredCar';
import { DispatchMap } from '@/constants';
import AirLineModel from '../Modal/AirLineModel';
import DeliveryModal from '../DeliveryModal/AirLineModel';
const { Search } = Input;

const Inventory = () => {
  // const access = useAccess();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState({});
  const actionRef = useRef<any>();
  const handleOk = async () => {
    actionRef.current.reload();
    setIsModalOpen(false);
  };
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const clearSelec = useRef<any>(null);
  const handleCancel = () => {
    setIsModalOpen(false);
    setValue({});
  };
  const refreshTable = () => {
    actionRef.current.reload();
    clearSelec.current();
  };

  useEffect(() => {
    setValue({});
  }, []);

  /* 刷新表格 */
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Search
            placeholder="请输入标签名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },
    {
      title: '中文品名',
      hideInSearch: true,
      dataIndex: 'shipmentMethods',
      render: (text: any, record: any) => {
        return record?.sku?.cnName;
      },
    },
    {
      title: '英文品名',
      hideInSearch: true,
      dataIndex: 'form',
      render: (text: any, record: any) => {
        return record?.sku?.enName;
      },
    },
    {
      title: '规格',
      hideInSearch: true,
      dataIndex: 'model',
      render: (text: any, record: any) => {
        return record?.sku?.model;
      },
    },
    {
      title: '库存',
      hideInSearch: true,
      dataIndex: 'userage',
      render: (_: any, record: any) => {
        return record?.stat?.total;
      },
    },
    {
      title: '用途',
      hideInSearch: true,
      dataIndex: 'createTime',
      render: (_: any, record: any) => {
        return record?.sku?.usage;
      },
    },
    {
      title: '所在仓库',
      hideInSearch: true,
      dataIndex: 'isa',
      render: (_: any, record: any) => {
        return record?.warehouse?.name;
      },
    },
    {
      title: 'SKU编码',
      dataIndex: 'no',
      width: '200px',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return record?.sku?.code;
      },
    },
    /*   {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, ProgressStateType)}>
            {getLabelByValue(record?.state, ProgressStateType)}
          </Tag>
        );
      },
    },*/

    // {
    //   title: 'WMS（L*W*H-W）',
    //   dataIndex: 'name',
    //   width: 200,
    //   key: 'name',
    //   render: (_: any, record: any) => {
    //     return `${record?.sku?.length} * ${record?.sku?.width} * ${record?.sku?.height} - ${record?.sku?.weight}`;
    //   },
    // },
  ];
  return (
    <>
      <MrTable
        keyID="system/setting"
        columns={columns}
        rowKey="id"
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
        }}
        toolBarRender={
          <Space>
            <AirLineModel btnTitle={'入库'} refresh={refreshTable} />
            <DeliveryModal
              btnTitle={'出库'}
              refresh={refreshTable}
              checkData={checkData}
            />
          </Space>
        }
        request={async (params: any, action: any, clearSelectedRows: any) => {
          clearSelec.current = clearSelectedRows;
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getSummarizedListSelf({
            ...params,
            // self: 1,
          });
          return {
            data:
              msg?.data?.list?.map((item: any, index: number) => ({
                ...item,
                id: index,
              })) || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          warehouseId: {
            desc: '仓库',
            type: 'warehouse',
            value: '',
            multi: true,
          },
        }}
        /* toolBarRender={<Button
              key="primary"
              type="primary"
              onClick={() => setIsModalOpen(true)}
            >
              新建规则
            </Button>}*/
      />
    </>
  );
};
export default Inventory;
