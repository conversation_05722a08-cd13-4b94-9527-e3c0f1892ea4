import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, Form, Input, message, Table } from 'antd';
import React, { useState } from 'react';

import { getWarehouseList } from '@/services/InternalTrial';
import { createPurchaseOrder } from '@/services/home/<USER>';
import { getBrokerList } from '@/services/customer/CustomerHome';
import { ProviderType } from '@/shared/ProviderTypeFn';
import SkuModel from '@/pages/WarehouseManagement/Material/Modal/SkuModel';

const AirLineModel = (props: any) => {
  const { record, btnTitle, refresh } = props;
  console.log('record: ', record);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const [DataSource, setDataSource] = useState([]);
  /* 提交 */
  const onFinish = async (v: any) => {
    console.log('提交', v);
    const res = await createPurchaseOrder(v);
    if (res?.status) {
      message.success('提交成功');
      setModalVisit(false);
    }
  };
  const showModal = () => {
    setModalVisit(true);
  };
  const handleSkuData = (v: any) => {
    console.log('v', v);
    setDataSource(v);
  };
  const columns: any = [
    {
      title: 'SKU',
      dataIndex: 'code',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any, index: any) => {
        return (
          <>
            {record?.code}
            <Form.Item
              name={['details', index, 'skuId']}
              style={{ margin: 0 }}
              hidden={true}
              initialValue={record?.id}
            >
              <Input style={{ width: 100 }} value={record?.id} />
            </Form.Item>
          </>
        );
      },
    },
    {
      title: '中文名称',
      dataIndex: 'cnName',
      selectable: 'none',
      className: 'nonSelectable',
    },
    {
      title: '入库数量',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any, index: any) => {
        return (
          <Form.Item name={['details', index, 'quatity']} style={{ margin: 0 }}>
            <Input style={{ width: 100 }} />
          </Form.Item>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any) => {
        return (
          <>
            <a>{record?.name}</a>
          </>
        );
      },
    },
  ];
  return (
    <>
      <Button type={'primary'} onClick={showModal}>
        {btnTitle}
      </Button>
      <ModalForm
        title={btnTitle}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        open={modalVisit}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        width={1200}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        layout="horizontal"
        onFinish={onFinish}
      >
        <ProFormSelect
          name="warehouseId"
          label="仓库"
          labelCol={{ span: 4 }}
          request={async (params: any, action: any) => {
            // console.log('tableactiveKey',activeKey);
            let msg = await getWarehouseList({
              ...params,
              type: 3,
            });
            return msg?.data?.list.map((item: any) => {
              return { value: item.id, label: item.name };
            });
          }}
          placeholder="请选择仓库"
          rules={[{ required: true, message: '请选择仓库' }]}
        />
        <Form.Item label={'采购编号'} labelCol={{ span: 4 }} name={'outId'}>
          <Input />
        </Form.Item>
        <ProFormSelect
          name="providerId"
          showSearch
          // rules={[{ required: true, message: '请输入站点' }]}
          labelCol={{ span: 4 }}
          label={'供应商'}
          request={async () => {
            const { status, data } = await getBrokerList({
              type: ProviderType.Material.getCode(),
              len: 999,
            });
            if (status) {
              return data?.list?.map((item: any) => {
                return {
                  label: item.name,
                  value: item.id,
                };
              });
            }
            return [];
          }}
          fieldProps={{
            onChange: (value) => {
              //setParams({ ...params, brokerId: value });
            },
            filterOption: false,
          }}
        />
        <Form.Item label={'备注'} labelCol={{ span: 4 }} name={'remark'}>
          <Input />
        </Form.Item>
        <Form.Item label={'入库物流明细'} labelCol={{ span: 4 }}>
          <Table columns={columns} dataSource={DataSource} />
          <div style={{ marginTop: 10 }}>
            <SkuModel btnTitle={'批量选择'} handleSkuData={handleSkuData} />
          </div>
        </Form.Item>
      </ModalForm>
    </>
  );
};
export default AirLineModel;
