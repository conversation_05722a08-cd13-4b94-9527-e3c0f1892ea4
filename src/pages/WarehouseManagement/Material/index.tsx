// import { DownOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import TabsType from '@/components/TabsType';
import Inventory4 from './Inventory4';
import Inventory5 from './Inventory5';
import { useLocation } from '@@/exports';
import Delivery from './Delivery';
export default () => {
  const [toList] = usePermissionFiltering();
  const location = useLocation();
  const { search }: any = location;
  console.log('渲染');
  const [activeKey, setActiveKey] = useState<string>('1');
  useEffect(() => {
    if (search.includes('type=2')) {
      console.log('执行');
      setActiveKey('2');
    }
  }, [search]);
  const onChange = (key: any) => {
    console.log('key', key);
    console.log('keytt', typeof key);
    setActiveKey(key);
  };
  const items = [
    {
      label: '库存',
      key: '1',
      // children: <LabelModel />,
      //accessible: [':System:IDRuleFullAccess'],
    },
    {
      label: '入库记录',
      key: '2',
      // children: <LabelModel />,
      //accessible: [':System:IDRuleFullAccess'],
    },
    {
      label: '出库记录',
      key: '3',
    },
    /*   {
      label: '库存记录',
      key: '3',
      // children: <LabelModel />,
      //accessible: [':System:IDRuleFullAccess'],
    },*/
  ];
  const tabsProps = {
    activeKey,
    items: toList(items),
    onChange,
    defaultActiveKey: activeKey,
  };
  const renderComponents: any = {
    '1': <Inventory4 />,
    '2': <Inventory5 />,
    '3': <Delivery />,
    //'3': <Inventory5 />,
  };
  return (
    <>
      <TabsType {...tabsProps} />
      <div style={{ height: ' calc(100vh - 140px)' }}>
        {renderComponents[activeKey]}
      </div>
    </>
  );
};
