// import { Access, useAccess } from '@@/exports';
import React, { useEffect, useRef, useState } from 'react';
import { Input } from 'antd';
import { getOutBoundOrderListAPI } from '@/services/ConfiguredCar';
import { formatTime } from '@/utils/format';
import SuperTableMax from '@/components/SuperTableMax';

const { Search } = Input;

const Delivery = () => {
  // const access = useAccess();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState({});
  const actionRef = useRef<any>();
  const handleOk = async () => {
    actionRef.current.reload();
    setIsModalOpen(false);
  };
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const handleCancel = () => {
    setIsModalOpen(false);
    setValue({});
  };
  const refreshTable = () => {
    actionRef.current.reload();
  };

  useEffect(() => {
    setValue({});
  }, []);

  const columns: any = [
    {
      title: '物料出库单号',
      dataIndex: 'no',
      hideInSearch: true,
    },
    /*   {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, ProgressStateType)}>
            {getLabelByValue(record?.state, ProgressStateType)}
          </Tag>
        );
      },
    },*/
    {
      title: '仓库',
      hideInSearch: true,
      dataIndex: ['warehouse', 'name'],
      fieldFormat: (record: any) => {
        return record?.warehouse?.name;
      },
    },
    {
      title: '业务单号',
      hideInSearch: true,
      dataIndex: 'outNO',
    },
    {
      title: '出库物料总数',
      hideInSearch: true,
      dataIndex: 'summary',
    },

    {
      title: '创建时间',
      hideInSearch: true,
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return record?.createTime ? formatTime(record?.createTime) : '';
      },
    },
    {
      title: '申请人',
      hideInSearch: true,
      dataIndex: 'applyUser',
      fieldFormat: (record: any) => {
        return record?.applyUser?.name;
      },
    },
    {
      title: '备注',
      hideInSearch: true,
      dataIndex: 'remark',
    },
  ];
  return (
    <>
      <SuperTableMax
        instanceId="Material/Delivery"
        columns={columns}
        isWarpTabs={true}
        rowSelection={(value: any) => {
          setCheckData(value);
        }}
        // toolBarRender={
        //   <Space>
        //     <DeliveryModal btnTitle={'出库'} refresh={refreshTable} />
        //   </Space>
        // }
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getOutBoundOrderListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: { ...params.condition },
            self: 1,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          warehouseId: {
            desc: '仓库',
            type: 'warehouse',
            value: '',
            multi: true,
          },
        }}
        /* toolBarRender={<Button
              key="primary"
              type="primary"
              onClick={() => setIsModalOpen(true)}
            >
              新建规则
            </Button>}*/
      />
    </>
  );
};
export default Delivery;
