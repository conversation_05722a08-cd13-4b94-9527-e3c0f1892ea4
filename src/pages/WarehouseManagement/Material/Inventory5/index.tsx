// import { Access, useAccess } from '@@/exports';
import { useEffect, useRef, useState } from 'react';
import { Input } from 'antd';
import MrTable from '@/components/MrTable';
import { getPurchaseOrderListAPI } from '@/services/ConfiguredCar';
import { formatTime } from '@/utils/format';

const { Search } = Input;

const Inventory = () => {
  // const access = useAccess();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState({});
  const actionRef = useRef<any>();
  const handleOk = async () => {
    actionRef.current.reload();
    setIsModalOpen(false);
  };
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const handleCancel = () => {
    setIsModalOpen(false);
    setValue({});
  };
  const refreshTable = () => {
    actionRef.current.reload();
  };

  useEffect(() => {
    setValue({});
  }, []);

  /* 刷新表格 */
  const columns: any = [
    {
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Search
            placeholder="请输入标签名称,ID搜索"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '300px' }}
            onSearch={() => console.log(22222)}
          />
        );
      },
    },
    {
      title: '物料入库单号',
      dataIndex: 'no',
      hideInSearch: true,
    },
    /*   {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Tag color={getColorByValue(record?.state, ProgressStateType)}>
            {getLabelByValue(record?.state, ProgressStateType)}
          </Tag>
        );
      },
    },*/
    {
      title: '仓库',
      hideInSearch: true,
      dataIndex: ['warehouse', 'name'],
    },
    {
      title: '采购编号',
      hideInSearch: true,
      dataIndex: 'outId',
    },
    {
      title: '入库物料总数',
      hideInSearch: true,
      dataIndex: 'summary',
      // render: (text: any, record: any) => {
      //   return record?.warehouse?.name;
      // },
    },

    {
      title: '操作时间',
      hideInSearch: true,
      dataIndex: 'createTime',
      render: (text: any, record: any) => {
        return record?.createTime ? formatTime(record?.createTime) : '';
      },
    },
    {
      title: '操作人',
      hideInSearch: true,
      dataIndex: ['user', 'name'],
    },
    {
      title: '备注',
      hideInSearch: true,
      dataIndex: 'remark',
    },
  ];
  return (
    <>
      <MrTable
        keyID="system/setting"
        columns={columns}
        rowSelectionCablck={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        request={async (params: any, action: any) => {
          actionRef.current = action;
          // console.log('tableactiveKey',activeKey);
          let msg = await getPurchaseOrderListAPI({
            ...params,
            // self: 2,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.amount || msg?.data?.total,
          };
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          warehouseId: {
            desc: '仓库',
            type: 'warehouse',
            value: '',
            multi: true,
          },
        }}
        /* toolBarRender={<Button
              key="primary"
              type="primary"
              onClick={() => setIsModalOpen(true)}
            >
              新建规则
            </Button>}*/
      />
    </>
  );
};
export default Inventory;
