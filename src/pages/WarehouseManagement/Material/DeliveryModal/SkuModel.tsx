import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, Form, Input, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { getListSku } from '@/services/ConfiguredCar';

const SkuModel = (props: any) => {
  const { record, btnTitle, refresh, handleSkuData } = props;
  console.log('record: ', record);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const [Detail, setDetail] = useState([]);
  const [selectedRow, setSelectedRow] = useState<any>([]);
  /* 提交 */
  const onFinish = async (value: any) => {
    console.log('提交', value);
    handleSkuData(selectedRow);
    setModalVisit(false);
  };

  const getList = async () => {
    const res = await getListSku({});
    if (res?.status) {
      setDetail(res?.data?.list);
    }
  };
  const rowSelection = {
    selectedRowKeys: selectedRow?.map((item: any) => item.id),
    onChange: (Keys: any, selectedRows: any) => {
      console.log('selectedRows', selectedRows);
      setSelectedRow(selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
  };
  useEffect(() => {
    if (modalVisit) {
      getList();
    }
  }, [modalVisit]);
  const showModal = () => {
    setModalVisit(true);
  };
  const columns: any = [
    {
      title: 'SKU',
      dataIndex: 'code',
      selectable: 'none',
      className: 'nonSelectable',
    },
    {
      title: '中文名称',
      dataIndex: 'cnName',
      selectable: 'none',
      className: 'nonSelectable',
    },
    {
      title: '用途',
      dataIndex: 'usage',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any) => {
        return (
          <>
            <a>{record?.name}</a>
          </>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any) => {
        return (
          <>
            <a>{record?.name}</a>
          </>
        );
      },
    },
  ];
  return (
    <>
      <Button type={'primary'} onClick={showModal}>
        {btnTitle}
      </Button>
      <ModalForm
        title={btnTitle}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        open={modalVisit}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        layout="horizontal"
        onFinish={onFinish}
      >
        <Table
          columns={columns}
          dataSource={Detail}
          rowSelection={{ ...rowSelection }}
          rowKey={'id'}
        />
      </ModalForm>
    </>
  );
};
export default SkuModel;
