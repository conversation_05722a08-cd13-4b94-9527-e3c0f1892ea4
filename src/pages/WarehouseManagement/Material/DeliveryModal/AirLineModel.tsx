import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, Form, Input, message, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { getUserListAPI } from '@/services/booking';
import { getWarehouseList } from '@/services/InternalTrial';
import { createOutboundOrder } from '@/services/ConfiguredCar';

const DeliveryModal = (props: any) => {
  const { record, btnTitle, checkData, refresh } = props;
  // console.log('checkDatacheckDatacheckData: ', checkData);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const [DataSource, setDataSource] = useState([]);
  useEffect(() => {
    if (modalVisit) {
      setDataSource(
        checkData?.map((item: any, index: number) => ({
          ...item,
          code: item?.sku?.code,
          cnName: item?.sku?.cnName,
          id: item?.sku?.id,
        })),
      );
    } else {
      setDataSource([]);
    }
  }, [modalVisit]);
  /* 提交 */
  const onFinish = async (v: any) => {
    // console.log('提交', v);
    const res = await createOutboundOrder(v);
    if (res?.status) {
      message.success('提交成功');
      setModalVisit(false);
      setDataSource([]);
      if (refresh) {
        refresh();
      }
    }
  };
  const showModal = () => {
    if (!checkData?.length) return message.warning('请勾选');
    const warehouseId = [
      ...new Set(checkData?.map((item: any) => item?.warehouseId)),
    ];
    if (warehouseId?.length > 1) return message.warning('只能选择同一个仓库的');
    form.setFieldValue('warehouseId', warehouseId[0]);
    setModalVisit(true);
  };
  const handleSkuData = (v: any) => {
    console.log('v', v);
    setDataSource(v);
  };
  const columns: any = [
    {
      title: 'SKU',
      dataIndex: 'code',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any, index: any) => {
        return (
          <>
            {record?.code}
            <Form.Item
              name={['details', index, 'skuId']}
              style={{ margin: 0 }}
              hidden={true}
              initialValue={record?.id}
            >
              <Input style={{ width: 100 }} value={record?.id} />
            </Form.Item>
          </>
        );
      },
    },
    {
      title: '中文名称',
      dataIndex: 'cnName',
      selectable: 'none',
      className: 'nonSelectable',
    },
    {
      title: '库存',
      dataIndex: ['stat', 'total'],
      selectable: 'none',
      className: 'nonSelectable',
    },
    {
      title: '数量',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any, index: any) => {
        return (
          <Form.Item
            name={['details', index, 'quatity']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            style={{ margin: 0 }}
          >
            <Input style={{ width: 100 }} />
          </Form.Item>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      render: (text: any, record: any) => {
        return (
          <>
            <a
              onClick={() => {
                const newData = DataSource?.filter(
                  (item: any) => item?.id !== record?.id,
                );
                setDataSource([...newData]);
              }}
            >
              删除
            </a>
          </>
        );
      },
    },
  ];
  return (
    <>
      <Button type={'primary'} onClick={showModal}>
        {btnTitle}
      </Button>
      <ModalForm
        title="新建出库单"
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        open={modalVisit}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        width={1200}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        layout="horizontal"
        onFinish={onFinish}
      >
        <ProFormSelect
          name="warehouseId"
          label="仓库"
          labelCol={{ span: 4 }}
          request={async (params: any, action: any) => {
            // console.log('tableactiveKey',activeKey);
            let msg = await getWarehouseList({
              ...params,
              type: 0,
            });
            return msg?.data?.list.map((item: any) => {
              return { value: item.id, label: item.name };
            });
          }}
          placeholder="请选择仓库"
          rules={[{ required: true, message: '请选择仓库' }]}
        />
        <ProFormSelect
          name="applyUserId"
          showSearch
          rules={[{ required: true, message: '必填项不能为空' }]}
          labelCol={{ span: 4 }}
          label={'申请人'}
          request={async ({ keyWords }) => {
            const { status, data } = await getUserListAPI({
              keyword: keyWords,
              start: 0,
              len: 20,
            });
            if (status) {
              return data?.list?.map((item: any) => {
                return {
                  label: item?.user?.name,
                  value: item?.user?.id,
                };
              });
            }
            return [];
          }}
          fieldProps={{
            onChange: (value) => {
              //setParams({ ...params, brokerId: value });
            },
            filterOption: false,
          }}
        />
        <Form.Item label={'备注'} labelCol={{ span: 4 }} name={'remark'}>
          <Input />
        </Form.Item>
        <Form.Item label={'物料明细'} labelCol={{ span: 4 }}>
          <Table columns={columns} dataSource={DataSource} rowKey={'id'} />
          {/* <div style={{ marginTop: 10 }}>
            <SkuModel btnTitle={'批量选择'} handleSkuData={handleSkuData} />
          </div> */}
        </Form.Item>
      </ModalForm>
    </>
  );
};
export default DeliveryModal;
