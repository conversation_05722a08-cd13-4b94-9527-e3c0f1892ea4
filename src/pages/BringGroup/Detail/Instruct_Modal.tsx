import { getModifyGroupDestRemarkAPI } from '@/services/overview';
import { Button, Input, message, Modal } from 'antd';
import { useState } from 'react';
const { TextArea } = Input;

const Instruct_Modal = ({ recode, refresh, blnoOrderId }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState(recode?.groupDestRemark);
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    try {
      const { status } = await getModifyGroupDestRemarkAPI({
        value,
        key: recode?.dest,
        blnoOrderId,
      });
      if (status) {
        message.success('操作成功');
        setIsModalOpen(false);
        refresh();
      }
    } catch (error) {
      setIsModalOpen(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type="link" onClick={showModal}>
        修改指令
      </Button>
      <Modal
        title="修改指令"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
      >
        <TextArea
          placeholder="请输入"
          style={{ width: '420px' }}
          defaultValue={value}
          onChange={(e) => setValue(e?.target?.value)}
        />
      </Modal>
    </>
  );
};
export default Instruct_Modal;
