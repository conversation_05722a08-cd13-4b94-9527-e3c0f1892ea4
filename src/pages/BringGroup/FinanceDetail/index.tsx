import { ProCard } from '@ant-design/pro-components';
import { useLocation } from 'umi';
import styles from './index.less';
import { useEffect, useRef, useState } from 'react';
import { formatTime } from '@/utils/format';
import {
  getWaybillSummaryDetailAPI,
  getWMSBlnoDetailAPI,
} from '@/services/financeApi';
import { useRequest } from 'ahooks';
import ReceivableTable from './ReceivableTable';
import ExcelReader from '@/components/ExcelReader';
import { Button, Dropdown, message, Popconfirm } from 'antd';
import SuperTables from '@/components/SuperTables';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import {
  downloadFileResaleOrderAPI,
  getResaleOrderDeleteFile,
  getResaleOrderFile,
  uploadReResaleOrder,
} from '@/services/booking';
import { EllipsisOutlined } from '@ant-design/icons';
const FinanceDetail = () => {
  const { state }: any = useLocation();
  const actionRef = useRef<any>(null);
  //   const [detailData, setDetailData] = useState<any>({});
  const { run: getDetails, data } = useRequest(getWMSBlnoDetailAPI, {
    manual: true,
  });
  const refresh = () => {
    getDetails({ id: state?.financeId });
  };
  useEffect(() => {
    if (state?.financeId) {
      getDetails({ id: state?.financeId });
    }
  }, [state?.financeId]);
  const deliveryState: any = {
    '0': '整柜快递',
    '1': '整柜卡派',
    '2': '拼柜散货',
  };
  const uploadingColumns: any = [
    {
      title: '文件',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      fieldFormat: (record: any) => {
        return record?.name;
      },
      render: (text: any, record: any) => {
        return (
          <>
            <a
              onClick={() => {
                downloadFile(record?.url);
              }}
            >
              {record?.name}
            </a>
          </>
        );
      },
    },
    {
      title: '文件类型',
      dataIndex: 'type',
      fieldFormat: (record: any) => {
        return record?.typeDesc;
      },
    },
    {
      title: '上传人',
      dataIndex: 'userName',
      fieldFormat: (record: any) => {
        return record?.userName;
      },
      // render: (text: any, record: any) => {
      //   return (
      //     <Space>
      //       <Avatar
      //         src={`${
      //           record?.avatar?.includes('http')
      //             ? record?.avatar
      //             : `https://static.kmfba.com/${record?.avatar}`
      //         }`}
      //       />
      //       <div>{record?.userName}</div>
      //     </Space>
      //   );
      // },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      width: 250,
      key: 'option',
      valueType: 'option',
      render: (text: any, record: any) => [
        <Popconfirm
          key={1}
          title="删除确认"
          description="确认要删除次文件吗?"
          onConfirm={() => {
            deleteRelatedFile(record?.id);
          }}
          okText="删除"
          cancelText="取消"
          disabled={record?.auto === 1}
        >
          <Button type="link" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];
  /* 删除相关文件 */
  const deleteRelatedFile = async (id: string) => {
    try {
      const { status } = await getResaleOrderDeleteFile({
        outId: state?.financeId,
        fileId: id,
      });
      if (status) {
        message.success('删除成功');
        refresh();
        actionRef?.current?.refresh();

        // getRemarksList(state?.id);
      }
    } catch (e) {
      console.error('删除相关文件失败', e);
    }
  };
  /* 上传文件 */
  const uploadFile = async (type: any, files: any) => {
    const { status } = await uploadReResaleOrder({
      outId: state?.financeId,
      type: type,
      files: files,
    });
    if (status) {
      message.success('上传成功');
      refresh();
      actionRef?.current?.refresh();
      // getRemarksList(state?.id);
    }
  };
  /* 下载文件 */
  const downloadFile = async (url: string) => {
    try {
      const response: any = await downloadFileResaleOrderAPI({
        url,
      });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
    } catch (err) {
      console.log('下载失败: ', err);
    }
  };
  return (
    <>
      <ProCard
        style={{ marginBottom: '10px', height: '306px' }}
        gutter={24}
        title={
          <div>
            <span style={{ fontWeight: '700', marginRight: '20px' }}>
              基础信息
            </span>
            <span>单号：{data?.data?.no || '-'}</span>
          </div>
        }
      >
        <div className={styles.cabinet_indent}>
          <div>
            <div>
              <span>交货仓库</span>
              <div>{data?.data?.warehouse?.name}</div>
            </div>
            <div>
              <span>柜型</span>
              <div>{data?.data?.containerMode}</div>
            </div>
            <div>
              <span>预计到港时间</span>
              <div>
                {data?.data?.estimateArriveTime
                  ? formatTime(data?.data?.estimateArriveTime)
                  : '-'}
              </div>
            </div>

            <div>
              <span>实际到港时间</span>
              <div>
                {data?.data?.estimateArriveTime
                  ? formatTime(data?.data?.estimateArriveTime)
                  : '-'}
              </div>
            </div>
            {/* <div>
              <span>附件</span>
              <div>附件名称附件名称附件名称.cel</div>
            </div> */}
          </div>
          <div>
            <div>
              <span>交货方式</span>
              <div>{deliveryState[data?.data?.form]}</div>
            </div>
            <div>
              <span>柜号</span>
              <div>{data?.data?.containerNo}</div>
            </div>
            <div>
              <span>预计提货时间</span>
              <div>
                {data?.data?.estimatePickupTime
                  ? formatTime(data?.data?.estimatePickupTime)
                  : '-'}
              </div>
            </div>
            <div>
              <span>清关完成时间</span>
              <div>{'-'}</div>
            </div>
          </div>
          <div>
            <div>
              <span>提单号</span>
              <div>{data?.data?.blno}</div>
            </div>
            <div>
              <span>预计启航时间</span>
              <div>
                {data?.data?.estimateDepartTime
                  ? formatTime(data?.data?.estimateDepartTime)
                  : '-'}
              </div>
            </div>
            <div>
              <span>提柜完成时间</span>
              <div>
                {data?.data?.estimatePickupTime
                  ? formatTime(data?.data?.estimatePickupTime)
                  : '-'}
              </div>
            </div>
          </div>
        </div>
      </ProCard>
      <ReceivableTable
        dataList={data?.data?.shouldChargeFeeList}
        tableTitle="应收费用"
        itemData={data?.data}
        refresh={refresh}
      />
      <ReceivableTable
        dataList={data?.data?.shouldPayBillList2?.map((item: any) => ({
          ...item,
          extraId_1: item?.extraId1,
          currentRate: item?.exchangeRate,
        }))}
        tableTitle="应付费用"
        itemData={data?.data}
      />
      <ProCard style={{ marginBottom: '10px' }} title="文件管理">
        <SuperTables
          columns={uploadingColumns}
          // options={false}
          ref={actionRef}
          instanceId={'Booking/cabin/sellCabinet/detail'}
          request={async (params: any) => {
            // actionRef.current = action;
            const res = await getResaleOrderFile({
              ...params,
              outId: state?.financeId,
            });
            return {
              data: res.data.list || [],
              success: res.status,
              total: res.data?.total,
            };
          }}
          height={300}
          toolBarRender={() => (
            <Dropdown
              key="menu"
              menu={{
                items: [
                  {
                    label: (
                      <ExcelReader
                        Readertype={true}
                        onChange={(e: any, file: any) => {
                          uploadFile(36, file);
                        }}
                      >
                        参考文件
                      </ExcelReader>
                    ),
                    key: '1',
                  },
                  // {
                  //   label: (
                  //     <ExcelReader
                  //       Readertype={true}
                  //       onChange={(e: any, file: any) => {
                  //         uploadFile(38, file);
                  //       }}
                  //     >
                  //       客户BOL
                  //     </ExcelReader>
                  //   ),
                  //   key: '2',
                  // },
                ],
              }}
            >
              <Button>
                上传
                <EllipsisOutlined />
              </Button>
            </Dropdown>
          )}
        />
      </ProCard>
    </>
  );
};
export default FinanceDetail;
