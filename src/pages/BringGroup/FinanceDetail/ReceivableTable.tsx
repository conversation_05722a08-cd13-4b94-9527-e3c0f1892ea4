import AccessCard from '@/AccessCard';
import Surcharge from '@/pages/Waybilladministration/WaybillCheckIn/components/Surcharge';
import { getAddFee2API, getAddFee3API } from '@/services/booking';
import { getFeeTypeAPI } from '@/services/financeApi';
import {
  ProFormCheckbox,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Dropdown, Input, message, Modal, Space } from 'antd';
import _ from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import { DownOutlined } from '@ant-design/icons';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import AddBillsFee from '@/components/AddBillsFee';
// import ImportBullet from './ImportBullet';
import Others from '@/pages/FinancialManagement/BillsPayable/BillsPayableTable/Others';
interface Props {
  dataList: any;
  tableTitle?: string;
  itemData?: any;
  refresh?: any;
}
const ReceivableTable = ({
  dataList,
  tableTitle,
  itemData,
  refresh,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalOpen2, setIsModalOpen2] = useState(false);
  const [toList] = usePermissionFiltering();
  const [action, setAction] = useState<any>({});
  const [feeData, setFeeData] = useState<any>({
    counterpartyType: 'FreightForwarder',
    syncProfit: true,
    importType: '1',
    exchangeRate: 1,
    currencyTypeCode: 10,
  });
  const [billNameList, setBillNameList] = useState([]);
  const dataLists = useMemo(() => {
    const list = dataList?.map((item: any) => {
      for (const key in item) {
        if (Object.prototype.hasOwnProperty.call(item, key)) {
          item?.bills?.forEach((ele: any) => {
            if (key !== 'bills') {
              ele['amountCny'] = ele?.shouldPayAmountCny;
              ele['amount'] = ele?.shouldPayAmount;
              ele['exchangeRate'] = ele?.currentRate;
              ele['chargedAmountCny'] = ele?.paidAmountCny;
              ele['unchargedAmountCny'] = ele?.unpaidAmountCny;
              ele['createUserInfo'] = ele?.creatorUserInfo;

              // ele = { ...ele, [key]: item[key] }
              // ele[`${key}`] = item[key]
            }
          });
        }
      }
      item.key = _.uniqueId('uid_');
      return item;
    });

    return list;
  }, [dataList]);
  const columns: any = [
    {
      title: '费用名称',
      dataIndex: 'feeName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'feeName',
    },
    {
      title: '本位币/元',
      dataIndex: 'amountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'amountCny',
    },
    {
      title: '原币',
      dataIndex: 'amount',
      width: 120,
      hideInSearch: true,
      key: 'amount',
    },
    {
      title: '币种',
      dataIndex: 'currencyTypeDesc',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'currencyTypeDesc',
    },
    {
      title: ' 汇率',
      dataIndex: 'exchangeRate',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'exchangeRate',
    },
    {
      title: ' 已收款/元',
      dataIndex: 'chargedAmountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'chargedAmountCny',
    },
    {
      title: ' 待核销/元',
      dataIndex: 'unchargedAmountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'unchargedAmountCny',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'remark',
    },

    {
      title: '创建人',
      // dataIndex: ['createUserInfo', 'name'],
      dataIndex: 'createUserInfo',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'createUserInfo',
      render: (_: any, recode: any) => {
        return recode?.createUserInfo?.name;
        // return 'recode?.createUserInfo?.name'
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'createTime',
    },
  ];

  /* 应付费用 */
  const columns2: any = [
    {
      title: '结算服务商',
      dataIndex: 'counterpartyName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      sorter: (a: any, b: any) => {
        const valueA = a.counterpartyName || '';
        const valueB = b.counterpartyName || '';
        return valueA.localeCompare(valueB);
      },
    },
    {
      title: '费用名称',
      dataIndex: 'feeName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      sorter: (a: any, b: any) => {
        const valueA = a.feeName || '';
        const valueB = b.feeName || '';
        return valueA.localeCompare(valueB);
      },
    },
    {
      title: '费用分类',
      dataIndex: 'counterpartyTypeDesc',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '币种',
      dataIndex: 'currencyTypeDesc',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '汇率',
      dataIndex: 'currentRate',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },

    {
      title: '本位币金额',
      dataIndex: 'amountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      // render: (text: any, record: any) => {
      //   return (
      //     Number(record?.shouldPayAmount) * Number(record?.currentRate || 1)
      //   ).toFixed(2);
      // },
    },
    {
      title: '分摊源单号',
      dataIndex: 'extraId_1',
      width: 160,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    // {
    //   title: '发生时间',
    //   dataIndex: 'feeName',
    //   width: 120,
    //   hideInSearch: true,
    //   ellipsis: true,
    //   valueType: 'dateTime',
    // },
    {
      title: '创建人',
      dataIndex: 'creatorUserInfo',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      render: (text: any, record: any) => {
        return record?.creatorUserInfo?.name;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      valueType: 'dateTime',
    },
  ];
  const columns3: any = [
    {
      title: '费用名称',
      dataIndex: 'feeName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'feeName',
    },
    {
      title: '本位币/元',
      dataIndex: 'shouldPayAmountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'shouldPayAmountCny',
    },
    {
      title: '原币',
      dataIndex: 'shouldPayAmount',
      width: 120,
      hideInSearch: true,
      key: 'shouldPayAmount',
    },
    {
      title: '币种',
      dataIndex: 'currencyTypeDesc',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'currencyTypeDesc',
    },
    {
      title: ' 汇率',
      dataIndex: 'currentRate',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'currentRate',
    },
    {
      title: ' 已收款/元',
      dataIndex: 'paidAmountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'paidAmountCny',
    },
    {
      title: ' 待核销/元',
      dataIndex: 'unpaidAmountCny',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'unpaidAmountCny',
    },
    {
      title: ' 备注',
      dataIndex: 'remark',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'remark',
    },
    {
      title: '创建人',
      dataIndex: ['creatorUserInfo', 'name'],
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'creatorUserInfo',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
      key: 'createTime',
    },
  ];
  const getAddFee = async () => {
    if (!feeData?.billName) return message.warning('费用名称不能为空');
    if (!feeData?.exchangeRate) return message.warning('汇率不能为空');
    if (!feeData?.currencyTypeCode) return message.warning('币种不能为空');
    if (!feeData?.amount) return message.warning('金额不能为空');
    // if (!feeData?.counterpartyType) return message.warning('业务类型不能为空');
    if (!feeData?.importType) return message.warning('方式不能为空');
    try {
      const { status } = await getAddFee2API({
        ...feeData,
        clientId: action?.clientId,
        entityId: action?.entityId,
      });
      if (status) {
        message.success('添加成功');
        setIsModalOpen(false);
        // actionRef.current?.reload();
        refresh();
      }
    } catch (error) {}
  };
  const getAddFee2 = async () => {
    if (!feeData?.billName) return message.warning('费用名称不能为空');
    if (!feeData?.exchangeRate) return message.warning('汇率不能为空');
    if (!feeData?.currencyTypeCode) return message.warning('币种不能为空');
    if (!feeData?.amount) return message.warning('金额不能为空');
    // if (!feeData?.counterpartyType) return message.warning('业务类型不能为空');
    // if (!feeData?.importType) return message.warning('方式不能为空');
    try {
      const { status } = await getAddFee3API({
        ...feeData,
        clientId: action?.clientId,
        entityId: action?.entityId,
      });
      if (status) {
        message.success('添加成功');
        setIsModalOpen2(false);
        // actionRef.current?.reload();
        refresh();
      }
    } catch (error) {}
  };

  const getBillName = async (
    counterpartyType = 'FreightForwarder',
    keyWords?: string,
  ) => {
    const { status, data } = await getFeeTypeAPI({
      keyword: keyWords,
      counterpartyType,
    });
    if (status) {
      return setBillNameList(
        data?.map((item: any) => {
          return {
            label: Object.values(item)[0],
            value: Object.keys(item)[0],
          };
        }) || [],
      );
    }
  };
  const items = [
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
          types={'AirTransport'}
          TopTitle={'增加空运应付'}
          title={'空运应付'}
          type={'link'}
        />
      ),
      key: '0',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|AirTransport/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'ShipTransport'}
          TopTitle={'增加海运应付'}
          title={'海运应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '1',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|ShipTransport/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'Trailer'}
          TopTitle={'增加拖车应付'}
          title={'拖车应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '2',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|Trailer/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'Declaration'}
          TopTitle={'增加报关应付'}
          title={'报关应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '3',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|Declaration/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'Clearance'}
          TopTitle={'增加清关应付'}
          title={'清关应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '4',
      //   accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      //   dataValidator: (path: any, data: any) => {
      //     return /all|Clearance/.test(data.payableType.value);
      //   },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'TailChannel'}
          TopTitle={'增加尾程应付'}
          title={'尾程应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '5',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|TailChannel/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'FreightForwarder'}
          TopTitle={'增加卖货应付'}
          title={'卖货应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '6',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|FreightForwarder/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'AbroadOperate'}
          TopTitle={'增加后端应付'}
          title={'后端应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '7',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|AbroadOperate/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'ContainerLoading'}
          TopTitle={'增加装柜应付'}
          title={'装柜应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '8',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|ContainerLoading/.test(data.payableType.value);
      // },
    },
    {
      label: (
        <AddBillsFee
          getDetail={refresh}
          ghost={true}
          types={'Truck'}
          TopTitle={'增加卡派应付'}
          title={'卡派应付'}
          type={'link'}
          selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
        />
      ),
      key: '9',
      // accessible: ['TMS:Finance:PayablesCheckoutFullAccess'],
      // dataValidator: (path: any, data: any) => {
      //   return /all|Truck/.test(data.payableType.value);
      // },
    },
  ];

  useEffect(() => {
    getBillName();
  }, []);
  const handleMenuClick = (e: any) => {
    console.log('click', e);
  };
  const menuProps = {
    items: toList(items),
    onClick: handleMenuClick,
  };
  const tabletText = () => {
    if (tableTitle === '应收费用') {
      return (
        <Space key={3}>
          <div>{tableTitle}</div>
          {/* <div className="text-12px color-coolgray">
            <span>金额：</span>
            <span className='color-#4071FF'></span>
            <span>（RMB）</span>
          </div> */}
          <div className="text-12px color-coolgray">
            <span>本位币金额：</span>
            <span
              className="color-#4071FF"
              style={{ color: '#cc0000', fontSize: '14px' }}
            >
              {itemData?.shouldChargeTotalAmount}
            </span>
            <span>（美元）</span>
          </div>
          <AccessCard accessible={['TMS:Waybill:SurchargeAddAccess']}>
            <Surcharge
              btnText="添加附加费"
              btnType="link"
              record={{ id: itemData?.waybillId }}
              refresh={refresh}
              length={[1]}
            />
          </AccessCard>

          {/* <AccessCard accessible={['TMS:Finance:ReceivableFeeAddAccess']}>
            <Button
              type="link"
              onClick={() => {
                setAction({
                  waybillNo: itemData?.waybillNo,
                  clientName: itemData?.clientName,
                  clientId: itemData?.clientId,
                  entityId: itemData?.waybillId,
                });
                setIsModalOpen(true);
              }}
            >
              添加应收
            </Button>
          </AccessCard> */}
          <Others
            getDetail={refresh}
            selectedRowKeys={[{ extraId_1: itemData?.no }]}
            btnText="添加应收"
            btnType="link"
            addFlag
          />
        </Space>
      );
    }

    if (tableTitle === '业务员成本费用') {
      return (
        <Space key={2}>
          <div>{tableTitle}</div>
          {/* <div className="text-12px color-coolgray">
            <span>金额：</span>
            <span className='color-#4071FF'>-</span>
            <span>（RMB）</span>
          </div> */}
          {/* <div className="text-12px color-coolgray">
            <span>本位币金额：</span>
            <span className="color-#4071FF">
              {itemData?.salesmanCostTotalAmount}
            </span>
            <span>（RMB）</span>
          </div> */}
          <div className="text-12px color-coolgray">
            <span>毛利润：</span>
            <span className="color-#4071FF">{itemData?.salesmanProfit}</span>
            <span>（美元）</span>
          </div>
          <div className="text-12px color-coolgray">
            <span>毛利润率：</span>
            <span className="color-#4071FF">
              {itemData?.salesmanProfitRatio}
            </span>
            <span>（%）</span>
          </div>
          <div className="text-12px color-coolgray">
            <span>本位币金额：</span>
            <span style={{ color: '#cc0000', fontSize: '14px' }}>
              {dataList
                ?.reduce(
                  (pre: any, cur: any) =>
                    pre + (cur?.amountCny ? Number(cur?.amountCny) : 0),
                  0,
                )
                .toFixed(2)}
            </span>
            <span>（美元）</span>
          </div>
          <AccessCard accessible={['TMS:Finance:ReceivableFeeAddAccess']}>
            <Button
              type="link"
              onClick={() => {
                setAction({
                  waybillNo: itemData?.waybillNo,
                  clientName: itemData?.clientName,
                  clientId: itemData?.clientId,
                  entityId: itemData?.waybillId,
                });
                setIsModalOpen2(true);
              }}
            >
              添加成本
            </Button>
          </AccessCard>

          {/* <ImportBullet btnText="导入成本" btnType="link" refresh={refresh} /> */}
        </Space>
      );
    }

    if (tableTitle === '应付费用') {
      return (
        <Space key={1}>
          <div>{tableTitle}</div>
          <div className="text-12px color-coolgray">
            <span>毛利润：</span>
            <span className="color-#4071FF">{itemData?.companyProfit}</span>
            <span>（美元）</span>
          </div>
          <div className="text-12px color-coolgray">
            <span>毛利率：</span>
            <span className="color-#4071FF">
              {/* eslint-disable-next-line eqeqeq */}
              {itemData?.profitRatio == 1 ? '100' : itemData?.profitRatio}
            </span>
            <span>（%）</span>
          </div>
          <div className="text-12px color-coolgray">
            <span>本位币金额：</span>
            <span style={{ color: '#cc0000', fontSize: '14px' }}>
              {dataList
                ?.reduce(
                  (pre: any, cur: any) =>
                    pre +
                    Number(cur?.amountCny) * Number(cur?.currentRate || 1),
                  0,
                )
                .toFixed(2)}
            </span>
            <span>（美元）</span>
            <Others
              getDetail={refresh}
              selectedRowKeys={[{ extraId_1: itemData?.no }]}
              btnText="添加应付"
              btnType="link"
              addFlag
            />
          </div>
          {/* <Dropdown menu={menuProps}>
            <Button type="link" size={'small'}>
              <Space>
                添加应付
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
          <Others
            getDetail={refresh}
            selectedRowKeys={[{ extraId_1: itemData?.waybillNo }]}
          />  */}
        </Space>
      );
    }
  };
  return (
    <div className="mb-20px">
      <Modal
        title="添加应收"
        open={isModalOpen}
        destroyOnClose
        onCancel={() => setIsModalOpen(false)}
        width={800}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              key="back"
              onClick={() => setIsModalOpen(false)}
              style={{ marginRight: '12px' }}
            >
              取消
            </Button>
            <Button key="submit" type="primary" onClick={getAddFee}>
              确认
            </Button>
          </div>,
        ]}
      >
        <div className={styles.billNumber}>
          <div>
            <span>运单号</span>
            <span>{action?.waybillNo}</span>
            {/* <span>客户</span>
            <span>{`${action?.clientName}`}</span> */}
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName}>
            <ProFormSelect
              label="费用名称"
              showSearch
              colon={false}
              options={billNameList}
              // valueEnum={feeData?.billName}
              onChange={(e) => {
                setFeeData({ ...feeData, billName: e });
                // setBlno({ ...blno, clientId: e })
              }}
              // request={async ({ keyWords }) => {
              //     const { status, data } = await getClientListAPI({
              //         type: 0,
              //         len: 100,
              //         keyword: keyWords,
              //     });
              //     if (status) {
              //         return data.list.map((item: any) => {
              //             return {
              //                 label: `【${item.name}】 - ${item.code}`,
              //                 value: item.id,
              //             };
              //         });
              //     }
              //     return [];
              // }}
            />

            <div className={styles.money}>
              <span>金额</span>
              <Input
                placeholder="请输入"
                type="number"
                onChange={(e) => {
                  setFeeData({ ...feeData, amount: e?.target?.value });
                }}
              />
            </div>
          </div>

          <div className={styles.clientName}>
            {/* <ProFormSelect
              label="业务类型"
              showSearch
              colon={false}
              // mode="multiple"
              options={[
                // { label: '订舱', value: 'ShipTransport' },
                // { label: '清关', value: 'Clearance' },
                // { label: '后端', value: 'AbroadOperate' },
                { label: '货代', value: 'FreightForwarder' },
              ]}
              onChange={(e: any) => {
                setFeeData({ ...feeData, counterpartyType: e, billName: '' });
                getBillName(e);
              }}
            /> */}
            <div className={styles.money}>
              <ProFormSelect
                label="方式"
                fieldProps={{
                  defaultValue: '1',
                }}
                showSearch
                colon={false}
                options={[
                  { label: '追加', value: '1' },
                  { label: '覆盖', value: '2' },
                  //{ label: '总值', value: '3' },
                ]}
                onChange={(e) => {
                  setFeeData({ ...feeData, importType: e });
                }}
              />
            </div>
            <div className={styles.money}>
              <span>汇率</span>
              <Input
                placeholder="请输入"
                type="number"
                defaultValue={1}
                onChange={(e) => {
                  setFeeData({ ...feeData, exchangeRate: e?.target?.value });

                  // setBlno({ ...blno, clientId: e })
                }}
              />
            </div>
          </div>
        </div>

        <div style={{ width: '100%' }}>
          <div className={styles.clientName}>
            <ProFormSelect
              name="clientName"
              label="币种"
              colon={false}
              fieldProps={{
                defaultValue: 10,
              }}
              onChange={(e) => {
                setFeeData({ ...feeData, currencyTypeCode: e });
              }}
              options={[
                {
                  label: '人民币',
                  value: 10,
                },
                {
                  label: '美元',
                  value: 20,
                },
                {
                  label: '加币',
                  value: 30,
                },
                {
                  label: '欧元',
                  value: 40,
                },
                {
                  label: '日元',
                  value: 50,
                },
              ]}
            />
            <ProFormSelect
              name="syncProfit"
              label="同步业务员成本"
              colon={false}
              onChange={(e) => {
                setFeeData({ ...feeData, syncProfit: e });
              }}
              fieldProps={{
                defaultValue: true,
              }}
              options={[
                {
                  label: '同步',
                  value: true,
                },
                {
                  label: '不同步',
                  value: false,
                },
              ]}
            />
          </div>
        </div>
        <div className={styles.remark}>
          <span>备注</span>
          <Input
            placeholder="请输入"
            onChange={(e) => {
              setFeeData({ ...feeData, remark: e?.target?.value });
            }}
          />
        </div>
      </Modal>
      <Modal
        title="添加成本"
        open={isModalOpen2}
        destroyOnClose
        onCancel={() => setIsModalOpen2(false)}
        width={800}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              key="back"
              onClick={() => setIsModalOpen2(false)}
              style={{ marginRight: '12px' }}
            >
              取消
            </Button>
            <Button key="submit" type="primary" onClick={getAddFee2}>
              确认
            </Button>
          </div>,
        ]}
      >
        <div className={styles.billNumber}>
          <div>
            <span>运单号</span>
            <span>{action?.waybillNo}</span>
            {/* <span>客户</span>
            <span>{`${action?.clientName}`}</span> */}
          </div>
        </div>
        <div style={{ width: '100%' }}>
          <div className={styles.clientName}>
            <ProFormSelect
              label="费用名称"
              showSearch
              colon={false}
              options={billNameList}
              // valueEnum={feeData?.billName}
              onChange={(e) => {
                setFeeData({ ...feeData, billName: e });
                // setBlno({ ...blno, clientId: e })
              }}
              // request={async ({ keyWords }) => {
              //     const { status, data } = await getClientListAPI({
              //         type: 0,
              //         len: 100,
              //         keyword: keyWords,
              //     });
              //     if (status) {
              //         return data.list.map((item: any) => {
              //             return {
              //                 label: `【${item.name}】 - ${item.code}`,
              //                 value: item.id,
              //             };
              //         });
              //     }
              //     return [];
              // }}
            />
            <div className={styles.money}>
              <span>金额</span>
              <Input
                placeholder="请输入"
                type="number"
                onChange={(e) => {
                  setFeeData({ ...feeData, amount: e?.target?.value });
                }}
              />
            </div>
          </div>

          <div className={styles.clientName}>
            {/* <ProFormSelect
              label="业务类型"
              showSearch
              colon={false}
              // mode="multiple"
              options={[
                // { label: '订舱', value: 'ShipTransport' },
                // { label: '清关', value: 'Clearance' },
                // { label: '后端', value: 'AbroadOperate' },
                { label: '货代', value: 'FreightForwarder' },
              ]}
              onChange={(e: any) => {
                setFeeData({ ...feeData, counterpartyType: e, billName: '' });
                getBillName(e);
              }}
            /> */}
            {/* <div className={styles.money}>
              <ProFormSelect
                label="方式"
                showSearch
                colon={false}
                options={[
                  { label: '追加', value: '1' },
                  { label: '覆盖', value: '2' },
                  { label: '总值', value: '3' },
                ]}
                onChange={(e) => {
                  setFeeData({ ...feeData, importType: e });
                }}
              />
            </div> */}
            <div className={styles.clientName}>
              <ProFormSelect
                name="clientName"
                label="币种"
                colon={false}
                fieldProps={{
                  defaultValue: 10,
                }}
                onChange={(e) => {
                  setFeeData({ ...feeData, currencyTypeCode: e });
                }}
                options={[
                  {
                    label: '人民币',
                    value: 10,
                  },
                  {
                    label: '美元',
                    value: 20,
                  },
                  {
                    label: '加币',
                    value: 30,
                  },
                  {
                    label: '欧元',
                    value: 40,
                  },
                  {
                    label: '日元',
                    value: 50,
                  },
                ]}
              />
            </div>
            <div className={styles.money}>
              <span>汇率</span>
              <Input
                placeholder="请输入"
                type="number"
                defaultValue={1}
                onChange={(e) => {
                  setFeeData({ ...feeData, exchangeRate: e?.target?.value });

                  // setBlno({ ...blno, clientId: e })
                }}
              />
            </div>
          </div>
        </div>
        {/* <div style={{ width: '100%' }}>
          
        </div> */}

        <div className={styles.remark}>
          <span>备注</span>
          <Input
            placeholder="请输入"
            onChange={(e) => {
              setFeeData({ ...feeData, remark: e?.target?.value });
            }}
          />
        </div>
      </Modal>
      <ProTable
        headerTitle={[tabletText()]}
        rowKey="key"
        search={false}
        columns={tableTitle === '应付费用' ? columns2 : columns}
        dataSource={dataLists || []}
        // expandable={tableTitle==='应付费用'?false:{}}
        bordered
        // options={{
        //   reload: false,
        // }}
        options={false}
        scroll={{
          y: '400px',
        }}
        expandable={{
          // columnWidth:120,
          childrenColumnName: 'bills',
          // expandedRowRender: (record: any) => (
          //   <div>
          //     <ProTable
          //       rowKey="id"
          //       search={false}
          //       columns={columns3}
          //       dataSource={record?.bills || []}
          //       pagination={false}
          //       options={false}
          //     // bordered
          //     // bordered
          //     />
          //   </div>
          // ),
          rowExpandable: () => tableTitle !== '应付费用',
        }}
      />
    </div>
  );
};

export default ReceivableTable;
