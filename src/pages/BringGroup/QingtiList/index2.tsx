import { history } from 'umi';
import { Image, message, Modal, Space, Tabs, TabsProps, Tag } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { TableDropdown } from '@ant-design/pro-components';
import MoreModal from '../MoreModal';
import Delivery from '../Delivery';
import { cancelAPI, getBlnoList } from '@/services/overview';
import { forecasterState } from '@/shared/Enumeration';
import RemarksModal from '@/components/RemarksModal';
import { formatTime, formatTimes } from '@/utils/format';
import SureArriveModal from '@/pages/BringGroup/SureArriveModal';
import ClearCustomModal from '@/pages/BringGroup/ClearCustomModal';
import RetrievalModal from '@/pages/BringGroup/RetrievalModal';
import { ProgressFilterStateType } from '@/utils/constant';
import FileList from '@/pages/BringGroup/Detail/FileList';
import smallLogoImg from '@/assets/yu.svg';
import LogDashboard from '@/components/LogDashboard';
import SuperTableMax from '@/components/SuperTableMax';

const QingtiList = () => {
  const actionRef = useRef<any>();
  // const [activeKey, setActiveKey] = useState<any>('0,1,-1');
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [State, setState] = useState('');
  const refreshTable = () => {
    actionRef?.current?.refresh();
  };

  const auditState: any = {
    '0': {
      text: '待审核',
      color: 'geekblue',
    },
    '1': {
      text: '审核通过',
      color: 'green',
    },
    '-1': {
      text: '待审核',
      color: 'magenta',
    },
  };

  const handleMenuClick = async (id: string) => {
    try {
      const { status } = await cancelAPI({
        id,
      });
      if (status) {
        message.success('操作成功');
        refreshTable();
      }
    } catch (error) {}
  };

  const columns = useMemo(() => {
    return [
      {
        title: '单号',
        dataIndex: 'no',
        width: 135,
      },
      {
        title: '客户订单号',
        dataIndex: 'outOrderId',
        width: 135,
      },
      {
        title: '提单号',
        dataIndex: 'blno',
        width: 135,
      },
      {
        title: '状态',
        dataIndex: 'state',
        width: 120,
        fieldFormat: (value: any) => {
          return forecasterState[value?.state]?.text;
        },
        render: (_: any, recode: any) => {
          return (
            <Tag color={forecasterState[recode?.state]?.color}>
              {forecasterState[recode?.state]?.text}
            </Tag>
          );
        },
      },
      {
        title: '审核状态',
        dataIndex: 'approvalState',
        width: 120,
        fieldFormat: (value: any) => {
          return auditState[value?.approvalState]?.text;
        },
        render: (_: any, recode: any) => {
          return (
            <Tag color={auditState[recode?.approvalState]?.color}>
              {auditState[recode?.approvalState]?.text}
            </Tag>
          );
        },
      },
      {
        title: '交货仓库',
        dataIndex: 'warehouse.name',
        width: 200,
        // fieldFormat: (recode: any) => {
        //   return recode?.warehouse?.name;
        // },
      },
      {
        title: '客户名称',
        dataIndex: 'clientName',
        width: 200,
      },
      {
        title: '销售产品',
        dataIndex: 'productName',
        width: 200,
        enableRowGroup: true,
      },
      {
        title: '柜型',
        dataIndex: 'containerMode',
        width: 80,
      },
      {
        title: '柜号',
        dataIndex: 'containerNo',
        width: 80,
      },
      {
        title: '总票数',
        dataIndex: 'waybillNum',
        width: 80,
      },
      {
        title: '打托/实收/预报（件数）',
        dataIndex: 'palletsPieceNum',
        width: 200,
        fieldFormat: (recode: any) => {
          return `${recode?.palletsPieceNum} / ${recode?.checkinPieceNum} / ${recode?.pieceNum}`;
        },
      },
      // {
      //   title: '总件数',
      //   dataIndex: 'pieceNum',
      //   width: 80,
      // },
      // {
      //   title: '实收件数',
      //   dataIndex: 'checkinPieceNum',
      //   width: 80,
      // },
      {
        title: '总体积',
        dataIndex: 'volume',
        width: 80,
      },
      {
        title: '总重量',
        dataIndex: 'weight',
        width: 80,
      },
      {
        title: '托数',
        dataIndex: 'palletsNum',
        width: 80,
        fieldFormat: (record: any) => {
          return `${
            (record?.palletsNum ? record?.palletsNum : 0) -
            (record?.unStoredPalletsNum ? record?.unStoredPalletsNum : 0)
          }/${record?.palletsNum}`;
        },
      },
      {
        title: '签入时间',
        dataIndex: 'checkinTime',
        width: 200,
        fieldFormat: (record: any) => {
          return formatTime(record?.checkinTime);
        },
      },
      {
        title: '到港日期',
        dataIndex: 'estimateArriveTime',
        width: 200,
        sorter: true,
        renderType: 'text',
        fieldFormat: (record: any) => {
          return `${formatTimes(record?.estimateArriveTime)}`;
        },
        render: (text: any, record: any) => {
          return (
            <>
              {record?.state < 1 ? (
                <>
                  <Image
                    width={16}
                    height={16}
                    src={smallLogoImg}
                    preview={false}
                  />
                  <span className="ml-4px">
                    {formatTimes(record?.estimateArriveTime)}
                  </span>
                </>
              ) : (
                <span>{formatTimes(record?.estimateArriveTime)}</span>
              )}
            </>
          );
        },
      },
      {
        title: '提柜日期',
        dataIndex: 'estimatePickupTime',
        width: 200,
        sorter: true,
        fieldFormat: (record: any) => {
          return `${formatTimes(record?.estimatePickupTime)}`;
        },
        render: (text: any, record: any) => {
          return (
            <>
              {record?.state < 3 ? (
                <>
                  <Image
                    width={16}
                    height={16}
                    src={smallLogoImg}
                    preview={false}
                  />
                  <span className="ml-4px">
                    {formatTimes(record?.estimatePickupTime)}
                  </span>
                </>
              ) : (
                <span>{formatTimes(record?.estimatePickupTime)}</span>
              )}
            </>
          );
        },
      },
      {
        title: '提柜代理',
        dataIndex: 'pickupProviderName',
        width: 200,
        fieldFormat: (record: any) => {
          return record?.pickupProviderName;
        },
      },
      {
        title: '清关行',
        dataIndex: 'clearanceProviderName',
        width: 200,
        fieldFormat: (record: any) => {
          return record?.clearanceProviderName;
        },
      },
      {
        title: '清关时间',
        dataIndex: 'estimatePickupTime2',
        width: 200,
        fieldFormat: (record: any) => {
          return formatTime(record?.estimatePickupTime2);
        },
      },
      {
        title: '操作',
        width: 180,
        fixed: 'right',
        render: (text: any, record: any) => {
          return (
            <Space>
              <a
                key={'examine'}
                onClick={() => {
                  history.push('/BringGroup/detail', {
                    qingtiId: record?.id,
                  });
                }}
              >
                查看
              </a>
              <LogDashboard
                extraId_1={record?.id}
                btnType="link"
                btnText="日志"
              />
              {record?.approvalState !== 1 && (
                <MoreModal
                  title="审核"
                  width={750}
                  recode={record}
                  selectedRows={selectedRows}
                  refresh={refreshTable}
                />
              )}

              {/* <a key="inventory">拆柜清单</a> */}
              <TableDropdown
                key="actionGroup"
                menus={[
                  {
                    key: 'goods',
                    name: <Delivery id={record?.id} />,
                  },
                  {
                    key: 'modify',
                    name: (
                      <MoreModal
                        title="更改时间"
                        width={850}
                        recode={record}
                        refresh={refreshTable}
                        selectedRows={selectedRows}
                      />
                    ),
                  },

                  /*    {
                    key: 'SignForModal',
                    name: (
                      <SignForModal
                        title="确认签收"
                        width={850}
                        myList={[record]}
                        refresh={refreshTable}
                      />
                    ),
                  },*/
                  {
                    key: 'note',
                    name: (
                      <RemarksModal
                        btnText="备注"
                        btnType="link"
                        refresh={refreshTable}
                        record={record}
                      />
                    ),
                  },
                  {
                    key: 'delivery',
                    name: (
                      <MoreModal
                        title="整柜直送"
                        width={500}
                        recode={record}
                        refresh={refreshTable}
                        selectedRows={selectedRows}
                      />
                    ),
                  },
                  {
                    key: 'cancel',
                    name: (
                      <a
                        key={'cancellation'}
                        style={{ color: '#FF4040' }}
                        onClick={() => {
                          Modal.confirm({
                            title: '确认提示',
                            content: '确定取消吗?',
                            okText: '确定',
                            cancelText: '取消',
                            onCancel() {
                              message.warning('取消操作');
                            },
                            onOk: () => {
                              handleMenuClick(record?.id);
                            },
                          });
                        }}
                      >
                        取消
                      </a>
                    ),
                  },
                  {
                    key: 'management',
                    name: (
                      <a
                        style={{ color: '#1677ff' }}
                        onClick={() => {
                          history.push('/BringGroup/FinanceDetail', {
                            financeId: record?.id,
                          });
                        }}
                      >
                        费用管理
                      </a>
                    ),
                  },
                ]}
              />
            </Space>
          );
        },
      },
    ];
  }, []);

  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    clientId: {
      desc: '客户',
      type: 'client',
      value: '',
    },
    estimateArriveTime: {
      desc: '到港日期',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    estimatePickupTime: {
      desc: '提柜日期',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    form: {
      desc: '交货方式',
      type: 'select',
      multi: true,
      range: [
        { label: '整柜快递', value: 0 },
        { label: '整柜卡派', value: 1 },
        { label: '拼柜散货', value: 2 },
      ],
    },
    warehouseId: {
      desc: '交货仓库',
      type: 'overseas',
      value: '',
    },
    containerNo: {
      desc: '柜号',
      type: 'text',
      value: '',
    },
    state: {
      desc: '状态',
      type: 'select',
      multi: true,
      range: ProgressFilterStateType,
      value: '',
    },
  };
  const items: TabsProps['items'] = [
    {
      key: '0,1,-1',
      label: '全部',
    },
    {
      key: '0',
      label: '待审核',
    },
    {
      key: '1',
      label: '审核通过',
    },
    {
      key: '-1',
      label: '审核拒绝',
    },
  ];
  return (
    <SuperTableMax
      columns={columns}
      request={async (params: any) => {
        const msg = await getBlnoList({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          columnsFilter: params.columnsFilter || {},
          condition: { ...params.condition, approvalState: { value: State } },
        });
        return {
          data: msg?.data?.list || [],
          success: msg.status,
          total: msg?.data?.total,
        };
      }}
      rowSelection={(selectedRows: any) => {
        setSelectedRows(selectedRows);
      }}
      filters={filters}
      ref={actionRef}
      toolBarRender={() => {
        return (
          <div style={{ flex: 1 }}>
            <Tabs
              items={items}
              type={'line'}
              onChange={(e: any) => {
                setState(e);
                refreshTable();
              }}
            ></Tabs>
          </div>
        );
      }}
      toolBarRenderRight={() => (
        <div>
          <MoreModal
            title="批量审核"
            width={750}
            recode={selectedRows}
            selectedRows={selectedRows}
            refresh={refreshTable}
          />
          {/* <Button style={{ marginRight: '10px' }}>批量导出拆柜清单</Button>*/}
          {/* <Button style={{ marginLeft: '10px' }}>批量备注</Button> */}
          <RemarksModal
            btnText="批量备注"
            // style={{ marginLeft: '10px' }}
            refresh={refreshTable}
            btnType="primary"
            style={{}}
            batch={true}
            record={{
              id: selectedRows?.map((item: any) => item?.id).join(','),
            }}
          />
          <SureArriveModal
            title="确认到港"
            width={850}
            myList={selectedRows}
            refresh={refreshTable}
          />
          <ClearCustomModal
            title="完成清关"
            width={850}
            myList={selectedRows}
            refresh={refreshTable}
          />
          <RetrievalModal
            title="完成提柜"
            width={850}
            myList={selectedRows}
            refresh={refreshTable}
          />
          <FileList
            spaceId={selectedRows[0]?.id}
            name="拆柜清单"
            record={selectedRows}
          />
        </div>
      )}
      // toolbar={{
      //   menu: {
      //     type: 'tab',
      //     activeKey: activeKey,
      //     items: [],
      //     onChange: (key: any) => {
      //       console.log('key', key);
      //       setActiveKey(key as string);
      //       refreshTable();
      //     },
      //   },
      // }}
      instanceId={'QingtiListV4'}
    />
  );
};
export default QingtiList;
