import {
  ModalForm,
  ProForm,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Col, Form, Input, message, Row, Tabs, Tag } from 'antd';
import service from '@/services/home';
import React, { useEffect, useState } from 'react';
import {
  getCountryList,
  modifyWaybillAddressAPI,
} from '@/services/productAndPrice/api';
import { getWarehouseListAPI } from '@/services/home/<USER>';
import { useRequest } from 'ahooks';
import { waybillDetail } from '@/services/preplanCabin';
const { addressFuzzySearchAPI, addressFuzzyGetAPI } = service.UserHome;
const EditAddress = (props: any) => {
  const {
    record: propsRecord,
    btnTitle,
    length,
    refresh,
    getNewest,
    forecastPrinter,
    btnType,
    // dataStorage,
  } = props;
  const [record, setRecord] = useState<any>([{}]);
  const isRevised = props?.Revised;
  const enumMa: any = {
    0: '正常',
    1: '偏远',
    2: '超偏',
  };
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [isPrivate, setIsPrivate] = useState<number>(1);
  const [defaultValue, setDefaultValue] = useState<any>('');
  const [cityData, setCityData] = useState<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const [activeKey, setActiveKey] = useState<string>('1');
  const [fbaData, setFbaData] = useState<any>([]);
  const [fbaCode, setFbaCode] = useState('');
  const { runAsync: getWaybillDetail } = useRequest(waybillDetail, {
    manual: true,
  });
  const wmsOperate: any = {
    '1': 0,
    '2': 0,
    '4': 1,
    '3': 2,
  };
  const showModal = () => {
    if (!length) {
      setModalVisit(false);
      message.error(`请选择`);
    } else if (length > 1) {
      message.error(`只能修改单个地址`);
      setModalVisit(false);
    } else {
      const newRecord = [{ ...propsRecord[0] }];
      setRecord(newRecord);
      if (newRecord[0]?.shipmentMethod) {
        if (newRecord[0]?.shipmentMethod === 'PickUp') {
          setActiveKey('4');
        }
        if (newRecord[0]?.shipmentMethod === 'Storage') {
          setActiveKey('3');
        }
        if (newRecord[0]?.shipmentMethod === 'Amazon') {
          setActiveKey('2');
        }
        if (
          !['Amazon', 'Storage', 'PickUp'].includes(
            newRecord[0]?.shipmentMethod,
          )
        ) {
          setActiveKey('1');
        }
      }
      //  else {
      //   if (newRecord[0].recipient?.isFBA === 1) {
      //     setActiveKey('2');
      //   } else {
      //     setActiveKey('1');
      //   }
      // }
      setModalVisit(true);
      // if (
      //   record[0]?.wmsOperateType === 1 ||
      //   record[0]?.wmsOperateType === 2 ||
      //   record[0]?.wmsOperateType === 3
      // ) {
      //   console.log('其他');
      //   if (record[0]?.wmsOperateType === 1) {
      //     setActiveKey('4');
      //   }
      //   if (record[0]?.wmsOperateType === 2) {
      //     setActiveKey('3');
      //   }
      //   if (record[0]?.wmsOperateType === 3) {
      //     setActiveKey('3');
      //   }
      // } else {
      //   if (record[0].recipient?.isFBA === 1) {
      //     console.log('他2');
      //     setActiveKey('2');
      //   } else {
      //     console.log('他1');
      //     setActiveKey('1');
      //   }
      // }
      // setModalVisit(true);
    }
  };
  //fba和非fba切换
  const onFbaChange = (key: string) => {
    setActiveKey(key);
    form.resetFields();
  };

  const getCity = async (id: any) => {
    const { data } = await addressFuzzyGetAPI({ id });
    console.log('res', data);
    if (data) {
      if (record[0].recipient?.isFBA !== 1) {
        form.setFieldValue('province', {
          value: data?.id,
          label: `${data?.country?.replaceAll('CN', '中国')} - ${
            data?.provinceShortName
          } / ${data?.province} - ${data?.city} ${data?.county ? '-' : ''} ${
            data?.county
          } / ${data?.zipCode}`,
        });
        //补丁
        form.setFieldValue('province', data?.province);
      }
    }
    return data;
  };
  useEffect(() => {
    // getCity(defaultValue);
  }, [defaultValue, modalVisit]);
  useEffect(() => {
    if (modalVisit) {
      // if (record[0].recipient?.isFBA !== 1) {
      if (
        !['Amazon', 'Storage', 'PickUp'].includes(record[0]?.shipmentMethod)
      ) {
        form.setFieldsValue(record[0].recipient);
      } else {
        if (activeKey === '2') {
          // form.setFieldValue('recipientAddrId2', {
          // label: record[0].recipient?.fbaCode,
          // value: record[0].recipient?.id,
          // });
          form.setFieldsValue({
            ...record[0].recipient,
            recipientAddrId2: {
              label: record[0].recipient?.fbaCode,
              value: record[0].recipient?.id,
            },
          });
          // setFbaCode(record[0].recipient?.fbaCode);
        }
      }
      if (activeKey === '3') {
        form.setFieldValue('recipientAddrId3', {
          label: record[0].recipient?.fbaCode,
          value: record[0].recipient?.id,
        });
      }
      setDefaultValue(record[0]?.recipient?.id);
    }
  }, [defaultValue, modalVisit]);
  /* 提交 */
  const onFinish = async (values: any) => {
    let param = {};
    // const result = findParentItems(dataStorage, record);
    if (activeKey === '1') {
      param = {
        waybillId: record[0]?.id,
        recipientAddr: {
          ...values.recipientAddrId?.itemVal,
          ...values,
          isFBA: 0,
        },
      };
    } else if (activeKey === '2') {
      param = {
        waybillId: record[0]?.id,

        recipientAddr: {
          ...values,
          isFBA: 1,
        },
      };
    } else if (activeKey === '3') {
      param = {
        waybillId: record[0]?.id,
        recipientAddrId:
          typeof values?.recipientAddrId3 === 'string'
            ? values?.recipientAddrId3
            : values?.recipientAddrId3?.value,
      };
    } else if (activeKey === '4') {
      param = {
        waybillId: record[0]?.id,
        wmsOperateType: 'PickUp',
        recipientAddr: {
          ...values,
          // isFBA: 1,
        },
      };
    }
    param = {
      ...param,
      wmsOperateType: wmsOperate[activeKey],
    };
    if (getNewest) {
      getNewest(param);
    }
    const { status } = await modifyWaybillAddressAPI(param);
    if (status) {
      message.success('提交成功');
      if (refresh) {
        refresh();
      }
      setModalVisit(false);
      setFbaCode('');
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };
  function findParentItems(dataSource: any, selectedItems: any) {
    const selectedIds = new Set(
      selectedItems.map((item: any) => item.waybillId),
    );
    return dataSource.filter((parent: any) =>
      parent.children.some((child: any) => selectedIds.has(child.waybillId)),
    );
  }
  /* 修改只能修改这四个
    contactName
    contactPhone
    companyName
    mnemonicCode
  */
  const items = [
    {
      key: '1',
      label: '非FBA',
      // children: (
      //   <>
      //     <ProFormSelect
      //       name="province"
      //       label="城市"
      //       fieldProps={{
      //         // labelInValue: true,
      //         style: {
      //           minWidth: 140,
      //         },
      //         filterOption: false,
      //         onChange: (value, option) => {
      //           setCityData(option?.option);
      //         },
      //         onSearch: () => {
      //           setDefaultValue('');
      //         },
      //       }}
      //       showSearch
      //       disabled={isRevised}
      //       placeholder="请选择，支持模糊搜索"
      //       rules={[{ required: activeKey === '1', message: '必填不能为空' }]}
      //       debounceTime={300}
      //       request={async ({ keyWords }) => {
      //         const { status, data } = await addressFuzzySearchAPI({
      //           token: keyWords,
      //         });
      //         if (status) {
      //           // console.log('查询结果', data);
      //           //country - short_province_name / province - city
      //           return data.list.map((item: any) => {
      //             return {
      //               label: `${item.country.replaceAll('CN', '中国')} - ${
      //                 item.provinceShortName
      //               } / ${item.province} - ${item.city} ${
      //                 item.county ? '-' : ''
      //               } ${item.county} / ${item.zipCode}`,
      //               value: item.id,
      //               option: item,
      //             };
      //           });
      //         }
      //         return [];
      //       }}
      //     />
      //     {/* <Form.Item
      //   label="国家"
      //   name="province"
      //   rules={[
      //     {
      //       required: true,
      //       message: '请选择完整地址',
      //     },
      //   ]}
      // >
      //   <SelectionOfProvincesAndCities
      //     defaultValue2={defaultValue}
      //     disabled={isRevised}
      //     addressChange={addressChange}
      //   />
      // </Form.Item> */}

      //     <ProFormText
      //       name="street"
      //       rules={[{ required: activeKey === '1', message: '必填项不能为空' }]}
      //       label="地址"
      //       disabled={isRevised}
      //     />
      //     <ProFormText
      //       style={{ width: '60%' }}
      //       name="zipCode"
      //       rules={[{ required: activeKey === '1', message: '必填项不能为空' }]}
      //       label="邮编"
      //       disabled={isRevised}
      //     />
      //     <ProForm.Group>
      //       <ProFormText
      //         name="contactName"
      //         rules={[
      //           { required: activeKey === '1', message: '必填项不能为空' },
      //         ]}
      //         label="联系人"
      //       />
      //       <ProFormText
      //         name="contactPhone"
      //         rules={[
      //           { required: activeKey === '1', message: '必填项不能为空' },
      //         ]}
      //         label="电话"
      //       />
      //     </ProForm.Group>
      //     {/* isPrivate !== 1   */}
      //     {true? (
      //       <ProFormText
      //         name="companyName"
      //         // rules={[
      //         //   { required: activeKey === '1', message: '必填项不能为空' },
      //         // ]}
      //         label="公司名字"
      //       />
      //     ) : null}

      //     <ProFormText name="email" label="电子邮箱" />
      //     <ProFormText name="fax" label="传真" />
      //   </>
      // ),
      children: (
        <div key={1}>
          <Row>
            <Col span={24}>
              <ProFormSelect
                name="recipientAddrId"
                label="查询"
                fieldProps={{
                  labelInValue: true,
                  style: {
                    minWidth: 140,
                    // width: '90%',
                  },
                  filterOption: false,
                  onChange: (e, e2: any) => {
                    console.log('e2: ', e2);
                    const { zipCode, city, country, province } = e2?.itemVal;
                    form.setFieldValue('zipCode', zipCode);
                    form.setFieldValue('city', city);
                    form.setFieldValue('country', country);
                    form.setFieldValue('province', province);
                  },
                }}
                showSearch
                placeholder="请选择，支持模糊搜索"
                //rules={[{ required: true, message: '必填不能为空' }]}
                debounceTime={300}
                request={async ({ keyWords }) => {
                  const { status, data } = await addressFuzzySearchAPI({
                    token: keyWords,
                  });
                  if (status) {
                    //country - short_province_name / province - city
                    return data.list.map((item: any) => {
                      return {
                        label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                        value: item.id,
                        itemVal: item,
                      };
                    });
                  } else {
                    return [];
                  }
                }}
              />
            </Col>
            <Col span={8}>
              {/* <Form.Item
                label="国家"
                name="country"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item> */}
              <ProFormSelect
                name="country"
                label="国家"
                fieldProps={{
                  labelInValue: true,
                  style: {
                    width: '90%',
                  },
                  onChange: (e: any) => {
                    console.log('e: ', e);
                    form.setFieldValue('country', e?.value);
                  },
                }}
                showSearch
                placeholder="请选择，支持模糊搜索"
                debounceTime={300}
                rules={[{ required: true, message: '必填项不能为空' }]}
                disabled={isRevised}
                request={async ({ keyWords }) => {
                  const { status, data } = await getCountryList({
                    start: 0,
                    len: 200,
                    keyword: keyWords,
                  });
                  if (status) {
                    return data.list.map((item: any) => {
                      return {
                        label: `${item.name}-${item.cnName}-${item.code}`,
                        value: item.code,
                      };
                    });
                  }
                  return [];
                }}
              />
            </Col>
            <Col span={8}>
              <Form.Item
                label="邮编"
                name="zipCode"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="州/省"
                name="province"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="城市"
                name="city"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="公司名"
                name="companyName"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="邮箱"
                name="email"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="收件人"
                name="contactName"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="联系方式" name="contactPhone">
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="详细地址"
                name="street"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-95%" />
              </Form.Item>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: '2',
      label: 'FBA',
      children: (
        <div key={2}>
          <Row gutter={24}>
            <Col span={24}>
              <ProFormSelect
                showSearch
                key="2"
                name="recipientAddrId2"
                label={'查询'}
                placeholder={'请输入FBA编码搜索'}
                rules={[{ required: false }]}
                fieldProps={{
                  labelInValue: true,
                  filterOption: false,
                  onChange: (e: any) => {
                    const newValue = fbaData?.find(
                      (item: any) => item?.id === e?.value,
                    );
                    form.setFieldsValue({
                      ...newValue,
                    });
                    // form.setFieldsValue({
                    //   ...e,
                    // });
                    // const fba = fbaData?.find(
                    //   (item: any) => item?.id === e?.value,
                    // )?.name;
                    // setFbaCode(fba);
                  },
                }}
                request={async ({ keyWords }) => {
                  const { status, data } = await getWarehouseListAPI({
                    keyword: keyWords,
                    type: 1,
                  });

                  // console.log(data?.list, 'data?.listdata?.list');

                  // console.log('获取选项');
                  setFbaData(data?.list);
                  if (status) {
                    return data.list.map((item: any) => {
                      return {
                        value: item.id,
                        label: (
                          <div>
                            {' '}
                            {item.name}
                            <span>
                              {item.remoteLevel ? (
                                <Tag
                                  color="#9c27b0"
                                  style={{ marginLeft: '10px' }}
                                >
                                  {enumMa[item.remoteLevel]}
                                </Tag>
                              ) : (
                                <></>
                              )}
                            </span>
                          </div>
                        ),
                      };
                    });
                  }
                  return [];
                }}
              />
            </Col>
            <Col span={8}>
              <Form.Item
                label="FBA"
                name="fbaCode"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              {/* <Form.Item
                label="国家"
                name="country"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item> */}
              <ProFormSelect
                name="country"
                label="国家"
                fieldProps={{
                  labelInValue: true,
                  style: {
                    width: '90%',
                  },
                  onChange: (e: any) => {
                    console.log('e: ', e);
                    form.setFieldValue('country', e?.value);
                  },
                }}
                showSearch
                placeholder="请选择，支持模糊搜索"
                debounceTime={300}
                rules={[{ required: true, message: '必填项不能为空' }]}
                disabled={isRevised}
                request={async ({ keyWords }) => {
                  const { status, data } = await getCountryList({
                    start: 0,
                    len: 200,
                    keyword: keyWords,
                  });
                  if (status) {
                    return data.list.map((item: any) => {
                      return {
                        label: `${item.name}-${item.cnName}-${item.code}`,
                        value: item.code,
                      };
                    });
                  }
                  return [];
                }}
              />
            </Col>
            <Col span={8}>
              <Form.Item
                label="邮编"
                name="zipCode"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="州/省"
                name="province"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="城市"
                name="city"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="公司名"
                name="companyName"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="邮箱"
                name="email"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="收件人"
                name="contactName"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="联系方式" name="contactPhone">
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="详细地址"
                name="street"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-95%" />
              </Form.Item>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: '3',
      label: '仓储',
      children: (
        <div key={3}>
          <ProFormSelect
            showSearch
            key="3"
            name="recipientAddrId3"
            label={'仓库'}
            placeholder={'请输入关键字'}
            rules={[{ required: activeKey === '3' }]}
            fieldProps={{
              labelInValue: true,
              filterOption: false,
            }}
            request={async ({ keyWords }) => {
              const { status, data } = await getWarehouseListAPI({
                keyword: keyWords,
                type: 3,
                tag: '分拨',
              });
              console.log('获取选项');
              if (status) {
                return data.list.map((item: any) => {
                  return {
                    value: item.id,
                    label: (
                      <div>
                        {' '}
                        {item.name}
                        <span>
                          {item.remoteLevel ? (
                            <Tag color="#9c27b0" style={{ marginLeft: '10px' }}>
                              {enumMa[item.remoteLevel]}
                            </Tag>
                          ) : (
                            <></>
                          )}
                        </span>
                      </div>
                    ),
                  };
                });
              }
              return [];
            }}
          />
        </div>
      ),
    },
    {
      key: '4',
      label: '自提',
      // children: (
      //   <>
      //     <ProFormSelect
      //       name="province"
      //       label="城市"
      //       fieldProps={{
      //         // labelInValue: true,
      //         style: {
      //           minWidth: 140,
      //         },
      //         filterOption: false,
      //         onChange: (value, option) => {
      //           setCityData(option?.option);
      //         },
      //         onSearch: () => {
      //           setDefaultValue('');
      //         },
      //       }}
      //       showSearch
      //       disabled={isRevised}
      //       placeholder="请选择，支持模糊搜索"
      //       rules={[{ required: activeKey === '1', message: '必填不能为空' }]}
      //       debounceTime={300}
      //       request={async ({ keyWords }) => {
      //         const { status, data } = await addressFuzzySearchAPI({
      //           token: keyWords,
      //         });
      //         if (status) {
      //           // console.log('查询结果', data);
      //           //country - short_province_name / province - city
      //           return data.list.map((item: any) => {
      //             return {
      //               label: `${item.country.replaceAll('CN', '中国')} - ${
      //                 item.provinceShortName
      //               } / ${item.province} - ${item.city} ${
      //                 item.county ? '-' : ''
      //               } ${item.county} / ${item.zipCode}`,
      //               value: item.id,
      //               option: item,
      //             };
      //           });
      //         }
      //         return [];
      //       }}
      //     />
      //     {/* <Form.Item
      //   label="国家"
      //   name="province"
      //   rules={[
      //     {
      //       required: true,
      //       message: '请选择完整地址',
      //     },
      //   ]}
      // >
      //   <SelectionOfProvincesAndCities
      //     defaultValue2={defaultValue}
      //     disabled={isRevised}
      //     addressChange={addressChange}
      //   />
      // </Form.Item> */}

      //     <ProFormText
      //       name="street"
      //       rules={[{ required: activeKey === '1', message: '必填项不能为空' }]}
      //       label="地址"
      //       disabled={isRevised}
      //     />
      //     <ProFormText
      //       style={{ width: '60%' }}
      //       name="zipCode"
      //       rules={[{ required: activeKey === '1', message: '必填项不能为空' }]}
      //       label="邮编"
      //       disabled={isRevised}
      //     />
      //     <ProForm.Group>
      //       <ProFormText
      //         name="contactName"
      //         rules={[
      //           { required: activeKey === '1', message: '必填项不能为空' },
      //         ]}
      //         label="联系人"
      //       />
      //       <ProFormText
      //         name="contactPhone"
      //         rules={[
      //           { required: activeKey === '1', message: '必填项不能为空' },
      //         ]}
      //         label="电话"
      //       />
      //     </ProForm.Group>
      //     {/* isPrivate !== 1   */}
      //     {true? (
      //       <ProFormText
      //         name="companyName"
      //         // rules={[
      //         //   { required: activeKey === '1', message: '必填项不能为空' },
      //         // ]}
      //         label="公司名字"
      //       />
      //     ) : null}

      //     <ProFormText name="email" label="电子邮箱" />
      //     <ProFormText name="fax" label="传真" />
      //   </>
      // ),
      children: (
        <div key={1}>
          <Row>
            <Col span={24}>
              <ProFormSelect
                name="recipientAddrId"
                label="查询"
                fieldProps={{
                  labelInValue: true,
                  style: {
                    minWidth: 140,
                    // width: '90%',
                  },
                  filterOption: false,
                  onChange: (e, e2: any) => {
                    console.log('e2: ', e2);
                    const { zipCode, city, country, province } = e2?.itemVal;
                    form.setFieldValue('zipCode', zipCode);
                    form.setFieldValue('city', city);
                    form.setFieldValue('country', country);
                    form.setFieldValue('province', province);
                  },
                }}
                showSearch
                placeholder="请选择，支持模糊搜索"
                //rules={[{ required: true, message: '必填不能为空' }]}
                debounceTime={300}
                request={async ({ keyWords }) => {
                  const { status, data } = await addressFuzzySearchAPI({
                    token: keyWords,
                  });
                  if (status) {
                    //country - short_province_name / province - city
                    return data.list.map((item: any) => {
                      return {
                        label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                        value: item.id,
                        itemVal: item,
                      };
                    });
                  } else {
                    return [];
                  }
                }}
              />
            </Col>
            <Col span={8}>
              {/* <Form.Item
                label="国家"
                name="country"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item> */}
              <ProFormSelect
                name="country"
                label="国家"
                fieldProps={{
                  labelInValue: true,
                  style: {
                    width: '90%',
                  },
                  onChange: (e: any) => {
                    console.log('e: ', e);
                    form.setFieldValue('country', e?.value);
                  },
                }}
                showSearch
                placeholder="请选择，支持模糊搜索"
                debounceTime={300}
                rules={[{ required: true, message: '必填项不能为空' }]}
                disabled={isRevised}
                request={async ({ keyWords }) => {
                  const { status, data } = await getCountryList({
                    start: 0,
                    len: 200,
                    keyword: keyWords,
                  });
                  if (status) {
                    return data.list.map((item: any) => {
                      return {
                        label: `${item.name}-${item.cnName}-${item.code}`,
                        value: item.code,
                      };
                    });
                  }
                  return [];
                }}
              />
            </Col>
            <Col span={8}>
              <Form.Item
                label="邮编"
                name="zipCode"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="州/省"
                name="province"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="城市"
                name="city"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="公司名"
                name="companyName"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="邮箱"
                name="email"
                // rules={[
                //   {
                //     required: true,
                //     message: '必填项不能为空',
                //   },
                // ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="收件人"
                name="contactName"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="联系方式" name="contactPhone">
                <Input className="w-90%" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="详细地址"
                name="street"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <Input className="w-95%" />
              </Form.Item>
            </Col>
          </Row>
        </div>
      ),
    },
  ];
  return (
    <>
      {forecastPrinter ? (
        <a
          style={{ color: '#7E859E', textDecorationLine: 'underline' }}
          onClick={showModal}
        >
          {btnTitle}
        </a>
      ) : (
        <Button type={btnType} onClick={showModal}>
          {btnTitle}
        </Button>
      )}

      <ModalForm
        title={btnTitle}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        open={modalVisit}
        layout="horizontal"
        submitTimeout={2000}
        onFinish={onFinish}
        /*  initialValues={{
          ...record[0]?.recipient,
        }}*/
      >
        <Tabs
          // defaultActiveKey="1"
          items={items}
          onChange={onFbaChange}
          activeKey={activeKey}
          destroyInactiveTabPane
          tabBarExtraContent={
            <Button
              onClick={async () => {
                const item = form.getFieldsValue();
                console.log('item', item);
                navigator.clipboard.writeText(
                  `${item?.companyName}\n${item?.contactName}\n${item?.street}\n${item?.city}\n${item?.province}\n${item?.country}\n${item?.zipCode}\n${item?.contactPhone}\n`,
                ); // 将文本写入剪切板
                message.success('地址已复制到剪切板');
              }}
            >
              复制地址
            </Button>
          }
        />

        {/*     <ProFormRadio.Group
          name="residential"
          label="性质"
          disabled={isRevised}
          fieldProps={{
            onChange: (e: any) => {
              setIsPrivate(e.target.value);
            },
          }}
          options={[
            {
              label: '民宅',
              value: 1,
            },
            {
              label: '商业',
              value: 0,
            },
          ]}
        />
        <ProFormText name="mnemonicCode" label="助记码" />*/}
      </ModalForm>
    </>
  );
};
export default EditAddress;
