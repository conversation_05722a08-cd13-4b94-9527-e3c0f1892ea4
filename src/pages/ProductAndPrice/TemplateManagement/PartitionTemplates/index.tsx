import SuperTableMax from '@/components/SuperTableMax';
import {
  copyPartitionTemplateAPI,
  deletePartitionTemplateAPI,
  getPartitionTemplateListAPI,
} from '@/services/home/<USER>';
import React, { useRef, useContext, useEffect } from 'react';
import dayjs from 'dayjs';
import { Button, message, Popconfirm } from 'antd';
import { history } from '@umijs/max';
// import AccessCard from '@/AccessCard';
import LogDashboard from '@/components/LogDashboard';
import { TableDropdown } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { KeepAliveTabContext } from '@/layouts/context';

const PartitionTemplates = () => {
  const actionRef = useRef<any>(null);
  const { onShow } = useContext(KeepAliveTabContext);

  // 监听页面重新显示，自动刷新数据
  useEffect(() => {
    const handlePageShow = () => {
      if (actionRef.current) {
        actionRef.current.reload();
      }
    };
    onShow(handlePageShow);
  }, [onShow]);

  /* 分区模版删除 */
  const deletePartitionTemplate = async (record: any) => {
    try {
      const { status } = await deletePartitionTemplateAPI({
        zoneId: record.id,
      });
      if (status) {
        actionRef.current.reload();
        message.success('删除成功');
      }
    } catch (err) {
      console.error('分区模版删除接口出错: ', err);
    }
  };

  const { run } = useRequest(copyPartitionTemplateAPI, {
    manual: true,
    onSuccess: (res) => {
      if (res.status) {
        actionRef.current.reload();
      }
    },
  });

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      // flex: 1,
    },
    {
      title: '分区',
      dataIndex: 'code',
      // flex: 1,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value?.createTime
          ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
      // flex: 1,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 250,
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              key={1}
              onClick={() => {
                history.push(
                  `/productAndPrice/templateManagement/PartitionTemplateDetails?id=${record.id}`,
                  { ...record },
                );
              }}
            >
              详情
            </Button>
            {/* <AccessCard
              key={2}
              accessible={[':Product:RuleFullAccess']}
              dataValidator={(path: any, data: any) => {
                return /[01]/.test(data.type.value);
              }}
            > */}
            <Popconfirm
              title="确定要删除吗？"
              description="确定要删除吗?"
              onConfirm={() => deletePartitionTemplate(record)}
              okText="删除"
              cancelText="取消"
              key={2}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
            <LogDashboard
              extraId_1={record?.id}
              btnType="link"
              btnText="日志"
              key="journal"
            />
            <TableDropdown
              key="actionGroup"
              menus={[
                {
                  key: 'copy',
                  name: '复制',
                },
              ]}
              onSelect={(key: any) => {
                if (key === 'copy') {
                  // console.log('key: ', key);
                  run({ zoneId: record?.id });
                }
              }}
            />
            {/* </AccessCard> */}
          </>
        );
      },
    },
  ];
  return (
    <SuperTableMax
      columns={columns}
      ref={actionRef}
      instanceId={'productAndPrice/partitionTemplates'}
      request={async (params: any) => {
        const res = await getPartitionTemplateListAPI({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          columnsFilter: params.columnsFilter || {},
          condition: params.condition,
          zoneType: 0,
        });
        return {
          data: res?.data?.list || [],
          success: res.status,
          total: res?.data?.total,
        };
      }}
      getRowIdFn={(record: any) => record?.data?.id}
      isWarpTabs={true}
      filters={{
        keyword: {
          type: 'keyword',
          value: '',
          tabs: true,
        },
      }}
      toolBarRender={() => {
        return (
          <Button
            type="primary"
            onClick={() => {
              history.push(
                '/productAndPrice/templateManagement/PartitionTemplateDetails',
              );
            }}
          >
            新建
          </Button>
        );
      }}
    />
  );
};

export default React.memo(PartitionTemplates);
