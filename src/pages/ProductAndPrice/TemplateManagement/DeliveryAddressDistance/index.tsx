import SuperTableMax from '@/components/SuperTableMax';
import { useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { Radio, Space, Tag } from 'antd';
import LogDashboard from '@/components/LogDashboard';
import DeliveryAddressDistanceAddModal from './AddDeliveryModal';
import { getDistanceList } from '@/services/overview';
import { formatSeconds } from '@/utils/public';

const DeliveryAddressDistance = () => {
  const actionRef = useRef<any>(null);
  const [distanceUnit, setDistanceUnit] = useState('km');
  const refresh = () => {
    actionRef.current?.reload();
  };
  const columns = useMemo(
    () => [
      {
        title: '海外仓',
        dataIndex: 'src',
      },
      {
        title: '目的地',
        dataIndex: 'dest',
      },
      {
        title: `${distanceUnit === 'km' ? '公里（KM）' : '英里（MI）'}`,
        dataIndex: 'value',
        fieldFormat: (value: any) => {
          return distanceUnit === 'km'
            ? value?.value
            : (value.value * 0.621371).toFixed(2);
        },
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        fieldFormat: (value: any) => {
          return value?.enabled === 1 ? '生效' : '不生效';
        },
        render: (value: any, record: any) => {
          return (
            <Tag color={record?.enabled === 1 ? 'green' : ''}>
              {record?.enabled === 1 ? '生效' : '不生效'}
            </Tag>
          );
        },
      },
      {
        title: '预计耗时',
        dataIndex: 'duration',
        fieldFormat: (value: any) => {
          return formatSeconds(value?.duration);
        },
      },
      {
        title: '创建人',
        dataIndex: 'user',
        fieldFormat: (value: any) => {
          return value?.user?.name;
        },
      },
      {
        title: '生效时间',
        dataIndex: 'enabledTime',
        fieldFormat: (value: any) => {
          return value?.enabledTime
            ? dayjs(value?.enabledTime).format('YYYY-MM-DD HH:mm:ss')
            : '';
        },
      },
      {
        title: '失效时间',
        dataIndex: 'disabledTime',
        fieldFormat: (value: any) => {
          return value?.disabledTime
            ? dayjs(value?.disabledTime).format('YYYY-MM-DD HH:mm:ss')
            : '';
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 230,
        render: (text: any, record: any) => {
          return (
            <>
              <DeliveryAddressDistanceAddModal
                btnText="修改"
                btnType="link"
                record={record}
              />
              {/*      <Popconfirm
                title="确定要作废吗？"
                description="确定要作废吗?"
                onConfirm={() => {}}
                okText="作废"
                cancelText="取消"
                key={2}
              >
                <Button type="link" danger>
                  作废
                </Button>
              </Popconfirm>*/}
              <LogDashboard
                extraId_1={record?.id}
                btnType="link"
                btnText="日志"
                key="journal"
              />
            </>
          );
        },
      },
    ],
    [distanceUnit],
  );
  return (
    <SuperTableMax
      columns={columns}
      ref={actionRef}
      key={distanceUnit}
      instanceId={'productAndPrice/deliveryAddressDistance'}
      request={async (params: any) => {
        const res = await getDistanceList({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          columnsFilter: params.columnsFilter || {},
          condition: params.condition,
          zoneType: 0,
        });
        return {
          data: res?.data?.list || [],
          success: res.status,
          total: res?.data?.total,
        };
      }}
      filters={{
        keyword: {
          type: 'keyword',
          value: '',
          tabs: true,
        },
        state: {
          desc: '状态',
          type: 'select',
          range: [
            { label: '生效', value: 1 },
            { label: '不生效', value: 0 },
          ],
        },
        time: {
          desc: '创建时间',
          type: 'dateTimeRange',
          startTime: '',
          endTime: '',
        },
      }}
      toolBarRender={() => {
        return (
          <DeliveryAddressDistanceAddModal
            btnText="新建"
            btnType="primary"
            refresh={refresh}
          />
        );
      }}
      getRowIdFn={(record: any) => record?.data?.id}
      toolBarRenderRight={() => {
        return (
          <Space className="mr-6px" size={10}>
            <div className="text-#666 font-bold">距离单位：</div>
            <Radio.Group
              value={distanceUnit}
              buttonStyle="solid"
              size="small"
              onChange={(e) => setDistanceUnit(e.target.value)}
            >
              <Radio.Button value="km">公里 KM</Radio.Button>
              <Radio.Button value="mi">英里 MI</Radio.Button>
            </Radio.Group>
          </Space>
        );
      }}
    />
  );
};

export default DeliveryAddressDistance;
