import React, { useState } from 'react';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Space,
} from 'antd';
import { addDistance, editDistance } from '@/services/overview';
import dayjs from 'dayjs';
import { ProFormSelect } from '@ant-design/pro-components';
import { getCountryList } from '@/services/productAndPrice/api';
import { getWarehouseListAPI } from '@/services/booking';

interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
}

const DeliveryAddressDistanceAddModal = ({
  btnText,
  btnType,
  refresh,
  record,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  console.log('record', record);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const formData = {
        ...values,
        enabledTime: dayjs(values?.enabledTime).format('YYYY-MM-DD HH:mm:ss'),
      };

      console.log('Form values:', formData);
      if (record) {
        //修改
        formData.id = record.id;
        const res = await editDistance(formData);
        setIsModalOpen(false);
        form.resetFields();
        refresh();
      } else {
        const res = await addDistance(formData);
        setIsModalOpen(false);
        form.resetFields();
        refresh();
      }
    } catch (error) {
      console.log('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={btnText}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={700}
        okText="确认"
        cancelText="取消"
      >
        <style>{`
          .distance-unit-select .ant-select-selector {
            background-color: #4071ff !important;
            color: white !important;
            border-color: #4071ff !important;
          }
          .distance-unit-select .ant-select-selection-item {
            color: white !important;
          }
          .distance-unit-select .ant-select-arrow {
            color: white !important;
          }
          .distance-unit-select:hover .ant-select-selector {
            background-color: #4071ff !important;
            border-color: #4071ff !important;
          }
          .distance-unit-select.ant-select-focused .ant-select-selector {
            background-color: #4071ff !important;
            border-color: #4071ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
          }
        `}</style>
        <Form
          form={form}
          layout="vertical"
          style={{ padding: '20px 0' }}
          size="middle"
          key={record?.id}
          initialValues={
            record
              ? {
                  destAddress: {
                    country: record?.destStreet?.country,
                    city: record?.destStreet?.city,
                    province: record?.destStreet?.province,
                    street: record?.destStreet?.street,
                    zipCode: record?.destStreet?.zipCode,
                  },
                  //warehouseId: record?.from,
                  enabledTime: dayjs(record?.enabledTime),
                  value: record?.value,
                }
              : {}
          }
        >
          <Row gutter={16}>
            <Col span={12}>
              <ProFormSelect
                name={['destAddress', 'country']}
                style={{ minWidth: '196px' }}
                label="国家"
                showSearch
                rules={[{ required: true, message: '必填项不能为空' }]}
                request={async ({ keyWords }) => {
                  const { status, data } = await getCountryList({
                    start: 0,
                    len: 200,
                    keyword: keyWords,
                  });
                  if (status) {
                    return data.list.map((item: any) => {
                      return {
                        label: `${item.name}-${item.cnName}-${item.code}`,
                        value: item.code,
                      };
                    });
                  }
                  return [];
                }}
              />
              {/*      <Form.Item
                label="国家"
                name={['destAddress', 'country']}
                rules={[{ required: true, message: '请选择国家' }]}
              >
                <Select placeholder="请选择" options={countryOptions} />
              </Form.Item>*/}
            </Col>
            <Col span={12}>
              <Form.Item
                label="州/省"
                name={['destAddress', 'province']}
                rules={[{ required: true, message: '请输入州/省' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="邮编"
                name={['destAddress', 'zipCode']}
                rules={[{ required: true, message: '请输入邮编' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="城市"
                name={['destAddress', 'city']}
                rules={[{ required: true, message: '请输入城市' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={24}>
              <Form.Item
                label="具体地址"
                name={['destAddress', 'street']}
                rules={[{ required: true, message: '请输入具体地址' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <ProFormSelect
                name={'warehouseId'}
                label="海外仓"
                key={record?.id}
                //showSearch
                rules={[{ required: true, message: '必填项不能为空' }]}
                request={async ({ keyWords }) => {
                  const { status, data } = await getWarehouseListAPI({
                    type: 3,
                    keyword: keyWords,
                  });
                  if (status) {
                    return data.list.map((item: any) => {
                      return {
                        label: item.name,
                        value: item.id,
                      };
                    });
                  }
                  return [];
                }}
              />
            </Col>
            <Col span={12}>
              <Form.Item
                label="计费距离"
                //name="distance"
                rules={[{ required: true, message: '请输入计费距离' }]}
              >
                <Space.Compact style={{ display: 'flex' }}>
                  <Form.Item
                    name="value"
                    noStyle
                    rules={[{ required: true, message: '请输入距离' }]}
                  >
                    <InputNumber
                      placeholder="请输入"
                      style={{
                        width: '70%',
                        borderRight: 'none',
                      }}
                      min={0}
                    />
                  </Form.Item>
                  <Form.Item
                    name="measure"
                    noStyle
                    rules={[{ required: true, message: '请选择单位' }]}
                    initialValue={3}
                  >
                    <Select
                      placeholder="单位"
                      style={{
                        width: '30%',
                      }}
                      dropdownStyle={{
                        minWidth: '80px',
                      }}
                      className="distance-unit-select"
                      options={[
                        { label: 'MI', value: 4 },
                        { label: 'KM', value: 3 },
                      ]}
                    />
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                label="生效时间"
                name="enabledTime"
                rules={[{ required: true, message: '请选择生效时间' }]}
              >
                <DatePicker
                  showTime
                  placeholder="年/月/日 --:--"
                  style={{ width: '100%' }}
                  format="YYYY/MM/DD HH:mm"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default DeliveryAddressDistanceAddModal;
