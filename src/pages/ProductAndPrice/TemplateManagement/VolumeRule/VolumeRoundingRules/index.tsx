import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Table, Button, Input, Space, message } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import EditPopUpBox from './EditPopUpBox';
import { history, useLocation } from '@umijs/max';
import { KeepAliveTabContext } from '@/layouts/context';
import {
  getCustomRuleDetail,
  saveCustomRule,
} from '@/services/productAndPrice/api';
import { useRequest } from 'ahooks';

interface RuleData {
  key?: string;
  ruleDetail: string;
  ruleResult: string;
  id?: string;
}

const pattern = /\[.+?\]/g;

const VolumeRoundingRules: React.FC = () => {
  const { closeTab } = useContext(KeepAliveTabContext);
  const location = useLocation();
  const id = useMemo(() => {
    return location?.search?.split('?')[1]?.split('=')[1];
  }, [location?.search]);

  const [ruleName, setRuleName] = useState('');
  const [dataSource, setDataSource] = useState<RuleData[]>([
    {
      key: '1',
      ruleDetail: '',
      ruleResult: '',
    },
  ]);

  const { runAsync: saveCustomRuleAsync } = useRequest(saveCustomRule, {
    manual: true,
  });

  const { runAsync: getCustomRuleDetailAsync } = useRequest(
    getCustomRuleDetail,
    {
      manual: true,
    },
  );

  // 添加规则
  const handleAddRule = () => {
    const newRule: RuleData = {
      key: Date.now().toString(),
      ruleDetail: '',
      ruleResult: '',
    };
    setDataSource([...dataSource, newRule]);
  };

  // 删除规则
  const handleDeleteRule = (record: any) => {
    const newDataSource = dataSource.filter(
      (item) => (item.id || item.key) !== (record.id || record.key),
    );
    setDataSource(newDataSource);
    message.success('删除成功');
  };

  // 保存
  const handleSave = () => {
    saveCustomRuleAsync({
      ruleName: ruleName,
      type: 3,
      chargeWeightRuleDetailList: dataSource,
      id: id,
    }).then((res) => {
      if (res.status) {
        message.success('保存成功');
        history.push('/productAndPrice/templateManagement');
        closeTab();
      }
    });
  };

  // 取消
  const handleCancel = () => {
    history.push('/productAndPrice/templateManagement');
    closeTab();
    message.info('已取消');
  };

  useEffect(() => {
    if (id) {
      getCustomRuleDetailAsync({ id: id as string }).then((res) => {
        if (res.status) {
          setRuleName(res.data.ruleName);
          setDataSource(res.data.chargeWeightRuleDetailList);
        }
      });
    }
  }, [id]);

  const columns: TableColumnsType<RuleData> = [
    {
      title: '条件表达式',
      dataIndex: 'ruleDetail',
      key: 'ruleDetail',
      width: '40%',
      render: (text: string, record: RuleData) => {
        const str = text.replace(pattern, (match: any) => {
          return ` <span style="background:#FFE58F">${match}</span> `;
        });
        return (
          <div>
            <EditPopUpBox
              onOk={(e: any) => {
                record.ruleDetail = e;
                setDataSource([...dataSource]);
              }}
            />
            <span dangerouslySetInnerHTML={{ __html: str }} />
          </div>
        );
      },
    },
    {
      title: '体积进位结果',
      dataIndex: 'ruleResult',
      key: 'ruleResult',
      width: '45%',
      render: (text: string, record: RuleData) => {
        const str = text.replace(pattern, (match: any) => {
          return ` <span style="background:#FFE58F">${match}</span> `;
        });
        return (
          <div>
            <EditPopUpBox
              onOk={(e: any) => {
                record.ruleResult = e;
                setDataSource([...dataSource]);
              }}
            />
            <span dangerouslySetInnerHTML={{ __html: str }} />
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: '15%',
      render: (_, record: RuleData) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteRule(record)}
        >
          删除
        </Button>
      ),
    },
  ];

  return (
    <div style={{ backgroundColor: '#f5f5f5' }}>
      {/* 固定的标题栏 */}
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          backgroundColor: '#fff',
          borderBottom: '1px solid #e8e8e8',
          padding: '16px 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 500 }}>
          {id ? '编辑体积进位规则' : '新建体积进位规则'}
        </h3>
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleSave}>
            保存
          </Button>
        </Space>
      </div>

      <div style={{ padding: '24px', backgroundColor: '#fff' }}>
        <div style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
            <span style={{ fontWeight: 500 }}>名称：</span>
          </div>
          <Input
            value={ruleName}
            onChange={(e) => setRuleName(e.target.value)}
            placeholder="请输入规则名称"
            style={{ maxWidth: 500 }}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: '#ff4d4f', marginRight: 4 }}>*</span>
            <span style={{ fontWeight: 500 }}>规则</span>
          </div>
        </div>

        <Table
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          bordered
          size="middle"
          style={{ marginBottom: 16 }}
          rowClassName={() => 'table-row-light'}
          rowKey={(record) => record.id || record.key || ''}
        />

        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={handleAddRule}
          style={{
            width: '200px',
            height: '40px',
            borderStyle: 'dashed',
            borderColor: '#1890ff',
            color: '#1890ff',
          }}
        >
          添加规则
        </Button>
      </div>
    </div>
  );
};

export default VolumeRoundingRules;
