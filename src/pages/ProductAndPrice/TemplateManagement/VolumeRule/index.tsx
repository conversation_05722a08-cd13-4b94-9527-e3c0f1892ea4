import SuperTableMax from '@/components/SuperTableMax';
import { useContext, useEffect, useRef } from 'react';
import { Button, message, Popconfirm } from 'antd';
import dayjs from 'dayjs';
import LogDashboard from '@/components/LogDashboard';
import { history } from '@umijs/max';
import { KeepAliveTabContext } from '@/layouts/context';
import {
  deleteCustomRule,
  getCustomRuleList,
} from '@/services/productAndPrice/api';
import { EnterOutlined } from '@ant-design/icons';
const pattern = /\[([^\]]+)\]|(或者|并且)/g;

const VolumeRule = () => {
  const actionRef = useRef<any>(null);

  const { onShow } = useContext(KeepAliveTabContext);

  // 监听页面重新显示，自动刷新数据
  useEffect(() => {
    const handlePageShow = () => {
      if (actionRef.current) {
        actionRef.current.reload();
      }
    };
    onShow(handlePageShow);
  }, [onShow]);

  const deleteCustomRuleAPI = async (record: any) => {
    try {
      const { status } = await deleteCustomRule({
        id: record.id,
      });
      if (status) {
        actionRef.current.reload();
        message.success('删除成功');
      }
    } catch (err) {
      console.error('计重规则删除接口出错: ', err);
    }
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'ruleName',
    },
    {
      wrapText: true,
      autoHeight: true,
      title: '规则',
      dataIndex: 'chargeWeightRuleDetailList',
      fieldFormat: (value: any) => {
        return value?.chargeWeightRuleDetailList
          ?.map((item: any, index: any) => {
            return `【${index + 1}】${item?.ruleDetail} ${item?.ruleResult};`;
          })
          .join('\n');
      },
      render: (text: any, record: any) => {
        return (
          <>
            {record?.chargeWeightRuleDetailList?.map((item: any) => {
              const strText = `${item?.ruleDetail} ${item?.ruleResult}`;
              // const str = strText.replace(
              //   pattern,
              //   (match: any) =>
              //     ` <span style="background:#FFE58F">${match}</span> `,
              // );
              const highlightedText = strText.replace(
                pattern,
                (match, p1, p2) => {
                  if (p1) {
                    return `<span style="background:#FFE58F">${match}</span>`;
                  } else if (p2) {
                    return `<span style="color:#bdc3c7">${match}</span>`;
                  }
                  return match;
                },
              );
              return (
                <div key={item.id}>
                  <span dangerouslySetInnerHTML={{ __html: highlightedText }} />
                  <EnterOutlined style={{ color: 'blue' }} />
                </div>
              );
            })}
          </>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value?.createTime
          ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 230,
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                history.push(
                  `/productAndPrice/templateManagement/volumeRoundingRules?id=${record?.id}`,
                  record,
                );
              }}
            >
              详情
            </Button>
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                deleteCustomRuleAPI(record);
              }}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
            <LogDashboard
              extraId_1={record?.id}
              btnType="link"
              btnText="日志"
              key="journal"
            />
          </>
        );
      },
    },
  ];

  return (
    <SuperTableMax
      columns={columns}
      ref={actionRef}
      instanceId={'productAndPrice/volumeRule'}
      request={async (params: any) => {
        const res = await getCustomRuleList({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          columnsFilter: params.columnsFilter || {},
          condition: params.condition,
          type: 3,
        });
        return {
          data: res?.data?.list || [],
          success: res.status,
          total: res?.data?.total,
        };
      }}
      getRowIdFn={(record: any) => record?.data?.id}
      isWarpTabs={true}
      filters={{
        keyword: {
          type: 'keyword',
          value: '',
          tabs: true,
        },
      }}
      toolBarRender={() => {
        return (
          <Button
            type="primary"
            onClick={() => {
              history.push(
                '/productAndPrice/templateManagement/volumeRoundingRules',
              );
            }}
          >
            新建
          </Button>
        );
      }}
    />
  );
};

export default VolumeRule;
