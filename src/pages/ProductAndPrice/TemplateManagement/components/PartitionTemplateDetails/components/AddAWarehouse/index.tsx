import React, { useState, useMemo, useCallback } from 'react';
import {
  Button,
  Modal,
  Input,
  Checkbox,
  Typography,
  Empty,
  Spin,
  Pagination,
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import './index.less';
import { useRequest } from 'ahooks';
import { getWarehouseListAPI } from '@/services/booking';

const { Search } = Input;
const { Text, Link } = Typography;

interface WarehouseItem {
  id: string;
  name: string;
}

interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: () => void;
  record?: any;
  onConfirm?: (selectedWarehouses: WarehouseItem[]) => void;
  loading?: boolean;
  warehouseData?: WarehouseItem[];
  disabled?: boolean;
}

const AddAWarehouse: React.FC<Props> = ({
  btnText,
  btnType,
  onConfirm,
  loading = false,
  disabled = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [selectedWarehouses, setSelectedWarehouses] = useState<
    Map<string, WarehouseItem>
  >(new Map());

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const [warehouseList, setWarehouseList] = useState<WarehouseItem[]>([]);

  const { runAsync: getWarehouseList, loading: apiLoading } = useRequest(
    getWarehouseListAPI,
    {
      manual: true,
    },
  );

  const fetchWarehouseData = useCallback(
    async (
      params: {
        current?: number;
        pageSize?: number;
        keyword?: string;
      } = {},
    ) => {
      try {
        const { current = 1, pageSize = 20, keyword = '' } = params;
        const res: any = await getWarehouseList({
          start: (current - 1) * pageSize,
          len: pageSize,
          type: 1,
          keyword,
        });

        if (res.status) {
          const { list = [], total = 0 } = res.data || {};
          setWarehouseList(list);
          setPagination((prev) => ({
            ...prev,
            current,
            total,
          }));
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
        setWarehouseList([]);
      }
    },
    [getWarehouseList],
  );

  const currentPageWarehouseIds = useMemo(
    () => warehouseList.map((w) => w.id),
    [warehouseList],
  );

  const isAllSelected = useMemo(
    () =>
      currentPageWarehouseIds.length > 0 &&
      currentPageWarehouseIds.every((id) => selectedWarehouses.has(id)),
    [currentPageWarehouseIds, selectedWarehouses],
  );

  const isPartialSelected = useMemo(
    () =>
      selectedWarehouses.size > 0 &&
      currentPageWarehouseIds.some((id) => selectedWarehouses.has(id)) &&
      !isAllSelected,
    [selectedWarehouses, currentPageWarehouseIds, isAllSelected],
  );

  const currentPageSelectedIds = useMemo(
    () => currentPageWarehouseIds.filter((id) => selectedWarehouses.has(id)),
    [selectedWarehouses, currentPageWarehouseIds],
  );

  const resetState = useCallback(() => {
    setSearchValue('');
    setSelectedWarehouses(new Map());
    setWarehouseList([]);
    setPagination({ current: 1, pageSize: 20, total: 0 });
  }, []);

  const showModal = useCallback(async () => {
    setIsModalOpen(true);
    resetState();
    await fetchWarehouseData({ current: 1, pageSize: 20, keyword: '' });
  }, [fetchWarehouseData, resetState]);

  const handleOk = useCallback(() => {
    onConfirm?.(Array.from(selectedWarehouses.values()));
    setIsModalOpen(false);
  }, [selectedWarehouses, onConfirm]);

  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  const handleSearch = useCallback(
    async (value: string) => {
      setSearchValue(value);
      setPagination((prev) => ({ ...prev, current: 1 }));
      await fetchWarehouseData({
        current: 1,
        pageSize: pagination.pageSize,
        keyword: value,
      });
    },
    [fetchWarehouseData, pagination.pageSize],
  );

  // 单个仓库选择变更
  const handleWarehouseChange = useCallback(
    (checkedValues: string[]) => {
      const currentPageWarehousesMap = new Map(
        warehouseList.map((w) => [w.id, w]),
      );

      setSelectedWarehouses((prev) => {
        const newSelected = new Map(prev);
        currentPageWarehousesMap.forEach((_, id) => {
          newSelected.delete(id);
        });
        checkedValues.forEach((id) => {
          if (currentPageWarehousesMap.has(id)) {
            newSelected.set(id, currentPageWarehousesMap.get(id)!);
          }
        });
        return newSelected;
      });
    },
    [warehouseList],
  );

  const handleSelectAll = useCallback(() => {
    const currentPageWarehousesMap = new Map(
      warehouseList.map((w) => [w.id, w]),
    );

    setSelectedWarehouses((prev) => {
      const newSelected = new Map(prev);
      if (isAllSelected) {
        currentPageWarehousesMap.forEach((_, id) => {
          newSelected.delete(id);
        });
      } else {
        currentPageWarehousesMap.forEach((warehouse, id) => {
          newSelected.set(id, warehouse);
        });
      }
      return newSelected;
    });
  }, [isAllSelected, warehouseList]);

  const handlePageChange = useCallback(
    async (page: number, pageSize?: number) => {
      const newPageSize = pageSize || pagination.pageSize;
      setPagination((prev) => ({
        ...prev,
        current: page,
        pageSize: newPageSize,
      }));
      await fetchWarehouseData({
        current: page,
        pageSize: newPageSize,
        keyword: searchValue,
      });
    },
    [fetchWarehouseData, pagination.pageSize, searchValue],
  );

  const renderWarehouseGrid = useCallback(() => {
    if (apiLoading) {
      return (
        <div className="warehouse-loading">
          <Spin size="large" tip="加载仓库数据中..." />
        </div>
      );
    }

    if (warehouseList.length === 0) {
      return (
        <div className="warehouse-empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchValue ? (
                <>
                  <Text type="secondary">未找到匹配的仓库</Text>
                  <br />
                  <Link onClick={() => handleSearch('')}>清除搜索条件</Link>
                </>
              ) : (
                <Text type="secondary">暂无仓库数据</Text>
              )
            }
          />
        </div>
      );
    }

    return (
      <div className="warehouse-grid">
        {warehouseList.map((warehouse) => (
          <div key={warehouse.id} className="warehouse-item">
            <Checkbox
              value={warehouse.id}
              className="warehouse-checkbox"
              aria-label={`选择仓库 ${warehouse.name}`}
            >
              <Text className="warehouse-text">{warehouse.name}</Text>
            </Checkbox>
          </div>
        ))}
      </div>
    );
  }, [apiLoading, warehouseList, searchValue, handleSearch]);

  return (
    <>
      <Button
        type={btnType}
        onClick={showModal}
        disabled={disabled}
        loading={loading}
      >
        {btnText}
      </Button>
      <Modal
        title="添加仓库"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        okText="确认"
        cancelText="取消"
        className="add-warehouse-modal"
        confirmLoading={loading}
        maskClosable={!loading && !apiLoading}
        closable={!loading && !apiLoading}
        afterClose={resetState}
      >
        <div className="add-warehouse-content">
          <div className="search-section">
            <Search
              size="large"
              placeholder="搜索仓库代码或名称"
              allowClear
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
              prefix={<SearchOutlined />}
              disabled={apiLoading}
              value={searchValue}
            />
          </div>

          {!apiLoading && warehouseList.length > 0 && (
            <div className="select-all-section">
              <Checkbox
                indeterminate={isPartialSelected}
                checked={isAllSelected}
                onChange={handleSelectAll}
                className="select-all-checkbox"
              >
                <Text className="select-all-text">
                  {isAllSelected ? '取消全选' : '全选'}
                  {searchValue && <Text type="secondary"> (当前搜索结果)</Text>}
                </Text>
              </Checkbox>
              {selectedWarehouses.size > 0 && (
                <Button
                  type="link"
                  size="small"
                  onClick={() => setSelectedWarehouses(new Map())}
                  style={{ marginLeft: 16 }}
                >
                  清空所有选择
                </Button>
              )}
            </div>
          )}

          <div className="warehouse-grid-section">
            <Checkbox.Group
              value={currentPageSelectedIds}
              onChange={handleWarehouseChange}
              className="warehouse-checkbox-group"
              disabled={apiLoading}
            >
              {renderWarehouseGrid()}
            </Checkbox.Group>
          </div>

          {!apiLoading && pagination.total > 0 && (
            <div className="pagination-section">
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                size="small"
              />
            </div>
          )}

          {selectedWarehouses.size > 0 && (
            <div className="selected-info">
              <Text type="secondary">
                已选择 {selectedWarehouses.size} 个仓库
                {selectedWarehouses.size > 0 && (
                  <>
                    {!searchValue && pagination.total > pagination.pageSize && (
                      <Text type="secondary"> (含其他页面的选择)</Text>
                    )}
                    {searchValue && (
                      <Text type="secondary"> (含搜索范围外的选择)</Text>
                    )}
                  </>
                )}
              </Text>
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default AddAWarehouse;
