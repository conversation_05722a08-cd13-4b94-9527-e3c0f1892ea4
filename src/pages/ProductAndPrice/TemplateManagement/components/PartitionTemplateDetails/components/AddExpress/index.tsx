import React, { useState, useCallback } from 'react';
import { Button, Modal, Checkbox } from 'antd';
import type { ButtonProps } from 'antd';

// 快递选项定义 - 移到组件外部避免重复创建
const EXPRESS_OPTIONS = [
  { label: 'DHL', value: 'DHL' },
  { label: 'FEDEX', value: 'FEDEX' },
  { label: 'DPD', value: 'DPD' },
  { label: 'UPS', value: 'UPS' },
  { label: 'USPS', value: 'USPS' },
];

interface Props {
  btnText: string;
  btnType: ButtonProps['type'];
  onConfirm?: (selectedExpress: string[]) => void;
}

const AddExpress = ({ btnText, btnType, onConfirm }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedExpress, setSelectedExpress] = useState<string[]>([]);

  // 提取公共的重置逻辑
  const resetModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedExpress([]);
  }, []);

  const showModal = useCallback(() => {
    setIsModalOpen(true);
  }, []);

  const handleOk = useCallback(() => {
    // 调用确认回调
    if (onConfirm && selectedExpress.length > 0) {
      onConfirm(selectedExpress);
    }
    resetModal();
  }, [onConfirm, selectedExpress, resetModal]);

  const handleCancel = useCallback(() => {
    resetModal();
  }, [resetModal]);

  const handleCheckboxChange = useCallback((value: string, checked: boolean) => {
    if (checked) {
      setSelectedExpress(prev => [...prev, value]);
    } else {
      setSelectedExpress(prev => prev.filter(item => item !== value));
    }
  }, []);

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal 
        title="添加快递" 
        open={isModalOpen} 
        onOk={handleOk} 
        onCancel={handleCancel}
        okText="确认"
        cancelText="取消"
        okButtonProps={{ disabled: selectedExpress.length === 0 }}
      >
        <div className="p-4 space-y-3">
          {EXPRESS_OPTIONS.map((option) => {
            const isChecked = selectedExpress.includes(option.value);
            return (
              <div
                key={option.value}
                className={`
                  p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer
                  hover:shadow-sm hover:border-blue-200
                  ${isChecked 
                    ? 'bg-blue-50 border-blue-300 shadow-sm' 
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                  }
                `}
                onClick={(e) => {
                  e.preventDefault();
                  handleCheckboxChange(option.value, !isChecked);
                }}
              >
                <Checkbox
                  value={option.value}
                  checked={isChecked}
                  className="w-full"
                >
                  <span className={`ml-2 font-medium ${isChecked ? 'text-blue-700' : 'text-gray-700'}`}>
                    {option.label}
                  </span>
                </Checkbox>
              </div>
            );
          })}
        </div>
      </Modal>
    </>
  );
};

export default AddExpress;