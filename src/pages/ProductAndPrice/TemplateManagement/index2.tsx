import TabsType from '@/components/SuperTables/components/TabsType';
import { useMemo, useState, useCallback, ReactNode } from 'react';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import PartitionTemplates from './PartitionTemplates';
import VolumeRule from './VolumeRule';
import DeliveryAddressDistance from './DeliveryAddressDistance';

const TAB_CONFIG = [
  {
    key: '分区模版',
    label: '分区模版',
  },
  {
    key: '体积进位规则',
    label: '体积进位规则',
  },
  {
    key: '派送地址距离',
    label: '派送地址距离',
  },
] as const;

type TabKey = (typeof TAB_CONFIG)[number]['key'];

const TemplateManagement2 = () => {
  const [toList] = usePermissionFiltering();

  const TAB_ITEMS = useMemo(() => {
    return toList(TAB_CONFIG);
  }, [toList]);

  const [activeKey, setActiveKey] = useState<TabKey>(
    () => TAB_ITEMS[0]?.key || '分区模版',
  );

  const TAB_COMPONENTS: Record<TabKey, ReactNode> = {
    分区模版: <PartitionTemplates />,
    体积进位规则: <VolumeRule />,
    派送地址距离: <DeliveryAddressDistance />,
  };

  const handleTabChange = useCallback((key: string) => {
    setActiveKey(key as TabKey);
  }, []);

  return (
    <div>
      <TabsType
        items={TAB_ITEMS}
        defaultActiveKey={activeKey}
        onChange={handleTabChange}
      />
      {TAB_COMPONENTS[activeKey]}
    </div>
  );
};

export default TemplateManagement2;
