import { getProductList, saveProduct } from '@/services/productAndPrice/api';
import { Button, message } from 'antd';
import { useEffect, useRef } from 'react';
import { history } from '@umijs/max';
import AccessCard from '@/AccessCard';
import LogDashboard from '@/components/LogDashboard';
import SuperTableMax from '@/components/SuperTableMax';
import dayjs from 'dayjs';

const typeState: any = {
  0: {
    text: '草稿',
    color: '#999999',
  },
  10: {
    text: '已上架',
    color: 'green',
  },
  '-10': {
    text: '已下架',
    color: 'red',
  },
};
const Warehouse = () => {
  const actionRef = useRef<any>();

  /* 商品下架操作 */
  const handleProductDown = async (record: any) => {
    try {
      const { status } = await saveProduct({
        ...record,
        state: '-10',
      });
      if (status) {
        actionRef?.current?.reload();
        message.success('商品下架成功');
      }
    } catch (err) {
      console.error('商品下架失败: ', err);
    }
  };

  /* 刷新 */
  const refreshB = async () => {
    actionRef.current.reload();
  };

  const columns: any = [
    {
      title: 'ID',
      dataIndex: 'id',
      // width: 200,
      render: (text: any, record: any) => {
        return <div>{record?.id}</div>;
      },
    },
    // {
    //   title: '类目',
    //   dataIndex: 'category',
    //   hideInSearch: true,
    //   width: 120,
    //   valueEnum: {
    //     1: {
    //       text: '自营专线',
    //     },
    //     2: {
    //       text: '同行专线',
    //     },
    //     3: {
    //       text: '商业快递',
    //     },
    //   },
    // },
    {
      title: '中文名',
      dataIndex: 'name',
      // width: 180,
    },
    {
      title: '英文名',
      dataIndex: 'nameEn',
    },
    {
      title: '派送时效/工作日',
      dataIndex: 'leastDays',
      // width: 120,
      fieldFormat: (value: any) => {
        return `${value?.leastDays || ''}~${value?.mostDays || ''}`;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      fieldFormat: (value: any) => {
        return typeState[value?.state]?.text;
      },
      style: (e: any, r: any) => {
        return {
          color: typeState[r?.state]?.color,
        };
      },
    },
    // {
    //   title: '尾程派送',
    //   dataIndex: 'tailTransType',
    //   // hideInSearch: true,
    //   valueType: 'select',
    //   valueEnum: {
    //     1: {
    //       text: '本土快递',
    //     },
    //     2: {
    //       text: '卡车运输',
    //     },
    //   },
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    // {
    //   title: '生效时间',
    //   dataIndex: 'enabledTime',
    //   hideInSearch: true,
    //   valueType: 'date',
    // },
    // {
    //   title: '失效时间',
    //   dataIndex: 'disabledTime',
    //   hideInSearch: true,
    //   valueType: 'date',
    // },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 230,
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                history.push(
                  `/productAndPrice/productManagement/DetailsOfClearanceAndDemolition?id=${record?.id}`,
                );
              }}
            >
              详情
            </Button>
            <AccessCard accessible={[':Product:ProductFullAccess']}>
              <Button
                type="link"
                danger
                onClick={() => handleProductDown(record)}
                disabled={record?.state === -10}
              >
                下架
              </Button>
            </AccessCard>
            <LogDashboard
              extraId_1={record?.id}
              btnType="link"
              btnText="日志"
            />
          </>
        );
      },
    },
  ];
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
      tabs: true,
    },
    createTime: {
      desc: '时间',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    // tailTransType: {
    //   desc: '尾程派送',
    //   type: 'select',
    //   range: [
    //     {
    //       label: '本土快递',
    //       value: 1,
    //     },
    //     {
    //       label: '卡车运输',
    //       value: 2,
    //     },
    //   ],
    // },
    // "category": {
    //   "desc": "类目",
    //   "type": "select",
    //   "range": [
    //     {
    //       label: '自营专线',
    //       value: 1,
    //     },
    //     {
    //       label: '同行专线',
    //       value: 2,
    //     },
    //     {
    //       label: '商业快递',
    //       value: 3,
    //     },
    //   ],
    // },
    state: {
      desc: '状态',
      type: 'select',
      range: [
        {
          label: '草稿',
          value: 0,
        },
        {
          label: '已上架',
          value: 10,
        },
        {
          label: '已下架',
          value: '-10',
        },
      ],
    },
  };

  useEffect(() => {
    const handleRefreshB = () => refreshB();
    window.addEventListener('refreshB', handleRefreshB);

    return () => {
      window.removeEventListener('refreshB', handleRefreshB);
    };
  }, []);
  return (
    <>
      <SuperTableMax
        columns={columns}
        ref={actionRef}
        instanceId={'productAndPrice/productManagementv5'}
        request={async (params: any) => {
          params.condition.counterpartyType = {
            value: 'Storage',
          };
          const msg = await getProductList({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: params.condition,
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg?.data?.total,
          };
        }}
        getRowIdFn={(record: any) => record?.data?.id}
        filters={filters}
        isWarpTabs={true}
        toolBarRender={() => {
          return (
            <AccessCard key={1} accessible={[':Product:ProductFullAccess']}>
              <Button
                key="add"
                type="primary"
                onClick={() => {
                  history.push(
                    '/productAndPrice/productManagement/DetailsOfClearanceAndDemolition',
                  );
                }}
              >
                新增产品
              </Button>
            </AccessCard>
          );
        }}
      />
    </>
  );
};
export default Warehouse;
