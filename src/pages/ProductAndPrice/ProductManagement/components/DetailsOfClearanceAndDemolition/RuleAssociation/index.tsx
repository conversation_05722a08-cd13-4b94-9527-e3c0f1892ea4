import TabsType from '@/components/TabsType';
import { ProCard } from '@ant-design/pro-components';
import { useState } from 'react';
import VolumeCarryRule from './VolumeCarryRule';

const RuleAssociation = ({ id, activeKey: activeKeyProps }: any) => {
  const [activeKey, setActiveKey] = useState<any>(300);
  return (
    <ProCard title="关联规则">
      <TabsType
        style={{ padding: '0px 0px 10px 0px', marginTop: '-20px' }}
        items={[
          ...(activeKeyProps === 'Storage'
            ? [
                {
                  label: '体积进位规则',
                  key: 300,
                  children: <VolumeCarryRule id={id} activeKey={activeKey} />,
                },
              ]
            : []),
        ]}
        onChange={(key) => {
          setActiveKey(key);
        }}
        defaultActiveKey={activeKey}
      />
    </ProCard>
  );
};

export default RuleAssociation;
