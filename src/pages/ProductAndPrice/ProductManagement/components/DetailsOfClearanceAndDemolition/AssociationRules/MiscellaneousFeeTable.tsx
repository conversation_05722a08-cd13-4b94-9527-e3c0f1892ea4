import SuperTableMax from '@/components/SuperTableMax';
import { getExtraPriceListAPI } from '@/services/financeApi';
import {
  getProductItem,
  saveProductItem,
} from '@/services/productAndPrice/api';
import { currency_map } from '@/utils/constant';
import { useRequest } from 'ahooks';
import { Button, DatePicker, message, Modal, Space, Table } from 'antd';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { typeState } from '@/assets/types';

interface MiscellaneousFeeTableProps {
  activeKey: any;
  id: string;
}

/* 添加模版 */
const AddTemplate = ({ btnText, btnType, onOk }: any) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [effectiveTime, setEffectiveTime] = useState<any>(null);
  const actionRef = useRef<any>();
  const column = [
    {
      title: '费用名称',
      dataIndex: 'feeName',
    },
    {
      title: '服务项目',
      dataIndex: 'itemName',
    },
    {
      title: '结算币种',
      dataIndex: 'currencyType',
      fieldFormat: (value: any) => {
        return currency_map[value?.currencyType] || '-';
      },
    },
    {
      title: '计费条件',
      dataIndex: 'conditionExp',
    },
    {
      title: '计费公式',
      dataIndex: 'priceExp',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
  ];
  const showModal = () => {
    setIsModalOpen(true);
    setEffectiveTime(null);
    setSelectedRowKeys([]);
  };
  const handleOk = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请选择一个模版');
      return;
    }
    if (!effectiveTime) {
      message.error('请选择生效时间');
      return;
    }
    setIsModalOpen(false);
    onOk?.(selectedRowKeys, effectiveTime);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const modalTitle = (
    <Space>
      <div className="mr-20px">添加</div>
      <div>
        <span style={{ color: 'red', marginRight: '4px' }}>*</span>
        <span className="mr-4px text-14px">生效时间：</span>
        <DatePicker
          showTime
          value={effectiveTime}
          onChange={(date) => setEffectiveTime(date)}
          placeholder="请选择生效时间"
        />
      </div>
    </Space>
  );
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
      >
        <SuperTableMax
          ghost={true}
          columns={column}
          ref={actionRef}
          instanceId="miscellaneousFeeTable"
          request={async (params: any) => {
            const res = await getExtraPriceListAPI({
              start: (params.current - 1) * params.pageSize,
              len: params.pageSize,
              columnsFilter: params.columnsFilter || {},
              condition: params.condition,
            });
            return {
              data: res?.data?.list || [],
              success: res.status,
              total: res?.data?.total,
            };
          }}
          getRowIdFn={(record: any) => record?.data?.id}
          filters={{
            keyword: {
              type: 'keyword',
              value: '',
              tabs: true,
            },
          }}
          height="400px"
          rowSelection={(e: any) => {
            setSelectedRowKeys(e);
          }}
          rowSelectionMode="singleRow"
          // toolBarRender={() => {
          //   return (
          //     <Button
          //       type="primary"
          //       onClick={() => {
          //         history.push(
          //           '/productAndPrice/priceManagement/detailsOfDistribution',
          //         );
          //       }}
          //     >
          //       新建
          //     </Button>
          //   );
          // }}
        />
      </Modal>
    </>
  );
};

const MiscellaneousFeeTable = ({
  id,
  activeKey,
}: MiscellaneousFeeTableProps) => {
  const { runAsync: getProductItemList } = useRequest(getProductItem, {
    manual: true,
  });
  const [dataSource, setDataSource] = useState<any[]>([]);
  useEffect(() => {
    if (id && activeKey === 290) {
      getProductItemList({
        productId: id,
        type: activeKey,
      }).then((e) => {
        if (e.status) {
          setDataSource([...e.data]);
        }
      });
    }
  }, [activeKey]);

  /* 保存关联规则 */
  const { runAsync: saveAssociationRule } = useRequest(saveProductItem, {
    manual: true,
  });

  const column = [
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: (text: any, record: any) => {
        return (
          <span style={{ color: typeState[record?.state]?.color }}>
            {typeState[record?.state]?.text}
          </span>
        );
      },
    },
    {
      title: '费用名称',
      dataIndex: ['itemDetail', 'feeName'],
    },
    {
      title: '服务项目',
      dataIndex: ['itemDetail', 'itemName'],
    },
    {
      title: '结算币种',
      dataIndex: ['itemDetail', 'currencyType'],
      render: (value: any, record: any) => {
        return currency_map[record?.itemDetail?.currencyType] || '-';
      },
    },
    {
      title: '计费条件',
      dataIndex: ['itemDetail', 'conditionExp'],
    },
    {
      title: '计费公式',
      dataIndex: ['itemDetail', 'priceExp'],
    },
    {
      title: '备注',
      dataIndex: ['itemDetail', 'remark'],
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (value: any, record: any) => {
        return record?.createTime
          ? dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '生效时间',
      dataIndex: 'enabledTime',
      render: (value: any, record: any) => {
        return record?.enabledTime
          ? dayjs(record?.enabledTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '失效时间',
      dataIndex: 'disabledTime',
      render: (value: any, record: any) => {
        return record?.disabledTime
          ? dayjs(record?.disabledTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
  ];
  return (
    <div style={{ position: 'relative' }}>
      <div
        style={{
          position: 'absolute',
          top: '-12px',
          right: 0,
          zIndex: 10,
          transform: 'translateY(-100%)',
        }}
      >
        <AddTemplate
          btnText="添加"
          btnType="primary"
          onOk={(e: any, effectiveTime: any) => {
            saveAssociationRule({
              productId: id,
              type: activeKey,
              itemId: e[0]?.id,
              enabledTime: effectiveTime.valueOf(),
            }).then((e) => {
              if (e.status) {
                message.success('添加成功');
                getProductItemList({
                  productId: id,
                  type: activeKey,
                }).then((e) => {
                  if (e.status) {
                    setDataSource([...e.data]);
                  }
                });
              }
            });
          }}
        />
      </div>
      <Table
        columns={column}
        dataSource={dataSource}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1200 }}
        bordered
        size="small"
      />
    </div>
  );
};

export default MiscellaneousFeeTable;
