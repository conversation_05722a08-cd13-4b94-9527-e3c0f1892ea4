import { currency_map } from '@/utils/constant';
import dayjs from 'dayjs';
import { Button, DatePicker, message, Modal, Space, Table } from 'antd';
import { useRequest } from 'ahooks';
import {
  getProductItem,
  saveProductItem,
} from '@/services/productAndPrice/api';
import { useEffect, useRef, useState } from 'react';
import { getStoragePriceListAPI } from '@/services/financeApi';
import SuperTableMax from '@/components/SuperTableMax';
import { typeState } from '@/assets/types';

interface ProposalProps {
  activeKey: any;
  id: string;
}

const priceBy_map: any = {
  6: '按总体积',
  9: '按箱数',
  10: '按托盘数',
};

const priceUnit_map: any = {
  6: '/CBM/天',
  9: '/箱/天',
  10: '/托盘/天',
};

/* 添加模版 */
const AddTemplate = ({ btnText, btnType, onOk }: any) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [effectiveTime, setEffectiveTime] = useState<any>(null);
  const actionRef = useRef<any>();
  const column = [
    {
      title: '模板名称',
      dataIndex: 'name',
    },
    {
      title: '计算方式',
      dataIndex: 'priceBy',
      fieldFormat: (value: any) => {
        return priceBy_map[value?.priceBy] || '-';
      },
    },
    {
      title: '计算单位',
      dataIndex: 'priceUnit',
      fieldFormat: (value: any) => {
        return `${value?.priceUnit}${priceUnit_map[value?.priceBy]}`;
      },
    },
    {
      title: '计费币种',
      dataIndex: 'currencyType',
      fieldFormat: (value: any) => {
        return currency_map[value?.currencyType] || '-';
      },
    },
    {
      title: '含上架日',
      dataIndex: 'containStackingDay',
      fieldFormat: (value: any) => {
        return value?.containStackingDay ? '是' : '否';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value?.createTime
          ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
  ];
  const showModal = () => {
    setIsModalOpen(true);
    setEffectiveTime(null);
    setSelectedRowKeys([]);
  };
  const handleOk = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请选择一个模版');
      return;
    }
    if (!effectiveTime) {
      message.error('请选择生效时间');
      return;
    }
    setIsModalOpen(false);
    onOk?.(selectedRowKeys, effectiveTime);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const modalTitle = (
    <Space>
      <div className="mr-20px">添加</div>
      <div>
        <span style={{ color: 'red', marginRight: '4px' }}>*</span>
        <span className="mr-4px text-14px">生效时间：</span>
        <DatePicker
          showTime
          value={effectiveTime}
          onChange={(date) => setEffectiveTime(date)}
          placeholder="请选择生效时间"
        />
      </div>
    </Space>
  );

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
      >
        <SuperTableMax
          ghost={true}
          columns={column}
          ref={actionRef}
          instanceId="warehousingPriceList"
          request={async (params: any) => {
            const res = await getStoragePriceListAPI({
              start: (params.current - 1) * params.pageSize,
              len: params.pageSize,
              columnsFilter: params.columnsFilter || {},
              condition: params.condition,
            });
            return {
              data: res?.data?.list || [],
              success: res.status,
              total: res?.data?.total,
            };
          }}
          getRowIdFn={(record: any) => record?.data?.id}
          filters={{
            keyword: {
              type: 'keyword',
              value: '',
              tabs: true,
            },
          }}
          height="400px"
          rowSelection={(e: any) => {
            setSelectedRowKeys(e);
          }}
          rowSelectionMode="singleRow"
          // toolBarRender={() => {
          //   return (
          //     <Button
          //       type="primary"
          //       onClick={() => {
          //         history.push(
          //           '/productAndPrice/priceManagement/detailsOfDistribution',
          //         );
          //       }}
          //     >
          //       新建
          //     </Button>
          //   );
          // }}
        />
      </Modal>
    </>
  );
};

const WarehousingPriceList = ({ id, activeKey }: ProposalProps) => {
  const { runAsync: getProductItemList } = useRequest(getProductItem, {
    manual: true,
  });
  const [dataSource, setDataSource] = useState<any[]>([]);
  useEffect(() => {
    if (id && activeKey === 270) {
      getProductItemList({
        productId: id,
        type: activeKey,
      }).then((e) => {
        if (e.status) {
          setDataSource(e.data);
        }
      });
    }
  }, [activeKey]);

  /* 保存关联规则 */
  const { runAsync: saveAssociationRule } = useRequest(saveProductItem, {
    manual: true,
  });
  const column = [
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: (text: any, record: any) => {
        return (
          <span style={{ color: typeState[record?.state]?.color }}>
            {typeState[record?.state]?.text}
          </span>
        );
      },
    },
    {
      title: '模板名称',
      dataIndex: ['itemDetail', 'name'],
    },
    {
      title: '计算方式',
      dataIndex: ['itemDetail', 'priceBy'],
      render: (value: any, record: any) => {
        return priceBy_map[record?.itemDetail?.priceBy] || '-';
      },
    },
    {
      title: '计算单位',
      dataIndex: ['itemDetail', 'priceUnit'],
      render: (value: any, record: any) => {
        return `${record?.itemDetail?.priceUnit}${priceUnit_map[record?.itemDetail?.priceBy]}`;
      },
    },
    {
      title: '计费币种',
      dataIndex: ['itemDetail', 'currencyType'],
      render: (value: any, record: any) => {
        return currency_map[record?.itemDetail?.currencyType] || '-';
      },
    },
    {
      title: '含上架日',
      dataIndex: ['itemDetail', 'containStackingDay'],
      render: (value: any, record: any) => {
        return record?.itemDetail?.containStackingDay ? '是' : '否';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (value: any, record: any) => {
        return record?.createTime
          ? dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '生效时间',
      dataIndex: 'enabledTime',
      render: (value: any, record: any) => {
        return record?.enabledTime
          ? dayjs(record?.enabledTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '失效时间',
      dataIndex: 'disabledTime',
      render: (value: any, record: any) => {
        return record?.disabledTime
          ? dayjs(record?.disabledTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   render: (value: any, record: any) => {
    //     return (
    //       <Button type="link" danger onClick={() => {
    //         Modal.confirm({
    //           title: '停用',
    //           content: '确定停用该关联规则吗？',
    //           onOk: () => {
    //             saveAssociationRule({
    //               productId: id,
    //               type: activeKey,
    //               itemId: record?.id,
    //               state: 50,
    //             }).then(async (e) => {
    //               if (e.status) {
    //                 message.success('停用成功');
    //                 const listRes = await getProductItemList({
    //                   productId: id,
    //                   type: activeKey,
    //                 });
    //                 if (listRes.status) {
    //                   setDataSource([...listRes.data]);
    //                 }
    //               }
    //             });
    //           },
    //         });
    //       }}>
    //         停用
    //       </Button>
    //     );
    //   },
    // },
  ];
  return (
    <div style={{ position: 'relative' }}>
      <div
        style={{
          position: 'absolute',
          top: '-12px',
          right: 0,
          zIndex: 10,
          transform: 'translateY(-100%)',
        }}
      >
        <AddTemplate
          btnText="添加"
          btnType="primary"
          onOk={async (e: any, effectiveTime: any) => {
            const saveRes = await saveAssociationRule({
              productId: id,
              type: activeKey,
              itemId: e[0]?.id,
              enabledTime: effectiveTime.valueOf(),
            });

            if (saveRes.status) {
              message.success('添加成功');
              const listRes = await getProductItemList({
                productId: id,
                type: activeKey,
              });
              if (listRes.status) {
                setDataSource([...listRes.data]);
              }
            }
          }}
        />
      </div>
      <Table
        columns={column}
        dataSource={dataSource}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1200 }}
        bordered
        size="small"
      />
    </div>
  );
};

export default WarehousingPriceList;
