import TabsType from '@/components/TabsType';
import { ProCard } from '@ant-design/pro-components';
import { useState } from 'react';
import Proposal from './Proposal';
import MiscellaneousFeeTable from './MiscellaneousFeeTable';
import WarehousingPriceList from './WarehousingPriceList';

const AssociationRules = ({ id, activeKey: activeKeyProps }: any) => {
  const [activeKey, setActiveKey] = useState<any>(
    activeKeyProps === 'CPDD' ? 260 : 270,
  );
  return (
    <ProCard title="关联计费">
      <TabsType
        style={{ padding: '0px 0px 10px 0px', marginTop: '-20px' }}
        items={[
          ...(activeKeyProps === 'CPDD'
            ? [
                {
                  label: '提拆派价格表',
                  key: 260,
                  children: <Proposal id={id} activeKey={activeKey} />,
                },
              ]
            : []),
          ...(activeKeyProps === 'Storage'
            ? [
                {
                  label: '仓储价格表',
                  key: 270,
                  children: (
                    <WarehousingPriceList id={id} activeKey={activeKey} />
                  ),
                },
              ]
            : []),
          // {
          //   label: '仓储价格表',
          //   key: 270,
          //   children: <div>111</div>,
          // },
          // {
          //   label: '派送价格表',
          //   key: 280,
          //   children: <div>111</div>,
          // },
          {
            label: '杂费价格表',
            key: 290,
            children: <MiscellaneousFeeTable id={id} activeKey={activeKey} />,
          },
        ]}
        onChange={(key) => {
          setActiveKey(key);
        }}
        defaultActiveKey={activeKey}
      />
    </ProCard>
  );
};

export default AssociationRules;
