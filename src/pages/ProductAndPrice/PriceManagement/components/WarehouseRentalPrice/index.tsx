import SuperTableMax from '@/components/SuperTableMax';
import {
  deleteStoragePriceAPI,
  getStoragePriceListAPI,
} from '@/services/financeApi';
import { currency_map } from '@/utils/constant';
import { Button, message, Popconfirm, Space } from 'antd';
import { useContext, useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import { history } from '@umijs/max';
import { KeepAliveTabContext } from '@/layouts/context';
import { recordKeyToString } from '@ant-design/pro-components';

const priceBy_map: any = {
  6: '按总体积',
  9: '按箱数',
  10: '按托盘数',
};

const priceUnit_map: any = {
  6: '/CBM/天',
  9: '/箱/天',
  10: '/托盘/天',
};

const WarehouseRentalPrice = () => {
  const actionRef = useRef<any>();
  const { onShow } = useContext(KeepAliveTabContext);
  useEffect(() => {
    const handlePageShow = () => {
      if (actionRef.current) {
        actionRef.current.reload();
      }
    };
    onShow(handlePageShow);
  }, [onShow]);
  /*
  模板名称 计算方式 计算单位 计费币种 含上架日 创建时间 操作
  */
  const column = [
    {
      title: '模板名称',
      dataIndex: 'name',
    },
    {
      title: '计算方式',
      dataIndex: 'priceBy',
      fieldFormat: (value: any) => {
        return priceBy_map[value?.priceBy] || '-';
      },
    },
    {
      title: '计算单位',
      dataIndex: 'priceUnit',
      fieldFormat: (value: any) => {
        return `${value?.priceUnit}${priceUnit_map[value?.priceBy]}`;
      },
    },
    {
      title: '计费币种',
      dataIndex: 'currencyType',
      fieldFormat: (value: any) => {
        return currency_map[value?.currencyType] || '-';
      },
    },
    {
      title: '含上架日',
      dataIndex: 'containStackingDay',
      fieldFormat: (value: any) => {
        return value?.containStackingDay ? '是' : '否';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value?.createTime
          ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_: any, recode: any, params: any) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                history.push(
                  '/productAndPrice/priceManagement/addWarehouseRentalPrice',
                  recode,
                );
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="删除"
              description="确定删除吗?"
              onConfirm={() => getDelete(recode?.id)}
              onCancel={() => message.info('取消操作')}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  // 删除价格表
  const getDelete = async (id: string) => {
    try {
      const { status } = await deleteStoragePriceAPI({
        id,
      });
      if (status) {
        actionRef.current?.refresh();
      }
    } catch (error) {}
  };
  return (
    <div>
      <SuperTableMax
        columns={column}
        ref={actionRef}
        instanceId="warehouseRentalPrice"
        request={async (params: any) => {
          const res = await getStoragePriceListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: params.condition,
          });
          return {
            data: res?.data?.list || [],
            success: res.status,
            total: res?.data?.total,
          };
        }}
        getRowIdFn={(record: any) => record?.data?.id}
        isWarpTabs={true}
        filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}
        toolBarRender={() => {
          return (
            <Button
              type="primary"
              onClick={() => {
                history.push(
                  '/productAndPrice/priceManagement/addWarehouseRentalPrice',
                );
              }}
            >
              新建
            </Button>
          );
        }}
      />
    </div>
  );
};

export default WarehouseRentalPrice;
