import SuperTableMax from '@/components/SuperTableMax';
import { getDeliveryPriceListAPI } from '@/services/financeApi';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useContext, useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import { history } from '@umijs/max';
import { currency_map } from '@/utils/constant';
import { deleteDeliveryPrice } from '@/services/productAndPrice/api';
import { KeepAliveTabContext } from '@/layouts/context';

const DeliveryFeeSchedule = () => {
  const actionRef = useRef<any>();
  const { onShow } = useContext(KeepAliveTabContext);
  useEffect(() => {
    const handlePageShow = () => {
      if (actionRef.current) {
        actionRef.current.reload();
      }
    };
    onShow(handlePageShow);
  }, [onShow]);
  const deleteRelatedFile = async (id: any) => {
    const res = await deleteDeliveryPrice({
      id,
    });
    if (res.status) {
      actionRef?.current?.reload();
      message.success('删除成功');
    }
    console.log('id', id);
  };
  //名称、距离段、起点、备注、创建人、创建时间、操作
  const column = [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '距离段',
      dataIndex: 'distanceName',
      fieldFormat: (value: any) => {
        return (
          value?.detailList?.map((item: any) => item?.distanceName).join(',') ||
          '-'
        );
      },
    },
    {
      title: '计费币种',
      dataIndex: 'currencyType',
      fieldFormat: (value: any) => {
        return currency_map[value?.currencyType] || '-';
      },
    },
    {
      title: '起点',
      dataIndex: 'warehouseInfo',
      fieldFormat: (value: any) => {
        return `${value?.warehouseInfo?.subject}`;
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '创建人',
      dataIndex: 'createUserInfo.name',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value?.createTime
          ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (text: any, record: any) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                console.log('record?.id', record?.id);
                history.push(
                  `/productAndPrice/priceManagement/CreateDeliveryFeeTemplate`,
                  {
                    record: record,
                  },
                );
              }}
            >
              编辑
            </Button>
            <Popconfirm
              key={1}
              title="删除确认"
              description="确认要删除吗?"
              onConfirm={() => {
                deleteRelatedFile(record?.id);
              }}
              okText="删除"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  return (
    <div>
      <SuperTableMax
        columns={column}
        ref={actionRef}
        instanceId="deliveryFeeSchedule"
        request={async (params: any) => {
          const res = await getDeliveryPriceListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: params.condition,
          });
          return {
            data: res?.data?.list || [],
            success: res.status,
            total: res?.data?.total,
          };
        }}
        getRowIdFn={(record: any) => record?.data?.id}
        isWarpTabs={true}
        filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}
        toolBarRender={() => {
          return (
            <Button
              type="primary"
              onClick={() => {
                history.push(
                  '/productAndPrice/priceManagement/CreateDeliveryFeeTemplate',
                );
              }}
            >
              新建
            </Button>
          );
        }}
      />
    </div>
  );
};

export default DeliveryFeeSchedule;
