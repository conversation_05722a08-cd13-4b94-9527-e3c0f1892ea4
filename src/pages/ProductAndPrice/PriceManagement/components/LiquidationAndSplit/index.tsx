import SuperTableMax from '@/components/SuperTableMax';
import { Button, message, Popconfirm, Space } from 'antd';
import { useRef, useContext, useEffect } from 'react';
import { history } from '@umijs/max';
import { deletePddPriceAPI, getPddPriceListAPI } from '@/services/financeApi';
import { currency_map } from '@/utils/constant';
import dayjs from 'dayjs';
import { KeepAliveTabContext } from '@/layouts/context';
import { useRequest } from 'ahooks';

const LiquidationAndSplit = () => {
  const actionRef = useRef<any>();
  const { onShow } = useContext(KeepAliveTabContext);

  useEffect(() => {
    const handlePageShow = () => {
      if (actionRef.current) {
        actionRef.current.reload();
      }
    };
    onShow(handlePageShow);
  }, [onShow]);

  const { runAsync: deletePddPrice } = useRequest(deletePddPriceAPI, {
    manual: true,
  });

  const column = [
    {
      title: '模版名称',
      dataIndex: 'name',
    },
    // {
    //   title: '单据类型',
    //   dataIndex: 'documentType',
    // },
    {
      title: '结算币种',
      dataIndex: 'currencyType',
      fieldFormat: (value: any) => {
        return currency_map[value?.currencyType] || '-';
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '创建人',
      dataIndex: 'createUserInfo.name',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      fieldFormat: (value: any) => {
        return value?.createTime
          ? dayjs(value?.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (text: any, record: any) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                history.push(
                  `/productAndPrice/priceManagement/detailsOfDistribution?id=${record.id}`,
                  {
                    ...record,
                  },
                );
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                deletePddPrice({
                  id: record.id,
                }).then((res) => {
                  if (res.status) {
                    message.success('删除成功');
                    actionRef.current?.reload();
                  }
                });
              }}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  return (
    <div>
      <SuperTableMax
        columns={column}
        ref={actionRef}
        instanceId="liquidationAndSplit"
        request={async (params: any) => {
          const res = await getPddPriceListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: params.condition,
          });
          return {
            data: res?.data?.list || [],
            success: res.status,
            total: res?.data?.total,
          };
        }}
        getRowIdFn={(record: any) => record?.data?.id}
        isWarpTabs={true}
        filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}
        toolBarRender={() => {
          return (
            <Button
              type="primary"
              onClick={() => {
                history.push(
                  '/productAndPrice/priceManagement/detailsOfDistribution',
                );
              }}
            >
              新建
            </Button>
          );
        }}
      />
    </div>
  );
};

export default LiquidationAndSplit;
