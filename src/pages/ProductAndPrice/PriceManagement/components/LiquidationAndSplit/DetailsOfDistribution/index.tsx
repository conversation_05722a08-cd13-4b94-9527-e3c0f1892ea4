import React, { useState, useEffect, useContext, useMemo } from 'react';
import {
  Form,
  Input,
  Select,
  Radio,
  Button,
  Space,
  Row,
  Col,
  message,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';
import ZonePriceTable from './ZonePriceTable';
import { CurrencyType } from '@/shared/EnumerationTypes';
import { useRequest } from 'ahooks';
import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
import {
  createPddPriceAPI,
  getPddPriceTemplateDetailAPI,
  getPddPriceTemplateListAPI,
} from '@/services/financeApi';
import { history, useLocation } from '@umijs/max';
import { KeepAliveTabContext } from '@/layouts/context';

const STYLES = {
  container: {
    backgroundColor: '#f5f5f5',
  },
  stickyHeader: {
    position: 'sticky' as const,
    top: 0,
    zIndex: 1000,
    backgroundColor: '#fff',
    borderBottom: '1px solid #e8e8e8',
    padding: '16px 24px',
    display: 'flex',
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  title: {
    margin: 0,
    fontSize: '16px',
    fontWeight: 500,
  },
  content: {
    backgroundColor: '#fff',
  },
} as const;

const DetailsOfDistribution = () => {
  const location = useLocation();
  const id = useMemo(() => {
    return location?.search?.split('?')[1]?.split('=')[1];
  }, [location?.search]);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [templateOptions, setTemplateOptions] = useState([]);
  //分区模版对象
  const [zoneTemplate, setZoneTemplate] = useState<any>(null);
  //分区价格表数据
  const [priceTableData, setPriceTableData] = useState<any[]>([]);

  const { closeTab } = useContext(KeepAliveTabContext);

  /* 获取详情 */
  const { runAsync: getPddPriceDetail } = useRequest(
    getPddPriceTemplateDetailAPI,
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (id) {
      getPddPriceDetail({ id: id as string }).then((res) => {
        if (res.status) {
          form.setFieldsValue(res?.data?.pddPrice);
          setZoneTemplate({
            contentData: res?.data?.contentData,
            headData: res?.data?.headData,
          });
        }
      });
    }
  }, [id]);
  //获取分区模版下拉框数据
  const { run: fetchTemplates, loading: templateLoading } = useRequest(
    async (keyword?: string) => {
      const params: any = {
        start: 0,
        len: 100,
        zoneType: 0,
      };
      if (keyword) {
        params.condition = {
          keyword: {
            value: keyword,
          },
        };
      }
      const res = await getPartitionTemplateListAPI(params);
      if (res.status && res?.data?.list) {
        const options = res.data.list.map((item: any) => ({
          value: item.id,
          label: item.name,
        }));
        setTemplateOptions(options);
      }
    },
    {
      debounceWait: 500,
      manual: true,
    },
  );

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  /* 新建提拆派价格表 */
  const { runAsync: createPddPrice } = useRequest(createPddPriceAPI, {
    manual: true,
  });

  /* 查询提拆派价格模版 */
  const { runAsync: getPddPriceTemplateList } = useRequest(
    getPddPriceTemplateListAPI,
    {
      manual: true,
    },
  );

  // 保存处理
  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      createPddPrice({
        ...values,
        priceCellList: priceTableData,
        id: id,
      }).then((res) => {
        if (res.status) {
          message.success('保存成功');
          history.back();
          closeTab();
        }
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 取消处理
  const handleCancel = () => {
    history.back();
    closeTab();
  };

  return (
    <div style={STYLES.container}>
      {/* 固定的标题栏 */}
      <div style={STYLES.stickyHeader}>
        <h3 style={STYLES.title}>{id ? '编辑' : '新建'}提拆派价格表</h3>
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSave}>
            保存
          </Button>
        </Space>
      </div>

      <div style={STYLES.content}>
        {/* 基础信息表单 */}
        <ProCard title="基础信息">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              currencyType: 20,
              weightVolumeUnit: 1,
              distanceUnit: 1,
            }}
          >
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item
                  label="名称"
                  name="name"
                  rules={[{ required: true, message: '请输入名称' }]}
                >
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="计费币种"
                  name="currencyType"
                  rules={[{ required: true, message: '请选择计费币种' }]}
                >
                  <Select placeholder="请选择" options={CurrencyType} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="分区模板"
                  name="zoneId"
                  rules={[{ required: true, message: '请选择分区模板' }]}
                >
                  <Select
                    showSearch
                    placeholder="请选择分区模板"
                    onSearch={fetchTemplates}
                    loading={templateLoading}
                    filterOption={false}
                    options={templateOptions}
                    onChange={(value) => {
                      getPddPriceTemplateList({
                        zoneId: value,
                      }).then((res) => {
                        if (res.status) {
                          console.log('res.data: ', res);
                          setZoneTemplate(res);
                        }
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="材积单位"
                  name="weightVolumeUnit"
                  rules={[{ required: true, message: '请选择材积单位' }]}
                >
                  <Radio.Group
                    style={{ width: '100%', display: 'flex' }}
                    buttonStyle="solid"
                  >
                    <Radio.Button value={1} style={{ flex: 1 }}>
                      LB / CBM
                    </Radio.Button>
                    <Radio.Button value={2} style={{ flex: 1 }}>
                      KG / CBM
                    </Radio.Button>
                    <Radio.Button value={3} style={{ flex: 1 }}>
                      LB / CBF
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              {/* <Col span={6}>
                <Form.Item
                  label="距离单位"
                  name="distanceUnit"
                  rules={[{ required: true, message: '请选择距离单位' }]}
                >
                  <Radio.Group
                    style={{ width: '100%', display: 'flex' }}
                    buttonStyle="solid"
                  >
                    <Radio.Button value={1} style={{ flex: 1 }}>
                      MI
                    </Radio.Button>
                    <Radio.Button value={2} style={{ flex: 1 }}>
                      KM
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col> */}
              <Col span={12}>
                <Form.Item label="备注" name="remark">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </ProCard>
      </div>
      <ProCard className="mt-20px" title="分区价格表">
        <ZonePriceTable
          zoneTemplate={zoneTemplate}
          onDataChange={(transformedData: any) => {
            setPriceTableData(transformedData);
          }}
          priceId={id}
        />
      </ProCard>
    </div>
  );
};

export default DetailsOfDistribution;
