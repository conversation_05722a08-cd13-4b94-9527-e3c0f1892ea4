.priceTableContainer {
  .zoneColumn {
    background: #f9f9f9;
  }
  
  .priceCell {
    .ant-space {
      align-items: flex-start;
    }
    
    .anticon-close {
      &:hover {
        color: #ff7875 !important;
        transform: scale(1.1);
        transition: all 0.2s ease;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .zonePriceContainer {
    padding: 16px;
  }

  .priceTableContainer {
    .priceTable {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 6px 4px;
        font-size: 11px;
      }
    }
  }
}
