import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Table, Space, Modal } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import styles from './index.less';
import TrialPartition from '../TrialPartition';
import PriceConfiguration from '../PriceConfiguration';

interface PriceTableProps {
  zoneTemplate?: {
    contentData?: any[];
    headData?: string[];
    data?: any[];
  };
  onDataChange?: any;
  priceId?: string;
}

const transformDataSource = (data: any[]) => {
  const result: any[] = [];
  data.forEach((row) => {
    const zoneCode = row.zoneName;
    Object.keys(row).forEach((key) => {
      if (key !== 'id' && key !== 'zoneName') {
        const containerModel = key;
        const values = row[key];

        if (Array.isArray(values) && values.length > 0) {
          const transformedValues = values.map((value) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { key, groupId, ...rest } = value;
            return rest;
          });

          result.push({
            zoneCode: zoneCode,
            containerModel: containerModel,
            values: transformedValues,
          });
        }
      }
    });
  });
  return result;
};

const PriceTable: React.FC<PriceTableProps> = ({
  zoneTemplate,
  onDataChange,
  priceId,
}) => {
  const [dataSource, setDataSource] = useState<any[]>([]);

  const originalData = useMemo(() => {
    if (
      !zoneTemplate ||
      !zoneTemplate.contentData ||
      !zoneTemplate.headData ||
      zoneTemplate.contentData.length === 0 ||
      zoneTemplate.headData.length === 0
    ) {
      return [];
    }
    const { contentData, headData } = zoneTemplate;
    return contentData.map((row: any) => {
      const newRow: { [key: string]: any } = {};
      headData.forEach((header: string, index: number) => {
        newRow[header] = row[`data${index}`];
      });
      return newRow;
    });
  }, [zoneTemplate]);

  useEffect(() => {
    if (!originalData || originalData.length === 0 || !zoneTemplate?.headData) {
      setDataSource([]);
      return;
    }

    const specKey = '分区/箱规';
    const zoneKeys = zoneTemplate.headData.filter((key) => key !== specKey);

    const finalDataSource = zoneKeys.map((zoneKey, index) => {
      const rowData: any = {
        id: index.toString(),
        zoneName: zoneKey,
      };

      originalData.forEach((specData) => {
        const specName = specData[specKey];
        if (specName) {
          const value = specData[zoneKey];
          rowData[specName] = value;
        }
      });

      return rowData;
    });
    setDataSource(finalDataSource);
  }, [originalData, zoneTemplate?.headData]);

  const handlePriceConfigChange = useCallback(
    (value: any, rowIndex: number, specName: string) => {
      setDataSource((currentDataSource) => {
        const newDataSource = [...currentDataSource];
        if (newDataSource[rowIndex]) {
          const newRow = { ...newDataSource[rowIndex] };
          newRow[specName] = value;
          newDataSource[rowIndex] = newRow;
        }
        return newDataSource;
      });
    },
    [],
  );

  const handleClearPrice = useCallback(
    (rowIndex: number, specName: string) => {
      Modal.confirm({
        title: '确认清除',
        content: '确定要清除此价格配置吗？',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          handlePriceConfigChange(undefined, rowIndex, specName);
        },
      });
    },
    [handlePriceConfigChange],
  );

  useEffect(() => {
    if (dataSource.length > 0) {
      const transformedData = transformDataSource(dataSource);
      onDataChange?.(transformedData, dataSource);
    }
  }, [dataSource]);

  const columns: ColumnsType<any> = useMemo(() => {
    if (!originalData || originalData.length === 0) {
      return [];
    }
    const specKey = '分区/箱规';
    const specNames = originalData
      .map((item) => item[specKey])
      .filter((item) => item !== undefined && item !== null);

    const finalColumns: ColumnsType<any> = [
      {
        title: specKey,
        dataIndex: 'zoneName',
        key: 'zoneName',
        width: 120,
        fixed: 'left',
        // className: styles.zoneColumn,
        render: (text: string) => <div className={styles.zoneCell}>{text}</div>,
      },
      ...specNames.map((specName) => ({
        title: specName,
        dataIndex: specName,
        key: specName,
        width: 200,
        // className: styles.priceColumn,
        render: (text: any, record: any, rowIndex: number) => {
          const isNonEmptyString =
            typeof text === 'string' && text.trim() !== '';
          const isNonEmptyArray = Array.isArray(text) && text.length > 0;
          const hasValue = isNonEmptyString || isNonEmptyArray;

          let content: React.ReactNode = null;
          if (isNonEmptyArray) {
            content = text.map((item: any, i: number) => {
              if (item.feeName) {
                return (
                  <div key={i} className={styles.priceLine}>
                    {`${item?.conditionExp} ${item.feeName} = ${
                      item.type === 1 ? item.refPriceName : item.priceExp
                    }`}
                  </div>
                );
              }
              return null;
            });
          } else if (isNonEmptyString) {
            content = text.split('\n').map((line: string, i: number) => (
              <div key={i} className={styles.priceLine}>
                {line}
              </div>
            ));
          }

          return (
            <div className={styles.priceCell} style={{ marginLeft: hasValue ? 8 : 0 }}>
              <Space size={4} align="start">
                <PriceConfiguration
                  btnText={hasValue ? content : '配置'}
                  btnType="link"
                  edit={hasValue ? true : false}
                  record={record[specName]}
                  onOk={(e: any) =>
                    handlePriceConfigChange(e, rowIndex, specName)
                  }
                />
                {hasValue && (
                  <DeleteOutlined
                    style={{
                      color: '#ff4d4f',
                      cursor: 'pointer',
                      // fontSize: '12px',
                      marginTop: '2px',
                    }}
                    onClick={() => handleClearPrice(rowIndex, specName)}
                    title="清除配置"
                  />
                )}
              </Space>
            </div>
          );
        },
      })),
    ];
    return finalColumns;
  }, [originalData, handlePriceConfigChange]);

  return (
    <div className={styles.priceTableContainer}>
      {priceId && (
        <TrialPartition btnText="试算" btnType="primary" priceId={priceId} />
      )}
      <Table
        style={{ marginTop: '10px' }}
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1200 }}
        bordered
        size="small"
        className={styles.priceTable}
        rowClassName={(record, index) =>
          index % 2 === 0 ? styles.evenRow : styles.oddRow
        }
      />
    </div>
  );
};

export default PriceTable;
