import React, { useState, useMemo } from 'react';
import { Button, Modal, Table, Typography, Row, Col, Statistic } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import './index.less';
import { calculatePddPriceAPI } from '@/services/financeApi';
import { useRequest } from 'ahooks';

const KG_TO_LB = 2.20462;

interface DataType {
  volume: number;
  palletsNum: number;
  amount: number;
  pieceNum: number;
  priceExpStr: string;
  feeCode: string;
  feeName: string;
  weight: number;
  volumePercent: number;
  key: string;
  zoneCode: string;
}
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  blnoOrderId: string;
  priceId?: string;
}

const { Title, Text, Link } = Typography;

const TrialCalculationModal = ({
  btnText,
  btnType,
  blnoOrderId,
  priceId,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [orderInfo, setOrderInfo] = useState<any>({});
  const [tableData, setTableData] = useState<any[]>([]);

  const processedData = useMemo(() => {
    return tableData.map((item, index) => {
      if (index > 0 && item.key === tableData[index - 1].key) {
        return { ...item, rowSpan: 0 };
      }
      let rowSpan = 1;
      for (let i = index + 1; i < tableData.length; i++) {
        if (tableData[i].key === item.key) {
          rowSpan++;
        } else {
          break;
        }
      }
      return { ...item, rowSpan };
    });
  }, [tableData]);

  const { runAsync: getPddPrice } = useRequest(calculatePddPriceAPI, {
    manual: true,
  });

  const showModal = () => {
    getPddPrice({
      priceId,
      blnoOrderId,
    }).then((res: any) => {
      if (res.status) {
        console.log('res', res);
        setOrderInfo(res.data.order || {});
        setTableData(
          res.data.calResultDetails.map((item: any, index: number) => ({
            ...item,
            uid: `${item.key}-${item.feeCode}-${index}`,
          })),
        );
        setIsModalOpen(true);
      }
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const columns: ColumnsType<DataType> = [
    {
      title: '被计费主体',
      dataIndex: 'key',
      key: 'key',
      onCell: (record: any) => ({
        rowSpan: record.rowSpan,
      }),
    },
    {
      title: '命中条件',
      dataIndex: 'zoneCode',
      key: 'zoneCode',
    },
    {
      title: '货物 体积(CBM)',
      dataIndex: 'volume',
      key: 'volume',
    },
    {
      title: '体积占比(%)',
      dataIndex: 'volumePercent',
      key: 'volumePercent',
      render: (text) => `${(text * 100).toFixed(2)}%`,
    },
    {
      title: '托数',
      dataIndex: 'palletsNum',
      key: 'palletsNum',
    },
    {
      title: '箱数',
      dataIndex: 'pieceNum',
      key: 'pieceNum',
    },
    {
      title: '距离',
      dataIndex: 'distance',
      key: 'distance',
    },
    { title: '费用科目', dataIndex: 'feeName', key: 'feeName' },
    {
      title: '计费公式',
      dataIndex: 'priceExpStr',
      key: 'priceExpStr',
    },
    {
      title: '小计金额($)',
      dataIndex: 'amount',
      key: 'amount',
      align: 'right',
      render: (text: any, record: any) => {
        const amount = record?.amount;
        if (amount === -99999 || parseFloat(amount) === -99999) {
          return '询价';
        }
        return amount;
      },
    },
  ];

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="试算结果"
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        width={1250}
        destroyOnClose
        className="trial-calculation-modal"
        centered
      >
        <div className="header-section">
          <Title level={5} className="order-id-title">
            订单号: <Link>{orderInfo.no}</Link>
          </Title>
          <Row gutter={32}>
            <Col span={4}>
              <Statistic title="柜型" value={orderInfo.containerMode} />
            </Col>
            <Col span={4}>
              <Statistic title="总重量(KG)" value={orderInfo.weight} />
            </Col>
            <Col span={4}>
              <Statistic
                title="总重量(LB)"
                value={(orderInfo.weight * KG_TO_LB).toFixed(2)}
              />
            </Col>
            <Col span={4}>
              <Statistic title="总体积(CBM)" value={orderInfo.volume} />
            </Col>
            <Col span={4}>
              <Statistic title="托数(PLTS)" value={orderInfo.palletsNum} />
            </Col>
            <Col span={4}>
              <Statistic title="箱数(CTNS)" value={orderInfo.pieceNum} />
            </Col>
          </Row>
        </div>
        <Table
          columns={columns}
          dataSource={processedData}
          rowKey="uid"
          pagination={false}
          bordered
          scroll={{ y: 400 }}
          sticky
          summary={(pageData) => {
            let totalAmount = 0;
            pageData.forEach(({ amount }) => {
              if (typeof amount === 'number' && amount !== -99999) {
                totalAmount += amount;
              } else if (typeof amount === 'string' && amount !== '-99999') {
                const numericAmount = parseFloat(amount);
                if (!isNaN(numericAmount)) {
                  totalAmount += numericAmount;
                }
              }
            });
            return (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={9}>
                    <div className="summary-total-text">
                      <Text strong>总计</Text>
                    </div>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={9} align="right">
                    <Text strong type="danger">
                      {totalAmount.toFixed(2)}
                    </Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />
      </Modal>
    </>
  );
};

export default TrialCalculationModal;
