import React, { useRef, useState } from 'react';
import { Button, Modal, Space, Tabs, TabsProps } from 'antd';
import SuperTableMax from '@/components/SuperTableMax';
import { getBlnoList } from '@/services/overview';
import TrialCalculationModal from './TrialCalculationModal';
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  priceId?: string;
}
const TrialPartition = ({ btnText, btnType, priceId }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const actionRef = useRef<any>(null);
  const [State, setState] = useState('');
  const showModal = () => {
    setIsModalOpen(true);
  };
  
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const items: TabsProps['items'] = [
    {
      key: '0,1,-1',
      label: '全部',
    },
    {
      key: '0',
      label: '待审核',
    },
    {
      key: '1',
      label: '审核通过',
    },
    {
      key: '-1',
      label: '审核拒绝',
    },
  ];

  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    // clientId: {
    //   desc: '客户',
    //   type: 'client',
    //   value: '',
    // },
    // estimateArriveTime: {
    //   desc: '到港日期',
    //   type: 'dateTimeRange',
    //   startTime: '',
    //   endTime: '',
    // },
    // estimatePickupTime: {
    //   desc: '提柜日期',
    //   type: 'dateTimeRange',
    //   startTime: '',
    //   endTime: '',
    // },
    // form: {
    //   desc: '交货方式',
    //   type: 'select',
    //   multi: true,
    //   range: [
    //     { label: '整柜快递', value: 0 },
    //     { label: '整柜卡派', value: 1 },
    //     { label: '拼柜散货', value: 2 },
    //   ],
    // },
    // warehouseId: {
    //   desc: '交货仓库',
    //   type: 'overseas',
    //   value: '',
    // },
    // containerNo: {
    //   desc: '柜号',
    //   type: 'text',
    //   value: '',
    // },
    // state: {
    //   desc: '状态',
    //   type: 'select',
    //   multi: true,
    //   range: ProgressFilterStateType,
    //   value: '',
    // },
  };
  const refreshTable = () => {
    actionRef?.current?.refresh();
  };

  const columns = [
    {
      title: '单号',
      dataIndex: 'no',
      width: 150,
    },
    {
      title: '柜型',
      dataIndex: 'containerMode',
      width: 120,
    },
    {
      title: '柜号',
      dataIndex: 'containerNo',
      width: 135,
    },
    {
      title: '总重量（KG）',
      dataIndex: 'weight',
      width: 138,
    },
    {
      title: '总体积（CBM）',
      dataIndex: 'volume',
      width: 150,
    },
    {
      title: '预报件数',
      dataIndex: 'pieceNum',
      width: 138,
    },
    {
      title: '托数',
      dataIndex: 'palletsNum',
      width: 80,
      fieldFormat: (record: any) => {
        return `${
          (record?.palletsNum ? record?.palletsNum : 0) -
          (record?.unStoredPalletsNum ? record?.unStoredPalletsNum : 0)
        }/${record?.palletsNum}`;
      },
    },
  ];

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        width={1250}
        title="费用试算"
        open={isModalOpen}
        // onOk={handleOk}
        onCancel={handleCancel}
        footer={() => {
          return (
            <Space>
              <Button onClick={handleCancel}>取消</Button>
              <TrialCalculationModal
                btnText="试算"
                btnType="primary"
                priceId={priceId}
                blnoOrderId={selectedRow?.[0]?.id}
              />
            </Space>
          );
        }}
        destroyOnClose
        centered
      >
        <SuperTableMax
          columns={columns}
          request={async (params: any) => {
            const msg = await getBlnoList({
              start: (params.current - 1) * params.pageSize,
              len: params.pageSize,
              columnsFilter: params.columnsFilter || {},
              condition: {
                ...params.condition,
                approvalState: { value: State },
              },
            });
            return {
              data: msg?.data?.list || [],
              success: msg.status,
              total: msg?.data?.total,
            };
          }}
          rowSelection={(selectedRows: any) => {
            setSelectedRow(selectedRows);
          }}
          getRowIdFn={(record: any) => record?.data?.id}
          rowSelectionMode="singleRow"
          filters={filters}
          ref={actionRef}
          height={500}
          toolBarRender={() => {
            return (
              <div style={{ flex: 1 }}>
                <Tabs
                  items={items}
                  type={'line'}
                  onChange={(e: any) => {
                    setState(e);
                    refreshTable();
                  }}
                ></Tabs>
              </div>
            );
          }}
          toolBarRenderRight={() => <div> </div>}
          instanceId={'QingtiListV4-2'}
        />
      </Modal>
    </>
  );
};
export default TrialPartition;
