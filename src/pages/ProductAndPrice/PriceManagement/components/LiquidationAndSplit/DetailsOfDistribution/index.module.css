.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}
.basicInfo {
  margin-bottom: 20px;
}
.basicInfo :global .ant-pro-card-header {
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}
.basicInfo :global .ant-pro-card-body {
  padding: 24px;
}
.priceTable :global .ant-pro-card-header {
  padding: 16px 24px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}
.priceTable :global .ant-pro-card-body {
  padding: 24px;
}
.priceTable :global .ant-table-thead > tr > th {
  background: #fafafa;
  color: #262626;
  font-weight: 600;
}
.priceTable :global .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  .header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  .title {
    text-align: center;
  }
}
