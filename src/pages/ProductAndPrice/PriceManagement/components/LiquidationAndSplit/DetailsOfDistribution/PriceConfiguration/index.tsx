import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Button, Modal, Table, Select, Space, message } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { getServiceSubjectAPI } from '@/services/productAndPrice/api';
import { useRequest } from 'ahooks';
import EditPopUpBox from './EditPopUpBox';
import DeliveryFeePriceList from './DeliveryFeePriceList';

interface Props {
  btnText: any;
  btnType?: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  onOk?: (e: any) => void;
  edit?: boolean; // 是否可编辑
}

interface ChargeNameOption {
  value: string;
  label: string;
}

// 数据项的类型定义 - 已被扁平化
interface DataItem {
  key: string; // 每行的唯一标识
  groupId: number; // 用于标识同一计费条件的分组
  conditionExp?: string;
  feeCode?: string;
  feeName?: string;
  type: number;
  priceExp?: string;
  refPriceId?: string;
  refPriceName?: string;
}

const CHARGING_FORMULA_OPTIONS = [
  { label: '公式', value: 0 },
  { label: '价格表', value: 1 },
];

const PriceConfiguration = ({ btnText, onOk, record, edit }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [chargeNameOptions, setChargeNameOptions] = useState<
    ChargeNameOption[]
  >([]);
  const [dataSource, setDataSource] = useState<DataItem[]>([
    {
      key: '1',
      groupId: 1,
      type: 0,
    },
  ]);

  const showModal = useCallback(() => {
    if (edit && record) {
      const groupedByCondition = new Map<string, any[]>();
      const ungroupedItems: any[] = []; // 用于存放没有 conditionExp 的项目

      // 根据 conditionExp 分组
      record.forEach((item: any) => {
        if (item.conditionExp) {
          if (!groupedByCondition.has(item.conditionExp)) {
            groupedByCondition.set(item.conditionExp, []);
          }
          groupedByCondition.get(item.conditionExp)!.push(item);
        } else {
          // 没有 conditionExp 的项目单独存放
          ungroupedItems.push(item);
        }
      });

      let groupIdCounter = 1;
      const newData: DataItem[] = [];

      // 处理有 conditionExp 的分组
      groupedByCondition.forEach((group) => {
        const currentGroupId = groupIdCounter++;
        group.forEach((item) => {
          newData.push({
            ...item,
            key: item.id,
            groupId: currentGroupId,
          });
        });
      });

      // 处理没有 conditionExp 的独立项目
      ungroupedItems.forEach((item) => {
        newData.push({
          ...item,
          key: item.id,
          groupId: groupIdCounter++,
        });
      });

      setDataSource(newData);
      setIsModalOpen(true);
    } else {
      setIsModalOpen(true);
    }
  }, [edit, record]);

  const handleOk = useCallback(() => {
    if (dataSource.length === 0) {
      message.error('请至少添加一条价格配置');
      return;
    }

    // 增加重复计费项校验
    const groupFeeCodeMap = new Map<number, Set<string>>();
    for (const item of dataSource) {
      if (!groupFeeCodeMap.has(item.groupId)) {
        groupFeeCodeMap.set(item.groupId, new Set<string>());
      }
      const feeCodes = groupFeeCodeMap.get(item.groupId)!;
      if (item.feeCode) {
        if (feeCodes.has(item.feeCode)) {
          message.error(
            `序号 ${item.groupId} 的计费条件中存在重复的计费项，请修改`,
          );
          return;
        }
        feeCodes.add(item.feeCode);
      }
    }

    for (const item of dataSource) {
      if (!item.conditionExp) {
        message.error(`序号 ${item.groupId} 的计费条件未填写，请补充`);
        return;
      }
      if (!item.feeCode) {
        message.error(`序号 ${item.groupId} 的计费项名称未选择，请补充`);
        return;
      }
      const isPricingComplete = !!item.priceExp || !!item.refPriceId;
      if (!isPricingComplete) {
        message.error(`序号 ${item.groupId} 的计费公式未填写，请补充或删除`);
        return;
      }
    }

    onOk?.(dataSource);
    setIsModalOpen(false);
  }, [dataSource, onOk]);

  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  //获取计费名称
  const { run: getChargeName, loading: templateLoading } = useRequest(
    async (keyword?: string) => {
      const params: any = {
        start: 0,
        len: 100,
        condition: {
          counterpartyTypes: {
            value: 'CPDD',
          },
        },
      };
      if (keyword) {
        params.condition = {
          keyword: {
            value: keyword,
          },
          counterpartyTypes: {
            value: 'CPDD',
          },
        };
      }
      const res = await getServiceSubjectAPI(params);
      if (res.status && res?.data?.list) {
        const options = res.data.list.map((item: any) => ({
          value: item.code,
          label: item.tag,
        }));
        setChargeNameOptions(options);
      }
    },
    {
      debounceWait: 500,
      manual: true,
    },
  );

  useEffect(() => {
    if (isModalOpen) {
      getChargeName();
    }
  }, [isModalOpen]);

  // 生成唯一的Key
  const generateUniqueKey = () => `${Date.now()}-${Math.random()}`;

  // 添加新行（新的计费条件分组）
  const handleAddRow = useCallback(() => {
    const newGroupId =
      dataSource.length > 0
        ? Math.max(...dataSource.map((item) => item.groupId)) + 1
        : 1;
    const newItem: DataItem = {
      key: generateUniqueKey(),
      groupId: newGroupId,
      type: 0,
    };
    setDataSource((prev) => [...prev, newItem]);
    message.success('添加成功');
  }, [dataSource]);

  // 删除行 (单个计费项)
  const handleDeleteRow = useCallback((key: string) => {
    setDataSource((prev) => prev.filter((item) => item.key !== key));
    message.success('删除成功');
  }, []);

  // 更新行数据
  const handleUpdateRow = useCallback(
    (key: string, field: keyof DataItem, value: any) => {
      setDataSource((prev) => {
        const currentItem = prev.find((item) => item.key === key);
        if (!currentItem) return prev;

        // 如果更新的是计费项目，则同时更新名称
        if (field === 'feeCode') {
          const option = chargeNameOptions.find((opt) => opt.value === value);
          return prev.map((item) =>
            item.key === key
              ? { ...item, feeCode: value, feeName: option ? option.label : '' }
              : item,
          );
        }

        // 如果更新的是计费条件，则更新整个分组
        if (field === 'conditionExp') {
          return prev.map((item) =>
            item.groupId === currentItem.groupId
              ? { ...item, conditionExp: value }
              : item,
          );
        }

        // 更新类型时，清空另一类型的数据
        if (field === 'type') {
          const isFormula = value === 0;
          return prev.map((item) =>
            item.key === key
              ? {
                  ...item,
                  type: value,
                  priceExp: isFormula ? item.priceExp : undefined,
                  refPriceId: isFormula ? undefined : item.refPriceId,
                  refPriceName: isFormula ? undefined : item.refPriceName,
                }
              : item,
          );
        }

        // 否则，只更新当前行
        return prev.map((item) =>
          item.key === key ? { ...item, [field]: value } : item,
        );
      });
    },
    [chargeNameOptions],
  );

  // 在指定分组添加计费项
  const handleAddChargingItem = useCallback((recordKey: string) => {
    setDataSource((prev) => {
      const anchorItem = prev.find((item) => item.key === recordKey);
      if (!anchorItem) return prev;

      const newItem: DataItem = {
        key: generateUniqueKey(),
        groupId: anchorItem.groupId,
        conditionExp: anchorItem.conditionExp,
        feeCode: '',
        type: 0,
      };

      // 找到同组的最后一个元素，在其后插入
      let lastIndexInGroup = -1;
      for (let i = prev.length - 1; i >= 0; i--) {
        if (prev[i].groupId === anchorItem.groupId) {
          lastIndexInGroup = i;
          break;
        }
      }

      const newDataSource = [...prev];
      if (lastIndexInGroup !== -1) {
        newDataSource.splice(lastIndexInGroup + 1, 0, newItem);
      } else {
        newDataSource.push(newItem);
      }

      return newDataSource;
    });
  }, []);

  // 预处理数据以计算rowSpan，并为每一行计算可用的计费项选项
  const processedData = useMemo(() => {
    // 步骤1: 按 groupId 对已选的 feeCode进行分组
    const groupedFeeCodes = new Map<number, string[]>();
    dataSource.forEach((item) => {
      if (!item.feeCode) return;
      if (!groupedFeeCodes.has(item.groupId)) {
        groupedFeeCodes.set(item.groupId, []);
      }
      groupedFeeCodes.get(item.groupId)!.push(item.feeCode);
    });

    // 步骤2: 扁平化数据，并计算 rowSpan 和 availableOptions
    const groupedData: Record<number, DataItem[]> = {};
    dataSource.forEach((item) => {
      if (!groupedData[item.groupId]) {
        groupedData[item.groupId] = [];
      }
      groupedData[item.groupId].push(item);
    });

    return Object.values(groupedData).flatMap((group) =>
      group.map((item, index) => {
        // 计算当前行可用的选项
        const usedCodesInGroup = (groupedFeeCodes.get(item.groupId) || []).filter(
          (code) => code !== item.feeCode, // 排除自身
        );
        const availableOptions = chargeNameOptions.filter(
          (option) => !usedCodesInGroup.includes(option.value as string),
        );

        return {
          ...item,
          rowSpan: index === 0 ? group.length : 0,
          isFirstInGroup: index === 0,
          isLastInGroup: index === group.length - 1,
          availableOptions, // 将计算好的选项附加到行数据上
        };
      }),
    );
  }, [dataSource, chargeNameOptions]);

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'groupId',
      key: 'groupId',
      width: 35,
      onCell: (record: any) => ({
        rowSpan: record.rowSpan,
      }),
    },
    {
      title: (
        <Space>
          计费条件
          {/* <Tooltip title="若不设置条件系统将按照规则自动计算所有计费项">
            <InfoCircleOutlined style={{ color: 'rgba(0, 0, 0, 0.45)' }} />
          </Tooltip> */}
        </Space>
      ),
      dataIndex: 'conditionExp',
      key: 'conditionExp',
      width: 120,
      render: (text: string, record: DataItem) => (
        <>
          {record.conditionExp ? (
            <>
              <span>{record.conditionExp}</span>
              <DeleteOutlined
                onClick={() => {
                  handleUpdateRow(record.key, 'conditionExp', '');
                }}
                style={{ color: 'red', cursor: 'pointer', marginLeft: '10px' }}
              />
            </>
          ) : (
            <EditPopUpBox
              expType={70}
              onOk={(e: any) => {
                handleUpdateRow(record.key, 'conditionExp', e);
              }}
            />
          )}
        </>
      ),
      onCell: (record: any) => ({
        rowSpan: record.rowSpan,
      }),
    },
    {
      title: '计费项名称',
      dataIndex: 'feeCode',
      key: 'feeCode',
      width: 220,
      render: (text: string, record: any) => {
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space align="baseline">
              <Select
                style={{ width: 150 }}
                showSearch
                placeholder="请选择"
                value={text || undefined}
                onSearch={getChargeName}
                options={record.availableOptions} // 直接使用预先计算好的选项
                filterOption={false}
                loading={templateLoading}
                onChange={(value) =>
                  handleUpdateRow(record.key, 'feeCode', value)
                }
                allowClear
              />
              <DeleteOutlined
                style={{ color: 'red', cursor: 'pointer' }}
                onClick={() => {
                  // 仅当分组中只有最后一行时，才显示删除分组的确认框
                  const isLastItemInGroup =
                    dataSource.filter((item) => item.groupId === record.groupId)
                      .length === 1;
                  if (isLastItemInGroup) {
                    Modal.confirm({
                      title: '确认删除',
                      content: '确定要删除此计费条件下的所有计费项吗？',
                      okText: '确定',
                      cancelText: '取消',
                      onOk() {
                        setDataSource((prev) =>
                          prev.filter(
                            (item) => item.groupId !== record.groupId,
                          ),
                        );
                        message.success('删除成功');
                      },
                    });
                  } else {
                    handleDeleteRow(record.key);
                  }
                }}
              />
              {record.isLastInGroup && (
                <Button
                  type="link"
                  onClick={() => handleAddChargingItem(record.key)}
                  style={{ padding: '2px' }}
                >
                  添加计费项
                </Button>
              )}
            </Space>
          </Space>
        );
      },
    },
    {
      title: '计费公式',
      dataIndex: 'type',
      key: 'type',
      width: 200,
      render: (text: number, record: DataItem) => (
        <Space>
          {record.refPriceId || record.priceExp ? (
            <span>
              {record.refPriceName || record.priceExp}
              <DeleteOutlined
                onClick={() => {
                  handleUpdateRow(
                    record.key,
                    record.refPriceId ? 'refPriceId' : 'priceExp',
                    null,
                  );
                  handleUpdateRow(record.key, 'refPriceName', null);
                }}
                style={{
                  color: 'red',
                  cursor: 'pointer',
                  marginLeft: '10px',
                }}
              />
            </span>
          ) : (
            <Select
              style={{ width: 100 }}
              placeholder="公式"
              value={text}
              options={CHARGING_FORMULA_OPTIONS}
              onChange={(value) => handleUpdateRow(record.key, 'type', value)}
            />
          )}
          {record.type === 0 ? (
            <EditPopUpBox
              expType={80}
              onOk={(e: any) => {
                handleUpdateRow(record.key, 'priceExp', e);
              }}
            />
          ) : (
            <DeliveryFeePriceList
              btnText="引入价格表"
              btnType="link"
              onOk={(e: any) => {
                handleUpdateRow(record.key, 'refPriceId', e?.id);
                handleUpdateRow(record.key, 'refPriceName', e?.name);
              }}
            />
          )}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center' as const,
      render: (text: any, record: DataItem) => (
        <Button
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除此计费条件下的所有计费项吗？',
              okText: '确定',
              cancelText: '取消',
              onOk() {
                setDataSource((prev) =>
                  prev.filter((item) => item.groupId !== record.groupId),
                );
                message.success('删除成功');
              },
            });
          }}
        >
          删除
        </Button>
      ),
      onCell: (record: any) => ({
        rowSpan: record.rowSpan,
      }),
    },
  ];

  return (
    <>
      <a
        onClick={showModal}
        // style={{
        //   ...(edit ? { color: '#ff4071' } : {}),
        // }}
      >
        {btnText}
      </a>
      <Modal
        title="价格配置"
        width={1200}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        okText="确认"
        cancelText="取消"
        centered
        maskClosable={false}
      >
        <div style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={handleAddRow}>
            添加
          </Button>
          <span style={{ marginLeft: 16, color: '#666', fontSize: '12px' }}>
            共 {dataSource.length} 条记录
          </span>
        </div>
        <Table
          columns={columns}
          dataSource={processedData}
          pagination={false}
          bordered
          size="small"
          scroll={{ y: 300 }}
          locale={{ emptyText: '暂无数据，请点击"添加"按钮添加记录' }}
        />
      </Modal>
    </>
  );
};

export default PriceConfiguration;
