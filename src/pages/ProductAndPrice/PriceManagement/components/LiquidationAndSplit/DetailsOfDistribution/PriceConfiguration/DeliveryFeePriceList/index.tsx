import React, { useEffect, useState } from 'react';
import { Button, Modal, Input, Table, message } from 'antd';
import { useRequest } from 'ahooks';
import { getDeliveryPriceListAPI } from '@/services/financeApi';
import { currency_map } from '@/utils/constant';
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  onOk?: (e: any) => void;
}
const { Search } = Input;
const DeliveryFeePriceList = ({ btnText, btnType, onOk }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const { runAsync: getDeliveryPriceList } = useRequest(
    getDeliveryPriceListAPI,
    {
      manual: true,
    },
  );

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (selectedRowKeys.length > 0) {
      setIsModalOpen(false);
      onOk?.(selectedRowKeys[0]);
    } else {
      message.error('请选择价格表');
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (isModalOpen) {
      getDeliveryPriceList({
        len: 500,
        start: 0,
      }).then((res) => {
        if (res.status) {
          setDataSource(res.data.list);
        }
      });
    }
  }, [isModalOpen]);

  const handleSearch = (value: string) => {
    getDeliveryPriceList({
      condition: {
        keyword: {
          value: value,
        },
      },
      len: 500,
      start: 0,
    }).then((res) => {
      if (res.status) {
        setDataSource(res.data.list);
      }
    });
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '距离段',
      dataIndex: 'detailList',
      key: 'detailList',
      render: (text: any, record: any) => {
        return record?.detailList?.map((item: any) => {
          return item?.distanceName;
        });
      },
    },
    {
      title: '币种',
      dataIndex: 'currencyType',
      key: 'currencyType',
      render: (text: any, record: any) => {
        return currency_map[record?.currencyType];
      },
    },
  ];

  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="引用价格表"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
      >
        <Search
          className="mb-2 w-1/4"
          placeholder="搜索关键词"
          onSearch={handleSearch}
        />
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          scroll={{ y: 500 }}
          rowKey="id"
          rowSelection={{
            type: 'radio',
            onChange: (e: any, record: any) => {
              setSelectedRowKeys(record);
            },
          }}
        />
      </Modal>
    </>
  );
};
export default DeliveryFeePriceList;
