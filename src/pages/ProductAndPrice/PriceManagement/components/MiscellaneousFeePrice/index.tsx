import SuperTableMax from '@/components/SuperTableMax';
import {
  deleteExtraPriceAPI,
  getExtraPriceListAPI,
} from '@/services/financeApi';
import { currency_map } from '@/utils/constant';
import { Button, message, Popconfirm, Space } from 'antd';
import { useRef } from 'react';
import MiscellaneousPopUpBox from './MiscellaneousPopUpBox';
import { useRequest } from 'ahooks';

const MiscellaneousFeePrice = () => {
  const actionRef = useRef<any>();

  const { runAsync: deleteExtraPrice } = useRequest(deleteExtraPriceAPI, {
    manual: true,
  });

  const refresh = () => {
    actionRef.current?.reload();
  };

  const handleDelete = (id: string) => {
    deleteExtraPrice({
      id,
    }).then((res) => {
      if (res.status) {
        message.success('删除成功');
        actionRef.current?.reload();
      }
    });
  };

  const column = [
    {
      title: '费用名称',
      dataIndex: 'feeName',
    },
    {
      title: '服务项目',
      dataIndex: 'itemName',
    },
    {
      title: '结算币种',
      dataIndex: 'currencyType',
      fieldFormat: (value: any) => {
        return currency_map[value?.currencyType] || '-';
      },
    },
    {
      title: '计费条件',
      dataIndex: 'conditionExp',
    },
    {
      title: '计费公式',
      dataIndex: 'priceExp',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (text: any, record: any) => {
        return (
          <Space>
            <MiscellaneousPopUpBox
              btnText="编辑"
              btnType="link"
              record={record}
              refresh={refresh}
            />
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                handleDelete(record.id);
              }}
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  return (
    <div>
      <SuperTableMax
        columns={column}
        ref={actionRef}
        instanceId="miscellaneousFeePrice"
        request={async (params: any) => {
          const res = await getExtraPriceListAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: params.condition,
          });
          return {
            data: res?.data?.list || [],
            success: res.status,
            total: res?.data?.total,
          };
        }}
        getRowIdFn={(record: any) => record?.data?.id}
        isWarpTabs={true}
        filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}
        toolBarRender={() => {
          return (
            <MiscellaneousPopUpBox
              btnText="新建"
              btnType="primary"
              refresh={refresh}
            />
          );
        }}
      />
    </div>
  );
};

export default MiscellaneousFeePrice;
