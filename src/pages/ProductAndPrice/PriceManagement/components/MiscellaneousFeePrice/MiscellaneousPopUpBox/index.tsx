import React, { useEffect, useState } from 'react';
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Tooltip,
  Space,
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { CurrencyType } from '@/shared/EnumerationTypes';
import EditPopUpBoxMax from '../EditPopUpBoxMax';
import { saveExtraPriceAPI } from '@/services/financeApi';
import { useRequest } from 'ahooks';
import { getServiceSubjectAPI } from '@/services/productAndPrice/api';

const { TextArea } = Input;
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  record?: any;
  refresh?: any;
}
const MiscellaneousPopUpBox = ({
  btnText,
  btnType,
  record,
  refresh,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const isEdit = btnText === '编辑';
  const conditionExp = Form.useWatch('conditionExp', form);
  const priceExp = Form.useWatch('priceExp', form);
  const [chargeNameOptions, setChargeNameOptions] = useState([]);

  useEffect(() => {
    if (isEdit && record && isModalOpen) {
      form.setFieldsValue({
        ...record,
        conditionExp: record.conditionExp || undefined,
        priceExp: record.priceExp || undefined,
      });
    }
  }, [isEdit, record, isModalOpen, form]);

  //获取计费名称
  const { run: getChargeName, loading: templateLoading } = useRequest(
    async (keyword?: string) => {
      const params: any = {
        start: 0,
        len: 100,
        condition: {
          counterpartyTypes: {
            value: 'CPDD',
          },
        },
      };
      if (keyword) {
        params.condition = {
          keyword: {
            value: keyword,
          },
          counterpartyTypes: {
            value: 'CPDD',
          },
        };
      }
      const res = await getServiceSubjectAPI(params);
      if (res.status && res?.data?.list) {
        const options = res.data.list.map((item: any) => ({
          value: item.code,
          label: item.tag,
        }));
        setChargeNameOptions(options);
      }
    },
    {
      debounceWait: 500,
      manual: true,
    },
  );

  const showModal = () => {
    setIsModalOpen(true);
    getChargeName();
  };

  const { runAsync: saveExtraPrice } = useRequest(saveExtraPriceAPI, {
    manual: true,
  });

  const handleOk = async () => {
    const values = await form.validateFields();
    const params = {
      ...values,
      id: record?.id,
    };
    saveExtraPrice(params).then((res: any) => {
      if (res.status) {
        refresh?.();
        setIsModalOpen(false);
        form.resetFields();
      }
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={isEdit ? '编辑杂费' : '新建杂费'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        okText="确认"
        cancelText="取消"
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          name="miscellaneous_fee_form"
          initialValues={{
            currencyType: 20,
          }}
          size="middle"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="feeCode"
                label="费用名称"
                rules={[{ required: true, message: '请输入费用名称' }]}
              >
                <Select
                  style={{ width: '100%' }}
                  showSearch
                  placeholder="请选择"
                  value={record?.feeCode || undefined}
                  onSearch={getChargeName}
                  options={chargeNameOptions}
                  filterOption={false}
                  loading={templateLoading}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="itemName" label="服务项目">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={
                  <span>
                    计费条件{' '}
                    <Tooltip title="若不设置条件，系统将按计费公式自动计算">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </span>
                }
                required
              >
                <Space>
                  {conditionExp && (
                    <span style={{ marginLeft: '10px' }}>{conditionExp}</span>
                  )}
                  <EditPopUpBoxMax
                    btnText={conditionExp ? '修改' : '编辑'}
                    initialValue={conditionExp}
                    expType={70}
                    onOk={(e: any) => {
                      form.setFieldsValue({
                        conditionExp: e,
                      });
                    }}
                  />
                </Space>
                <Form.Item
                  name="conditionExp"
                  noStyle
                  rules={[{ required: true, message: '请编辑计费条件' }]}
                >
                  <Input type="hidden" />
                </Form.Item>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="计费公式" required>
                <Space>
                  {priceExp && (
                    <span style={{ marginLeft: '10px' }}>{priceExp}</span>
                  )}
                  <EditPopUpBoxMax
                    btnText={priceExp ? '修改' : '编辑'}
                    initialValue={priceExp}
                    expType={80}
                    onOk={(e: any) => {
                      form.setFieldsValue({
                        priceExp: e,
                      });
                    }}
                  />
                </Space>
                <Form.Item
                  name="priceExp"
                  noStyle
                  rules={[{ required: true, message: '请编辑计费公式' }]}
                >
                  <Input type="hidden" />
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="currencyType"
                label="计费币种"
                rules={[{ required: true, message: '请选择计费币种' }]}
              >
                <Select options={CurrencyType} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="remark" label="备注">
                <TextArea rows={4} placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default MiscellaneousPopUpBox;
