import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Input, Modal, Row, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
const { TextArea } = Input;
import classNames from 'classnames';
import { validateRuleExpAPI } from '@/services/financeApi';

interface IButtonItem {
  id: number;
  name: string;
  value: string;
  tip?: string;
}

interface EditPopUpBoxProps {
  onOk: (value: string) => void;
  text?: string;
  businessList?: IButtonItem[];
  btnText?: string;
  initialValue?: string;
  expType?: 70 | 80 | undefined;
}

interface ICheckResult {
  valid?: boolean;
  msg?: string;
}

const calculationList: IButtonItem[] = [
  { id: 1, name: '+', value: '+' },
  { id: 2, name: '-', value: '-' },
  { id: 3, name: 'x', value: '×' },
  { id: 4, name: '÷', value: '÷' },
  { id: 5, name: '不等于', value: '不等于' },
  { id: 6, name: '>', value: '>' },
  { id: 7, name: '≥', value: '≥' },
  { id: 8, name: '=', value: '=' },
  { id: 9, name: '<', value: '<' },
  { id: 10, name: '≤', value: '≤' },
  { id: 11, name: '（', value: '（' },
  { id: 12, name: '）', value: '）' },
  { id: 13, name: '或者', value: '或者' },
  { id: 14, name: '并且', value: '并且' },
  { id: 15, name: '空格', value: '  ' },
  {
    id: 16,
    name: '向上进位',
    value: '向上进位',
    tip: '向上进位（数值，基数）;例：向上进位(13.3,0.5)=13.5',
  },
  {
    id: 17,
    name: '四舍五入进位',
    value: '四舍五入进位',
    tip: '四舍五入进位（数值,位数）；例：四舍五入进位（15.3345，3）=15.335 ',
  },
  {
    id: 18,
    name: '取最大值',
    value: '取最大值',
    tip: '取最大值（数值,数值,...）；例：取最大值（12.4,13.5）=13.5',
  },
  { id: 19, name: '占位', value: '占位' },
  { id: 20, name: '占位', value: '占位' },
];

const defaultBusinessList: IButtonItem[] = [
  { id: 1, name: '[派送方式]', value: '[派送方式]', tip: '' },
  { id: 2, name: '[货物重量]', value: '[货物重量]' },
  { id: 3, name: '[货物体积]', value: '[货物体积]' },
  { id: 4, name: '[箱数]', value: '[箱数]' },
  { id: 5, name: '[托数]', value: '[托数]' },
  { id: 6, name: '[总体积]', value: '[总体积]' },
  { id: 7, name: '[总重量]', value: '[总重量]' },
  { id: 7, name: '[操作数量]', value: '[操作数量]' },
  { id: 8, name: '占位', value: '占位' },
  { id: 9, name: '占位', value: '占位' },
  { id: 10, name: '占位', value: '占位' },
];

const ButtonGrid = ({
  title,
  items,
  onItemClick,
}: {
  title: string;
  items: readonly IButtonItem[];
  onItemClick: (value: string) => void;
}) => (
  <div className={styles.warp}>
    <div className={styles.title}>{title}</div>
    <div style={{ background: '#FBFBFB', padding: 8 }}>
      <Row gutter={[0, 16]} style={{ marginBottom: 2 }}>
        {items.map((item) => (
          <Col flex={1} key={item.id}>
            <Tooltip title={item?.tip}>
              <div
                onClick={() => {
                  onItemClick(item.value);
                }}
                className={classNames(
                  item.name === '占位' ? styles['no-btn'] : styles.btn,
                )}
              >
                <span>{item.name === '占位' ? null : item.name}</span>
              </div>
            </Tooltip>
          </Col>
        ))}
      </Row>
    </div>
  </div>
);

const EditPopUpBoxMax = ({
  onOk,
  text,
  businessList = [],
  btnText = '编辑',
  initialValue,
  expType,
}: EditPopUpBoxProps) => {
  const [value, setValue] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (isModalOpen) {
      setValue(initialValue || text || '');
    }
  }, [isModalOpen, initialValue, text]);

  const finalBusinessList =
    businessList.length > 0 ? businessList : defaultBusinessList;

  /* 语法检测 */
  const [checkResult, setCheckResult] = useState<ICheckResult>({});
  const checkRule = async () => {
    try {
      const { status, data } = await validateRuleExpAPI({
        ruleExp: value,
        ...(expType && { expType }),
      });
      if (status) {
        setCheckResult(data);
      }
    } catch (error) {
      console.error('语法检测接口报错', error);
    }
  };
  const reset = () => {
    setValue('');
    setCheckResult({});
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    onOk(value);
    setIsModalOpen(false);
    reset();
  };
  const handleCancel = () => {
    reset();
    setIsModalOpen(false);
  };
  const handleManualBilling = () => {
    onOk('手动计费');
    setIsModalOpen(false);
    reset();
  };

  useEffect(() => {
    setCheckResult({});
  }, [value]);

  return (
    <>
      <Button type="link" onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="条件公式设置"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        // maskClosable={false}
        footer={[
          <div
            key={1}
            style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}
          >
            <Button
              type="primary"
              disabled={!checkResult?.valid}
              onClick={handleOk}
            >
              确定
            </Button>
            <Tooltip
              title="默认为“系统自动计费”，处理完成后将判断是否满足计费条件，然后根据计费公式自动计算费用；
“手动计费”，需通过人工方式在WMS输入具体的费用和计费数量。"
            >
              <Button type="primary" onClick={handleManualBilling}>
                手动计费
              </Button>
            </Tooltip>
          </div>,
        ]}
      >
        <ButtonGrid
          title="计算规则"
          items={calculationList}
          onItemClick={(v) => setValue((prev) => prev + v)}
        />
        <ButtonGrid
          title="业务属性"
          items={finalBusinessList}
          onItemClick={(v) => setValue((prev) => prev + v)}
        />

        <div className={styles.warp}>
          <div className={classNames(styles.title, styles.titbox)}>
            <div>预览</div>
            <div>
              <Button type="link" disabled={!value} onClick={reset}>
                重置
              </Button>
              <Button type="link" disabled={!value} onClick={checkRule}>
                语法检测
              </Button>
            </div>
          </div>
          <div style={{ padding: 8 }}>
            <TextArea
              value={value}
              onChange={(e: any) => {
                setValue(e.target.value);
              }}
            />
            {!checkResult?.valid && checkResult?.msg && value ? (
              <div style={{ marginTop: 12 }}>
                <Alert message={checkResult?.msg} type="warning" />
              </div>
            ) : null}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default EditPopUpBoxMax;
