import { testStoragePriceAPI } from '@/services/financeApi';
import { formatTimes } from '@/utils/format';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
  Table,
} from 'antd';
import { useState } from 'react';
import styles from './index.less';
const Trial = ({ btnText, btnType, formulaMode, priceId }: any) => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [dataSource, setDataSource] = useState<any>([]);
  const [detail, setDetail] = useState<any>({});
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => form.submit();

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };
  const onFinish = async (value: any) => {
    try {
      const { status, data } = await testStoragePriceAPI({
        ...value,
        inboundTime: formatTimes(value?.inboundTime),
        priceId,
      });
      if (status) {
        setDataSource([
          ...data?.details?.map((item: any) => ({
            ...item,
            rule: `库龄段[${item?.dayStart},${item?.dayEnd}]`,
            name: '基础仓租费',
            day: item?.totalDays,
          })),
          ...data?.peakSeasonSurcharge.map((item: any) => ({
            ...item,
            rule: `旺季月份[${item?.year}-${item?.month}]`,
            name: '旺季附加费',
            day: item?.days,
          })),
        ]);
        setDetail(data);
        message.success('试算成功');
        setIsModalOpen(false);
        form.resetFields();
        setOpen(true);
      }
    } catch (error) {}
  };
  const priceUnitEnu: any = {
    6: '总体积（CBM）',
    9: '总箱数',
    10: '总托数',
  };
  const columns = [
    {
      title: '费用细项',
      dataIndex: 'name',
    },
    {
      title: '库龄（天）',
      dataIndex: 'day',
    },
    {
      title: '命中规则',
      dataIndex: 'rule',
    },
    {
      title: '价格（$/CBM/天）',
      dataIndex: 'price',
    },
    {
      title: '金额（$）',
      dataIndex: 'amount',
    },
  ];
  return (
    <>
      <Button type="primary" onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={btnText}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={400}
        destroyOnClose
        okText="试算"
      >
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                label={priceUnitEnu[formulaMode]}
                name="number"
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input
                  placeholder="请输入"
                  type="number"
                  style={{ width: '280px' }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="入库时间"
                name="inboundTime"
                rules={[{ required: true, message: '请选择入库时间' }]}
              >
                <DatePicker
                  //   showTime
                  placeholder="请选择"
                  style={{ width: '280px' }}
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                label="库龄（天）"
                name="stockAge"
                rules={[{ required: true, message: '请输入库龄' }]}
              >
                <Input
                  placeholder="请输入"
                  type="number"
                  style={{ width: '280px' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      {/* 试算结果 */}
      <Modal
        title="仓租费试算结果"
        footer={false}
        destroyOnClose
        open={open}
        width={800}
        onCancel={() => {
          setOpen(false);
          setDetail({});
          setDataSource([]);
        }}
      >
        <div className={styles.trial_result}>
          <div>
            <span>{detail?.number}</span>
            <span>{priceUnitEnu[formulaMode]}</span>
          </div>
          <div>
            <span>{detail?.inboundTime}</span>
            <span>入库时间</span>
          </div>
          <div>
            <span>{detail?.stockAge}</span>
            <span>库龄（天）</span>
          </div>
          <div>
            <span>{detail?.totalAmount}</span>
            <span>仓租总额（$）</span>
          </div>
        </div>
        <h4 style={{ marginTop: '30px', marginBottom: '20px' }}>费用明细</h4>
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          scroll={{ y: 400 }}
        />
      </Modal>
    </>
  );
};
export default Trial;
