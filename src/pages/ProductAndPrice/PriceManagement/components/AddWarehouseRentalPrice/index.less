.container {
  background-color: #f5f5f5;
}
.stickyHeader {
  position: sticky;
  z-index: 1000;
  top: 0;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
}
.title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.content {
  background-color: #fff;
}
.boundary {
  width: 1px;
  background-color: #e8e3e3;
}
//   title: {
//     margin: 0,
//     fontSize: '16px',
//     fontWeight: 500,
//   },
//   content: {
//     backgroundColor: '#fff',
//   },
