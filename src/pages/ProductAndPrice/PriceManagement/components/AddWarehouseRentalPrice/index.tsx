import { ProCard } from '@ant-design/pro-components';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
  Table,
} from 'antd';
import { useContext, useEffect, useState } from 'react';

import { useLocation } from 'umi';
import { CloseOutlined } from '@ant-design/icons';
import Trial from '../Trial';
import { saveStoragePriceAPI } from '@/services/financeApi';
const { Option } = Select;
import STYLES from './index.less';
import { KeepAliveTabContext } from '@/layouts/context';
const AddWarehouseRentalPrice = () => {
  const [form] = Form.useForm();
  const { state }: any = useLocation();
  const { closeTab } = useContext(KeepAliveTabContext);
  const [loading, setLoading] = useState(false);
  const [formulaMode, setFormulaMode] = useState(6);
  const [peakSeasonMonth, setPeakSeasonMonth] = useState([]);
  const [storageData, setStorageData] = useState<any>([]);
  const [additional, setAdditional] = useState<any>([]);
  const [priceUnit, setPriceUnit] = useState(1);
  // 保存处理
  const handleSave = async () => {
    const values = await form.validateFields();
    if (!storageData?.length) return message.error('请添加基础仓租规则');
    // if (!additional?.length) return message.error('请选择旺季附加费规则');
    storageData.forEach((item: any) => {
      delete item?.id;
    });
    const storageFlag = storageData?.every(
      (item: any) => item?.dayStart >= 0 && item?.dayEnd && item?.price >= 0,
    );
    if (!storageFlag)
      return message.warning('库龄段（天）和基础单价 不能为空并且不能为0');
    const additionalFlag = additional.every(
      (item: any) => item?.highAdditional && item?.highAdditional > 0,
    );
    if (!additionalFlag)
      return message.warning('旺季附加费不能为空，并且不能为0或者负数');
    const peakSeasonConfig = additional.reduce((acc: any, item: any) => {
      acc[item.month] = item.highAdditional;
      delete item?.id;
      return acc;
    }, {});
    try {
      setLoading(true);
      const { status } = await saveStoragePriceAPI({
        ...values,
        id: state?.id,
        peakSeasonMonth: peakSeasonMonth?.join(','),
        peakSeasonConfig,
        priceDetailList: storageData,
      });
      if (status) {
        message.success('保存成功');
        setPriceUnit(1);
        form.resetFields();
        setPeakSeasonMonth([]);
        setStorageData([]);
        setAdditional([]);
        history.back();
        closeTab();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (state?.id) {
      setPriceUnit(state?.priceUnit);
      setFormulaMode(state?.priceBy);
      form.setFieldsValue({ ...state });
      setPeakSeasonMonth(
        state?.peakSeasonMonth
          ?.split(',')
          .filter(Boolean)
          ?.map((item: any) => Number(item)),
      );
      setStorageData(
        state?.priceDetailList?.map((item: any, i: number) => ({
          ...item,
          id: `${i}_index`,
        })),
      );
      const data = Object.entries(state?.peakSeasonConfig ?? {}).map(
        ([month, highAdditional], index) => ({
          month: Number(month), // 确保 month 是数字（如果键是字符串）
          highAdditional,
          id: `${index}_index`, // 生成 id
        }),
      );
      setAdditional(data);
    }
  }, [state?.id]);
  // 取消处理
  const handleCancel = () => {
    history.back();
    setPriceUnit(1);
    form.resetFields();
    setPeakSeasonMonth([]);
    setStorageData([]);
    setAdditional([]);
    closeTab();
  };
  const highColumns = [
    {
      title: '旺季月份',
      dataIndex: 'month',
    },
    {
      title: '旺季附加费',
      dataIndex: 'highAdditional',
      render: (_: any, recode: any, index: number) => {
        return (
          <Input
            type="number"
            value={recode?.highAdditional}
            onChange={(e) => {
              additional[index].highAdditional = Number(e.target.value);
              setAdditional([...additional]);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: '120px',
      render: (_: any, recode: any) => {
        return (
          <div
            style={{ color: 'red', cursor: 'pointer' }}
            onClick={() => {
              const newData = additional.filter(
                (item: any) => item?.id !== recode?.id,
              );
              setAdditional([...newData]);
              const newMonth = newData.map((item: any) => item?.month);
              setPeakSeasonMonth(
                peakSeasonMonth?.filter((item: any) => newMonth.includes(item)),
              );
            }}
          >
            <CloseOutlined /> &nbsp;删除
          </div>
        );
      },
    },
  ];
  const storageColumns = [
    {
      title: '库龄段（天）',
      dataIndex: 'day',
      render: (_: any, recode: any, index: number) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Input
              placeholder="输入最小天数"
              type="number"
              value={recode?.dayStart}
              onChange={(e) => {
                storageData[index].dayStart = Number(e?.target?.value);
                setStorageData([...storageData]);
              }}
            />
            <span style={{ margin: '0 10px' }}>-</span>
            <Input
              placeholder="输入最大天数"
              type="number"
              value={recode?.dayEnd}
              onChange={(e) => {
                if (Number(e?.target?.value) <= storageData[index].dayStart)
                  message.error('最大天数要大于最小天数');
                storageData[index].dayEnd = Number(e?.target?.value);
                setStorageData([...storageData]);
              }}
            />
          </div>
        );
      },
    },
    {
      title: '基础单价',
      dataIndex: 'highAdditional',
      render: (_: any, recode: any, index: number) => {
        return (
          <Input
            type="number"
            placeholder="请输入"
            value={recode?.price}
            onChange={(e) => {
              const value = e.target.value;
              // 验证输入是否是合法数字（包括小数）
              if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                storageData[index].price = value;
                setStorageData([...storageData]);
              }
              // storageData[index].price = Number(e?.target?.value);
              // setStorageData([...storageData]);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: '120px',
      render: (_: any, recode: any) => {
        return (
          <div
            style={{ color: 'red', cursor: 'pointer' }}
            onClick={() => {
              const newData = storageData.filter(
                (item: any) => item?.id !== recode?.id,
              );
              setStorageData([...newData]);
            }}
          >
            <CloseOutlined /> &nbsp;删除
          </div>
        );
      },
    },
  ];
  const priceUnitEnu: any = {
    6: '/CBM/天',
    9: '/箱/天',
    10: '/托/天',
  };
  console.log(storageData, 'storageDatastorageData');
  console.log(additional, 'additionaladditional');

  return (
    <>
      <div className={STYLES.container}>
        {/* 固定的标题栏 */}
        <div className={STYLES.stickyHeader}>
          <h3 className={STYLES.title}>{`${
            state?.id ? '修改' : '新建'
          }仓租费价格表`}</h3>
          <Space>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" loading={loading} onClick={handleSave}>
              保存
            </Button>
          </Space>
        </div>

        <div className={STYLES.content}>
          {/* 基础信息表单 */}
          <ProCard title="基础信息">
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                priceBy: 6,
                containStackingDay: true,
                currencyType: 20,
                priceUnit: 1,
              }}
            >
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="名称"
                    name="name"
                    rules={[{ required: true, message: '请输入名称' }]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="计费币种"
                    name="currencyType"
                    rules={[{ required: true, message: '请选择计费币种' }]}
                  >
                    <Select
                      placeholder="请选择"
                      options={[
                        {
                          label: '人民币',
                          value: 10,
                        },
                        {
                          label: '美元',
                          value: 20,
                        },
                        {
                          label: '加币',
                          value: 30,
                        },
                        {
                          label: '欧元',
                          value: 40,
                        },
                        {
                          label: '日元',
                          value: 50,
                        },
                        {
                          label: '英镑',
                          value: 70,
                        },
                      ]}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="计算方式"
                    name="priceBy"
                    rules={[{ required: true, message: '请选择分区模板' }]}
                  >
                    <Select
                      placeholder="请选择"
                      onChange={(e) => setFormulaMode(e)}
                    >
                      <Option value={6}>按总体积</Option>
                      <Option value={9}>按箱数</Option>
                      <Option value={10}>按托盘数</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="计算单位"
                    name="priceUnit"
                    rules={[{ required: true, message: '请选择材积单位' }]}
                  >
                    <div style={{ display: 'flex' }}>
                      <Input
                        style={{
                          width: '50%',
                          borderRadius: '0px',
                        }}
                        // defaultValue={1}
                        value={priceUnit}
                        placeholder="请输入"
                        type="number"
                        onChange={(e: any) => {
                          setPriceUnit(e?.target?.value);
                        }}
                      />
                      <div
                        style={{
                          width: '50%',
                          backgroundColor: 'rgba(239,239,239,1)',
                          display: 'flex',
                          alignItems: 'center',
                          paddingLeft: '10px',
                        }}
                      >
                        {priceUnitEnu[formulaMode]}
                      </div>
                    </div>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="含上架日"
                    name="containStackingDay"
                    rules={[{ required: true, message: '请选择距离单位' }]}
                  >
                    <Radio.Group
                    //   style={{ width: '100%', display: 'flex' }}
                    //   buttonStyle="solid"
                    >
                      <Radio value={true} style={{ flex: 1 }}>
                        是
                      </Radio>
                      <Radio value={false} style={{ flex: 1 }}>
                        否
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col span={18}>
                  <Form.Item label="备注" name="remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </ProCard>
        </div>
        <ProCard className="mt-20px" title="">
          <ProCard title="基础仓租规则">
            <div style={{ marginBottom: '20px' }}>
              <Button
                style={{ marginRight: '20px' }}
                onClick={() => {
                  storageData.splice(storageData?.length, 0, {
                    // day: '',
                    dayStart: undefined,
                    dayEnd: undefined,
                    price: undefined,
                    id: `${storageData?.length}_index`,
                  });
                  setStorageData([...storageData]);
                }}
              >
                添加
              </Button>
              {state?.id && (
                <Trial
                  btnText="试算"
                  formulaMode={formulaMode}
                  priceUnitEnu={priceUnitEnu}
                  priceId={state?.id}
                />
              )}
            </div>
            <Table
              columns={storageColumns}
              dataSource={storageData}
              // dataSource={[]}
              pagination={false}
              scroll={{ y: 400 }}
              rowKey={'id'}
            />
          </ProCard>
          <div className={STYLES.boundary}></div>
          <ProCard title="旺季附加费规则">
            <Select
              placeholder="请选择"
              style={{ width: '400px', marginBottom: '20px' }}
              mode="multiple"
              allowClear
              value={peakSeasonMonth}
              options={[
                {
                  label: '1月',
                  value: 1,
                },
                {
                  label: '2月',
                  value: 2,
                },
                {
                  label: '3月',
                  value: 3,
                },
                {
                  label: '4月',
                  value: 4,
                },
                {
                  label: '5月',
                  value: 5,
                },
                {
                  label: '6月',
                  value: 6,
                },
                {
                  label: '7月',
                  value: 7,
                },
                {
                  label: '8月',
                  value: 8,
                },
                {
                  label: '9月',
                  value: 9,
                },
                {
                  label: '10月',
                  value: 10,
                },
                {
                  label: '11月',
                  value: 11,
                },
                {
                  label: '12月',
                  value: 12,
                },
              ]}
              onChange={(e) => {
                setPeakSeasonMonth(e?.filter(Boolean));
                // ({
                // month: item,
                // highAdditional: undefined,
                // id: `${index}_index`,
                // })
                // const monthData = additional.map((item:any) => item?.month)
                setAdditional(
                  e?.map((item: any, index: number) => {
                    if (
                      additional.map((ele: any) => ele?.month).includes(item)
                    ) {
                      const newItem = additional?.find(
                        (ele: any) => ele?.month === item,
                      );
                      return {
                        ...newItem,
                        highAdditional: newItem?.highAdditional,
                      };
                    }
                    return {
                      month: item,
                      highAdditional: undefined,
                      id: `${index}_index`,
                    };
                  }),
                );
              }}
            />
            <Table
              columns={highColumns}
              dataSource={additional}
              // dataSource={[]}
              pagination={false}
              scroll={{ y: 400 }}
              rowKey={'id'}
            />
          </ProCard>
        </ProCard>
      </div>
    </>
  );
};
export default AddWarehouseRentalPrice;
