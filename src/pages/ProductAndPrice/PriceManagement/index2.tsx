import { useRef, useState, useMemo, useEffect } from 'react';
// import styles from './index.less';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
// import AccessCard from '@/AccessCard';
// import Price from './Price';
// import Additional from './Additional';
// import Fuel from './Fuel';
// import ServiceFee from './serviceFee';
import LiquidationAndSplit from './components/LiquidationAndSplit';
import DeliveryFeeSchedule from './components/DeliveryFeeSchedule';
import WarehouseRentalPrice from './components/WarehouseRentalPrice';
import MiscellaneousFeePrice from './components/MiscellaneousFeePrice';
import TabsType from '@/components/SuperTables/components/TabsType';
// import ServiceSubject from './ServiceSubject';
// import ServiceSubject2 from './ServiceSubject2';
// import { Result } from 'antd';

const PriceManagement2 = () => {
  const [toList] = usePermissionFiltering();

  const newList: any = useMemo(() => {
    return toList([
      {
        key: '提拆派价格表',
        label: '提拆派价格表',
      },
      {
        key: '派送费价格表',
        label: '派送费价格表',
      },
      {
        key: '仓租价格表',
        label: '仓租价格表',
      },
      {
        key: '杂费价格表',
        label: '杂费价格表',
      },
    ]);
  }, []);
  const actionRef = useRef<any>();
  const [activeKey, setActiveKey] = useState<string>(newList[0]?.key);
  // /* 删除 附加费 */
  // const handleDelete = (record: any) => {
  //   deleteSurcharge({ id: record.id }).then((res) => {
  //     if (res.status) {
  //       message.success('删除成功');
  //       actionRef.current?.reloadAndRest?.();
  //     }
  //   });
  // };

  // /* 附加费 修改 */
  // const handleEdit = (record: any) => {
  //   history.push(`/productAndPrice/priceManagement/createSurchargeTemplate`, {
  //     ...record,
  //   });
  // };

  const handleTabChange = (key: string) => {
    localStorage.setItem('activeKey13', key as string);
    setActiveKey(key);
    actionRef.current?.reload?.();
  };

  useEffect(() => {
    const key = localStorage.getItem('activeKey13');
    if (key !== null) {
      setActiveKey(key);
    }
  }, []);

  useEffect(() => {
    if (newList.length) {
      localStorage.setItem('activeKey13', newList[0]?.key);
    }
  }, [newList]);

  return (
    <>
      {/* <AccessCard
        accessible={[':Product:Fee']}
        fallback={[<Result key={1} status="403" title="403" />]}
      > */}
      <TabsType
        defaultActiveKey={activeKey}
        items={newList}
        onChange={(key) => handleTabChange(key as string)}
      />
      {activeKey === '提拆派价格表' && <LiquidationAndSplit />}
      {activeKey === '派送费价格表' && <DeliveryFeeSchedule />}
      {activeKey === '仓租价格表' && <WarehouseRentalPrice />}
      {activeKey === '杂费价格表' && <MiscellaneousFeePrice />}
      {/* </AccessCard> */}
    </>
  );
};

export default PriceManagement2;
