import React, { useContext, useEffect, useState } from 'react';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
} from 'antd';
import { ProCard, ProFormSelect } from '@ant-design/pro-components';
import TableComponent from './TableComponent';
import { getWarehouseListAPI } from '@/services/booking';
import { saveDeliveryPrice } from '@/services/productAndPrice/api';
import TrialPartition from './ZhuJian/TrialPartition';
import { useLocation } from '@@/exports';
import { KeepAliveTabContext } from '@/layouts/context';

const { Option } = Select;

const STYLES = {
  container: {
    backgroundColor: '#f5f5f5',
  },
  stickyHeader: {
    position: 'sticky' as const,
    top: 0,
    zIndex: 1000,
    backgroundColor: '#fff',
    borderBottom: '1px solid #e8e8e8',
    padding: '16px 24px',
    display: 'flex',
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  title: {
    margin: 0,
    fontSize: '16px',
    fontWeight: 500,
  },
  content: {
    backgroundColor: '#fff',
  },
} as const;

const DetailsOfDistribution = () => {
  const [form] = Form.useForm();
  const { closeTab } = useContext(KeepAliveTabContext);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<any>([]);
  const location = useLocation();
  const { state }: any = location;

  console.log('state', state?.record?.detailList);
  useEffect(() => {
    if (state?.record) {
      const { warehouseList, ...rest } = state?.record;
      form.setFieldsValue(rest);
      setTableData(
        state?.record?.detailList?.map((item: any) => ({
          ...item,
          warehouseList: item?.fbaCodes?.split(',').map((item: any) => {
            return {
              warehouseName: item,
              fbaCode: item,
            };
          }),
        })),
      );
    } else {
    }
  }, []);
  // 保存处理
  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      console.log('table数据', tableData);
      console.log('表单数据:', values);
      function validateTableData(tableData) {
        for (const row of tableData) {
          if (!row['distanceName'] || row['distanceName'] === '') {
            message.error('距离名称不能为空');
            return true;
          }
          if (!row['priceExp'] || row['priceExp'] === '') {
            message.error('价格配置不能为空');
            return true;
          }
          if (
            row['priceBy'] === 7 &&
            (!row['distanceBegin'] || row['distanceBegin'] === '')
          ) {
            message.error('开始距离不能为空');
            return true;
          }
          if (
            row['priceBy'] === 7 &&
            (!row['distanceEnd'] || row['distanceEnd'] === '')
          ) {
            message.error('结束距离不能为空');
            return true;
          }
          if (
            row['priceBy'] === 8 &&
            (!row['warehouseList'] || row['warehouseList'].length === 0)
          ) {
            message.error('仓库不能为空');
            return true;
          }
        }
        return false;
      }

      const isValid = validateTableData(tableData);
      console.log('validation result', isValid);
      if (isValid) {
        return; // 只有验证通过时才返回
      }

      const detailList = tableData?.map((item: any) => {
        const { id, warehouseList, distanceBegin, distanceEnd, ...rest } = item;
        const fbaCodes = item?.warehouseList
          ?.map((item: any) => item.fbaCode || item.warehouseName)
          .join(',');
        console.log('rest', rest);
        if (item?.priceBy == 7) {
          //按距离
          return {
            ...rest,
            distanceBegin,
            distanceEnd,
          };
        } else {
          return {
            ...rest,
            fbaCodes,
          };
        }
      });
      const res = await saveDeliveryPrice({
        detailList,
        ...values,
        id: state?.record?.id,
      });
      console.log('保存结果:', res);
      if (res.status) {
        message.success('保存成功');
        history.back();
        closeTab();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 取消处理
  const handleCancel = () => {
    //
    history.back();
    closeTab();
  };

  return (
    <div style={STYLES.container}>
      {/* 固定的标题栏 */}
      <div style={STYLES.stickyHeader}>
        <h3 style={STYLES.title}>
          {state?.record ? '修改' : '新建'}派送费价格表
        </h3>
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          {state?.record && (
            <TrialPartition
              btnText="试算"
              btnType="primary"
              record={state?.record}
            />
          )}
          <Button type="primary" loading={loading} onClick={handleSave}>
            保存
          </Button>
        </Space>
      </div>

      <div style={STYLES.content}>
        {/* 基础信息表单 */}
        <ProCard title="基础信息">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              currencyType: 20,
              distanceUnit: 1,
            }}
            /*       initialValues={
              state?.record
                ? state?.record
                : {
                    currencyType: '20',
                    distanceUnit: '2',
                  }
            }*/
          >
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label="名称"
                  name="name"
                  rules={[{ required: true, message: '请输入名称' }]}
                >
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="计费币种"
                  name="currencyType"
                  rules={[{ required: true, message: '请选择计费币种' }]}
                >
                  <Select placeholder="请选择">
                    <Option value={10}>人民币</Option>
                    <Option value={20}>美元</Option>
                    <Option value={30}>加元</Option>
                    <Option value={40}>欧元</Option>
                    <Option value={50}>日元</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="距离单位"
                  name="distanceUnit"
                  rules={[{ required: true, message: '请选择距离单位' }]}
                >
                  <Radio.Group
                    style={{ width: '100%', display: 'flex' }}
                    buttonStyle="solid"
                  >
                    <Radio.Button value={2} style={{ flex: 1 }}>
                      KM
                    </Radio.Button>
                    <Radio.Button value={1} style={{ flex: 1 }}>
                      MI
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={8}>
                <ProFormSelect
                  name={['warehouseId']}
                  label="起点"
                  //showSearch
                  rules={[{ required: true, message: '必填项不能为空' }]}
                  request={async ({ keyWords }) => {
                    const { status, data } = await getWarehouseListAPI({
                      type: 3,
                      keyword: keyWords,
                    });
                    if (status) {
                      return data.list.map((item: any) => {
                        return {
                          label: item.name,
                          value: item.id,
                        };
                      });
                    }
                    return [];
                  }}
                />
              </Col>
              <Col span={16}>
                <Form.Item label="备注" name="remark">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </ProCard>
      </div>
      <ProCard className="mt-20px" title="">
        <TableComponent
          weightDetailList={tableData}
          company={'KG'}
          onChange={(e: any) => {
            setTableData(e);
          }}
        />
      </ProCard>
    </div>
  );
};

export default DetailsOfDistribution;
