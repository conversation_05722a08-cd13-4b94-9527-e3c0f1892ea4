import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Input, Modal, Row, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import service from '@/services/home';
import { validateRuleExp } from '@/services/home/<USER>';

const { TextArea } = Input;
const { checkRuleAPI } = service.UserHome;

const EditPopUpBox = ({ onOk, text }: any) => {
  const [value, setValue] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [passedOrNot, setPassedOrNot] = useState<boolean>(false);
  console.log('passedOrNot: ', passedOrNot);
  useEffect(() => {
    if (text) {
      setValue(text);
    }
  }, [text]);

  /* 计算按钮 */
  const calculationList = [
    { id: 1, name: '+', value: '+' },
    { id: 2, name: '-', value: '-' },
    { id: 3, name: 'x', value: '×' },
    { id: 4, name: '÷', value: '÷' },
    { id: 5, name: '不等于', value: '不等于' },
    { id: 6, name: '>', value: '>' },
    { id: 7, name: '≥', value: '≥' },
    { id: 8, name: '=', value: '=' },
    { id: 9, name: '<', value: '<' },
    { id: 10, name: '≤', value: '≤' },
    { id: 11, name: '（', value: '（' },
    { id: 12, name: '）', value: '）' },
    { id: 13, name: '或者', value: '或者' },
    { id: 14, name: '并且', value: '并且' },
    { id: 15, name: '空格', value: '  ' },
    {
      id: 16,
      name: '向上进位',
      value: '向上进位',
      tip: '向上进位（数值，基数）;例：向上进位(13.3,0.5)=13.5',
    },
    {
      id: 17,
      name: '四舍五入进位',
      value: '四舍五入进位',
      tip: '四舍五入进位（数值,位数）；例：四舍五入进位（15.3345，3）=15.335 ',
    },
    {
      id: 18,
      name: '取最大值',
      value: '取最大值',
      tip: '取最大值（数值,数值,...）；例：取最大值（12.4,13.5）=13.5',
    },
    { id: 19, name: '占位', value: '占位' },
    { id: 20, name: '占位', value: '占位' },
  ];
  /* 业务熟悉按钮 */
  const businessList = [
    {
      id: 1,
      name: '[货物重量]',
      value: '[货物重量]',
      tip: '货物重量',
    },
    {
      id: 2,
      name: '[货物体积]',
      value: '[货物体积]',
      tip: '货物体积',
    },
    {
      id: 3,
      name: '[箱数]',
      value: '[箱数]',
      tip: '箱数',
    },
    {
      id: 5,
      name: '[托数]',
      value: '[托数]',
      tip: '托数',
    },
    { id: 6, name: '占位', value: '占位' },
  ];
  /* 语法检测 */
  const [checkResult, setCheckResult] = useState<any>({});
  const checkRule = async () => {
    try {
      const { status, data } = await validateRuleExp({
        ruleExp: value,
        expType: 80,
      });
      if (status) {
        setCheckResult(data);
        if (data.valid) {
          setPassedOrNot(true);
        }
      }
    } catch (error) {
      console.error('语法检测接口报错', error);
    }
  };
  const reset = () => {
    setValue('');
    setCheckResult({});
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    onOk(value);
    setIsModalOpen(false);
    reset();
  };
  const handleCancel = () => {
    reset();
    setIsModalOpen(false);
  };

  useEffect(() => {
    setPassedOrNot(false);
  }, [value]);

  return (
    <>
      <Button type="link" onClick={showModal}>
        编辑
      </Button>
      <Modal
        title="条件公式设置"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        // maskClosable={false}
        footer={[
          <div key={1} style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              type="primary"
              // disabled={!checkResult?.valid || !passedOrNot} // 暂时不写语法检测
              onClick={handleOk}
            >
              确定
            </Button>
          </div>,
        ]}
      >
        <div className={styles.warp}>
          <div className={styles.title}>计算规则</div>
          <div style={{ background: '#FBFBFB', padding: 8 }}>
            <Row gutter={[0, 16]} style={{ marginBottom: 2 }}>
              {calculationList.map((item) => (
                <Col flex={1} key={item.id}>
                  <Tooltip title={item?.tip}>
                    <div
                      onClick={() => {
                        setValue(value + item.value);
                      }}
                      className={classNames(
                        item.name === '占位' ? styles['no-btn'] : styles.btn,
                      )}
                    >
                      <span>{item.name === '占位' ? null : item.name}</span>
                    </div>
                  </Tooltip>
                </Col>
              ))}
            </Row>
          </div>
        </div>
        <div className={styles.warp}>
          <div className={styles.title}>业务属性</div>
          <div style={{ background: '#FBFBFB', padding: 8 }}>
            <Row gutter={[0, 16]} style={{ marginBottom: 2 }}>
              {businessList.map((item) => (
                <Tooltip title={item?.tip} key={item.id}>
                  <Col flex={1}>
                    <div
                      onClick={() => {
                        setValue(value + item.value);
                      }}
                      className={classNames(
                        item.name === '占位' ? styles['no-btn'] : styles.btn,
                      )}
                    >
                      {item.name === '占位' ? null : item.name}
                    </div>
                  </Col>
                </Tooltip>
              ))}
            </Row>
          </div>
        </div>

        <div className={styles.warp}>
          <div className={classNames(styles.title, styles.titbox)}>
            <div>预览</div>
            <div>
              <Button type="link" disabled={!value} onClick={reset}>
                重置
              </Button>
              <Button type="link" disabled={!value} onClick={checkRule}>
                语法检测
              </Button>
            </div>
          </div>
          <div style={{ padding: 8 }}>
            <TextArea
              value={value}
              onChange={(e: any) => {
                setValue(e.target.value);
              }}
            />
            {!checkResult?.valid && checkResult?.msg && value ? (
              <div style={{ marginTop: 12 }}>
                <Alert message={checkResult?.msg} type="warning" />
              </div>
            ) : null}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default EditPopUpBox;
