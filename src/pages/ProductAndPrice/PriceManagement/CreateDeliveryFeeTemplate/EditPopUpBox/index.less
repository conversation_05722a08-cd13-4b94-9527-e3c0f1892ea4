.warp{
  .title{
    color:#707070
  }

  .titbox{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }

  .gutter-box {
    width: 140px;
    padding: 8px 0;
    background: #00a0e9;
  }

  .btn{
    width: 140px;
    line-height: 40px;
    background: #FFF;
    box-shadow: 0 6px 6px 0 #F4F4F4;
    border-radius: 4px;
    border: 1px solid #F4F4F4;
    text-align: center;
    font-size: 14px;
    color: #333;
    //禁止复制
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -webkit-user-select: none;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -moz-user-select: none;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -ms-user-select: none;
    user-select: none;
  }
  
  .no-btn{
    width: 140px;
    line-height: 48px;
  }

  .btn:hover{
    background: #FFF;
    box-shadow: 0 6px 10px 0 #bcd5ff;
    border-radius: 4px;
    border: 1px solid #7899fa;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
  }

  //点击效果
  .btn:active{
    background: #4071FF;
    box-shadow: 0 6px 10px 0 #bcd5ff;
    border-radius: 4px;
    border: 1px solid #4071FF;
    text-align: center;
    color: #fff;
    transition: all 0.3s;

  }

  .highlight {
    background: #FFE58F;
  }
}

