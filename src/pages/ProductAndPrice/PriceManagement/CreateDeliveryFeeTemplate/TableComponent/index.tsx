import { Input, InputNumber, Select, Space, Table, Tag } from 'antd';
import styles from './index.less';
import { DownOutlined, MinusOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import utiles from '@/utils/utils';
import EditPopUpBox from '../EditPopUpBox';
import AddAWarehouse from '../ZhuJian/AddAWarehouse';

const { convertWeight } = utiles;
interface Props {
  weightDetailList: any;
  company: string;
  onChange?: any;
}
const typeVal: any = {
  7: {
    color: 'cyan',
    text: '按距离',
  },
  8: {
    color: 'blue',
    text: '按仓库',
  },
};
const pattern = /\[.+?\]/g;
const TableComponent = ({ weightDetailList, company, onChange }: Props) => {
  const [data, setData] = useState<any>([]);
  useEffect(() => {
    if (!weightDetailList.length) return;
    setData(weightDetailList);
  }, [weightDetailList]);

  useEffect(() => {
    if (!onChange) return;
    onChange(data);
  }, [data]);
  const columns = [
    {
      title: '距离段名称',
      dataIndex: 'distanceName',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <Input
            placeholder="请输入距离段名称"
            value={text}
            onChange={(e) => {
              record.distanceName = e.target.value;
              setData([...data]);
            }}
          />
        );
      },
    },
    {
      title: `距离模式`,
      dataIndex: 'priceBy',
      width: 200,
      render: (text: any, record: any) => {
        const handleSelectChange = (value: any) => {
          record.priceBy = value;
          setData([...data]);
        };
        const handleTagClick = () => {
          record.countryFlage = true;
          setData([...data]);
        };
        const handleBlur = () => {
          record.countryFlage = false;
          setData([...data]);
        };
        return (
          <div>
            {record.countryFlage ? (
              <Select
                style={{ width: 160 }}
                autoFocus
                defaultValue={text}
                onBlur={handleBlur}
                onChange={handleSelectChange}
                options={[
                  {
                    value: 7,
                    label: '按距离',
                  },
                  {
                    value: 8,
                    label: '按仓库',
                  },
                ]}
              />
            ) : (
              <span style={{ cursor: 'pointer' }} onClick={handleTagClick}>
                <Tag color="#108ee9">{typeVal[text]?.text}</Tag>
              </span>
            )}
            {!record.countryFlage ? (
              <span
                style={{
                  color: '#cfcfcf',
                  marginLeft: 12,
                  cursor: 'pointer',
                  lineHeight: '32px',
                }}
                onClick={handleTagClick}
              >
                <DownOutlined />
              </span>
            ) : null}
          </div>
        );
      },
    },
    {
      title: '起止距离/仓库',
      dataIndex: 'startWeight',
      width: 500,
      render: (text: any, record: any) => {
        const handleStartWeightChange = (e: any) => {
          record.distanceBegin = e;
          setData([...data]);
        };
        const handleEndWeightChange = (e: any) => {
          record.distanceEnd = e;
          setData([...data]);
        };
        if (record?.priceBy === 7) {
          return (
            <div style={{ display: 'flex' }}>
              <InputNumber
                placeholder="开始值（不含）"
                style={{ width: '50%', marginRight: 20 }}
                value={record?.distanceBegin}
                onChange={handleStartWeightChange}
              />

              <InputNumber
                placeholder="结束值（含）"
                style={{ width: '50%' }}
                value={record?.distanceEnd}
                onChange={handleEndWeightChange}
              />
            </div>
          );
        } else {
          return (
            <>
              <AddAWarehouse
                btnText="添加仓库"
                btnType="link"
                onConfirm={(selectedWarehouses: any) => {
                  console.log('selectedWarehouses', selectedWarehouses);
                  if (record?.priceBy === 8) {
                    if (!record.warehouseList) record.warehouseList = [];
                    selectedWarehouses.forEach((warehouse: any) => {
                      const exists = record.warehouseList.some(
                        (item: any) => item.id === warehouse.id,
                      );
                      if (!exists) {
                        record.warehouseList.push({
                          ...warehouse,
                          warehouseId: warehouse.id,
                          warehouseName: warehouse.name,
                        });
                      }
                    });
                    setData([...data]);
                  }
                }}
              />
              <Space size={[6, 12]} wrap>
                {record?.warehouseList?.map((item: any, index: any) => {
                  return (
                    <Tag
                      closable
                      key={index}
                      onClose={(e) => {
                        e.preventDefault();
                        console.log(
                          'record.warehouseList',
                          record.warehouseList,
                        );

                        record.warehouseList = record.warehouseList.filter(
                          (item2: any) => {
                            return item2.fbaCode !== item.fbaCode;
                          },
                        );

                        setData([...data]);
                      }}
                    >
                      {item?.warehouseName || item?.name}
                    </Tag>
                  );
                })}
              </Space>
            </>
          );
        }
      },
    },

    {
      title: `价格配置`,
      dataIndex: 'priceExp',
      render: (text: any, record: any) => {
        const str = text?.replace(
          pattern,
          (match: any) => ` <span style="background:#FFE58F">${match}</span> `,
        );
        return (
          <>
            <EditPopUpBox
              onOk={(e: any) => {
                record.priceExp = e;
                setData([...data]);
              }}
            />
            <span dangerouslySetInnerHTML={{ __html: str }} />
          </>
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 100,
      render: (text: any, record: any) => {
        const handleReduceClick = () => {
          const newData = data.filter((item: any) => item.id !== record.id);
          setData(newData);
        };
        return (
          <div className={styles['operation']}>
            <div className={styles['reduce']} onClick={handleReduceClick}>
              <MinusOutlined />
            </div>
          </div>
        );
      },
    },
  ];
  const handleNewlyAddedClick = () => {
    const newData = {
      id: new Date().getTime(),
      //name: '',
      priceBy: 7,
      //startWeight: '',
      //endWeight: '',
      //billingWeight: '',
    };
    setData([...data, newData]);
  };
  return (
    <>
      <div className={styles.box}>
        <Table
          size="large"
          columns={columns}
          dataSource={data}
          // bordered
          rowKey="id"
          // scroll={{ x: 1200 }}
          pagination={false}
          footer={() => {
            return (
              <div className={styles['footer-box']}>
                <div
                  className={styles['new-btn']}
                  onClick={handleNewlyAddedClick}
                >
                  + 新建距离段
                </div>
                {/*    <WeightSegmentModal
                  btnText="+ 重量段快捷添加"
                  btnType="primary"
                  handleOnChange={(e: any) => setData([...e])}
                />*/}
              </div>
            );
          }}
        />
      </div>
    </>
  );
};
export default TableComponent;
