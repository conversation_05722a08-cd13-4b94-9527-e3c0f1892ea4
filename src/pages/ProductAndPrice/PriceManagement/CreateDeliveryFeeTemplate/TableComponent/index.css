.cell-back {
  background: #f9f9f9 !important;
}
.new-btn {
  width: 167px;
  border-radius: 4px;
  border: 1px dashed #4071FF;
  padding: 5px 10px;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
  color: #4071FF;
  margin-right: 10px;
}
.footer-box {
  background: #fff;
  display: flex;
}
.footer-box .ant-table-footer {
  background: #fff;
}
.box :global(.ant-table-footer) {
  background: #fff;
  border-color: #fff;
}
.operation {
  display: flex;
  color: #4071FF;
  font-size: 16px;
}
.operation .reduce {
  cursor: pointer;
  padding: 1px 5px;
  border-radius: 4px;
}
.operation .add {
  cursor: pointer;
  margin-left: 22px;
  padding: 1px 5px;
  border-radius: 4px;
}
.operation .reduce:hover {
  background: #edf0ff;
  padding: 1px 5px;
  border-radius: 4px;
  transition: all 0.3s;
}
.operation .add:hover {
  background: #edf0ff;
  padding: 1px 5px;
  border-radius: 4px;
  transition: all 0.3s;
}
