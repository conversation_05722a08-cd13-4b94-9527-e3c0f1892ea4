import React, { useState } from 'react';
import { Button, Input,  Modal } from 'antd';
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  zoneDetailList?: any;
  countryList?: any;
  handleOnChange?: any;
}

const WeightSegmentModal = ({ btnText, btnType, handleOnChange }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [values, setValues] = useState<any>('');

  // const parseData = () => {
  //   const lines = values.trim().split('\n');
  //   const typeMapping:any = {
  //     '单价': 2,
  //     '总价': 0,
  //     '续单价': 1,
  //   };

  //   let previousEndWeight = '0';

  //   const parsedArray = lines.map((line: { split: (arg0: string) => [any, any]; }, index: number) => {
  //     const [id, typeName] = line.split('\t');
  //     if (!id || !typeName) {
  //       return false;
  //     }

  //     let startWeight;
  //     let endWeight;

  //     if (id.includes('-')) {
  //       const [start, end] = id.split('-').map(Number);
  //       if (isNaN(start) || isNaN(end)) {
  //         return false;
  //       }
  //       startWeight = start.toString();
  //       endWeight = end.toString();
  //     } else {
  //       const idFloat = parseFloat(id);
  //       if (isNaN(idFloat)) {
  //         return false;
  //       }
  //       startWeight = previousEndWeight;
  //       endWeight = idFloat.toString();
  //     }

  //     previousEndWeight = endWeight;

  //     return {
  //       id: index + 1,
  //       name: id,
  //       type: typeMapping[typeName],
  //       billingWeight: '1',
  //       startWeight,
  //       endWeight,
  //     };
  //   });

  //   if (parsedArray.includes(false)) {
  //     return false;
  //   }

  //   return parsedArray;
  // };
  // const parseData: any = () => {
  //   const lines = values.trim().split(/\s*\n\s*/);
  //   function convert(v: any) {
  //     switch (v) {
  //       case '单价':
  //       case '1':
  //       case 1:
  //         return 2;
  //       case '总价':
  //       case 2:
  //       case '2':
  //         return 0;
  //       case '续单价':
  //       case 3:
  //       case '3':
  //         return 1;
  //     }
  //   }
  //   let resultArr = [];
  //   let last = 0;
  //   for (let i = 0; i < lines.length; i++) {
  //     let line = lines[i];
  //     let arr = line.split(/[\t\s]+/);
  //     let e: any = {
  //       id: i + 1,
  //       type: 2,
  //       billingWeight: '1',
  //       name: arr[0],
  //     };
  //     if (arr.length > 1) {
  //       e.type = convert(arr[1]);
  //     }
  //     if (/\-/.test(arr[0])) {
  //       let ns = arr[0].split(/\-/);
  //       e.startWeight = Number(ns[0]);
  //       e.endWeight = Number(ns[1]);
  //     } else {
  //       e.startWeight = last;
  //       e.endWeight = Number(arr[0]);
  //     }
  //     if (isNaN(e.startWeight) || isNaN(e.endWeight)) continue;
  //     last = e.endWeight;
  //     resultArr.push(e);
  //   }

  //   return resultArr;
  // };
  // const parseData = (dir=2) => {
  //   const lines = dir==1?values.trim().split(/\s*\t\s*/):values.trim().split(/\s*\n\s*/);
  //   function convert(v: any){
  //     switch(v){
  //       case '单价':
  //       case '1':
  //       case 1:
  //       return 2;
  //       case '总价':
  //       case 2:
  //       case '2':
  //       return 0;
  //       case '续单价':
  //       case 3:
  //       case '3':
  //       return 1;
  //     }
  //   }
  //   let resultArr:any = [];
  //   let last = 99999;
  //   for(let i=lines.length-1;i>=0;i--){
  //       let line = lines[i].replaceAll(/\s*[,，]+\s*/g,'').replaceAll(/\s*\-\s*/g,'-');
  //       let arr = line.split(/[\t\s]+/);
  //       let e :any= {
  //         id:i+1,
  //         type:2,
  //         billingWeight:'1',
  //         name:arr[0]
  //       }
  //       if(arr.length>1){
  //         e.type = convert(arr[1]);
  //       }
  //       if(/\-/.test(arr[0])){
  //         let ns = arr[0].split(/\s*\-\s*/);
  //         e.startWeight = Number(ns[0]);
  //         e.endWeight = Number(ns[1]);
  //         if(e.endWeight<last){
  //           e.endWeight+=0.001;
  //         }
  //       } else {
  //         e.startWeight = Number(arr[0]);
  //         e.endWeight = last;
  //       }
  //       if(isNaN(e.startWeight)||isNaN(e.endWeight))
  //         continue;
  //       last = e.startWeight;
  //       resultArr.unshift(e);
  //   }
  //   return resultArr;
  // };
  const parseData = ( dir = 2) => {
    const lines = dir === 1 ? values.trim().split(/\s*\t\s*/) : values.trim().split(/\s*\n\s*/);
    
    const convert = (v: string | number): number => {
      const conversionMap: { [key: string]: number } = {
        '单价': 2, '1': 2, '总价': 0, '2': 0, '续单价': 1, '3': 1
      };
      return conversionMap[v.toString()] ?? 2; // Default to 2 if not found
    };
  
    const resultArr: any[] = [];
    let last = Infinity;
  
    for (let i = lines.length - 1; i >= 0; i--) {
      const line = lines[i].replace(/\s*[,，]+\s*/g, '').replace(/\s*\-\s*/g, '-');
      const arr = line.split(/[\s\t]+/);
  
      const e: any = {
        id: i + 1,
        type: 2,
        billingWeight: '1',
        name: arr[0]
      };
  
      const weightRegex = /^(\d+(?:\.\d+)?)\s*(?:kg)?(?:-(\d+(?:\.\d+)?)\s*(?:kg)?|(?:kg)?\+)?\s*(单价|总价|续单价)?$/i;
      const match = arr[0].match(weightRegex);
  
      if (match) {
        e.startWeight = parseFloat(match[1]);
        if (match[2]) {
          e.endWeight = parseFloat(match[2]);
        } else if (match[0].includes('+' || 'kg+' || 'KG+')) {
          e.endWeight = 9999;
        } else {
          e.endWeight = last;
        }
  
        if (e.endWeight < last && e.endWeight !== 9999) {
          e.endWeight += 0.001;
        }
  
        if (arr.length > 1) {
          e.type = convert(arr[1]);
        } else if (match[3]) {
          e.type = convert(match[3]);
        }
  
        last = e.startWeight;
        resultArr.unshift(e);
      }
    }
  
    return resultArr;
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    // console.log(parseData());
    // if (parseData() === false)
    //   return message.error('数据格式有误,不能存在中文或字符');
    if (handleOnChange) {
      handleOnChange(parseData(1));
      setIsModalOpen(false);
      setValues('');
    }
  };
  const handleOk2 = () => {
    // console.log(parseData());
    // if (parseData() === false)
    //   return message.error('数据格式有误,不能存在中文或字符');
    if (handleOnChange) {
      handleOnChange(parseData(2));
      // console.log('parseData(2): ', parseData(2));
      setIsModalOpen(false);
      setValues('');
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setValues('');
  };
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="重量段快捷添加"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={<>
        <Button key="back" onClick={handleCancel}>
          取消
        </Button>
        <Button key="submit" type="primary" onClick={handleOk}>
          横向
        </Button>
        <Button key="submit" type="primary" onClick={handleOk2}>
          竖向
        </Button>

        </>}
      >
        <div className="">
          <Input.TextArea
            value={values}
            placeholder="请从 Excel中复制粘贴数据进行填充 避免格式错误"
            onChange={(e) => setValues(e.target.value)}
            rows={6}
          />
        </div>
      </Modal>
    </>
  );
};
export default WeightSegmentModal;
