import { InputNumber, Space, Table, Tag } from 'antd';
import styles from './index.less';
import { MinusOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import utiles from '@/utils/utils';
import AddAddress from '../../AddAddress';

const { convertWeight } = utiles;
interface Props {
  weightDetailList: any;
  company: string;
  onChange?: any;
}
const typeVal: any = {
  7: {
    color: 'cyan',
    text: '按距离',
  },
  8: {
    color: 'blue',
    text: '按仓库',
  },
};
const pattern = /\[.+?\]/g;
const TableComponent = ({ weightDetailList, company, onChange }: Props) => {
  const [data, setData] = useState<any>([]);
  useEffect(() => {
    if (!weightDetailList.length) return;
    setData(weightDetailList);
  }, [weightDetailList]);
  useEffect(() => {
    if (!data.length) return;
    data.forEach((item: any) => {
      item.startWeight = convertWeight(Number(item.startWeight), company, 2);
      item.endWeight = convertWeight(Number(item.endWeight), company, 2);
      item.billingWeight = convertWeight(
        Number(item.billingWeight),
        company,
        2,
      );
    });
    setData([...data]);
  }, [company]);
  useEffect(() => {
    if (!onChange) return;
    onChange(data);
  }, [data]);
  const columns = [
    {
      title: '目的地',
      dataIndex: 'distanceName',
      width: 240,
      render: (text: any, record: any) => {
        return (
          <>
            <AddAddress
              btnText={' 选择'}
              btnType={'link'}
              onConfirm={(selectedWarehouses: any) => {
                console.log('selectedWarehouses', selectedWarehouses);
                if (!record.warehouseList) record.warehouseList = [];
                selectedWarehouses.forEach((warehouse: any) => {
                  console.log('warehouse', warehouse);
                  if (warehouse?.destStreet) {
                    record.warehouseList = [
                      {
                        name: warehouse?.dest,
                        ...warehouse?.destStreet,
                      },
                    ];
                  } else {
                    record.warehouseList = [
                      {
                        ...warehouse,
                      },
                    ];
                  }
                });
                setData([...data]);
              }}
            />
            <Space size={[6, 12]} wrap>
              {record?.warehouseList?.map((item: any, index: any) => {
                return (
                  <Tag
                    closable
                    key={index}
                    onClose={(e) => {
                      e.preventDefault();

                      record.warehouseList = record.warehouseList.filter(
                        (item2: any) => {
                          return item2.id !== item.id;
                        },
                      );

                      setData([...data]);
                    }}
                  >
                    {item?.warehouseName || item?.name || item?.dest}
                  </Tag>
                );
              })}
            </Space>
          </>
        );
      },
    },
    {
      title: `货物体积`,
      dataIndex: 'volume',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <InputNumber
            placeholder="请输入货物体积"
            value={text}
            onChange={(e) => {
              record.volume = e;
              setData([...data]);
            }}
          />
        );
      },
    },
    {
      title: `货物重量`,
      dataIndex: 'weight',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <InputNumber
            placeholder="请输入货物重量"
            value={text}
            onChange={(e) => {
              record.weight = e;
              setData([...data]);
            }}
          />
        );
      },
    },
    {
      title: `箱数`,
      dataIndex: 'pieceNum',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <InputNumber
            placeholder="请输入箱数"
            value={text}
            onChange={(e) => {
              record.pieceNum = e;
              setData([...data]);
            }}
          />
        );
      },
    },
    /*    {
      title: `距离`,
      dataIndex: 'priceBy',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <Input
            placeholder="请输入距离"
            disabled={true}
            value={text}
            onChange={(e) => {
              record.distanceName = e.target.value;
              setData([...data]);
            }}
          />
        );
      },
    },*/
    {
      title: `托数`,
      dataIndex: 'palletsNum',
      width: 200,
      render: (text: any, record: any) => {
        return (
          <InputNumber
            placeholder="请输入托数"
            value={text}
            onChange={(e) => {
              record.palletsNum = e;
              setData([...data]);
            }}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 100,
      render: (text: any, record: any) => {
        const handleReduceClick = () => {
          const newData = data.filter((item: any) => item.id !== record.id);
          setData(newData);
        };
        return (
          <div className={styles['operation']}>
            <div className={styles['reduce']} onClick={handleReduceClick}>
              <MinusOutlined />
            </div>
          </div>
        );
      },
    },
  ];
  const handleNewlyAddedClick = () => {
    const newData = {
      id: new Date().getTime(),
      name: '',
      priceBy: 7,
      startWeight: '',
      endWeight: '',
      billingWeight: '',
    };
    setData([...data, newData]);
  };
  return (
    <>
      <div className={styles.box}>
        <Table
          size="large"
          columns={columns}
          dataSource={data}
          // bordered
          rowKey="id"
          // scroll={{ x: 1200 }}
          pagination={false}
          footer={() => {
            return (
              <div className={styles['footer-box']}>
                <div
                  className={styles['new-btn']}
                  onClick={handleNewlyAddedClick}
                >
                  + 新建
                </div>
                {/*    <WeightSegmentModal
                  btnText="+ 重量段快捷添加"
                  btnType="primary"
                  handleOnChange={(e: any) => setData([...e])}
                />*/}
              </div>
            );
          }}
        />
      </div>
    </>
  );
};
export default TableComponent;
