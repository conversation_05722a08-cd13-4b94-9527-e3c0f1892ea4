import React, { useState } from 'react';
import { Button, Modal } from 'antd';
import { CalculatorOutlined } from '@ant-design/icons';
import TableComponent from './TableComponent';
import TralModal from '@/pages/ProductAndPrice/PriceManagement/CreateDeliveryFeeTemplate/ZhuJian/TrialPartition/TralModal';

interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
}
const TrialPartition = ({ btnText, btnType, record }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTestOpen, setIsTestOpen] = useState(false);
  const [TableData, setTableData] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    //setIsModalOpen(false);
    setIsTestOpen(true);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type={btnType} onClick={showModal} icon={<CalculatorOutlined />}>
        {btnText}
      </Button>
      <Modal
        title="派送费试算"
        open={isModalOpen}
        onOk={handleOk}
        okText={'试算'}
        onCancel={handleCancel}
        width={1200}
      >
        <TableComponent
          weightDetailList={[]}
          company={'KG'}
          onChange={(e: any) => {
            console.log('e', e);
            setTableData(e);
          }}
        />
        <TralModal
          isTestOpen={isTestOpen}
          setIsTestOpen={setIsTestOpen}
          TableData={TableData}
          record={record}
        />
      </Modal>
    </>
  );
};
export default TrialPartition;
