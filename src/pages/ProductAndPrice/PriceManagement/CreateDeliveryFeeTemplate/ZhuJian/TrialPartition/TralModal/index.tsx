import React, { useEffect, useState } from 'react';
import { Modal, Table } from 'antd';
import { testDeliveryPrice } from '@/services/productAndPrice/api';

interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  isTestOpen?: boolean;
  setIsTestOpen: (value: boolean) => void;
}
const TralModal = ({
  btnText,
  btnType,
  isTestOpen,
  setIsTestOpen,
  TableData,
  record,
}: Props) => {
  const [DataSource, setDataSource] = useState([]);
  const handleOk = () => {
    setIsTestOpen(false);
  };
  const handleCancel = () => {
    setIsTestOpen(false);
  };
  const columns = [
    {
      title: '目的地',
      dataIndex: 'recipient',
      fixed: true,
      width: 100,
      render: (text: any, record: any) => {
        if (record?.recipient?.fbaCode) {
          return record?.recipient?.fbaCode;
        } else {
          return record?.recipient?.subject;
        }
      },
    },
    {
      title: '货物体积',
      dataIndex: 'volume',
      fixed: true,
      width: 100,
    },
    {
      title: '货物重量',
      dataIndex: 'weight',
      fixed: true,
      width: 100,
    },
    {
      title: '箱数',
      dataIndex: 'pieceNum',
      fixed: true,
      width: 100,
    },
    {
      title: '距离',
      dataIndex: 'distanceValue',
      fixed: true,
      width: 100,
    },
    {
      title: '托数',
      dataIndex: 'palletsNum',
      fixed: true,
      width: 100,
    },
    {
      title: '命中距离段',
      dataIndex: 'distanceName',
      fixed: true,
      width: 100,
    },
    {
      title: '派送费（$）',
      dataIndex: 'amount',
      fixed: true,
      width: 100,
      render: (text: any, record: any) => {
        if (record?.amount == -99999) {
          return '询价';
        } else {
          return record?.amount;
        }
      },
    },
  ];
  const TextApi = async () => {
    const id = record?.id;
    console.log('TableData', TableData);
    const res = await testDeliveryPrice({
      paramList: TableData?.map((item: any) => ({
        recipient: {
          ...item?.warehouseList[0],
        },
        volume: item?.volume,
        weight: item?.weight,
        pieceNum: item?.pieceNum,
        palletsNum: item?.palletsNum,
      })),
      priceId: id,
    });
    setDataSource(res?.data);
    console.log('TableData', res);
  };
  useEffect(() => {
    if (isTestOpen) {
      TextApi();
    }
  }, [isTestOpen]);
  return (
    <>
      <Modal
        title="试算结果"
        open={isTestOpen}
        onOk={handleOk}
        okText={'确认'}
        onCancel={handleCancel}
        width={1200}
      >
        <Table columns={columns} dataSource={DataSource} />
      </Modal>
    </>
  );
};
export default TralModal;
