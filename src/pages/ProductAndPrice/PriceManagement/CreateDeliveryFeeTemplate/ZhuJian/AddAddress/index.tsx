import React, { useCallback, useMemo, useState } from 'react';
import {
  Button,
  Checkbox,
  Empty,
  Input,
  message,
  Modal,
  Pagination,
  Spin,
  Table,
  Tabs,
  TabsProps,
  Typography,
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import './index.less';
import { useRequest } from 'ahooks';
import { getWarehouseListAPI } from '@/services/booking';
import { getDistanceList } from '@/services/overview';

const { Search } = Input;
const { Text, Link } = Typography;

interface WarehouseItem {
  id: string;
  name: string;
}

interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: () => void;
  record?: any;
  onConfirm?: (selectedWarehouses: WarehouseItem[]) => void;
  loading?: boolean;
  warehouseData?: WarehouseItem[];
  disabled?: boolean;
}

const AddAWarehouse: React.FC<Props> = ({
  btnText,
  btnType,
  onConfirm,
  loading = false,
  disabled = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [selectedWarehouses, setSelectedWarehouses] = useState<string[]>([]);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [Tab, setTab] = useState('1');
  // 仓库列表状态
  const [warehouseList, setWarehouseList] = useState<WarehouseItem[]>([]);
  // 存储所有已加载的仓库数据，用于跨页面选择
  const [allLoadedWarehouses, setAllLoadedWarehouses] = useState<
    WarehouseItem[]
  >([]);

  // 接口请求
  const { runAsync: getWarehouseList, loading: apiLoading } = useRequest(
    getWarehouseListAPI,
    {
      manual: true,
    },
  );
  // 渲染仓库网格
  const renderWarehouseGrid = useCallback(() => {
    if (apiLoading) {
      return (
        <div className="warehouse-loading">
          <Spin size="large" tip="加载仓库数据中..." />
        </div>
      );
    }

    if (filteredWarehouses.length === 0) {
      return (
        <div className="warehouse-empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchValue ? (
                <>
                  <Text type="secondary">未找到匹配的仓库</Text>
                  <br />
                  <Link onClick={() => handleSearch('')}>清除搜索条件</Link>
                </>
              ) : (
                <Text type="secondary">暂无仓库数据</Text>
              )
            }
          />
        </div>
      );
    }

    return (
      <div className="warehouse-grid">
        {filteredWarehouses.map((warehouse) => (
          <div key={warehouse.id} className="warehouse-item">
            <Checkbox
              value={warehouse.id}
              className="warehouse-checkbox"
              aria-label={`选择仓库 ${warehouse.name}`}
            >
              <Text className="warehouse-text">{warehouse.name}</Text>
            </Checkbox>
          </div>
        ))}
      </div>
    );
  }, [apiLoading, filteredWarehouses, searchValue, handleSearch]);
  // 获取仓库数据
  const fetchWarehouseData = useCallback(
    async (
      params: {
        current?: number;
        pageSize?: number;
        keyword?: string;
      } = {},
    ) => {
      try {
        const { current = 1, pageSize = 20, keyword = '' } = params;
        const res: any = await getWarehouseList({
          start: (current - 1) * pageSize,
          len: pageSize,
          type: 1,
          keyword,
        });

        if (res.status) {
          const { list = [], total = 0 } = res.data || {};
          setWarehouseList(list);
          // 更新所有已加载的仓库数据，避免重复
          setAllLoadedWarehouses((prev) => {
            const existingIds = prev.map((w) => w.id);
            const newWarehouses = list.filter(
              (w: WarehouseItem) => !existingIds.includes(w.id),
            );
            return [...prev, ...newWarehouses];
          });
          setPagination((prev) => ({
            ...prev,
            current,
            total,
          }));
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
        setWarehouseList([]);
      }
    },
    [getWarehouseList],
  );

  // 根据搜索关键词过滤仓库列表（现在主要用于UI显示）
  const filteredWarehouses = useMemo(() => {
    return warehouseList;
  }, [warehouseList]);

  // 全选状态计算
  const allFilteredIds = useMemo(
    () => filteredWarehouses.map((w) => w.id),
    [filteredWarehouses],
  );

  const isAllSelected = useMemo(
    () =>
      allFilteredIds.length > 0 &&
      allFilteredIds.every((id) => selectedWarehouses.includes(id)),
    [allFilteredIds, selectedWarehouses],
  );

  const isPartialSelected = useMemo(
    () =>
      selectedWarehouses.some((id) => allFilteredIds.includes(id)) &&
      !isAllSelected,
    [selectedWarehouses, allFilteredIds, isAllSelected],
  );
  const [Address, setAddress] = useState<any>([]);
  const getList = async () => {
    const res = await getDistanceList({});
    console.log('res', res);
    setAddress(res?.data?.list);
  };
  // 优化后的事件处理函数
  const showModal = useCallback(async () => {
    setIsModalOpen(true);
    // 重置状态
    setPagination({ current: 1, pageSize: 20, total: 0 });
    setSearchValue('');
    // 获取初始数据
    await fetchWarehouseData({ current: 1, pageSize: 20, keyword: '' });
    const res = await getList();
  }, [fetchWarehouseData]);

  const resetState = useCallback(() => {
    setSearchValue('');
    setSelectedWarehouses([]);
    setWarehouseList([]);
    setAllLoadedWarehouses([]);
    setPagination({ current: 1, pageSize: 20, total: 0 });
  }, []);

  const handleOk = useCallback(() => {
    console.log('selectedWarehouses', selectedWarehouses);
    const selectedWarehouseObjects = selectedWarehouses
      .map((id) => {
        let warehouse = warehouseList.find((w) => w.id === id);
        if (!warehouse) {
          warehouse = allLoadedWarehouses.find((w) => w.id === id);
        }
        return warehouse;
      })
      .filter(Boolean) as WarehouseItem[];
    if (selectedWarehouseObjects?.length > 1) {
      message.error('只能选择一个仓库');
      return;
    }
    if (Tab === '2') {
      onConfirm?.(selectedWarehouseObjects);
    } else {
      onConfirm?.(selectedWarehouses);
    }

    setIsModalOpen(false);
    resetState();
  }, [
    selectedWarehouses,
    warehouseList,
    allLoadedWarehouses,
    onConfirm,
    resetState,
  ]);

  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
    resetState();
  }, [resetState]);

  const handleSearch = useCallback(
    async (value: string) => {
      setSearchValue(value);
      setPagination((prev) => ({ ...prev, current: 1 }));
      await fetchWarehouseData({
        current: 1,
        pageSize: pagination.pageSize,
        keyword: value,
      });
    },
    [fetchWarehouseData, pagination.pageSize],
  );

  const handleWarehouseChange = useCallback((checkedValues: string[]) => {
    setSelectedWarehouses(checkedValues);
  }, []);

  // 全选/取消全选功能
  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      // 取消全选：移除当前过滤结果中的所有选中项
      setSelectedWarehouses((prev) =>
        prev.filter((id) => !allFilteredIds.includes(id)),
      );
    } else {
      // 全选：添加当前过滤结果中的所有项
      setSelectedWarehouses((prev) =>
        Array.from(new Set([...prev, ...allFilteredIds])),
      );
    }
  }, [isAllSelected, allFilteredIds]);

  // 分页变化处理
  const handlePageChange = useCallback(
    async (page: number, pageSize?: number) => {
      const newPageSize = pageSize || pagination.pageSize;
      setPagination((prev) => ({
        ...prev,
        current: page,
        pageSize: newPageSize,
      }));
      await fetchWarehouseData({
        current: page,
        pageSize: newPageSize,
        keyword: searchValue,
      });
    },
    [fetchWarehouseData, pagination.pageSize, searchValue],
  );
  const column2 = [
    {
      title: '海外仓',
      dataIndex: 'src',
      key: 'src',
    },
    {
      title: '目的地',
      dataIndex: 'dest',
      key: 'dest',
    },
    {
      title: '距离',
      dataIndex: 'value',
      key: 'value',
    },
  ];
  const onSelectChange = (v: any, record: any) => {
    console.log('record', record);
    setSelectedWarehouses(record);
  };
  const rowSelection = {
    selectedWarehouses,
    onChange: onSelectChange,
    type: 'radio',
  };
  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '非FBA仓库',
      children: (
        <Table
          rowKey={'id'}
          columns={column2}
          dataSource={Address}
          rowSelection={rowSelection}
        />
      ),
    },
    {
      key: '2',
      label: 'FBA仓库',
      children: (
        <div className="add-warehouse-content">
          {/* 搜索框 */}
          <div className="search-section">
            <Search
              size="large"
              placeholder="搜索仓库代码或名称"
              allowClear
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
              prefix={<SearchOutlined />}
              disabled={apiLoading}
              value={searchValue}
            />
          </div>

          {/*     全选控制
          {!apiLoading && filteredWarehouses.length > 0 && (
            <div className="select-all-section">
              <Checkbox
                indeterminate={isPartialSelected}
                checked={isAllSelected}
                onChange={handleSelectAll}
                className="select-all-checkbox"
              >
                <Text className="select-all-text">
                  {isAllSelected ? '取消全选' : '全选'}
                  {searchValue && <Text type="secondary"> (当前搜索结果)</Text>}
                </Text>
              </Checkbox>
            </div>
          )}*/}

          {/* 仓库选择网格 */}
          <div className="warehouse-grid-section">
            <Checkbox.Group
              value={selectedWarehouses}
              onChange={handleWarehouseChange}
              className="warehouse-checkbox-group"
              disabled={apiLoading}
            >
              {renderWarehouseGrid()}
            </Checkbox.Group>
          </div>

          {/* 分页组件 */}
          {!apiLoading && pagination.total > 0 && (
            <div className="pagination-section">
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                size="small"
              />
            </div>
          )}

          {/* 选中提示 */}
          {selectedWarehouses.length > 0 && (
            <div className="selected-info">
              <Text type="secondary">
                已选择 {selectedWarehouses.length} 个仓库
                {selectedWarehouses.length > 0 && searchValue && (
                  <Text type="secondary"> (含搜索范围外的选择)</Text>
                )}
              </Text>
            </div>
          )}
        </div>
      ),
    },
  ];
  const onChange = (value: any) => {
    setTab(value);
  };

  return (
    <>
      <Button
        type={btnType}
        onClick={showModal}
        disabled={disabled}
        loading={loading}
      >
        {btnText}
      </Button>
      <Modal
        title="添加仓库"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
        okText="确认"
        cancelText="取消"
        className="add-warehouse-modal"
        confirmLoading={loading}
        maskClosable={!loading && !apiLoading}
        closable={!loading && !apiLoading}
      >
        <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
      </Modal>
    </>
  );
};

export default AddAWarehouse;
