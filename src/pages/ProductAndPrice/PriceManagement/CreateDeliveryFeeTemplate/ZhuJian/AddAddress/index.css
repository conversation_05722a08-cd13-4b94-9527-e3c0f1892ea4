.add-warehouse-modal .ant-modal-body {
  padding: 10px;
}
.add-warehouse-content .search-section {
  margin-bottom: 16px;
}
.add-warehouse-content .select-all-section {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}
.add-warehouse-content .select-all-section .select-all-checkbox {
  margin: 0;
}
.add-warehouse-content .select-all-section .select-all-checkbox .ant-checkbox .ant-checkbox-inner {
  border-radius: 4px;
  width: 16px;
  height: 16px;
}
.add-warehouse-content .select-all-section .select-all-checkbox .select-all-text {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  user-select: none;
}
.add-warehouse-content .select-all-section .select-all-checkbox.ant-checkbox-wrapper-checked .select-all-text {
  color: #1890ff;
}
.add-warehouse-content .warehouse-grid-section {
  margin-bottom: 16px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group {
  width: 100%;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 32px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-loading .ant-spin-text {
  margin-top: 16px;
  color: #8c8c8c;
  font-size: 14px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 32px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-empty .ant-empty .ant-empty-description {
  margin-top: 12px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-empty .ant-empty .ant-empty-description .ant-typography {
  margin: 0;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px 16px;
  min-height: 200px;
  max-height: 280px;
  overflow-y: auto;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  width: 100%;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #ffffff;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 40px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:hover {
  border-color: #1890ff;
  background-color: #f0f8ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox {
  margin: 0;
  width: 100%;
  display: flex;
  align-items: center;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .ant-checkbox {
  margin-right: 8px;
  flex-shrink: 0;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .ant-checkbox .ant-checkbox-inner {
  border-radius: 4px;
  width: 16px;
  height: 16px;
  transition: all 0.2s ease;
  border: 1px solid #d9d9d9;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .ant-checkbox.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .ant-checkbox.ant-checkbox-checked::after {
  border-color: #ffffff;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .ant-checkbox:hover .ant-checkbox-inner {
  border-color: #1890ff;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .warehouse-text {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  user-select: none;
  line-height: 1.4;
  flex: 1;
  text-align: left;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox.ant-checkbox-wrapper-checked .warehouse-text {
  color: #1890ff;
  font-weight: 600;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox:hover .warehouse-text {
  color: #1890ff;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-checked) {
  border-color: #1890ff;
  background-color: #e6f7ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-disabled) {
  background-color: #f5f5f5;
  border-color: #e8e8e8;
  cursor: not-allowed;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-disabled):hover {
  transform: none;
  box-shadow: none;
  border-color: #e8e8e8;
  background-color: #f5f5f5;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-disabled) .warehouse-checkbox .warehouse-text {
  color: #bfbfbf;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid::-webkit-scrollbar {
  width: 6px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}
.add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
.add-warehouse-content .pagination-section {
  margin-bottom: 16px;
  padding: 16px 0;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
}
.add-warehouse-content .selected-info {
  padding: 8px 12px;
  background-color: #e6f7ff;
  border-radius: 6px;
  border: 1px solid #91d5ff;
  border-left: 3px solid #1890ff;
  margin-top: 12px;
  animation: fadeIn 0.3s ease;
}
.add-warehouse-content .selected-info .ant-typography {
  margin: 0;
  font-size: 13px;
  color: #1890ff;
  font-weight: 500;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@media (max-width: 900px) {
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px 12px;
  }
  .add-warehouse-content .pagination-section {
    padding: 12px 0;
  }
}
@media (max-width: 768px) {
  .add-warehouse-modal .ant-modal {
    width: calc(100vw - 32px) !important;
    max-width: none !important;
  }
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px;
    max-height: 240px;
  }
  .add-warehouse-content .pagination-section .ant-pagination .ant-pagination-total-text {
    display: none;
  }
  .add-warehouse-content .pagination-section .ant-pagination .ant-pagination-options .ant-pagination-options-quick-jumper {
    display: none;
  }
}
@media (max-width: 480px) {
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 10px;
  }
  .add-warehouse-content .select-all-section {
    padding: 6px 10px;
  }
  .add-warehouse-content .select-all-section .select-all-checkbox .select-all-text {
    font-size: 13px;
  }
  .add-warehouse-content .pagination-section .ant-pagination .ant-pagination-options {
    display: none;
  }
}
.dark .add-warehouse-content .select-all-section {
  background-color: #1f1f1f;
  border-color: #434343;
}
.dark .add-warehouse-content .select-all-section .select-all-checkbox .select-all-text {
  color: #ffffff;
}
.dark .add-warehouse-content .select-all-section .select-all-checkbox.ant-checkbox-wrapper-checked .select-all-text {
  color: #1890ff;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-loading .ant-spin-text {
  color: #ffffff;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-empty .ant-empty-description {
  color: #ffffff;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid {
  background-color: #1f1f1f;
  border-color: #434343;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item {
  background-color: #262626;
  border-color: #434343;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:hover {
  background-color: #303030;
  border-color: #1890ff;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item .warehouse-checkbox .warehouse-text {
  color: #ffffff;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-checked) {
  background-color: #111b26;
  border-color: #1890ff;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-disabled) {
  background-color: #1a1a1a;
  border-color: #434343;
}
.dark .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-disabled) .warehouse-checkbox .warehouse-text {
  color: #8c8c8c;
}
.dark .add-warehouse-content .pagination-section {
  border-top-color: #434343;
}
.dark .add-warehouse-content .selected-info {
  background-color: #111b26;
  border-color: #1890ff;
  border-left-color: #1890ff;
}
@media (prefers-contrast: high) {
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item {
    border-width: 2px;
  }
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:hover {
    border-width: 2px;
  }
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:has(.ant-checkbox-checked) {
    border-width: 2px;
  }
}
@media (prefers-reduced-motion: reduce) {
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item {
    transition: none;
  }
  .add-warehouse-content .warehouse-grid-section .warehouse-checkbox-group .warehouse-grid .warehouse-item:hover {
    transform: none;
  }
  .add-warehouse-content .selected-info {
    animation: none;
  }
}
