import { message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { removeWaybillAPI } from '@/services/bol';
import { getOutBoundOrderList } from '@/services/ConfiguredCar';
import SuperTables from '@/components/SuperTables';
import { formatTime } from '@/utils/format';

const Material = (props: any) => {
  const { state, details } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [declaration, setDeclaration] = useState('');
  const actionRef = useRef<any>();
  const [waybillRecordId, setWaybillRecordId] = useState('');
  //统计的数据
  const [checkData, setCheckData] = useState<any>([]);
  const refreshTable = () => {
    actionRef?.current?.reload();
  };
  useEffect(() => {
    if (state?.id) {
      refreshTable();
    }
  }, [state?.id]);

  const columns = [
    {
      title: '物料出库单号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 150,
    },

    {
      title: '物料出库总量',
      hideInSearch: true,
      width: 100,
      dataIndex: 'summary',
    },
    {
      title: '操作时间',
      hideInSearch: true,
      width: 100,
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return record?.createTime ? formatTime(record?.createTime) : '';
      },
    },

    {
      title: '操作员',
      hideInSearch: true,
      dataIndex: 'use',
      width: 100,
      fieldFormat: (record: any) => {
        return record?.user?.name;
      },
    },
  ];
  const handleOk = async () => {
    if (!declaration) return message.warning('请选择报关类型');
    try {
      const { status } = await removeWaybillAPI({
        waybillRecordId,
        declarationType: declaration,
      });
      if (status) {
        message.success('操作成功');
        setIsModalOpen(false);
        props?.getDetail();
      }
    } catch (error) {}
  };
  return (
    <>
      <SuperTables
        instanceId="detailmaterrail"
        columns={columns}
        ref={actionRef}
        warpHeight={500}
        /*     rowSelection={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}*/
        /*   TotalConfiguration={[
          {
            desc: '票数',
            field: 'groupNumber',
            // value: statistic?.sumReceiptAmount,
          },
          {
            desc: '件数',
            field: 'pieceNum',
            // value: statistic?.sumReceiptAmountCny,
          },
          {
            desc: '立方',
            field: 'totalvolume',
            // value: statistic?.sumRemainAmount,
          },
          {
            desc: '重量',
            field: 'totalweight',
            // value: statistic?.sumWriteOffAmount,
          },
        ]}*/
        /*     filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}*/

        request={async (params: any, action: any) => {
          // console.log('tableactiveKey',activeKey);
          console.log('params', params);
          let msg = await getOutBoundOrderList({
            condition: params?.condition,
            outId: details?.id,
          });
          console.log('msg', msg);
          return {
            data: msg?.data?.list || [],
            success: msg?.status,
            total: msg?.data?.list?.length,
          };
        }}
      />
    </>
  );
};
export default Material;
