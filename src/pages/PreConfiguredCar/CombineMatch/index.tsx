import { shipmentMerge } from '@/services/ConfiguredCar';
import { Button, Input, message, Modal, Table } from 'antd';
import { useEffect, useState } from 'react';
const { TextArea } = Input;
const CombineMatch = ({ btnText, btnType, selectRow, refresh }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [remark, setRemark] = useState('');
  const [selectedRows, setSelectedRows] = useState<any>([]);
  useEffect(() => {
    if (isModalOpen) {
      setDataSource(selectRow);
    }
  }, [isModalOpen]);
  const showModal = () => {
    // if (dataSource?.length < 2) return message.error('请勾选两个及两个以上');
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const columns: any = [
    {
      title: '原配车单号',
      dataIndex: 'no',
      key: 'no',
    },
    {
      title: '配车标识',
      dataIndex: 'dest',
      key: 'dest',
    },
    {
      title: '总件数',
      dataIndex: 'pcs',
      key: 'pcs',
    },
    {
      title: '总立方数/CBM',
      dataIndex: 'volume',
      key: 'volume',
    },
    {
      title: '总重量/KG',
      dataIndex: 'weight',
      key: 'weight',
    },
    {
      title: '操作',
      key: 'option',
      width: 80,
      fixed: 'right',
      valueType: 'option',
      render: (_: any, recode: any) => {
        return (
          <a
            onClick={() => {
              const newData = dataSource?.filter(
                (item: any) => item?.id !== recode?.id,
              );
              setDataSource([...newData]);
            }}
          >
            移除
          </a>
        );
      },
    },
  ];
  const rowSelection: any = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedRows(selectedRows);
      //   console.log(
      //     `selectedRowKeys: ${selectedRowKeys}`,
      //     'selectedRows: ',
      //     selectedRows,
      //   );
    },
  };

  //   合并配车
  const getCombineDeserve = async () => {
    if (!selectedRows?.length) return message.error('请选择要合并的目标配车单');
    setLoading(true);
    try {
      const { status } = await shipmentMerge({
        targetId: selectedRows[0]?.id,
        shipmentIds: selectRow
          ?.map((item: any) => item?.id)
          ?.filter(Boolean)
          ?.join(','),
        remark,
      });
      if (status) {
        message.success('操作成功');
        setLoading(false);
        setIsModalOpen(false);
        refresh();
        setSelectedRows([]);
      }
    } catch (error) {
      setLoading(false);
    }
  };
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={btnText}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        width={800}
        afterClose={() => {
          setRemark('');
        }}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'end' }}>
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              style={{ marginLeft: '20px' }}
              onClick={getCombineDeserve}
              loading={loading}
            >
              确认
            </Button>
          </div>,
        ]}
      >
        <span style={{ color: 'orange', fontSize: '14px', fontWeight: 500 }}>
          合并后原配车单内所有货物/托盘均会绑定到所选择的配车计划中，原配车单将作废
        </span>
        <Table
          style={{ margin: '10px 0' }}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
          scroll={{ y: 400 }}
          rowSelection={{ type: 'radio', ...rowSelection }}
          rowKey={'id'}
        />
        <p>备注</p>
        <TextArea
          showCount
          //   maxLength={100}
          style={{ width: '600px' }}
          onChange={(e) => {
            setRemark(e?.target?.value);
          }}
          placeholder="请输入备注"
        />
      </Modal>
    </>
  );
};
export default CombineMatch;
