import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useState } from 'react';
import { creatShipment } from '@/services/ConfiguredCar';

const SpecialDeliveryModal = (props: any) => {
  const { record, btnText, myList, refresh, title } = props;
  console.log('mylist', myList);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  const showModal = () => {
    if (!myList?.length) {
      console.log('由于myList为空而关闭弹窗');
      setModalVisit(false);
      message.error(`请选择需要操作的数据`);
    } else {
      setModalVisit(true);
    }
  };

  /* 提交 */
  const onFinish = async (values: any) => {
    console.log('values', values);
    const ids = myList
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id)
      .join(',');
    //退件退款
    const { data, status } = await creatShipment({
      remark: values?.remark,
      operateState: 4,
      pieceIds: ids,
    });
    if (status) {
      message.success('操作成功');
      setModalVisit(false);
      refresh();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };

  return (
    <>
      <Button
        onClick={showModal}
        type={'primary'}
        ghost={true}
        size={'small'}
        style={{ marginLeft: 10, color: 'orange', border: '1px solid orange' }}
      >
        {title}
      </Button>
      <ModalForm
        title={'派送说明'}
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        width={500}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        open={modalVisit}
        layout="horizontal"
        submitTimeout={2000}
        onFinish={onFinish}
        initialValues={{ ...record }}
      >
        <ProFormTextArea
          name="remark"
          width={'md'}
          label=" "
          colon={false}
          required={true}
          rules={[{ required: true, message: '请输入派送说明！' }]}
        />
      </ModalForm>
    </>
  );
};
export default React.memo(SpecialDeliveryModal);
