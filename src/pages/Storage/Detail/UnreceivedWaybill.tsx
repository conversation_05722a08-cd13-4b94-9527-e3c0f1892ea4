import {
  Button,
  message,
  Modal,
  Radio,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import React, { useRef, useState } from 'react';
import { formatTime, GenerateTab } from '@/utils/format';
import {
  declarationType,
  getColorByValue,
  getLabelByValue,
  getObjectByValue,
  ProgressStateType,
  ProgressStateType2,
  waybillStateMap,
} from '@/utils/constant';
import { history } from '@@/core/history';
import { removeWaybillAPI } from '@/services/bol';
import {
  clearUnreceivedPieces,
  getPalletsList,
  getUnreceivedWaybillList,
} from '@/services/ConfiguredCar';
import { ProTable } from '@ant-design/pro-components';
import MrTable from '@/components/MrTable';
import SuperTables from '@/components/SuperTables';

const UnreceivedWaybill = (props: any) => {
  const { state, details } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [declaration, setDeclaration] = useState('');
  const actionRef = useRef<any>();
  const [waybillRecordId, setWaybillRecordId] = useState('');
  //统计的数据
  const [checkData, setCheckData] = useState<any>([]);
  const refreshTable = () => {
    actionRef.current.reload();
  };
  const handleClear = async () => {
    const { no, id } = details;

    const res = await clearUnreceivedPieces({ shipmentId: id });
    console.log('res', res);
    refreshTable();
  };

  const columns = [
    {
      title: '客户箱唛',
      dataIndex: 'waybillNo',
      hideInSearch: true,
      width: 150,
      fieldFormat: (record: any) => {
        /*没有就返回子单号*/
        return record?.waybillNo || record?.subWaybillNo;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 100,
      hideInSearch: true,
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Tag
            style={{
              background: obj?.background,
              color: obj?.color,
              border: 'none',
            }}
          >
            {obj?.label}
          </Tag>
        );
      },
    },
    {
      title: '派送方式',
      width: 100,
      dataIndex: 'shipmentMethod',
      hideInSearch: true,
    },
    {
      title: 'FBA仓库',
      hideInSearch: true,
      width: 100,
      dataIndex: 'fbaCode',
      fieldFormat: (record: any) => {
        return record?.recipient?.name || record?.recipient?.fbaCode;
      },
    },
    {
      title: '邮编',
      hideInSearch: true,
      width: 100,
      dataIndex: 'zipCode',
      fieldFormat: (record: any) => {
        return record?.recipient?.zipCode;
      },
    },
    {
      title: '总立方数/CBM',
      hideInSearch: true,
      width: 100,
      dataIndex: 'volume',
    },

    {
      title: '总重量/KG',
      hideInSearch: true,
      dataIndex: 'weight',
      width: 100,
    },
    {
      title: '总件数',
      hideInSearch: true,
      dataIndex: 'pieceNum',
      width: 100,
    },
    {
      title: '交货仓库',
      hideInSearch: true,
      dataIndex: 'recipient',
      width: 100,
      fieldFormat: (record: any) => {
        return record?.recipient?.name || record?.recipient?.fbaCode;
      },
    },
    {
      title: '提单号',
      hideInSearch: true,
      dataIndex: 'blnoOrder',
      width: 100,
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.blno;
      },
    },
    {
      title: '清提派单号',
      hideInSearch: true,
      dataIndex: 'blnoOrder',
      width: 120,
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.no;
      },
    },
    {
      title: '托盘标签号',
      width: 120,
      hideInSearch: true,
      dataIndex: 'palletsNO',
      fieldFormat: (record: any) => {
        return record?.palletsNO;
      },
    },
    {
      title: '客户',
      hideInSearch: true,
      width: 120,
      dataIndex: 'clientName',
    },
    {
      title: '柜型',
      hideInSearch: true,
      dataIndex: 'containerMode',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.containerMode;
      },
    },
    {
      title: '柜号',
      hideInSearch: true,
      dataIndex: 'blnoOrder',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.containerNo;
      },
    },
    {
      title: '预计到港时间',
      hideInSearch: true,
      dataIndex: 'estimateArriveTime',
      fieldFormat: (record: any) => {
        return formatTime(record?.blnoOrder?.estimateArriveTime);
      },
    },
    {
      title: '预计提货时间',
      hideInSearch: true,
      dataIndex: 'estimatePickupTime',
      valueType: 'dateTime',
      fieldFormat: (record: any) => {
        return formatTime(record?.blnoOrder?.estimatePickupTime);
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        /*      <Button
                key="edit"
                type="link"
                onClick={() => {
                  setValue(record);
                }}
              >
                编辑
              </Button>,
              <Button
                key="del"
                type="link"
                danger
                onClick={() => deleteIdRule(record.id)}
                style={{ visibility: record.removeable === 0 ? 'hidden' : 'visible' }}
                disabled={record.removeable === '0'}
              >
                删除
              </Button>,*/
      ],
    },
  ];
  const handleOk = async () => {
    if (!declaration) return message.warning('请选择报关类型');
    try {
      const { status } = await removeWaybillAPI({
        waybillRecordId,
        declarationType: declaration,
      });
      if (status) {
        message.success('操作成功');
        setIsModalOpen(false);
        props?.getDetail();
      }
    } catch (error) {}
  };
  return (
    <>
      <SuperTables
        instanceId="UnreceivedWaybill2"
        columns={columns}
        isTree={true}
        warpHeight={500}
        rowSelection={(value: any) => {
          setCheckData(value);
          console.log('value', value);
        }}
        options={false}
        TotalConfiguration={[
          {
            desc: '票数',
            field: 'groupNumber',
            // value: statistic?.sumReceiptAmount,
          },
          {
            desc: '件数',
            field: 'pieceNum',
            // value: statistic?.sumReceiptAmountCny,
          },
          {
            desc: '立方',
            field: 'totalvolume',
            // value: statistic?.sumRemainAmount,
          },
          {
            desc: '重量',
            field: 'totalweight',
            // value: statistic?.sumWriteOffAmount,
          },
        ]}
        filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}
        toolBarRender={() => (
          <Space>
            <Button type={'primary'} ghost onClick={handleClear}>
              移除全部
            </Button>
          </Space>
        )}
        request={async (params: any, action: any) => {
          // console.log('tableactiveKey',activeKey);
          console.log('params', params);
          let msg = await getUnreceivedWaybillList({
            condition: params?.condition,
            id: state?.id,
          });
          return {
            data:
              msg?.data?.list?.map((item: any) => {
                return {
                  ...item,
                  children: item?.pieceList,
                  groupNumber: 1,
                  totalvolume: item.volume,
                  totalweight: item.weight,
                };
              }) || [],
            success: msg?.status,
            total: msg?.data?.list?.length,
          };
        }}
      />
    </>
  );
};
export default UnreceivedWaybill;
