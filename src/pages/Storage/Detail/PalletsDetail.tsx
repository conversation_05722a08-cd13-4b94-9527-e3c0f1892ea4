import { Button, message, Popconfirm, Space, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { formatTime } from '@/utils/format';
import {
  getObjectByValue,
  ProgressStateType,
  ProgressStateType2,
} from '@/utils/constant';
import { removeWaybillAPI } from '@/services/bol';
import {
  checkoutPallets,
  getPalletsList,
  removePallets,
} from '@/services/ConfiguredCar';
import AddTray from '@/components/AddTray';
import SuperTables from '@/components/SuperTables';
import { getWarehouseListAPI } from '@/services/home/<USER>';

const PalletsDetail = (props: any) => {
  const { state, details, isDelivered } = props;
  console.log('details', details);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [declaration, setDeclaration] = useState('');
  const [waybillRecordId, setWaybillRecordId] = useState('');
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const actionRef = useRef<any>();
  const refreshTable = () => {
    actionRef.current.reload();
  };
  useEffect(() => {
    actionRef.current.resetScreenStore('PalletsDetail22');
  }, []);
  useEffect(() => {
    if (state?.id) {
      refreshTable();
    }
  }, [state?.id]);
  /*完成装车*/
  const handleTakeCar = async () => {
    const { no, id } = details;
    const ids = checkData
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id)
      .join(',');
    console.log('checkData', ids);
    const res = await checkoutPallets({ id: id, code: ids });
    console.log('res', res);
    refreshTable();
  };
  /*移除托盘*/
  const handleRemovePallets = async () => {
    const { no, id } = details;
    const ids = checkData
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id)
      .join(',');
    console.log('checkData', ids);
    const res = await removePallets({ shipmentId: id, palletsIds: ids });
    console.log('res', res);
    refreshTable();
  };
  const columns: any = [
    {
      title: '托盘标签号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      filters: {
        key: 'state',
        options: ProgressStateType.map((item: any) => {
          return { value: item?.value, text: item?.label };
        }),
      },
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Tag
            style={{
              background: obj?.background,
              color: obj?.color,
              border: 'none',
            }}
          >
            {obj?.label}
          </Tag>
        );
      },
    },
    {
      title: '所在仓库',
      dataIndex: 'content',
      hideInSearch: true,
      width: 150,
      fieldFormat: (record: any) => {
        console.log('record', record);
        return record?.warehouse?.name;
      },
    },
    {
      title: '所在货位',
      hideInSearch: true,
      dataIndex: 'slotId',
      width: 150,
      filters: {
        key: 'slotId',
      },
    },
    {
      title: '柜号',
      width: 120,
      dataIndex: 'containerNos',
    },
    {
      title: '配车单号',
      width: 150,
      dataIndex: 'truckShipmentNos',
    },
    {
      title: '配车标识',
      width: 150,
      hideInSearch: true,
      dataIndex: 'dest',
      filters: {
        key: 'dest',
        api: (v: any) => getWarehouseListAPI({ type: 1, ...v }),
        fieldNames: {
          //options取什么值 api 配置 存在必填
          label: 'name',
          value: 'name',
        },
      },
    },
    {
      title: '派送方式',
      width: 150,
      hideInSearch: true,
      dataIndex: 'shipmentMethods',
      filters: {
        key: 'shipmentMethod',
        options: [
          { text: 'UPS', value: 'UPS' },
          { text: 'FEDEX', value: 'FEDEX' },
          { text: 'USPS', value: 'USPS' },
          { text: 'Truck-Amazon', value: 'Truck-Amazon' },
          { text: 'Truck-Other', value: 'Truck-Other' },
          { text: 'Truck-Walmart', value: 'Truck-Walmart' },
          { text: 'PickUp', value: 'PickUp' },
          { text: 'Storage', value: 'Storage' },
          { text: 'Other', value: 'Other' },
        ],
      },
    },
    {
      title: '总件数',
      width: 150,
      hideInSearch: true,
      dataIndex: 'pieceNumber',
    },
    {
      title: '总立方数/CBM',
      hideInSearch: true,
      width: 150,
      dataIndex: 'volume',
    },
    {
      title: '总重量/Kg',
      width: 150,
      hideInSearch: true,
      dataIndex: 'weight',
    },
    {
      title: '创建时间',
      width: 150,
      hideInSearch: true,
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return record?.createTime ? formatTime(record?.createTime) : '';
      },
    },
    {
      title: '上架时间',
      width: 150,
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      fieldFormat: (record: any) => {
        return record?.createTime ? formatTime(record?.createTime) : '';
      },
    },
    {
      title: '出货时间',
      width: 150,
      hideInSearch: true,
      dataIndex: 'signOutTime',
      fieldFormat: (record: any) => {
        return record?.signOutTime ? formatTime(record?.signOutTime) : '';
      },
    },
    {
      title: '送达时间',
      width: 150,
      hideInSearch: true,
      dataIndex: 'deliveriedTime',
      fieldFormat: (record: any) => {
        return record?.deliveriedTime ? formatTime(record?.deliveriedTime) : '';
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        /*     <Button
               key="edit"
               type="link"
               onClick={() => {
                 setValue(record);
                 setIsModalOpen(true);
               }}
             >
               编辑
             </Button>,
             <Button
               key="del"
               type="link"
               danger
               onClick={() => deleteIdRule(record.id)}
               style={{ visibility: record.removeable === 0 ? 'hidden' : 'visible' }}
               disabled={record.removeable === '0'}
             >
               删除
             </Button>,*/
      ],
    },
  ];
  const handleOk = async () => {
    if (!declaration) return message.warning('请选择报关类型');
    try {
      const { status } = await removeWaybillAPI({
        waybillRecordId,
        declarationType: declaration,
      });
      if (status) {
        message.success('操作成功');
        setIsModalOpen(false);
        props?.getDetail();
      }
    } catch (error) {}
  };
  return (
    <div>
      <SuperTables
        instanceId="PalletsDetail22"
        columns={columns}
        options={false}
        ref={actionRef}
        rowSelection={(value: any) => {
          setCheckData(value);
        }}
        TotalConfiguration={[
          {
            desc: '托数',
            field: 'groupNumber',
            // value: statistic?.sumReceiptAmount,
          },
          {
            desc: '件数',
            field: 'pieceNumber',
            // value: statistic?.sumReceiptAmountCny,
          },
          {
            desc: '立方',
            field: 'volume',
            // value: statistic?.sumRemainAmount,
          },
          {
            desc: '重量',
            field: 'weight',
            // value: statistic?.sumWriteOffAmount,
          },
        ]}
        filters={{
          keyword: {
            type: 'keyword',
            value: '',
            tabs: true,
          },
        }}
        toolBarRender={() =>
          !isDelivered ? (
            <Space>
              <AddTray details={details} handleAdd={refreshTable} />
              <Popconfirm
                key="1"
                title="提示"
                description="确定要移除吗"
                onConfirm={handleRemovePallets}
                onCancel={() => {
                  message.info('已取消操作');
                }}
                okText="确认"
                cancelText="再想想"
              >
                <Button ghost key="primary" type="primary">
                  移除
                </Button>
              </Popconfirm>
            </Space>
          ) : (
            <Space>
              {details?.state < 50 && (
                <Popconfirm
                  key="3"
                  title="提示"
                  description="确定要装车吗"
                  onConfirm={handleTakeCar}
                  onCancel={() => {
                    message.info('已取消操作');
                  }}
                  okText="确认"
                  cancelText="再想想"
                >
                  <Button ghost key="primary" type="primary">
                    装车
                  </Button>
                </Popconfirm>
              )}

              <Popconfirm
                key="4"
                title="提示"
                description="确定要移除吗"
                onConfirm={handleRemovePallets}
                onCancel={() => {
                  message.info('已取消操作');
                }}
                okText="确认"
                cancelText="再想想"
              >
                <Button ghost key="primary" type="primary">
                  移除
                </Button>
              </Popconfirm>
            </Space>
          )
        }
        request={async (params: any, action: any) => {
          // console.log('tableactiveKey',activeKey);
          console.log('params', params);
          let msg = await getPalletsList({
            condition: params?.condition || {},
            columnsFilter: params?.columnsFilter || {},
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            // id: state?.id,
            shipmentId: state?.id,
          });
          const datas = msg?.data?.list?.map((item: any) => {
            return { ...item, groupNumber: 1 };
          });
          return {
            data: datas || [],
            success: msg?.status,
            total: msg?.data?.list?.length,
          };
        }}
        /*       request={async (params: any, action: any) => {
          // console.log('tableactiveKey',activeKey);
          let msg = await getPalletsList({
            id: state?.id,
          });
          return {
            data: msg?.data?.list || [],
            success: msg?.data?.status,
            total: msg?.data?.data?.amount || msg?.data?.data?.total,
          };
        }}*/
      />
    </div>
  );
};
export default PalletsDetail;
