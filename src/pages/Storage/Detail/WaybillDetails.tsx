import { Button, message, Popconfirm, Space, Tag } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { removeWaybillAPI } from '@/services/bol';
import MrTable from '@/components/MrTable';
import {
  clearUnreceivedPieces,
  getWaybillList,
  getWaybillList2,
  removePieces,
} from '@/services/ConfiguredCar';
import AdditionalGoods from '@/components/AdditionalGoods';
import {
  getColorByValue,
  getLabelByValue,
  getObjectByValue,
  ProgressStateType,
  ProgressStateType2,
} from '@/utils/constant';
import { formatTime } from '@/utils/format';
import SuperTables from '@/components/SuperTables';
import { getWarehouseListAPI } from '@/services/home/<USER>';

const WaybillDetails = (props: any) => {
  const { state, details, isDelivered, id } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [declaration, setDeclaration] = useState('');
  const [waybillRecordId, setWaybillRecordId] = useState('');
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  const actionRef = useRef<any>();
  const refreshTable = () => {
    actionRef.current.reload();
  };
  useEffect(() => {
    if (state?.id) {
      refreshTable();
    }
  }, [state?.id]);
  /*移除数据*/
  const handleRemove = async () => {
    const ids = checkData
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id)
      .join(',');
    console.log('checkData', ids);
    const res = await removePieces({ shipmentId: id, pieceIds: ids });
    console.log('res', res);
    refreshTable();
  };
  /*移除全部数据*/
  const handleClear = async () => {
    const { no } = details;

    const res = await clearUnreceivedPieces({ shipmentId: no });
    console.log('res', res);
    refreshTable();
  };
  const columns = [
    {
      title: '客户箱唛',
      dataIndex: 'waybillNo',
      hideInSearch: true,
      width: 150,
      fieldFormat: (record: any) => {
        /*没有就返回子单号*/
        return record?.waybillNo || record?.subWaybillNo;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 150,
      hideInSearch: true,
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Tag
            style={{
              background: obj?.background,
              color: obj?.color,
              border: 'none',
            }}
          >
            {obj?.label}
          </Tag>
        );
      },
    },
    {
      title: '派送方式',
      width: 150,
      dataIndex: 'shipmentMethod',
      hideInSearch: true,
    },
    {
      title: 'FBA仓库',
      hideInSearch: true,
      width: 150,
      dataIndex: 'fbaCode',
      fieldFormat: (record: any) => {
        return record?.recipient?.name || record?.recipient?.fbaCode;
      },
    },
    {
      title: 'FBA编号',
      hideInSearch: true,
      width: 150,
      dataIndex: 'fbaNo',
      fieldFormat: (record: any) => {
        return record?.fbaNo;
      },
    },
    {
      title: 'FBAReferenceId',
      hideInSearch: true,
      width: 150,
      dataIndex: 'fbaReferenceId',
      fieldFormat: (record: any) => {
        return record?.fbaReferenceId;
      },
    },
    {
      title: '邮编',
      hideInSearch: true,
      width: 150,
      dataIndex: 'zipCode',
      fieldFormat: (record: any) => {
        return record?.recipient?.zipCode;
      },
    },
    {
      title: '总立方数/CBM',
      width: 150,
      hideInSearch: true,
      dataIndex: 'volume',
    },

    {
      title: '总重量/KG',
      width: 150,
      hideInSearch: true,
      dataIndex: 'weight',
    },
    {
      title: '总件数',
      width: 150,
      hideInSearch: true,
      dataIndex: 'pieceNum',
    },
    {
      title: '交货仓库',
      width: 150,
      hideInSearch: true,
      dataIndex: 'warehouse',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.warehouse?.name;
      },
    },
    {
      title: '提单号',
      width: 150,
      hideInSearch: true,
      dataIndex: 'blno',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.blno;
      },
    },
    {
      title: '清提派单号',
      width: 150,
      hideInSearch: true,
      dataIndex: 'no',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.no;
      },
    },
    {
      title: '托盘标签号',
      width: 150,
      hideInSearch: true,
      dataIndex: 'palletsNO',
      fieldFormat: (record: any) => {
        return record?.palletsNO;
      },
    },
    {
      title: '客户',
      width: 150,
      hideInSearch: true,
      dataIndex: 'client',
    },
    {
      title: '柜型',
      width: 150,
      hideInSearch: true,
      dataIndex: 'containerMode',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.containerMode;
      },
    },
    {
      title: '柜号',
      width: 150,
      hideInSearch: true,
      dataIndex: 'blnoOrder',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.containerNo;
      },
    },
    {
      title: '预计到港时间',
      width: 150,
      hideInSearch: true,
      dataIndex: 'estimateArriveTime',
      fieldFormat: (record: any) => {
        return formatTime(record?.blnoOrder?.estimateArriveTime);
      },
    },
    {
      title: '预计提货时间',
      width: 150,
      hideInSearch: true,
      dataIndex: 'estimatePickupTime',
      valueType: 'dateTime',
      fieldFormat: (record: any) => {
        return formatTime(record?.blnoOrder?.estimatePickupTime);
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        /*      <Button
                key="edit"
                type="link"
                onClick={() => {
                  setValue(record);
                }}
              >
                编辑
              </Button>,
              <Button
                key="del"
                type="link"
                danger
                onClick={() => deleteIdRule(record.id)}
                style={{ visibility: record.removeable === 0 ? 'hidden' : 'visible' }}
                disabled={record.removeable === '0'}
              >
                删除
              </Button>,*/
      ],
    },
  ];
  const handleOk = async () => {
    if (!declaration) return message.warning('请选择报关类型');
    try {
      const { status } = await removeWaybillAPI({
        waybillRecordId,
        declarationType: declaration,
      });
      if (status) {
        message.success('操作成功');
        setIsModalOpen(false);
        props?.getDetail();
      }
    } catch (error) {}
  };
  return (
    <div>
      <SuperTables
        instanceId="WaybillDetails2"
        columns={columns}
        isTree={true}
        ref={actionRef}
        rowSelection={(value: any) => {
          console.log('value', value);
          setCheckData(value);
        }}
        TotalConfiguration={[
          {
            desc: '票数',
            field: 'groupNumber',
            // value: statistic?.sumReceiptAmount,
          },
          {
            desc: '件数',
            field: 'pieceNum',
            // value: statistic?.sumReceiptAmountCny,
          },
          {
            desc: '立方',
            field: 'totalvolume',
            // value: statistic?.sumRemainAmount,
          },
          {
            desc: '重量',
            field: 'totalweight',
            // value: statistic?.sumWriteOffAmount,
          },
        ]}
        options={false}
        toolBarRender={() =>
          !isDelivered ? (
            <Space>
              <AdditionalGoods details={details} Refresh={refreshTable} />
              <Popconfirm
                key="perform"
                title="提示"
                description="确定要移除吗"
                onConfirm={handleRemove}
                onCancel={() => {
                  message.info('已取消操作');
                }}
                okText="确认"
                cancelText="再想想"
              >
                <Button ghost key="primary" type="primary">
                  移除
                </Button>
              </Popconfirm>
            </Space>
          ) : (
            false
          )
        }
        request={async (params: any, action: any) => {
          // console.log('tableactiveKey',activeKey);
          console.log('params', params);
          let msg = await getWaybillList2({
            condition: params?.condition || {},
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            id: state?.id,
          });
          return {
            data:
              msg?.data?.list?.map((item: any) => {
                const fbaNos = [
                  ...new Set(item?.pieceList?.map((item: any) => item.fbaNo)),
                ];
                const fbaReferenceIds = [
                  ...new Set(
                    item?.pieceList?.map((item: any) => item.fbaReferenceId),
                  ),
                ];
                return {
                  ...item,
                  totalvolume: item.volume,
                  totalweight: item.weight,
                  fbaNo: fbaNos,
                  fbaReferenceId: fbaReferenceIds,
                  children: item?.pieceList?.map((items: any) => {
                    return {
                      ...items,
                      pieceNumber: 1,
                    };
                  }),
                  groupNumber: 1,
                };
              }) || [],
            success: msg?.status,
            total: msg?.data?.list?.length,
          };
        }}
        filters={{
          keyword: {
            type: 'search',
            value: '',
            tabs: true,
          },
          fbaCode: {
            desc: 'FBA仓库',
            type: 'custom',
            value: '',
            generate: async (v: any) => {
              const res = await getWarehouseListAPI({ keyword: v, type: 1 });
              if (res?.status) {
                return res?.data?.list.map((item: any) => {
                  return {
                    label: item?.name || item?.fbaCode,
                    value: item?.name || item?.fbaCode,
                  };
                });
              }
            },
          },
          clientId: {
            desc: '客户',
            type: 'client',
            value: '',
          },
        }}
      />
    </div>
  );
};
export default WaybillDetails;
