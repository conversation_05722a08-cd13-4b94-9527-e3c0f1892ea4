import { ProCard, ProDescriptions, ProList } from '@ant-design/pro-components';
import React, { useEffect, useRef, useState } from 'react';
import {
  Avatar,
  Button,
  Dropdown,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Tabs,
  Tag,
} from 'antd';
import { useLocation } from 'umi';
import WaybillDetails from './WaybillDetails';
import { formatTime } from '@/utils/format';
import styles from './index.less';
import {
  getDeclarationAPI,
  printCarAPI,
  ReadyFileApprove,
  removePodByBatchId,
  SaveDeclaration,
} from '@/services/bol';
import {
  convertObjectToArray,
  getColorByValue,
  getLabelByValue,
  prepareFileState,
  ProgressStateType,
  YuProgressStateType,
} from '@/utils/constant';
import {
  getShipmentDetail,
  outboard,
  storageDetailAPI,
} from '@/services/ConfiguredCar';
import PalletsDetail from './PalletsDetail';
import UnreceivedWaybill from '@/pages/PreConfiguredCar/Detail/UnreceivedWaybill';
import { saveAs } from 'file-saver';
import SuperTables from '@/components/SuperTables';
import UploadPodModal from '@/pages/CargoManagement/Modal/UploadPodModal';
import {
  EllipsisOutlined,
  ExclamationCircleFilled,
  VerticalAlignTopOutlined,
} from '@ant-design/icons';
import RemarksModal from '@/components/RemarksModal';
import dayjs from 'dayjs';
import { getRemarkListAPI, remarkTopAPI } from '@/services/productAndPrice/api';
import MassProductionModal from '@/pages/CargoManagement/Modal/MassProductionModal';
import { deleteRemarkAPI } from '@/services/financeApi';
import {
  downloadFileResaleOrderAPI,
  getResaleOrderDeleteFile,
  getResaleOrderFile,
  uploadReResaleOrder,
} from '@/services/booking';
import MrTable from '@/components/MrTable';
import ExcelReader from '@/components/ExcelReader';

const Details = () => {
  const location = useLocation();
  const { state }: any = location;
  const actionRef = useRef();
  const [details, setDetails] = useState<any>({});
  const [tab, setTab] = useState<any>(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [clearance, setClearance] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [notesList, setNotesList] = useState<any>([]); //备注列表
  const { confirm } = Modal;
  const isDelivered = details?.state > 39;
  /* 获取备注列表 */
  const getRemarksList = async (id: any) => {
    /*    if (batch) {
      return;
    }*/
    try {
      const { data, status } = await getRemarkListAPI({
        start: 0,
        len: 1000,
        outId: id,
      });
      if (status) {
        data?.list?.forEach((item: any) => {
          if (item.avatar) {
            item.avatar = `https://static.kmfba.com/${item.avatar}`;
          }
        });
        console.log(data, 'data?.listdata?.list');

        setNotesList(data?.list || []);
      }
    } catch (err) {
      console.error('获取备注失败', err);
    }
  };
  /*出库*/
  const handleOutPallets = async () => {
    console.log('details', details);
    const { no, id } = details;
    const res = await outboard({ id: id });
    console.log('res', res);
    getDetail();
  };
  const getDetail = async () => {
    try {
      const { status, data } = await storageDetailAPI({
        id: state?.id,
      });
      if (status) {
        // console.log('data', data);
        setDetails(data);
      }
    } catch (err) {
      console.log('获取信息抛出异常: ', err);
    }
  };
  //打印交货清单
  const printSurfaceSheet3 = async (id: any) => {
    try {
      const res = await printCarAPI({ id });
      console.log('res', res);
      const fileName = res?.headers?.['content-disposition'];
      console.log('fileName', fileName);
      const parts = fileName.split('filename=');
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, parts);
    } catch (e) {
      console.error('打印面单失败', e);
    }
  };
  useEffect(() => {
    if (state?.id) {
      getDetail();
      getRemarksList(state?.id);
    }
  }, [state?.id]);
  const columns = [
    {
      title: '预约送货日期',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text: any, record: any) => {
        return (
          <div>
            <a
              href={`https://static.kmfba.com/${record.url}`}
              target="_blank"
              rel="noreferrer"
            >
              {record.fileName}
            </a>
          </div>
        );
      },
    },
    {
      title: '预约标识',
      dataIndex: 'state',
      key: 'state',
      render: (text: any, row: any) => {
        console.log('text', text);
        return (
          <Select
            options={convertObjectToArray(prepareFileState, 'number')}
            onChange={(value: any) => {
              ReadyFileApprove({ id: row?.id, type: value }).then(() =>
                getDetail(),
              );

              console.log('value', value);
            }}
            value={text}
            style={{ minWidth: '80px' }}
          />
        );
      },
    },
    {
      title: '预约号/ISA',
      dataIndex: ['uploader', 'name'],
      key: 'uploaderId',
      render: (text: any, row: any) => {
        console.log('row', row);
        //1客户2员工
        return (
          <div>
            {row?.source === '1' ? '客户' : '客服'}-{text}
          </div>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: any) => {
        return <div>{formatTime(text)}</div>;
      },
    },
  ];

  const items: any = [
    {
      key: '1',
      label: `货物明细`,
      children: (
        <WaybillDetails
          getDetail={getDetail}
          state={state}
          details={details}
          id={state?.id}
          isDelivered={isDelivered}
        />
      ),
    },
    {
      key: '2',
      label: `托盘`,
      children: (
        <PalletsDetail
          getDetail={getDetail}
          state={state}
          details={details}
          isDelivered={isDelivered}
        />
      ),
    },
    /*    ...(isDelivered
      ? []
      : [*/
    {
      key: '3',
      label: '未到货明细',
      children: (
        <UnreceivedWaybill
          getDetail={getDetail}
          state={state}
          details={details}
        />
      ),
    },
    /*   ]),*/
  ];
  const onChange = (key: string) => {
    setTab(key);
  };

  const handleEdit = async (params: any) => {
    try {
      const { status } = await SaveDeclaration({
        ...params,
        id: state?.id,
      });
      if (status) {
        await getDetail();
      }
    } catch (err) {
      console.log('信息抛出异常: ', err);
    }
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (Keys: any, selectedRows: any) => {
      setSelectedRowKeys(Keys);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
  };
  // 合并报关
  const combineClearance = async () => {
    // if (selectedRowKeys?.length < 2) return message.warning('至少勾选两个报关单')
    const { status } = await getDeclarationAPI({
      ids: selectedRowKeys?.join(','),
      mainId: state?.id,
    });
    if (status) {
      message.success('操作成功');
      setIsModalOpen(false);
      setSelectedRowKeys([]);
      getDetail();
      // actionRef.current?.reload()
    }
  };
  const handleDelete = async (v: any) => {
    console.log('v', v);
    const { status } = await removePodByBatchId({
      shipmentId: state?.id,
      batchId: v,
    });
    if (status) {
      message.success('操作成功');
      getDetail();
    }
  };
  const uploadingColumns: any = [
    {
      title: '文件',
      dataIndex: 'name',
      selectable: 'none',
      className: 'nonSelectable',
      fieldFormat: (record: any) => {
        return record?.name;
      },
      render: (text: any, record: any) => {
        return (
          <>
            <a
              onClick={() => {
                downloadFile(record?.url);
              }}
            >
              {record?.name}
            </a>
          </>
        );
      },
    },
    {
      title: '文件类型',
      dataIndex: 'type',
      fieldFormat: (record: any) => {
        return record?.typeDesc;
      },
    },
    {
      title: '上传人',
      dataIndex: 'userName',
      fieldFormat: (record: any) => {
        return record?.userName;
      },
      // render: (text: any, record: any) => {
      //   return (
      //     <Space>
      //       <Avatar
      //         src={`${
      //           record?.avatar?.includes('http')
      //             ? record?.avatar
      //             : `https://static.kmfba.com/${record?.avatar}`
      //         }`}
      //       />
      //       <div>{record?.userName}</div>
      //     </Space>
      //   );
      // },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      width: 250,
      key: 'option',
      valueType: 'option',
      render: (text: any, record: any) => [
        <Popconfirm
          key={1}
          title="删除确认"
          description="确认要删除次文件吗?"
          onConfirm={() => {
            deleteRelatedFile(record?.id);
          }}
          okText="删除"
          cancelText="取消"
          disabled={record?.auto === 1}
        >
          <Button type="link" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];
  const podcolumns = [
    {
      title: '文件',
      dataIndex: 'waybillNo',
      hideInSearch: true,
      width: 150,
      render: (text: any, record: any) => {
        /*没有就返回子单号*/
        return (
          <Button
            type={'link'}
            onClick={() => {
              window.open(record.url);
            }}
          >
            查看文件
          </Button>
        );
      },
    },
    {
      title: '关联单号',
      dataIndex: 'waybillNOs',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '派送标志',
      dataIndex: 'dest',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '操作人',
      dataIndex: 'userName',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      width: 150,
      fieldFormat: (record: any) => {
        /*没有就返回子单号*/
        return formatTime(record?.createTime);
      },
    },
    {
      title: '操作',
      dataIndex: 'do',
      hideInSearch: true,
      width: 150,
      fixed: 'right',
      render: (text: any, record: any) => {
        return (
          <Space>
            <a
              style={{ color: 'red' }}
              onClick={() => {
                console.log('record', record);
                console.log('text', text);
                confirm({
                  title: '确认删除该文件吗',
                  icon: <ExclamationCircleFilled rev={undefined} />,
                  content: `删除后不可恢复`,
                  onOk() {
                    handleDelete(record?.batchId);
                  },
                  onCancel() {
                    //message.info('已取消删除');
                  },
                });
              }}
            >
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  /* 下载文件 */
  const downloadFile = async (url: string) => {
    try {
      const response: any = await downloadFileResaleOrderAPI({
        url,
      });
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
    } catch (err) {
      console.log('下载失败: ', err);
    }
  };
  /* 删除相关文件 */
  const deleteRelatedFile = async (id: string) => {
    try {
      const { status } = await getResaleOrderDeleteFile({
        outId: state?.id,
        fileId: id,
      });
      if (status) {
        message.success('删除成功');
        getRemarksList(state?.id);
      }
    } catch (e) {
      console.error('删除相关文件失败', e);
    }
  };
  /* 备注置顶 */
  const setTop = async (id: any) => {
    try {
      const { status } = await remarkTopAPI({
        commentId: id,
        type: 1,
      });
      if (status) {
        message.success('置顶成功');
        getRemarksList(state?.id);
      }
    } catch (err) {
      console.error('置顶操作失败', err);
    }
  };
  /* 取消置顶 */
  const cancelTop = async (id: any) => {
    try {
      const { status } = await remarkTopAPI({
        commentId: id,
        type: 0,
      });
      if (status) {
        message.success('取消置顶成功');
        getRemarksList(state?.id);
      }
    } catch (err) {
      console.error('取消置顶操作失败', err);
    }
  };

  /* 删除备注 */
  const deleteComment = async (id: any) => {
    try {
      const { status } = await deleteRemarkAPI({
        id: id,
      });
      if (status) {
        message.success('删除备注成功');
        getRemarksList(state?.id);
      }
    } catch (err) {
      console.error('删除备注失败：', err);
    }
  };
  /* 上传文件 */
  const uploadFile = async (type: any, files: any) => {
    const { status } = await uploadReResaleOrder({
      outId: state?.id,
      type: type,
      files: files,
    });
    if (status) {
      message.success('上传成功');
      getRemarksList(state?.id);
      // actionRef?.current?.refresh();
    }
  };
  return (
    <div className={styles.wrap}>
      <ProCard
        gutter={8}
        title="基本信息"
        // extra={
        //   isDelivered &&
        //   details?.state < 50 && (
        //     <MassProductionModal
        //       btnText={'出库'}
        //       myList={[{ id: details?.id }]}
        //       refresh={getDetail}
        //     />
        //   )
        // }
        style={{ marginBottom: '10px' }}
      >
        <ProDescriptions actionRef={actionRef} column={4} editable={false}>
          <ProDescriptions.Item
            dataIndex="no"
            label="单号"
            editable={false}
            valueType="text"
          >
            {details?.no}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['state']}
            label="状态"
            valueType="text"
            editable={false}
          >
            <Tag
              color={
                ProgressStateType?.find(
                  (item: any) => item?.value == details?.state,
                )?.color
              }
            >
              {
                ProgressStateType?.find(
                  (item: any) => item?.value == details?.state,
                )?.label
              }
            </Tag>
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="clientName"
            label="客户"
            valueType="text"
            editable={false}
          >
            {details?.clientName}
          </ProDescriptions.Item>

          <ProDescriptions.Item
            dataIndex={['waybillNum']}
            label="件数"
            valueType="text"
            editable={false}
          >
            {details?.waybillNum}
          </ProDescriptions.Item>

          <ProDescriptions.Item
            dataIndex="palletsNum"
            label="托数"
            valueType="text"
            editable={false}
          >
            {details?.palletsNum}
          </ProDescriptions.Item>
          {/* <ProDescriptions.Item
            dataIndex={['slotId']}
            label="库位"
            valueType="text"
            editable={false}
          >
            {details?.slotId}
          </ProDescriptions.Item> */}

          <ProDescriptions.Item
            dataIndex={['dest']}
            label="标识"
            valueType="text"
            editable={false}
          >
            {details.dest}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['createTime']}
            label="创建时间"
            valueType="text"
            editable={false}
          >
            {details.createTime ? formatTime(details?.createTime) : '-'}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['storeTime']}
            label="上架时间"
            valueType="text"
            editable={false}
          >
            {details.storeTime ? formatTime(details?.storeTime) : '-'}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex={['completeTime']}
            label="结束时间"
            valueType="text"
            editable={false}
          >
            {details.completeTime ? formatTime(details?.completeTime) : '-'}
          </ProDescriptions.Item>
        </ProDescriptions>
      </ProCard>
      {details?.appointmentTime && (
        <ProCard
          gutter={8}
          title="预约信息"
          /* extra={<Button type={'primary'}>更新</Button>}*/
        >
          <ProDescriptions actionRef={actionRef} column={4} editable={false}>
            <ProDescriptions.Item
              dataIndex={['appointmentTime']}
              label="预约送货日期"
              valueType="text"
              editable={false}
            >
              {formatTime(details?.appointmentTime)}
            </ProDescriptions.Item>
            {/*         <ProDescriptions.Item
              dataIndex={['isa']}
              label="预约标识"
              valueType="text"
              editable={false}
          >
            {details.isa}
          </ProDescriptions.Item>*/}
            <ProDescriptions.Item
              dataIndex={['isa']}
              label="预约号/ISA"
              valueType="text"
              editable={false}
            >
              {details?.isa}
            </ProDescriptions.Item>
            <ProDescriptions.Item
              dataIndex={['isa']}
              label="备注"
              valueType="text"
              editable={false}
            >
              {details?.remark}
            </ProDescriptions.Item>
          </ProDescriptions>
        </ProCard>
      )}
      {isDelivered && (
        <ProCard
          gutter={8}
          style={{
            margin: '8px 0',
          }}
          title="POD"
          extra={
            isDelivered && (
              <UploadPodModal
                btnText={'上传POD'}
                record={details}
                type={'primary'}
                style={{ color: '#fff' }}
                refresh={getDetail}
              />
            )
          }
        >
          <SuperTables
            instanceId={'podlist'}
            dataSource={details?.podList}
            columns={podcolumns}
            height={200}
            isHidePagination={true}
          />
        </ProCard>
      )}
      <ProCard style={{ marginBottom: '10px' }}>
        <SuperTables
          columns={uploadingColumns}
          // options={false}
          instanceId={'Booking/cabin/sellCabinet/detail'}
          request={async (params: any, action: any) => {
            actionRef.current = action;
            const res = await getResaleOrderFile({
              ...params,
              outId: state?.id,
            });
            return {
              data: res.data.list || [],
              success: res.status,
              total: res.data?.total,
            };
          }}
          height={300}
          toolBarRender={() => (
            <Dropdown
              key="menu"
              // disabled={!isAccessibleFlag}
              menu={{
                items: [
                  {
                    label: (
                      <ExcelReader
                        Readertype={true}
                        onChange={(e: any, file: any) => {
                          uploadFile(36, file);
                        }}
                      >
                        参考文件
                      </ExcelReader>
                    ),
                    key: '1',
                  },
                  {
                    label: (
                      <ExcelReader
                        Readertype={true}
                        onChange={(e: any, file: any) => {
                          uploadFile(38, file);
                        }}
                      >
                        客户BOL
                      </ExcelReader>
                    ),
                    key: '2',
                  },
                ],
              }}
            >
              <Button>
                上传
                <EllipsisOutlined />
              </Button>
            </Dropdown>
          )}
        />
      </ProCard>
      <ProCard
        style={{ marginBottom: '10px', marginTop: '10px' }}
        gutter={24}
        title={<span style={{ fontWeight: '700' }}>备注</span>}
        extra={
          <RemarksModal
            btnText="备注"
            btnType="primary"
            refresh={() => {
              getRemarksList(details?.id);
            }}
            ghost={false}
            style={{}}
            batch={true}
            record={{
              id: details?.id,
            }}
          />
        }
      >
        <ProList<any>
          // onRow={(record: any) => {
          //   return {
          //     // onMouseEnter: () => {
          //     //   console.log(record);
          //     // },
          //     onClick: () => {
          //       console.log(record);
          //     },
          //   };
          // }}
          rowKey="id"
          // headerTitle="基础列表"
          // tooltip="基础列表的配置"
          dataSource={notesList}
          showActions="hover"
          showExtra="hover"
          metas={{
            title: {
              dataIndex: 'userName',
            },
            avatar: {
              dataIndex: 'avatar',
            },
            description: {
              dataIndex: 'content',
            },
            subTitle: {
              render: (text: any, record: any) => {
                return (
                  <Space size={0}>
                    <div className="text-12px">
                      {dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                    <div>
                      <Button
                        type="link"
                        onClick={() => {
                          setTop(record?.id);
                        }}
                        size="small"
                        icon={<VerticalAlignTopOutlined />}
                      >
                        置顶
                      </Button>
                    </div>
                    {record?.top === 1 && (
                      <Button
                        type="link"
                        size="small"
                        className="color-coolGray"
                        onClick={() => cancelTop(record?.id)}
                      >
                        取消置顶
                      </Button>
                    )}
                  </Space>
                );
              },
            },
            actions: {
              render: (text, row) => {
                const currentUser = localStorage.getItem('user');
                const userId = currentUser ? JSON.parse(currentUser)?.id : null;

                if (row.uid === userId) {
                  return (
                    <Button
                      type="link"
                      onClick={() => {
                        deleteComment(row.id);
                      }}
                      key="del"
                      danger
                    >
                      删除
                    </Button>
                  );
                }
                return null;
              },
            },
          }}
        />
      </ProCard>
      <ProDescriptions
        actionRef={actionRef}
        column={3}
        editable={{
          onSave: async (keypath: any, newInfo) => {
            handleEdit(newInfo);
            return true;
          },
        }}
      ></ProDescriptions>

      <ProCard style={{ marginBlockStart: 8, background: '#fff' }}>
        <div style={{}}>
          <Tabs
            defaultActiveKey="1"
            onChange={onChange}
            items={items}
            tabBarExtraContent={
              isDelivered && (
                <Button
                  type="primary"
                  onClick={() => {
                    printSurfaceSheet3(details?.id);
                  }}
                  key={2}
                >
                  导出装车明细
                </Button>
              )
            }
          />
        </div>
      </ProCard>
    </div>
  );
};
export default Details;
