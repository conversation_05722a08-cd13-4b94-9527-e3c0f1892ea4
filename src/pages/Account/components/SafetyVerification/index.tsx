import { Modal } from 'antd';
import { useEffect, useState } from 'react';
import classnames from 'classnames';
import CheckTheFrame from '../CheckTheFrame';

import s from './index.less';
const App = ({ identity }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const listItem: any = {
    4: { title: '密码', img: 'bg-img4', value: 4 },
    1: { title: '邮箱验证', img: 'bg-img1', value: 1 ,key:identity?.list?.find((item:any)=>item.type===1)?.email},
    2: { title: '手机号码', img: 'bg-img2', value: 2 ,key:identity?.list?.find((item:any)=>item.type===2)?.phone},
  };

  const [checkData, setCheckData] = useState<any>({});

  const triggerModal = (item: any) => {
    setCheckData({...item,isModalOpen:true})
  };
  
  useEffect(() => {
    if (identity.isValidated === false) {
      showModal();
    }
  }, [identity]);
  return (
    <>
      <CheckTheFrame checkData={checkData} closeOpen={setIsModalOpen} />
      <Modal
        width={800}
        title="安全校验"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
        keyboard={false}
        maskClosable={false}
      >
        <div className={s.title}>开始修改前，请先完成身份验证，以下方式任选一种</div>
        <div className={s.warp}>
          {identity?.list
            ? identity?.list.map((item: any) => {
                return (
                  <div
                    className={classnames(s.card, s[listItem[item.type].img])}
                    key={listItem[item.type].value}
                    onClick={() => triggerModal(listItem[item.type])}
                  >
                    <div className={s['card-text']}>{listItem[item.type].title}</div>
                  </div>
                );
              })
            : null}
        </div>
      </Modal>
    </>
  );
};
export default App;
