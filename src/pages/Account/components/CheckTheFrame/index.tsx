import React, { useEffect, useRef, useState } from 'react';
import { Form, Input, Modal, message } from 'antd';
import service from '@/services/home';
import AES from '@/utils/secret';
import { ProFormCaptcha } from '@ant-design/pro-components';
const { Encrypt } = AES;
const { checkCodeAPI, sendEmailCodeAPI ,getPhoneCodeAPI} = service.UserHome;
const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

const CheckTheFrame = ({ checkData, closeOpen }: any) => {
  const captchaRef = useRef<any>();
  const { Search } = Input;
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleCancel = () => {
    form.resetFields();
    setIsModalOpen(false);
  };
  /* 短信邮箱密码校验 */
  const checkCode = async (values: any) => {
    try {
      const { status } = await checkCodeAPI({
        ...values,
        type: checkData?.value,
      });
      if (status) {
        message.success('校验成功');
        handleCancel();
        closeOpen(false);
      }
    } catch (error) {
      console.log('短信邮箱密码校验失败: ', error);
    }
  };
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    form.validateFields().then((values) => {
      if (checkData.title === '密码') {
        values.code = Encrypt(values.code);
      }
      checkCode(values);
    });
  };

  useEffect(() => {
    if (checkData.isModalOpen) {
      form.setFieldsValue({
        account: checkData.key,
      });
      showModal();
    }
  }, [checkData]);

  /* 邮箱验证 */
  const EmailVerification = () => {
    const [enterButtonText, setEnterButtonText] = useState('发送验证码');
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [count, setCount] = useState(60);
    const [disabled, setDisabled] = useState(false);

    /* 发送邮箱验证码 */
    const sendEmailCode = async () => {
      try {
        const { status } = await sendEmailCodeAPI({
          email: form.getFieldValue('account'),
        });
        if (status) {
          message.success('验证码发送成功');
          setDisabled(true);
          const timer = setInterval(() => {
            setCount((prevCount) => {
              const newCount = prevCount - 1;
              setEnterButtonText(`${newCount}秒后重试`);
              if (newCount === 0) {
                clearInterval(timer);
                setEnterButtonText('重新发送验证码');
                setCount(60);
                setDisabled(false);
              }
              return newCount;
            });
          }, 1000);
        }
      } catch (error) {
        console.log('发送邮箱验证码失败: ', error);
      }
    };

    const onSearch = () => {
      form.validateFields(['account']).then(() => {
        sendEmailCode();
      });
    };

    return (
      <>
        <Form.Item
          name="account"
          label="邮箱地址"
          rules={[
            {
              required: true,
              message: '请输入邮箱地址',
            },
            // {
            //   pattern:
            //     /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            //   message: '请输入正确的邮箱地址',
            // },
          ]}
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          name="code"
          label="校验码"
          rules={[
            {
              required: true,
              message: '请输入校验码',
            },
          ]}
        >
          <Search
            style={{ width: '80%' }}
            enterButton={enterButtonText}
            onSearch={onSearch}
            loading={disabled}
          />
        </Form.Item>
      </>
    );
  };

  /* 旧密码验证 */
  const OldPasswordVerification = () => {
    return (
      <>
        <Form.Item
          name="code"
          label="密码"
          rules={[
            {
              required: true,
              message: '请输入密码',
            },
          ]}
        >
          <Input.Password />
        </Form.Item>
      </>
    );
  };

  /* 手机号验证 */
  const PhoneVerification = () => {
    return (
      <>
        <Form.Item
          name="account"
          label="手机号"
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
            
          ]}
        >
          <Input disabled />
        </Form.Item>
        <ProFormCaptcha
            phoneName="account"
            name="code"
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
            ]}
            placeholder="请输入验证码"
            onGetCaptcha={async (phone) => {
              try {
                const { status } = await getPhoneCodeAPI({
                  phone: phone,
                });
                if (status) {
                  message.success('验证码发送成功！');
                  captchaRef.current?.focus();
                }
              }catch (error) {
                console.log('验证码发送失败: ', error);
              }
            }}
            fieldRef={captchaRef}
            label="验证码"
          />
      </>
    );
  };

  /* 策略模式 */
  const strategy: any = {
    1: <EmailVerification />,
    4: <OldPasswordVerification />,
    2: <PhoneVerification />,
  };
  return (
    <>
      <Modal
        title={checkData.title}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <Form {...layout} form={form} name="control-hooks">
          {checkData && strategy[checkData.value]}
        </Form>
      </Modal>
    </>
  );
};

export default CheckTheFrame;
