import { UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Upload, message } from 'antd';
import ImgCrop from 'antd-img-crop';
import { REQUESTADDRESS } from '@/globalData';
import { useModel } from 'umi';
import { useEffect, useState } from 'react';

const HeadPortarait = ({ userList, getUserInfoFun }: any) => {
  const { initialState, setInitialState } = useModel('@@initialState');
  /* loding */
  const [loading, setLoading] = useState<boolean>(false);
  const props: any = {
    name: 'multiFile',
    action: `${REQUESTADDRESS}/user/uploadAvatar`,
    // action: `api/home/<USER>/uploadAvatar`,
    accept: 'image/*',
    headers: {
      token: localStorage.getItem('token'),
    },
    withCredentials: true,
    maxCount: 1,
    //本地测试 走代理
    // beforeUpload: (file, fileList) => {
    //   const fd = new FormData();
    //   fd.append('multiFile', file, file.name);
      // fetch('/api/waybill/home/<USER>/uploadAvatar', {
      //   method: 'POST',
      //   body: fd,
      //   headers: {
      //     token: localStorage.getItem('token'),
      //   },
      // });
    //   return false; // 禁止 antd 标准处理
    // },
    showUploadList: false,
    onChange(info: any) {
      setLoading(true);
      // if (info.file.status !== 'uploading') {
      //   console.log(info.file, info.fileList);
      // }
      if (info.file.status === 'done') {
        if (info.file.response?.status) {
          message.success(`${info.file.name} 上传头像成功`);
          setLoading(false);
          getUserInfoFun();
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
        setLoading(false);
      }
    },
  };
  const modifyUrl = (url: any) => {
    if (url.startsWith('https')) {
      return url;
    } else {
      return 'https://static.kmfba.com/' + url;
    }
  };
  useEffect(() => {
    setInitialState({
      ...initialState,
      currentUser: {
        ...initialState?.currentUser,
        avatar: modifyUrl(userList?.info.avatar),
      },
    });
  }, [userList?.info.avatar]);
  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ color: '#AEAEAE' }}>头像：</div>
        <div>
          <Avatar
            size={64}
            icon={<UserOutlined />}
            src={modifyUrl(userList?.info.avatar)}
          />
        </div>
        <div>
          <ImgCrop rotationSlider quality={0.1}>
            <Upload {...props}>
              <Button type="link" loading={loading}>
                上传
              </Button>
            </Upload>
          </ImgCrop>
        </div>
      </div>
    </>
  );
};
export default HeadPortarait;
