import {  List } from 'antd';

// import BindPhoneModal from '../BindPhoneModal';
import UserID from './UserID';
import HeadPortarait from './HeadPortrait';
import FullName from './FullName';
import MobilePhone from './MobilePhone';
import Mailbox from './Mailbox';
// import WeChat from './WeChat';

const Business = (props: any) => {
  const { userList,getUserInfoFun } = props;
  const data = [
    {
      title: '用户ID',
      value: <UserID userList={userList} />,
    },
    {
      title: '头像',
      value: <HeadPortarait userList={userList} getUserInfoFun={getUserInfoFun} />,
    },
    {
      title: '姓名',
      value: <FullName userList={userList} />,
    },
    {
      title: '手机号码',
      value: <MobilePhone userList={userList}  getUserInfoFun={getUserInfoFun} />,
    },
    {
      title: '邮箱地址',
      value: <Mailbox userList={userList} getUserInfoFun={getUserInfoFun} />,
    },
    // {
    //   title: '微信号',
    //   value: <WeChat />,
    // },
  ];


  return (
    <>
      <List
        size="large"
        dataSource={data}
        renderItem={(item: any) => <List.Item>{item.value}</List.Item>}
      />
    </>
  );
};
export default Business;
