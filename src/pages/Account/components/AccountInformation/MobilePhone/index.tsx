import { useEffect, useState } from 'react';
import SafetyVerification from '../../SafetyVerification';
import usePreCheck from '../../hooks/usePreCheck';
import ModifyTheBulletBox from '../components/ModifyTheBulletBox';
import { Button, Modal, message } from 'antd';
import service from '@/services/home';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { unbindPhoneAPI } = service.UserHome;
const { confirm } = Modal;
const MobilePhone = ({ userList, getUserInfoFun }: any) => {
  const { setYseNo, identity }: any = usePreCheck();
  //是否存在手机号
  const [isExistPhone, setIsExistPhone] = useState(false);
  useEffect(() => {
    if (userList?.accountList?.find((item: any) => item.type === 2)?.key) {
      setIsExistPhone(true);
    } else {
      setIsExistPhone(false);
    }
  }, [userList]);
  /* 这里逻辑写的有问题麻烦了。。。时间太仓促 将错就错了 */
  const [text, setText] = useState<any>(null);
  useEffect(() => {
    if (identity.isValidated && text === '解绑') {
      confirm({
        title: '解绑手机号码',
        icon: <ExclamationCircleFilled />,
        content: '请确认要解绑手机吗？',
        async onOk() {
          if (identity.isValidated) {
            try {
              const { status } = await unbindPhoneAPI({});
              if (status) {
                message.success('解绑成功！');
                getUserInfoFun();
              }
            } catch (error) {
              console.log('解绑手机失败: ', error);
            }
          }
        },
        onCancel() {
          console.log('取消解绑');
        },
      });
    }
  }, [identity]);
  /* 解绑手机 */
  function unbindPhone() {
    setText('解绑');
    setYseNo(true);
  }

  return (
    <>
      <SafetyVerification identity={identity} />
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ color: '#AEAEAE' }}>手机号码：</div>
        <div>
          {userList?.accountList?.find((item: any) => item.type === 2)?.key ||
            ' '}
        </div>
        {isExistPhone ? (
          <Button type="link" onClick={unbindPhone}>
            解绑
          </Button>
        ) : (
          <>
            <Button
              type="link"
              onClick={() => {
                setText('去绑定');
                setYseNo(true);
              }}
            >
              去绑定
            </Button>
            <ModifyTheBulletBox
              getUserInfoFun={getUserInfoFun}
              title="绑定手机"
              identity={identity}
            />
          </>
        )}
      </div>
    </>
  );
};
export default MobilePhone;
