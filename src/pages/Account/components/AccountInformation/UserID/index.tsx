import usePreCheck from "../../hooks/usePreCheck";
import SafetyVerification from "../../SafetyVerification";
import ModifyTheBulletBox from "../components/ModifyTheBulletBox";
const UserID = ({userList}:any) => {
  const {setYseNo,identity}:any= usePreCheck();
  /* 重置密码前置校验 */

  /* 重置密码 */
  function resetPassword() {
    setYseNo(true);
  }
  return (
    <>
      <ModifyTheBulletBox title="重置密码" identity={identity}  />
      <SafetyVerification identity={identity} />
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ color: '#AEAEAE' }}>用户ID：</div>
        <div>{userList?.info?.id || ''}</div>
        <div
          style={{
            marginLeft: '40px',
            color: '#4071FF',
            cursor: 'pointer',
          }}
          onClick={resetPassword}
        >
          重置密码
        </div>
      </div>
    </>
  );
};
export default UserID;
