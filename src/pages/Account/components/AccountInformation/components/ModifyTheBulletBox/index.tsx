import { Form, Input, Modal, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import service from '@/services/home';
import AES from '@/utils/secret';
import { ProFormCaptcha } from '@ant-design/pro-components';
const { Encrypt } = AES;
const { resetPasswordAPI ,getPhoneCodeAPI,checkCodeAPI,bindPhoneAPI,sendEmailCodeAPI,bindEmailAPI} = service.UserHome;
const layout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};
const ModifyTheBulletBox = ({ title, identity,getUserInfoFun }: any) => {
  const captchaRef = useRef<any>();
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleCancel = () => {
    form.resetFields();
    setIsModalOpen(false);
  };
  /* 重置密码 */
  const resetPassword = async (values: any) => {
    try {
      const { status } = await resetPasswordAPI({
        code: values.code,
      });
      if (status) {
        message.success('密码修改成功！');
        handleCancel();
      }
    } catch (error) {
      console.log('重置密码失败: ', error);
    }
  };
  /* 绑定手机 */
  const bindPhone = async (values: any) => {
    try {
      const { status } = await bindPhoneAPI({
        phone: values.phone,
      });
      if (status) {
        getUserInfoFun()
        message.success('绑定成功！');
        handleCancel();
      }
    } catch (error) {
      console.log('绑定手机失败: ', error);
    }
  };

  /* 绑定邮箱 */
  const bindEmail = async (values: any) => {
    try {
      const { status } = await bindEmailAPI({
        email: values.email,
      });
      if (status) {
        getUserInfoFun()
        message.success('绑定成功！');
        handleCancel();
      }
    } catch (error) {
      console.log('绑定邮箱失败: ', error);
    }
  };


  /* 短信验证码校验 和 邮箱验证校验 */
  const getPhoneCode = async (values: any) => {
    try {
      const { status } = await checkCodeAPI({
        account: title=== '绑定手机'? values.phone:values.email,
        code: values.code,
        type: title === '绑定手机' ? 2 : 1,
      });
      if (status) {
        if(title === '绑定手机'){
          bindPhone(values);
        }
        if(title === '绑定邮箱'){
          bindEmail(values);
        }
      }
    } catch (error) {
      console.log('短信验证码校验失败: ', error);
    }

  };

  

  const handleOk = () => {
    form.validateFields().then((values) => {
      if (title === '重置密码') {
        values.code = Encrypt(values.code);
        resetPassword(values);
      }

      if (title === '绑定手机') {
        getPhoneCode(values);
      }

      if (title === '绑定邮箱') {
        console.log('绑定邮箱',values);
        getPhoneCode(values);
      }
    });
  };

  useEffect(() => {
    if (identity?.isValidated === true) {
      setIsModalOpen(true);
    }
  }, [identity]);

  /* 修改密码 */
  function ModifyPassword() {
    return (
      <>
        <Form.Item
          name="code"
          label="新密码"
          rules={[
            {
              required: true,
              message: '请输入新密码',
            },
          ]}
        >
          <Input.Password />
        </Form.Item>
        <Form.Item
          name="codes"
          label="确认密码"
          dependencies={['code']}
          rules={[
            {
              required: true,
              message: '请确认密码',
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('code') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次密码输入不一致'));
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>
      </>
    );
  }

  /* 绑定手机 */
  function BindPhone() {
    return (
      <>
        <Form.Item
          name="phone"
          label="手机号"
          
          rules={[
            {
              required: true,
              message: '请输入手机号码',
            },
          ]}
        >
          <Input placeholder="请输入手机号" />
        </Form.Item>
          <ProFormCaptcha
            phoneName="phone"
            name="code"
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
            ]}
            placeholder="请输入验证码"
            onGetCaptcha={async (phone) => {
              try {
                const { status } = await getPhoneCodeAPI({
                  phone: phone,
                });
                if (status) {
                  message.success('验证码发送成功！');
                  captchaRef.current?.focus();
                }
              }catch (error) {
                console.log('验证码发送失败: ', error);
              }
            }}
            fieldRef={captchaRef}
            label="验证码"
          />
      </>
    );
  }

  /* 绑定邮箱 */
  function BindEmail() {
    return (
      <>
        <Form.Item
          name="email"
          label="邮箱地址"
          rules={[
            {
              required: true,
              message: '请输入邮箱',
            },
          ]}
        >
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <ProFormCaptcha
            phoneName="email"
            name="code"
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
            ]}
            placeholder="请输入验证码"
            onGetCaptcha={async (phone) => {
              try {
                const { status } = await sendEmailCodeAPI({
                  email: phone,
                });
                if (status) {
                  message.success('验证码发送成功！');
                  captchaRef.current?.focus();
                }
              }catch (error) {
                console.log('验证码发送失败: ', error);
              }
            }}
            fieldRef={captchaRef}
            label="验证码"
          />
      </>
    );
  }



  /* 策略模式 */
  const strategy: any = {
    "重置密码": <ModifyPassword />,
    "绑定手机": <BindPhone />,
    "绑定邮箱": <BindEmail />,
  };
  return (
    <>
      <Modal
        title={title}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <Form {...layout} form={form} name="control-hooks">
          {title && strategy[title]}
        </Form>
      </Modal>
    </>
  );
};

export default ModifyTheBulletBox;
