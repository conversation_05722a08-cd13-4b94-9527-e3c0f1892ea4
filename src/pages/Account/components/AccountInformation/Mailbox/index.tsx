import { Button, Modal, message } from 'antd';
import { useEffect, useState } from 'react';
import usePreCheck from '../../hooks/usePreCheck';
import service from '@/services/home';
import { ExclamationCircleFilled } from '@ant-design/icons';
import SafetyVerification from '../../SafetyVerification';
import ModifyTheBulletBox from '../components/ModifyTheBulletBox';
const { unbindEmailAPI } = service.UserHome;
const { confirm } = Modal;
const Mailbox = ({ userList, getUserInfoFun }: any) => {
  const { setYseNo, identity }: any = usePreCheck();
  //是否存在邮箱
  const [isExistMail, setIsExistMail] = useState(false);
  useEffect(() => {
    if (userList?.accountList?.find((item: any) => item.type === 1)?.key) {
      setIsExistMail(true);
    } else {
      setIsExistMail(false);
    }
  }, [userList]);

  const [text, setText] = useState<any>(null);
  useEffect(() => {
    if (identity.isValidated && text === '解绑') {
      confirm({
        title: '解绑邮箱',
        icon: <ExclamationCircleFilled />,
        content: '请确认要解绑邮箱吗？',
        async onOk() {
          if (identity.isValidated) {
            try {
              const { status } = await unbindEmailAPI({});
              if (status) {
                message.success('解绑成功！');
                getUserInfoFun();
              }
            } catch (error) {
              console.log('解绑邮箱失败: ', error);
            }
          }
        },
        onCancel() {
          console.log('取消解绑');
        },
      });
    }
  }, [identity]);

  return (
    <>
      <SafetyVerification identity={identity} />
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ color: '#AEAEAE' }}>邮箱地址：</div>
        <div>
          {userList?.accountList?.find((item: any) => item.type === 1)?.key ||
            ' '}
        </div>
        {isExistMail ? (
          <Button
            type="link"
            onClick={() => {
              setText('解绑');
              setYseNo(true);
            }}
          >
            解绑
          </Button>
        ) : (
          <>
            <Button
              type="link"
              onClick={() => {
                setText('去绑定');
                setYseNo(true);
              }}
            >
              去绑定
            </Button>
            <ModifyTheBulletBox
              getUserInfoFun={getUserInfoFun}
              title="绑定邮箱"
              identity={identity}
            />
          </>
        )}
      </div>
    </>
  );
};
export default Mailbox;
