import { Input, Popconfirm, message } from 'antd';
import { useState } from 'react';
import { useModel } from 'umi';
import services from '@/services/home';
const { changeNameAPI } = services.UserHome;
const FullName = ({ userList }: any) => {
  const { initialState, setInitialState } = useModel('@@initialState');
  /* 是否编辑名称 */
  const [isEditName, setIsEditName] = useState<boolean>(true);
  /* 账号信息 */
  const [accountInfo, setAccountInfo] = useState<any>({
    name: userList?.info.name || '',
    avatar: '',
  });
  /* 修改名称接口 */
  const modifyName = async () => {
    const { status } = await changeNameAPI({
      name: accountInfo.name,
    });
    if (status) {
      setInitialState({
        ...initialState,
        currentUser: { ...initialState?.currentUser, name: accountInfo.name },
      });
      message.success('姓名修改成功');
      setIsEditName(true);
    }
  };
  /* 确认修改姓名 */
  const confirm = () => {
    modifyName();
  };
  const cancel = () => {
    message.info('已取消');
  };

  return (
    <>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ color: '#AEAEAE' }}>姓名：</div>
        <div>
          {isEditName ? (
            accountInfo.name || ''
          ) : (
            <Input
              value={accountInfo.name}
              onChange={(e) => {
                setAccountInfo({ ...accountInfo, name: e.target.value });
              }}
              placeholder="请输入要修改的名字"
            />
          )}
        </div>
        <div
          style={{
            marginLeft: '40px',
            color: '#4071FF',
            cursor: 'pointer',
          }}
        >
          {isEditName ? (
            <span onClick={() => setIsEditName(!isEditName)}>修改</span>
          ) : (
            <>
              <Popconfirm
                title="请确认"
                description="确认要变更为此名称吗？"
                onConfirm={confirm}
                onCancel={cancel}
                okText="确认"
                cancelText="取消"
              >
                <span>确认</span>
              </Popconfirm>
              <span
                style={{ marginLeft: '12px' }}
                onClick={() => {
                  setAccountInfo({
                    ...accountInfo,
                    name: userList?.info?.name || '',
                  });
                  setIsEditName(true);
                }}
              >
                取消
              </span>
            </>
          )}
        </div>
      </div>
    </>
  );
};
export default FullName;
