import { ProList } from "@ant-design/pro-components";
import { Space, Tag,message } from "antd";
import services from '@/services/home';
const { modifyDefaultEnterpriseAPI } = services.UserHome;
const Business = (props:any) => {
  const { tenantList }:any = props;
  /* 设置默认 */
  const setDefault = async(row:any)=>{
    const {status} = await modifyDefaultEnterpriseAPI({
      tenantId:row.tenantId
    })
    if(status){
      message.success('修改成功')
      props.getUserInfoFun()
    }
    
  }
  /* 通知刷新 */
  // const notifyModification = ()=>{

  // }
  return <>
      <ProList<any>
          rowKey="uid"
          dataSource={tenantList || []}
          style={{padding: 0}}
          metas={{
            title: {
              dataIndex: 'fullName',
              render: (text) => <div style={{ width: 300 }}>{text}</div>,
            },
            subTitle: {
              render: (_, row:any) => {
                return (
                  <div>
                    <Space size={100}>
                      <div></div>
                      <div style={{ width: 100 }}>
                        {row.isDefault ? (
                          <Tag color="magenta">默认</Tag>
                        ) : (
                          <a onClick={()=>setDefault(row)}> 设为默认 </a>
                        )}
                      </div>
                      <a key="view"> 退出 </a>
                    </Space>
                  </div>
                );
              },
            },
          }}
        />
    </>;
};
export default Business;