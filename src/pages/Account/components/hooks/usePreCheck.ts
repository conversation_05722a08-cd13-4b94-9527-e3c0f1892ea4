import { useState, useEffect } from 'react';
import service from '@/services/home';
const { identityCheckAPI } = service.UserHome;

function usePreCheck() {
  const [yesNo, setYseNo] = useState(false);
  const [identity, setIdentity] = useState({});
  /* 身份校验 */
  const identityCheck = async () => {
    try{
    const {status,data} = await identityCheckAPI({});
      if(status){
        setYseNo(false);
        setIdentity(data);
        
      }
    }catch(err){
      console.log('身份校验出错',err)
    }
  }

  useEffect(() => {
    if(yesNo){
      identityCheck();
    }
  }, [yesNo]);

  return { yesNo,setYseNo,identity};
}

export default usePreCheck;
