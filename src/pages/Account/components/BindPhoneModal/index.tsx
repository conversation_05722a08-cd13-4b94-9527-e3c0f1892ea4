import { Modal, Button, Form, message } from 'antd';
import { ProFormCaptcha, ProFormText } from '@ant-design/pro-components';
import services from '@/services/home';
const { bindPhoneAPI, getPhoneCodeAPI } = services.UserHome;

interface BindPhoneAPIModalProps {
  open: boolean; // 控制弹框是否可见
  onCancel: () => void; // 关闭弹框的回调函数
  onSuccess: () => void; // 绑定成功后的回调函数
}

const BindPhoneAPIModal = ({ open, onCancel, onSuccess }: BindPhoneAPIModalProps) => {
  const [form] = Form.useForm();

  // 获取短信验证码
  const onGetCaptcha = async () => {
    try {
      const phone = form.getFieldValue('phone');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }

      const { status, data } = await getPhoneCodeAPI({ phone });
      console.log('data: ', data);
      if (status) {
        message.success('验证码发送成功！');
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 绑定手机号
  const onFinish = async (values: any) => {
    try {
      const { status } = await bindPhoneAPI({
        phone: values.phone,
        code: values.captcha,
      });
      if (status) {
        message.success('绑定成功！');
        onSuccess();
      }
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <Modal
      open={open} // 控制弹框是否可见
      title="绑定手机号"
      onCancel={onCancel} // 关闭弹框的回调函数
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={form.submit}>
          确定
        </Button>,
      ]}
    >
      <Form form={form} onFinish={onFinish}>
        <ProFormText
          label="手机号"
          name="phone"
          placeholder={'手机号码'}
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
          ]}
        />
        <ProFormCaptcha
          label="验证码"
          placeholder={'请输入验证码'}
          captchaTextRender={(currentTiming, count) => {
            if (currentTiming) {
              return `${count} ${'获取验证码'}`;
            }
            return '获取验证码';
          }}
          phoneName="phone"
          name="captcha"
          rules={[
            {
              required: true,
              message: '请输入验证码！',
            },
          ]}
          onGetCaptcha={onGetCaptcha}
        />
      </Form>
    </Modal>
  );
};

export default BindPhoneAPIModal;
