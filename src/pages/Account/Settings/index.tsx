import { ProCard } from '@ant-design/pro-components';
import AccountInformation from '../components/AccountInformation';
import Business from '../components/Business';
import services from '@/services/home';
import { useEffect, useState } from 'react';

const { getUserInfo } = services.UserHome;

export default function HomePage() {
  // 定义loading状态
  const [loading, setLoading] = useState<boolean>(true);

  // 获取用户信息
  const [userInfo, setUserInfo] = useState<any>({});

  const getUserInfoFun = async () => {
    setLoading(true);
    const res = await getUserInfo({});
    if (res.status) {
      setLoading(false);
      setUserInfo({ ...res.data });
    }else{
      setLoading(false);
    }
  };

  useEffect(() => {
    getUserInfoFun();
  }, []);

  return (
    <ProCard ghost gutter={[24, 24]} split="horizontal">
      {/* 账号信息 */}
      <ProCard title="账号信息" collapsible loading={loading}>
        {Object.keys(userInfo).length && (
          <AccountInformation userList={userInfo} getUserInfoFun={getUserInfoFun} />
        )}
      </ProCard>

      {/* 所在企业 */}
      <ProCard title="所在企业" collapsible loading={loading}>
        <Business
          tenantList={userInfo.tenantList}
          getUserInfoFun={getUserInfoFun}
        />
      </ProCard>
    </ProCard>
  );
}
