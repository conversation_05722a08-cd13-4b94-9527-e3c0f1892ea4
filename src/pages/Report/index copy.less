.report_manage {
    width: 100%;
    height: 100%;
    // background: linear-gradient(180deg, #ECFAFF 0%, #FFFFFF 100%);
    background-color: #F6F6F8;
    border-radius: 10px 10px 0px 0px;
    box-sizing: border-box;

    // padding: 20px 42px;
    .search {
        display: flex;
        align-items: center;
        width: 100%;
        height: 78px;
        padding-left: 30px;
        margin-bottom: 20px;
        background-color: #fff;
    }

    .cont {
        // 

        .item_cont {
            position: relative;
            background-color: #fff;
            padding: 20px 30px 30px 30px;
            margin-bottom: 20px;
            .text_color {
                position: absolute;
                top: 22px;
                left: 0px;
                width: 8px;
                height: 24px;
                // background: #FF631E;
                border-radius: 0px 5px 5px 0px;
            }
        }
    }

    .title_add {
        display: flex;
        justify-content: space-between;
        // margin: 30px 0 16px 0;

        .add {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 32px;
            background: #FFFFFF;
            border-radius: 20px;
            border: 1px solid #DEEFF5;
            cursor: pointer;
            color: #707070;

            svg {
                font-size: 10px;
            }
        }
    }

    .classification {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        // margin-bottom: 20px;
        // background-color: #fff;
        .content {
            position: relative;
            width: 30%;
            min-height: 100px;
            background: #FBFBFB;
            border-radius: 20px;
            // border: 1px solid #DEEFF5;
            box-sizing: border-box;
            padding: 20px;
            margin: 0 15px 20px 0;

            >div {
                width: 100%;
                height: 100%;
                cursor: pointer;
            }

            .title {
                display: flex;
                justify-content: space-between;

                // width: 100%;

            }

            p {
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #707070;
                margin-bottom: 0px !important;
                font-size: 14px;
            }

            .remove {
                position: absolute;
                top: 15px;
                right: 15px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 54px;
                height: 26px;
                background: #F4F4F4;
                border-radius: 23px;
                cursor: pointer;
                opacity: 0;
            }
        }

        .content:hover {
            box-shadow: 0px 8px 24px 0px #E3EAFF;
            border-radius: 20px;
            border: 1px solid #4071FF;
        }

        .content:hover {
            .remove {
                opacity: 1;
                transition: all 1s;


            }
        }

        .margin {
            margin: 0 24px;
        }
    }

    .no_data {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 168px;
        background: #FFFFFF;
        border-radius: 20px;
        border: 1px solid #DEEFF5;
        margin-top: 10px;
        .no_bb {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            >img {
                width: 80px;
            }
        }
        a {
            color: #4071FF;
        }
    }

    .custom_modal {
        .content {
            width: 896px;
            height: 98px;
            background: #FFFFFF;
            border-radius: 10px;
            border: 1px solid #DEEFF5;
        }
    }
}

.tabItem {
    position: relative;
    
    &.active {
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 2px;
        bottom: 2px;
        width: 3px;
        background-color: #00195B;
        border-radius: 3px 0px 0px 3px;
      }
    }
  }