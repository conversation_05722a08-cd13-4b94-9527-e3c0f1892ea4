import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Col, message, Modal, Row, Tabs } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import {
  addStatisticsToInstanceAPI,
  getServiceStatisticsAPI,
} from '@/services/booking';
interface Props {
  btnText: string;
  btnType?: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  moduleList?: any;
}

const ReportCards: React.FC<any> = ({
  icon,
  title,
  description,
  onSelect,
  reportKey,
  checked: externalChecked,
}) => {
  const [internalChecked, setInternalChecked] = useState(false);

  useEffect(() => {
    setInternalChecked(externalChecked);
  }, [externalChecked]);

  const handleClick = () => {
    const newChecked = !internalChecked;
    setInternalChecked(newChecked);
    onSelect?.(report<PERSON><PERSON>, newChecked);
  };

  return (
    <div
      onClick={handleClick}
      className={`bg-#F7F8FB rounded-10px shadow-sm hover:shadow-lg transition-shadow cursor-pointer p-21px h-132px relative group
        ${internalChecked ? 'b-2 b-solid b-blue-500' : 'b-transparent'}`}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center gap-2 mb-3">
          <div className="text-2xl">{icon}</div>
          <h3 className="text-base text-[#444D6E] font-600 font-medium m-0 truncate">
            {title}
          </h3>
        </div>
        <div className="relative group">
          <p className="text-sm text-[#8C97B8] leading-normal m-0 line-clamp-2 overflow-hidden">
            {description}
          </p>
        </div>
      </div>
      <input
        type="checkbox"
        checked={internalChecked}
        onChange={() => {}}
        onClick={(e) => e.stopPropagation()}
        className="absolute bottom-[5px] right-[5px] w-4 h-4 accent-blue-500 pointer-events-none"
      />
    </div>
  );
};

const AddToModal = ({ btnText, btnType, moduleList, refresh }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeKey, setActiveKey] = useState(moduleList[0].name);
  const [serviceList, setServiceList] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const { runAsync: addStatisticsToInstance } = useRequest(
    addStatisticsToInstanceAPI,
    {
      manual: true,
    },
  );

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    // setIsModalOpen(false);
    console.log(selectedKeys);
    addStatisticsToInstance({
      key: selectedKeys.join(','),
    }).then((res: any) => {
      if (res?.status) {
        refresh?.();
        message.success('添加成功');
        setIsModalOpen(false);
      }
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const { runAsync: getServiceStatistics } = useRequest(
    getServiceStatisticsAPI,
    {
      manual: true,
    },
  );

  const items = useMemo(() => {
    return moduleList.map((item: any) => ({
      key: item.name,
      label: item.desc,
    }));
  }, [moduleList]);

  useEffect(() => {
    if (isModalOpen) {
      getServiceStatistics({
        module: activeKey,
        len: 999,
      }).then((res) => {
        setServiceList(res.data.list);
      });
    }
  }, [isModalOpen, activeKey]);

  const newServiceList = useMemo(() => {
    return serviceList.map((item: any) => ({
      icon: item?.icon ? (
        <img src={item?.icon} width={36} height={36} alt="icon" />
      ) : (
        <img
          src="https://web-common.kmfba.com/icon/shipping_fee.png"
          width={36}
          height={36}
        />
      ),
      title: item.name,
      description: item.content,
      index: item.id,
      reportKey: item?.key,
      onTop: item?.onTop,
    }));
  }, [serviceList]);

  const handleTabChange = (newKey: string) => {
    setActiveKey(newKey);
    setSelectedKeys([]);
  };

  const onSelect = (reportKey: string, checked: boolean) => {
    setSelectedKeys((prev) => {
      if (checked) {
        return [...prev, reportKey];
      } else {
        return prev.filter((key) => key !== reportKey);
      }
    });
  };

  return (
    <>
      <Button type={btnType} onClick={showModal} className="rounded-full">
        <PlusOutlined /> {btnText}
      </Button>
      <Modal
        width={1000}
        title="添加报表"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Tabs
          items={items}
          defaultActiveKey={activeKey}
          onChange={handleTabChange}
        />
        {activeKey && (
          <div className="h-[420px] overflow-y-auto overflow-x-hidden pr-2">
            <Row gutter={[16, 16]}>
              {newServiceList.map((report, index) => (
                <Col key={index} xs={24} sm={12} md={8} lg={6}>
                  <ReportCards
                    {...report}
                    onSelect={onSelect}
                    checked={selectedKeys.includes(report.reportKey)}
                  />
                </Col>
              ))}
            </Row>
          </div>
        )}
      </Modal>
    </>
  );
};
export default AddToModal;
