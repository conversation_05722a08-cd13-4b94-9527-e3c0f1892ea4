import React, { useEffect, useMemo, useState } from 'react';
import { Row, Col, Popconfirm, Input } from 'antd';
import topUp from '@/assets/images/report/topUp.webp';
import topDown from '@/assets/images/report/topDown.webp';
import removeHover from '@/assets/images/report/removeHover.webp';
import remove from '@/assets/images/report/remove.webp';
import { useRequest } from 'ahooks';
import {
  cancelTopAPI,
  removeStatisticsFromInstanceAPI,
  setTopAPI,
} from '@/services/booking';
import { history } from 'umi';
import AddToModal from './addToModal';
import AccessCard from '@/AccessCard';
import LogDashboard from '@/components/LogDashboard';

interface ReportCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  refresh: () => void;
  reportKey: string;
  onTop: boolean;
}

const ReportCards: React.FC<ReportCardProps> = ({
  icon,
  title,
  description,
  refresh,
  reportKey,
  onTop,
}) => {
  const { runAsync: setTop } = useRequest(setTopAPI, {
    manual: true,
  });
  const { runAsync: cancelTop } = useRequest(cancelTopAPI, {
    manual: true,
  });

  const { runAsync: removeStatisticsFromInstance } = useRequest(
    removeStatisticsFromInstanceAPI,
    {
      manual: true,
    },
  );
  return (
    <div className="bg-white rounded-10px shadow-sm hover:shadow-lg transition-shadow cursor-pointer p-21px h-[150px] relative group">
      {!onTop ? (
        <div
          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={(e) => e.stopPropagation()}
        >
          <Popconfirm
            title="置顶"
            description="置顶后，报表将显示在报表列表的顶部"
            onConfirm={() => {
              setTop({
                key: reportKey,
              }).then((e) => {
                if (e.status) {
                  refresh();
                }
              });
            }}
            okText="确定"
            cancelText="取消"
          >
            <img src={topDown} alt="置顶" width={30} height={30} />
          </Popconfirm>
        </div>
      ) : (
        <div
          className="absolute top-2 right-2"
          onClick={(e) => e.stopPropagation()}
        >
          <Popconfirm
            title="取消置顶"
            description="取消置顶后，报表将不再显示在报表列表的顶部"
            onConfirm={() => {
              cancelTop({
                key: reportKey,
              }).then((e) => {
                if (e.status) {
                  refresh();
                }
              });
            }}
            okText="确定"
            cancelText="取消"
          >
            <img src={topUp} alt="置顶" width={30} height={30} />
          </Popconfirm>
        </div>
      )}
      {/* <AccessCard accessible={['TMS:Statistics:FullAccess']}> */}
        <div
          className="absolute bottom-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
          onClick={(e) => e.stopPropagation()}
        >
          <LogDashboard
            extraId_1={reportKey}
            btnType="link"
            btnText="日志"
            styles={{marginBottom:'-10px'}}
          />
          <Popconfirm
            title="删除"
            description="删除后，报表将不再显示"
            onConfirm={() => {
              removeStatisticsFromInstance({
                key: reportKey,
              }).then((e) => {
                if (e.status) {
                  refresh();
                }
              });
            }}
            okText="确定"
            cancelText="取消"
          >
            <img
              src={remove}
              alt="移除"
              width={41}
              height={20}
              onMouseEnter={(e) => {
                e.currentTarget.src = removeHover;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.src = remove;
              }}
            />
          </Popconfirm>
        </div>
      {/* </AccessCard> */}
      <div className="flex flex-col h-full">
        <div className="flex items-start gap-2 mb-3">
          <div>{icon}</div>
          {/* <div className="text-[23px] leading-[23px] mt-[2px]">{icon}</div> */}
          <h3 className="text-[15px] text-[#444D6E] font-600 font-medium m-0 line-clamp-2 min-h-[40px] flex-1">
            {title}
          </h3>
        </div>
        <div className="relative group">
          <p className="text-sm text-[#8C97B8] leading-normal m-0 line-clamp-3 overflow-hidden">
            {description}
          </p>
          {/* <div className="absolute z-100 left-1/2 -translate-x-1/2 -bottom-2 translate-y-full w-[200px] px-2 py-1 bg-black/80 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        {description}
      </div> */}
        </div>
      </div>
    </div>
  );
};

const ReportCard: React.FC<{
  list: any[];
  refresh: any;
  moduleList: any[];
}> = ({ list, refresh, moduleList }) => {
  const [listData, setListData] = useState<any[]>([]);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    setListData(list);
  }, [list]);

  const filteredReports = useMemo(() => {
    const reports = listData.map((item) => ({
      icon: item?.icon ? (
        <img src={item?.icon} width={23} height={23} alt="icon" />
      ) : (
        <img
          src="https://web-common.kmfba.com/icon/shipping_fee.png"
          width={23}
          height={23}
        />
      ),
      title: item.name,
      description: item.content,
      index: item.id,
      reportKey: item?.key,
      onTop: item?.onTop,
    }));

    if (!searchText) return reports;
    return reports.filter((report) =>
      report.title.toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [listData, searchText]);

  return (
    <div className="p-[12px_10px_30px_10px]">
      <div className="flex justify-between mb-6 items-center">
        <Input.Search
          placeholder="搜索报表标题"
          allowClear
          enterButton
          style={{ width: 255 }}
          onChange={(e) => setSearchText(e.target.value)}
        />
        {/* <AccessCard accessible={['TMS:Statistics:FullAccess']}> */}
          <AddToModal
            btnText="添加"
            moduleList={moduleList}
            refresh={refresh}
          />
        {/* </AccessCard> */}
      </div>
      <Row gutter={[16, 16]}>
        {filteredReports.map((report, index) => (
          <Col
            key={index}
            xs={24}
            sm={12}
            md={8}
            lg={6}
            onClick={() => {
              history.push(
                `/report/particulars?id=${report?.title}&key=${report?.reportKey}`,
                {
                  ReportId: report.reportKey,
                },
              );
            }}
          >
            <ReportCards {...report} refresh={refresh} />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default ReportCard;
