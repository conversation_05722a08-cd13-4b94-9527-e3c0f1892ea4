import AccessCard from '@/AccessCard';
import MySearch from '@/components/MyProTable/MySearch';
import {
  addStatisticsToInstanceAPI,
  getAllInstanceStatistics,
  getServiceStatisticsAPI,
  removeStatisticsFromInstanceAPI,
} from '@/services/booking';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Checkbox, message, Modal, Popconfirm, Image } from 'antd';
import React, { useEffect, useState } from 'react';
import { history } from 'umi';
import './index.less';
import { useDispatch } from 'react-redux';
const Report = React.memo(() => {
  const dispath = useDispatch();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [reportData, setReportData] = useState([]);
  const [moduleId, setModuleId] = useState<any>();
  const [listData, setListData] = useState([]);
  const [keys, setKeys] = useState<any>([]);
  // const [keyword,setKeyword] = useState('')
  useEffect(() => {
    getAllReport();
  }, []);
  useEffect(() => {
    if (moduleId && isModalOpen) {
      getServiceStatistics(moduleId);
    }
  }, [moduleId, isModalOpen]);
  // 报表列表
  const getAllReport = async (keyword?: string) => {
    try {
      const { status, data } = await getAllInstanceStatistics({
        keyword,
      });
      if (status) {
        setReportData(data?.list);
      }
    } catch (error) {}
  };
  // 获取添报表的数据
  const getServiceStatistics = async (module: string) => {
    try {
      const { status, data } = await getServiceStatisticsAPI({
        module,
        len: 100,
      });
      if (status) {
        setListData(data?.list);
      }
    } catch (error) {}
  };
  // 添加报表
  const addStatisticsToInstance = async () => {
    try {
      const { status } = await addStatisticsToInstanceAPI({
        key: keys?.join(','),
      });
      if (status) {
        message.success('添加成功');
        getAllReport();
      }
    } catch (error) {}
  };
  // 删除报表
  const removeStatisticsFromInstance = async (key: string) => {
    try {
      const { status } = await removeStatisticsFromInstanceAPI({ key });
      if (status) {
        message.success('删除成功');
        getAllReport();
      }
    } catch (error) {}
  };
  const showModal = (item: any) => {
    var result = item.id?.split(':')[1];
    setModuleId(result);
    setIsModalOpen(true);
  };

  const handleOk = () => {
    addStatisticsToInstance();
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  //  报表详情
  const ReportParticulars = (id: string, name: string) => {
    history.push(`/report/particulars?id=${name}&key=${id}`, {
      ReportId: id,
    });
    // localStorage.setItem('statementName', name);
  };
  const modal_style = {
    width: '800px',
    minHeight: '98px',
    background: '#fff',
    borderRadius: '10px',
    border: '1px solid #DEEFF5',
    padding: '22px 0 0 10px',
  };
  const titleColor = (index: number) => {
    const colors = [
      '#FF631E',
      '#6F14F1',
      '#14B8F1',
      '#1464F1',
      '#FFB300',
      '#40B900',
      '#C42ED5',
      '#7977FF',
    ];
    const color = colors[index % colors.length]; // 使用取模运算来获取颜色索引
    return color;
  };
  return (
    <div className="report_manage">
      <div className="search">
        <MySearch
          placeholder="请输入关键字搜索"
          allowClear
          enterButton="搜索"
          size="large"
          style={{ width: '300px', background: '#FBFBFB' }}
          onSearch={(e: any) => {
            // setKeyword(e)
            getAllReport(e);
          }}
        />
      </div>
      <div className="cont">
        {reportData?.map((item: any, index) => (
          <div key={item?.id} className="item_cont">
            <div
              className="text_color"
              style={{ backgroundColor: titleColor(index) }}
            ></div>
            <div className="title_add">
              <h3>{item?.desc}</h3>
              <AccessCard accessible={['TMS:Statistics:FullAccess']}>
                <div className="add" onClick={() => showModal(item)}>
                  <PlusOutlined />
                  &nbsp;添加
                </div>
              </AccessCard>
            </div>
            <div className="classification">
              {item?.list?.length < 1 ? (
                <div className="no_data">
                  <div className="no_bb">
                    <img src={require('../../assets/images/no_bb.png')} />
                    <div>
                      {' '}
                      还没有报表，
                      <a onClick={() => showModal(item)}>添加报表</a>
                    </div>
                  </div>
                </div>
              ) : (
                item?.list?.map((ele: any, i: number) => (
                  <div key={ele?.id} className="content">
                    <div
                      key={ele?.id}
                      onClick={() =>
                        ReportParticulars(ele?.key, `${i + 1}.${ele?.name}`)
                      }
                    >
                      <div className="title">
                        <h4>
                          {i + 1}.{ele?.name}
                        </h4>
                      </div>
                      <p>{ele?.content}</p>
                    </div>
                    <AccessCard accessible={['TMS:Statistics:FullAccess']}>
                      <Popconfirm
                        key="delete"
                        title="删除报表"
                        description="想好了吗，删除报表?"
                        onConfirm={() => {
                          removeStatisticsFromInstance(ele?.key);
                        }}
                        onCancel={() => {
                          message.info('已取消操作');
                        }}
                        okText="确认"
                        cancelText="再想想"
                      >
                        <div
                          className="remove"
                          onClick={(e) => {
                            console.log(e, 'eeee');
                          }}
                        >
                          移除
                        </div>
                      </Popconfirm>
                    </AccessCard>
                  </div>
                ))
              )}
            </div>
          </div>
        ))}
      </div>

      <Modal
        title="添加报表"
        destroyOnClose
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        afterClose={() => setKeys([])}
        style={{ minWidth: '900px', top: '30px' }}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确认
            </Button>
          </div>,
        ]}
      >
        <div
          style={{
            width: '100%',
            maxHeight: '550px',
            overflowY: 'auto',
            paddingBottom: '20px',
          }}
        >
          <Checkbox.Group
            value={keys}
            onChange={(e) => {
              setKeys(e);
            }}
          >
            {listData?.map((item: any, i: number) => (
              <div
                key={item.key}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignContent: 'center',
                  marginTop: '20px',
                }}
              >
                <Checkbox
                  style={{ marginRight: '20px' }}
                  value={item?.key}
                ></Checkbox>
                <div style={modal_style}>
                  <h4 style={{ marginBottom: '5px' }}>
                    {i + 1}.{item?.name}
                  </h4>
                  <p style={{ marginBottom: '0px' }}>{item?.content}</p>
                </div>
              </div>
            ))}
          </Checkbox.Group>
        </div>
      </Modal>
    </div>
  );
});
export default Report;
