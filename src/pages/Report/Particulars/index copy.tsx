import { message } from 'antd';
import { useLocation } from '@umijs/max';
import { useEffect, useRef, useState } from 'react';
import { getDownloadAPI, getstatisticsAPI } from '@/services/booking';
import { saveAs } from 'file-saver';
import styles from './index.less';
import MrTable from '@/components/MrTable';
import {
  formatTime,
  getMonthRange,
  getTodayRange,
  getTodayWeekRange,
  getYearRange,
} from '@/utils/format';
const Particulars = () => {
  const [detail, setDetail] = useState<any>({});
  const [conditions, setConditions] = useState({});
  const { state }: any = useLocation();
  const actionRef = useRef<any>(null);
  const [newColumn, setNewColumn] = useState<any>([]);
  const stateKey = useRef<any>(null);
  const data = useRef<any>(null);
  const conditionRef = useRef<any>({});
  useEffect(() => {
    // pagingFlag.current = dataSources.length
    if (state?.ReportId && detail?.header?.length) {
      setNewColumn(newColumns());
    }
  }, [state?.ReportId, detail?.header]);
  // 表头
  const newColumns: any = () => {
    const columns: any = [];
    detail?.header?.forEach((item: any, index: number) => {
      columns.push({
        title: item[0],
        align: 'center',
        dataIndex: index + 1,
        hideInTable: false,
        hideInSearch: true,
        width: '200px',
        // render: (_: any, recode: any, index: number) => {
        //     if (index == data.current?.length - 1) {
        //         return <div style={{ backgroundColor: 'red', position: 'fixed', bottom: 0, width: '100%', height: '100%' }}>{recode[index + 1]}</div>
        //     } else {
        //         return <span>{recode[index + 1]}</span>
        //     }
        //     // console.log(_, '___');
        //     // console.log(recode, 'recoderecode');
        //     // console.log(index, 'indexindex');

        // }
      });
    });
    return columns;
  };
  // 数据源
  const dataSource = (itemList: any) => {
    const data: any = [];
    itemList?.forEach((item: any, index: number) => {
      let keys: any = {};
      item?.forEach((ele: any, fid: number) => {
        keys[fid + 1] = item[fid];
        keys['id'] = `${ele}_${index}`;
      });
      data.push(keys);
    });
    return data;
  };
  // 导出数据
  // 导出
  const getDownload = async (conditions: any, ReportId: string) => {
    try {
      const response: any = await getDownloadAPI(conditions, ReportId);
      const fileName = decodeURI(
        response.headers['content-disposition'].match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
      message.success('导出成功');
    } catch (err) {
      console.log('内部仓库转运导出失败: ', err);
    }
  };
  // const getDataSource = async (params: any) => {
  //     setLoading(true)
  //     let par_conditions: any = {}
  //     if (detail?.conditions) {
  //         par_conditions = parData
  //     }
  //     try {
  //         const { status, data } = await getstatisticsAPI(par_conditions, (params.current - 1) * params.pageSize, params.pageSize, state?.ReportId)
  //         if (status) {
  //             pageFlag.current = dataSource(data?.itemList)
  //             setDetail(data)
  //             setParData(data?.conditions)
  //             setDataSources(dataSource(data?.itemList) || [])
  //             setLoading(false)
  //             // actionRef.current?.reload();
  //         }
  //     } catch (error) {
  //         setLoading(false)

  //     }
  // }
  useEffect(() => {
    if (state?.ReportId) {
      stateKey.current = state?.ReportId;
      // conditionRef.current = {};
      setConditions({});
      actionRef.current?.reload();
    }
  }, [state?.ReportId]);
  // 整合时间组件
  const timeConformity = (data: any) => {
    if (typeof data !== 'object') return;
    if (!Object.keys(data)?.length) return;
    for (const key in data) {
      if (
        /def/i.test(data[key]?.startTime) ||
        /def/i.test(data[key]?.endTime)
      ) {
        if (data[key]?.def == 'ThisWeek') {
          data[key].startTime = formatTime(getTodayWeekRange().startDate);
          data[key].endTime = formatTime(getTodayWeekRange().endDate);
        } else if (data[key]?.def == 'ThisMonth') {
          data[key].startTime = formatTime(getMonthRange().start);
          data[key].endTime = formatTime(getMonthRange().end);
        } else if (data[key]?.def == 'ThisYear') {
          data[key].startTime = formatTime(getYearRange().start);
          data[key].endTime = formatTime(getYearRange().end);
        } else if (data[key]?.def == 'Today') {
          data[key].startTime = formatTime(getTodayRange().start);
          data[key].endTime = formatTime(getTodayRange().end);
        }
      }
    }
    for (const key in data) {
      data[key]['reportType'] = 'statement';
    }
    setConditions({ ...data });
  };
  return (
    <MrTable
      columns={newColumn}
      keyID={detail?.key ?? 'reportDetails'}
      // keyID={detail?.key}
      request={async (params: any, action: any) => {
        actionRef.current = action;
        const msg = await getstatisticsAPI({
          ...params,
          key: stateKey.current,
        });
        setDetail(msg?.data);
        timeConformity(msg?.data?.conditions);
        data.current = dataSource(msg?.data?.itemList);
        return {
          data: dataSource(msg?.data?.itemList) || [],
          success: msg.status,
          total: msg?.data?.total,
        };
      }}
      scrollX={newColumns()?.length * 160}
      // filters={detail?.conditions}
      filters={conditions}
      // filters={filters}
      exportCallback={(data) => {
        getDownload(data, state?.ReportId);
      }}
    />
  );
  {
    /* <DynamicSearch conditions={detail?.conditions} refresh={refresh} />
        <ProTable<any>
            actionRef={actionRef}
            columns={newColumn}
            className={styles.report_particulars}
            scroll={{
                x: newColumns()?.length * 140
                // x:1800

            }}
            dataSource={dataSources}
            onChange={(e) => {
                getDataSource({
                    current: e.current,
                    pageSize: e.pageSize
                })
                setParams({
                    current: e.current,
                    pageSize: e.pageSize
                })

            }}
            pagination={{
                total: getTotal(dataSources.length),
                pageSize: getPageSize(dataSources.length)
            }}
            // request={async (params: any) => {
            //     let par_conditions:any = {}
            //     if(detail?.conditions) {
            //         par_conditions = parData
            //     }
            //     const msg = await getstatisticsAPI(par_conditions,(params.current - 1) * params.pageSize,params.pageSize,state?.ReportId);
            //     setDetail(msg?.data)
            //     setParData(msg?.data?.conditions)
            //     return {
            //         data: dataSource(msg?.data?.itemList) as any || [],
            //         success: msg?.status,
            //         total: msg?.data?.total,
            //     };
            // }}
            rowKey="id"
            loading={loading}
            search={{
                labelWidth: 'auto',
                collapsed: false,
                optionRender: false

            }}
            options={{
                fullScreen: true,
                reload: (e) => {
                    getDataSource(params)
                }
            }}
            dateFormatter="string"
            toolBarRender={() => [
                <Button
                    key="button"
                    type="primary"
                    onClick={getDownload}>
                    导出数据
                </Button>,
            ]}
        /> */
  }
};
export default Particulars;
