
import { getAddFeeAPI, getCalinetIndent } from '@/services/booking';
import MrTable from '@/components/MrTable';
import styles from './index.less'
import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Input, message, Modal, Space } from 'antd';
import { ProFormSelect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { getFeeTypeAPI } from '@/services/financeApi';
import LogDashboard from '@/components/LogDashboard';
const SellCabinet = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const actionRef = useRef<any>(null)
    const [action, setAction] = useState<any>({})
    const [billNameList, setBillNameList] = useState([])
    const [feeData, setFeeData] = useState<any>({})
    const options = useMemo(() => {
        return [
            // { label: '空闲', value: '1' },
            // { label: '已装柜', value: '2' },
            // { label: '已开船', value: '3' },
            // { label: '已转卖', value: '4' },
            // { label: '已退订', value: '5' },
            { label: '未完成', value: '1' },
            { label: '部分完成', value: '2' },
            { label: '已完成', value: '3' },
            { label: '已撤销', value: '4' },
        ]
    }, [])
    const columns = useMemo(() => {
        return [

            {
                title: '提单号',
                // align: 'center',
                dataIndex: 'blno',
                width: 200,
                hideInSearch: true,
            },
            {
                title: '航线',
                dataIndex: 'state',
                // hideInTable: true,
                hideInSearch: true,
                // align: 'center',
                width: 100,
                render: (_: any, recode: any) => {
                    return recode?.vesselName
                }
            },
            {
                title: '船名航次',
                hideInSearch: true,
                dataIndex: 'voyageProperties',
                // align: 'center',
                width: 260,
                render: (text: any, recode: any) => {
                    return `${recode?.shipName}/${recode?.voyageCode}`
                }
            },
            {
                title: '规格',
                dataIndex: 'provider',
                hideInSearch: true,
                // align: 'center',
                width: 160,
                render: (_: any, text: any) => {
                    return text?.model
                }
            },
            {
                title: '客户',
                hideInSearch: true,
                dataIndex: 'vesselName',
                // align: 'center',
                width: 160,
                render: (_: any, recode: any) => {
                    return recode?.clientName
                }
            },
            {
                title: '出发地',
                hideInSearch: true,
                dataIndex: 'voyage',
                // align: 'center',
                width: 160,
                render: (_: any, recode: any) => {
                    return recode?.departsCity
                }
            },

            {
                title: '到达地',
                hideInSearch: true,
                // align: 'center',
                width: 120,
                render: (_: any, recode: any) => {
                    return recode?.arrivesCity
                }
            },
            {
                hideInSearch: true,
                title: '开船时间',
                // align: 'center',
                width: 160,
                render: (_: any, recode: any) => {
                    return recode?.estimatedDepartTime
                }

            },

            {
                title: '操作',
                valueType: 'option',
                key: 'option',
                // width: 200,
                // align: 'center',
                fixed: 'right',
                render: (_: any, recode: any) => {

                    return <Space>
                        <a onClick={() => {
                            history.push(`/Booking/cabinet/detail`, {
                                sellCabinetId: recode?.id
                            })
                        }}>查看</a>
                        <a onClick={() => {
                            setAction({
                                blon: recode?.blno,
                                clientName: recode?.clientName,
                                clientId: recode?.clientId,
                                entityId: recode?.id
                            })
                            setIsModalOpen(true)
                        }}>添加费用</a>
                        <LogDashboard extraId_1={recode?.id} btnType="link" btnText="日志" key="journal" />
                    </Space>
                }
            },
        ];
    }, [])
    const filters = {
        "clientId": {
            "desc": "客户",
            "type": "client",
            value: ''
        },
        "time": {
            "desc": "时间",
            "type": "dateTimeRange",
            "startTime": "",
            "endTime": "",
        },
    }
    useEffect(() => {
        getBillName()
    }, [])
    const getBillName = async (keyWords?: string) => {
        const { status, data } = await getFeeTypeAPI({
            keyword: keyWords,
            counterpartyType: 'ShipTransport'
        });
        if (status) {
            return setBillNameList(data?.map((item: any) => {
                return {
                    label: Object.values(item)[0],
                    value: Object.keys(item)[0],
                };
            }) || [])
        }
    }
    const getAddFee = async () => {
        if (!feeData?.billName) return message.warning('费用名称不能为空')
        if (!feeData?.exchangeRate) return message.warning('汇率不能为空')
        if (!feeData?.currencyTypeCode) return message.warning('币种不能为空')
        if (!feeData?.amount) return message.warning('金额不能为空')
        try {
            const { status } = await getAddFeeAPI({ ...feeData, clientId: action?.clientId, entityId: action?.entityId })
            if (status) {
                message.success('添加成功')
                setIsModalOpen(false)
                actionRef.current?.reload();

            }
        } catch (error) {

        }
    }
    return <>
        <MrTable
            columns={columns}
            keyID={'Booking/cabin/bookingPlanss/discharge'}
            request={async (params: any, action: any) => {
                actionRef.current = action
                // shippingPlanAPI
                // getCalinetIndent
                const res = await getCalinetIndent({
                    ...params
                })

                return {
                    data: res.data.list || [],
                    success: res.status,
                    total: res.data?.total,
                };
            }}
            filters={filters}
        />
        <Modal title='添加费用' open={isModalOpen}
            destroyOnClose
            onCancel={() => setIsModalOpen(false)}
            width={800}
            footer={[
                <div key='btn' style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button key="back" onClick={() => setIsModalOpen(false)} style={{ marginRight: '12px' }}>取消</Button>
                    <Button key="submit" type="primary" onClick={getAddFee} >确认</Button>
                </div>
            ]}>
            <div className={styles.billNumber}>
                <div><span>提单号</span><span>{action?.blon}</span><span>客户</span><span>{`${action?.clientName}`}</span></div>
            </div>
            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        name="billName"
                        label="费用名称"
                        showSearch
                        colon={false}
                        options={billNameList}
                        onChange={(e) => {
                            setFeeData({ ...feeData, billName: e })
                            // setBlno({ ...blno, clientId: e })
                        }}
                    // request={async ({ keyWords }) => {
                    //     const { status, data } = await getClientListAPI({
                    //         type: 0,
                    //         len: 100,
                    //         keyword: keyWords,
                    //     });
                    //     if (status) {
                    //         return data.list.map((item: any) => {
                    //             return {
                    //                 label: `【${item.name}】 - ${item.code}`,
                    //                 value: item.id,
                    //             };
                    //         });
                    //     }
                    //     return [];
                    // }}
                    />
                    <div className={styles.money}>
                        <span>汇率</span>
                        <Input
                            placeholder='请输入'
                            type='number'
                            onChange={(e) => {
                                setFeeData({ ...feeData, exchangeRate: e?.target?.value })

                                // setBlno({ ...blno, clientId: e })
                            }}
                        />
                    </div>
                </div>
            </div>
            <div style={{ width: '100%' }}>
                <div className={styles.clientName}>
                    <ProFormSelect
                        name="clientName"
                        label="币种"
                        colon={false}
                        onChange={(e) => {
                            setFeeData({ ...feeData, currencyTypeCode: e })

                        }}
                        options={[
                            {
                                label: '人民币',
                                value: 10,
                            },
                            {
                                label: '美元',
                                value: 20,
                            },
                            {
                                label: '加币',
                                value: 30,
                            },
                            {
                                label: '欧元',
                                value: 40,
                            },
                            {
                                label: '日元',
                                value: 50,
                            },
                        ]}
                    />
                    <div className={styles.money}>
                        <span>金额</span>
                        <Input
                            placeholder='请输入'
                            type='number'
                            onChange={(e) => {
                                setFeeData({ ...feeData, amount: e?.target?.value })
                                // setBlno({ ...blno, clientId: e })
                            }}
                        />
                    </div>
                </div>
            </div>
            <div className={styles.remark}>
                <span>备注</span>
                <Input
                    placeholder='请输入'
                    onChange={(e) => {
                        setFeeData({ ...feeData, remark: e?.target?.value })
                    }}
                />
            </div>
        </Modal>
    </>
}
export default SellCabinet
