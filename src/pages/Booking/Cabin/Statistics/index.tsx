import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import { Input, Modal, Select, message, Space, Tag, Tooltip } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { cancelShippingAPI, fastBookAPI, summaryAPI, cabinAPI } from '@/services/booking';
import { formatTimes } from '@/utils/format';
import { history } from '@umijs/max';
import MySearch from '@/components/MyProTable/MySearch';
import styles from './index.less'
import { KeepAliveTabContext } from '@/layouts/context';
import { ProTable } from '@ant-design/pro-components';
import { statusEnum } from '../../components/type';
import Time from '../../components/Time';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import MrTable from '@/components/MrTable';
import LogDashboard from '@/components/LogDashboard';
const Statistics = () => {
    const { refreshTab } = useContext(KeepAliveTabContext);
    const [toList] = usePermissionFiltering()
    const ref = useRef<any>();
    const actionRef = useRef<any>(null);
    const [keyword, setKeyword] = useState('')
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [fastBook, setFastBook] = useState<any>({})
    const [activeKey, setActiveKey] = useState<any>(1)
    const [time, setTime] = useState<any>({})
    const [bootHieght, setBootHieght] = useState(400)
    const [windowHeight, setWindowHeight] = useState(window.innerHeight);
    useEffect(() => {
        const handleResize = () => setWindowHeight(window.innerHeight);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    const textEllipsis = (text: any) => {
        return (
            <div style={{ overflow: "hidden", whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                <Tooltip title={text}>
                    <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{text}</span>
                </Tooltip>
            </div>
        );
    };
    const columns: any = useMemo(() => {
        if (activeKey == 1) {
            return [
                {
                    title: '航线',
                    dataIndex: 'voyages',
                    align: 'center',
                    width: 200,
                    hideInSearch: true,
                    render: (recode: any, text: any) => {
                        return textEllipsis(text?.voyage?.vessel?.name)
                    }
                },
                {
                    title: '船名航次',
                    dataIndex: 'age',
                    hideInSearch: true,
                    align: 'center',
                    width: 200,
                    render: (recode: any, text: any) => {
                        // return textEllipsis(`${recode?.voyageProperties?.shipName}/${recode?.voyageProperties?.code}`)
                        return textEllipsis(`${text?.voyage?.voyageProperties?.shipName}/${text?.voyage?.voyageProperties?.code}`)

                    }
                },

                {
                    title: '规格',
                    hideInSearch: true,
                    dataIndex: 'd',
                    align: 'center',
                    width: 120,
                    render: (text: any, _: any, index: number) => {
                        return _?.modelRecords?.map((v: any, i: number) => {
                            return <div key={i} style={{ width: '100%', height: '40px', lineHeight: '40px' }}>{v?.model}&nbsp;x&nbsp;{v?.classifications?.filter((ele: any) => ele?.state == 6)[0]?.number || 0}</div>
                        })
                    }

                },
                {
                    title: '舱位',
                    hideInSearch: true,
                    dataIndex: 'm',
                    align: 'center',
                    width: 120,
                    render: (text: any, _: any, index: number) => {
                        return _?.modelRecords?.map((v: any, i: number) => {
                            return <div key={i} style={{ width: '100%', height: '40px', color: '#49B000', lineHeight: '40px' }}>{v?.classifications?.filter((ele: any) => ele?.state == 6)[0]?.number || 0}&nbsp;条</div>
                        })
                    }

                },
                {
                    title: '卖柜',
                    hideInSearch: true,
                    width: 120,
                    dataIndex: 'mg',
                    align: 'center',
                    render: (text: any, _: any, index: number) => {
                        return _?.modelRecords?.map((v: any, i: number) => {
                            return <div key={i} style={{ width: '100%', height: '40px', color: "#4071FF", lineHeight: '40px' }}>{v?.classifications?.filter((ele: any) => ele?.state == 4)[0]?.number || 0}&nbsp;条</div>
                        })
                    }

                },
                {
                    title: '装柜',
                    hideInSearch: true,
                    width: 120,
                    dataIndex: 'zg',
                    align: 'center',
                    render: (text: any, _: any, index: number) => {
                        return _?.modelRecords?.map((v: any, i: number) => {
                            return <div key={i} style={{ width: '100%', height: '40px', color: '#EC841A', lineHeight: '40px' }}>{v?.classifications?.filter((ele: any) => ele?.state == 2)[0]?.number || 0}&nbsp;条</div>
                        })
                    }

                },
                {
                    title: '空余',
                    hideInSearch: true,
                    width: 120,
                    dataIndex: 'ky',
                    align: 'center',
                    render: (text: any, _: any, index: number) => {
                        return _?.modelRecords?.map((v: any, i: number) => {
                            return <div key={i} style={{ width: '100%', height: '40px', color: '#9840FF', lineHeight: '40px' }}>{v?.classifications?.filter((ele: any) => ele?.state == 1)[0]?.number || 0}&nbsp;条</div>
                        })
                    }

                },
                {
                    title: '预报',
                    width: 120,
                    hideInSearch: true,
                    dataIndex: 'yb',
                    align: 'center',
                    render: (text: any, _: any, index: number) => {
                        return <div style={{ color: '#9840FF' }}>{_?.modelRecords?.[index]?.forecastVolume || 0}&nbsp;m³</div>
                    }
                },
                {
                    title: '出发地',
                    hideInSearch: true,
                    dataIndex: 'cfd',
                    align: 'center',
                    width: 120,
                    render: (_: any, text: any) => {
                        return text?.voyage?.vessel?.vesselProperties?.departsCity
                    }
                },
                {
                    title: '到达地',
                    hideInSearch: true,
                    dataIndex: 'ddd',
                    align: 'center',
                    width: 120,
                    render: (_: any, text: any) => {
                        return text?.voyage?.vessel?.vesselProperties?.arrivesCity
                    }
                },
                {
                    title: '时效',
                    hideInSearch: true,
                    dataIndex: 'sx',
                    align: 'center',
                    width: 120,
                    render: (_: any, text: any) => {
                        return textEllipsis(`${text?.voyage?.vessel?.vesselProperties?.leastDays}~${text?.voyage?.vessel?.vesselProperties?.mostDays}`)
                    }
                },
                {
                    title: '最晚预报',
                    hideInSearch: true,
                    dataIndex: 'zwyb',
                    align: 'center',
                    width: 120,
                    render: (_: any, text: any) => {
                        return text?.voyage?.voyageProperties?.forecastLimitTime && textEllipsis(formatTimes(text?.voyage?.voyageProperties?.forecastLimitTime))
                    }
                },
                {
                    title: '最晚到货',
                    hideInSearch: true,
                    dataIndex: 'zwdh',
                    align: 'center',
                    width: 120,
                    render: (_: any, text: any) => {
                        return text?.voyage?.voyageProperties?.cargoLimitTime && textEllipsis(formatTimes(text?.voyage?.voyageProperties?.cargoLimitTime))
                    }
                },
                {
                    hideInSearch: true,
                    title: '预计开船',
                    dataIndex: 'yjkc',
                    width: 120,
                    align: 'center',
                    render: (_: any, text: any) => {
                        return text?.voyage?.voyageProperties?.estimatedDepartTime && textEllipsis(formatTimes(text?.voyage?.voyageProperties?.estimatedDepartTime))
                    }

                },
                {
                    title: '操作',
                    valueType: 'option',
                    align: 'center',
                    fixed: 'right',
                    width: 200,
                    selectable: 'none',
                    render: (text: any, record: any) => {
                        return <Space size="middle">
                            {/* <a
                                key="eKey"
                                style={{ width: 50 }}
                                onClick={() => {
                                    setIsModalOpen(true)
                                    setFastBook({
                                        name: record?.voyage?.vessel?.name,
                                        shipsVoyage: `${record?.voyage?.voyageProperties?.shipName}/${record?.voyage?.voyageProperties?.code}`,
                                        voyageId: record?.voyage?.id,
                                        agentId: undefined,
                                        model: '',
                                        number: 0,
                                        models: record?.models,
                                        agents: record?.voyage?.vessel?.agents
                                    })
                                }}
                            >
                                订舱
                            </a> */}
                            <a
                                key="aKey"
                                style={{ width: 50 }}
                                onClick={() => {
                                    history.push(`/Booking/cabin/particulars`, {
                                        id: record?.voyage?.id
                                    })
                                }} target="_blank" rel="noopener noreferrer" >
                                详情

                            </a>
                            {/* <Popconfirm
                                key="delete"
                                title="退订舱位"
                                description="想好了吗，退订舱位?"
                                onConfirm={() => {
                                    cancelShipping(record.id)
                                }}
                                onCancel={() => {
                                    message.info('已取消操作');
                                }}
                                okText="确认"
                                cancelText="再想想"
                            >
                                <a style={{ color: '#4071ff' }}>退订</a>
                            </Popconfirm> */}
                            <LogDashboard extraId_1={record?.id} btnType="link" btnText="日志" key="journal" />
                        </Space>
                    }
                },
            ];
        } else {
            return [
                {
                    title: '提单号',
                    dataIndex: 'blno',
                    align: 'center',
                    width: 200,
                    hideInSearch: true,
                },
                {
                    title: '航线',
                    dataIndex: 'vesselName',
                    align: 'center',
                    width: 200,
                    hideInSearch: true,

                },
                // {
                //     title: '类型',
                //     dataIndex: 'mode',
                //     align: 'center',
                //     width: 100,
                //     hideInSearch: true,
                //     render: (_: any, recode: any) => {
                //         return recode?.mode == 1 ? '空运' : '海运'
                //     }

                // },
                // airLicenseNo
                {
                    title: '体积（m³）',
                    dataIndex: 'airVolume',
                    align: 'center',
                    hideInSearch: true,
                    width: 120,


                },
                {
                    title: '进仓编号',
                    dataIndex: 'airLicenseNo',
                    align: 'center',
                    hideInSearch: true,
                    width: 120,
                },
                {
                    title: '状态',
                    width: 80,
                    dataIndex: 'state',
                    align: 'center',
                    hideInSearch: true,
                    render: (_: any, text: any) => {
                        return (
                            <Tag color={statusEnum[text?.state]?.color}>{statusEnum[text?.state]?.text}</Tag>
                        );
                    }

                },
                {
                    title: '出发地',
                    dataIndex: 'departsCity',
                    align: 'center',
                    hideInSearch: true,
                    width: 160,


                },
                {
                    title: '到达地',
                    dataIndex: 'arrivesCity',
                    align: 'center',
                    hideInSearch: true,
                    width: 160
                },
                {
                    title: '开船时间',
                    dataIndex: 'airDepartTime',
                    align: 'center',
                    hideInSearch: true,
                    width: 160,
                    render: (_: any, text: any) => {
                        return text?.airDepartTime ? formatTimes(text?.airDepartTime) : ''
                    }
                },
            ];
        }

    }, [activeKey])
    /* 退订舱位 */
    const cancelShipping = async (id: any) => {
        // 退仓
        try {
            const { status } = await cancelShippingAPI({ id: id });
            if (status) {
                message.success('退订成功');
                actionRef?.current?.reload()
            }
        } catch (err) {
            console.log(err, 'err');

        }
    };
    // 快捷订舱
    const getFastBook = async () => {
        if (!fastBook?.agentId) return message.warning('请选择船司代理！')
        if (!fastBook?.model) return message.warning('规格不能为空！')
        if (fastBook?.number == 0) return message.warning('数量不能为0')
        try {
            const { status } = await fastBookAPI({
                cabinPlans: [
                    {
                        voyageId: fastBook.voyageId,
                        agentId: fastBook.agentId,
                        planModelList: [
                            {
                                model: fastBook.model,
                                planNumber: fastBook.number
                            }
                        ]
                    }
                ]
            })
            if (status) {
                message.success('快捷订舱发布成功')
                setIsModalOpen(false)
                actionRef?.current?.reload()
                refreshTab('/Booking/cabin')
            }
        } catch (error) {
            console.log(JSON.stringify(error), 'error');

        }
    }
    // const enumerate = [
    //     {
    //         label: '海运',
    //         value: 1
    //     },
    //     {
    //         label: '空运',
    //         value: 2
    //     },
    // ]
    //你这里的按钮 大概率会存在一个问题 当默认只有一个空运权限的时候 默认选中存在问题 这里后面自己处理一下 
    const enumerate = useMemo(() => {
        return toList([
            {
                label: '海运',
                value: 1,
                accessible: [':Transport:SpaceStatisticsAccess'],
                dataValidator: (path: any, data: any) => {
                    return /[01]/.test(data.type.value)
                }
            },
            {
                label: '空运',
                value: 2,
                accessible: [':Transport:SpaceStatisticsAccess'],
                dataValidator: (path: any, data: any) => {
                    return /[02]/.test(data.type.value)
                }
            },
        ])
    }, [])

    const filters = {
        "keyword": {
            "type": "keyword",
            "value": '',
            tabs: true
        },
        "departTime": {
            "desc": "起飞时间",
            "type": "dateTimeRange",
            "endTime": "",
            "startTime": "",
        }
    }
    return (
        <div className={styles.statistics}>
            {/* <ProTable columns={columns}
                search={{
                    labelWidth: 'auto',
                    collapsed: false,
                    optionRender: false

                }}
                formRef={ref}
                actionRef={actionRef}
                scroll={{ x: 1200, y: windowHeight - bootHieght }}
                rowKey="id"
                request={async (params: { current: number, pageSize: number }) => {
                    const { current: start, pageSize: len } = params;
                    // 海运
                    let newData
                    let res
                    if (activeKey == 1) {
                        res = await summaryAPI({
                            start: (start - 1) * len,
                            len,
                            keyword,
                            ...time
                        })
                        newData = res?.data?.list.map((item: any, index: number) => ({ ...item, id: index }))
                    } else {
                        // 空运
                        res = await cabinAPI({
                            start: (start - 1) * len,
                            len,
                            keyword,
                            type: 1,
                            ...time
                        })
                        newData = res?.data?.list.map((item: any, index: number) => ({ ...item, id: index }))
                    }

                    return {
                        data: newData || [],
                        success: res.status,
                        total: res.data?.total,
                    };
                }}
                toolbar={{
                    subTitle: <div style={{ display: 'flex', lineHeight: '32px' }} className={styles.inland_search}>
                        <ul>
                            {enumerate.map((item: any) => {
                                return <li key={item.value} style={{
                                    color: item.value == activeKey ? '#fff' : '#707070',
                                    background: item.value == activeKey ? '#4071FF' : '#fff'
                                }} onClick={() => {
                                    setActiveKey(item.value)
                                    actionRef?.current?.reload()
                                }}>{item.label}</li>
                            })}
                        </ul>
                    </div>
                }} /> */}

            <MrTable
                columns={columns}
                keyID={'Booking/cabin/statistics'}
                request={async (params: any, action: any) => {
                    actionRef.current = action
                    // const { current: start, pageSize: len } = params;
                    // 海运
                    let newData
                    let res
                    if (activeKey == 1) {
                        res = await summaryAPI({
                            ...params
                        })
                        newData = res?.data?.list.map((item: any, index: number) => ({ ...item, id: index }))
                    } else {
                        // 空运
                        res = await cabinAPI({
                            ...params,
                            type: 1,
                        })
                        newData = res?.data?.list.map((item: any, index: number) => ({ ...item, id: index }))
                    }

                    return {
                        data: newData || [],
                        success: res.status,
                        total: res.data?.total,
                    };
                }}
                filters={filters}
                // exportCallback={(value: any) => exportDetails(Array.isArray(value) ? { ids: value?.map((item: any) => item?.id) } : value)}
                toolBarRender={<div style={{ display: 'flex', lineHeight: '32px' }} className={styles.inland_search}>
                    <ul>
                        {enumerate.map((item: any) => {
                            return <li key={item.value} style={{
                                color: item.value == activeKey ? '#fff' : '#707070',
                                background: item.value == activeKey ? '#4071FF' : '#fff'
                            }} onClick={() => {
                                setActiveKey(item.value)
                                actionRef?.current?.reload()
                            }}>{item.label}</li>
                        })}
                    </ul>
                </div>
                }
            />
            <Modal style={{ minWidth: 900 }} destroyOnClose title="快捷订舱"
                onOk={getFastBook} open={isModalOpen} onCancel={() => setIsModalOpen(false)}>
                <div style={{ display: 'flex', justifyContent: 'space-between', margin: '30px 0 30px 0' }}>
                    <span>{fastBook?.name}</span>
                    <span>{fastBook?.shipsVoyage}</span>
                    <Select
                        // defaultValue={fastBook.model}
                        placeholder="请选择规格"
                        style={{ width: 120 }}
                        onChange={(e) => {
                            setFastBook({ ...fastBook, model: e })
                        }}
                        options={fastBook?.models?.map((item: any) => ({ label: item, value: item }))}
                    />
                    <Select
                        style={{ width: 200 }}
                        onChange={(e) => {
                            setFastBook({ ...fastBook, agentId: e })
                        }}
                        fieldNames={{
                            label: 'name',
                            value: 'id'
                        }}
                        // value={fastBook.agentId}
                        placeholder="请选择船司代理"
                        options={fastBook?.agents}
                    />
                </div>
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <MinusOutlined style={{ cursor: 'pointer' }}
                        onClick={() => {
                            setFastBook(() => {
                                if (fastBook.number == 0) {
                                    return {
                                        ...fastBook,
                                        number: 0
                                    };
                                }
                                return {
                                    ...fastBook,
                                    number: fastBook.number - 1
                                };

                            });
                        }} rev={undefined} /> <div style={{ position: 'relative' }}>
                        <Input value={fastBook.number} type='number' style={{ width: '90%', margin: '0 10px 0 10px' }}
                        /><span style={{ display: 'inline-block', position: 'absolute', bottom: 2, right: 20, width: 20, height: 28, backgroundColor: '#ffffff' }}></span>
                    </div>
                    <PlusOutlined style={{ cursor: 'pointer' }}
                        onClick={() => setFastBook({ ...fastBook, number: fastBook.number + 1 })} rev={undefined} />
                </div>
            </Modal>
        </div>
    );
};
export default Statistics