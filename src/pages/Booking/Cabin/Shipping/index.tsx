import Selects from '../../components/Select';
import Time from '../../components/Time';
import { useEffect, useMemo, useRef, useState } from 'react';
import TablePage from '../../components/TablePage';
import {
  airLineListAPI,
  bookingListAPI,
  cabinResale,
  CancelcabinResale,
  cancelShippingAPI,
  getWarehouseListByProviderIdAPI,
  shippingAgencyListAPI,
  shippingListAPI,
  transportationAPI,
} from '@/services/booking';
import {
  Popconfirm,
  Tag,
  message,
  Button,
  Modal,
  Form,
  Row,
  Col,
  Input,
  Select,
  Checkbox,
  DatePicker,
  Space,
  Progress,
} from 'antd';
import { formatDate, formatTime, formatTimes } from '@/utils/format';
import { statusEnum } from '../../components/type';
import MySearch from '@/components/MyProTable/MySearch';
import _ from 'lodash';
import 'moment/locale/zh-cn';
import moment from 'moment';
moment.locale('zh-cn');
const MyIcon = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_3877009_slyb3nrsy5.js',
});
import { createFromIconfontCN, SwapRightOutlined } from '@ant-design/icons';
import styles from './index.less';
import { ProviderType } from '@/shared/ProviderTypeFn';
import AccessCard from '@/AccessCard';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import EditvoyageModel from '@/pages/Booking/Cabin/EditvoyageModel';
import MrTable from '@/components/MrTable';
import { ProFormSelect } from '@ant-design/pro-components';
import { getClientListAPI } from '@/services/carriers';
import LogDashboard from '@/components/LogDashboard';
const Shipping = () => {
  const [toList] = usePermissionFiltering();
  const [form] = Form.useForm();
  const ref = useRef<any>(null);
  const actionRef = useRef<any>(null);
  const [keyword, setKeyword] = useState('');
  const [type, setType] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [cabinetOpen, setCabinetOpen] = useState(false);
  const [agency, setAgency] = useState([]);
  const [shipping, setShipping] = useState([]);
  const [airLine, setAirLine] = useState([]);
  const [warehouse, setWarehouse] = useState([]);
  const [airId, setAirId] = useState<any>({});
  const [check, setCheck] = useState(false);
  const [param, setParam] = useState({});
  const [blno, setBlno] = useState<any>({})
  /* 退订舱位 */
  const cancelShipping = async (id: any) => {
    try {
      const { status } = await cancelShippingAPI({ id: id });
      if (status) {
        message.success('退订成功');
        actionRef?.current.reload();
      }
    } catch (err) {
      console.log(err, 'err');
    }
  };
  const confirm = (id: string) => {
    cancelShipping(id);
  };
  //刷新
  const fresh = () => {
    actionRef?.current.reload();
  };
  const changeTime = (time: any) => {
    setParam({
      startTime: time ? formatDate(time[0]) + '00:00:00' : '',
      endTime: time ? formatDate(time[1]) + '23:59:59' : '',
    });

    ref?.current?.submit();
  };
  const columns = useMemo(() => {
    return [
      {
        title: '状态',
        width: 80,
        dataIndex: 'state',
        align: 'center',
        hideInSearch: true,
        render: (_: any, text: any) => {
          return (
            <Tag color={statusEnum[text?.state]?.color}>{statusEnum[text?.state]?.text}</Tag>
          );
        },
      },
      {
        title: '航程',
        dataIndex: 'departsCity',
        width: 220,
        align: 'center',
        hideInSearch: true,
        render: (text: any, record: any) => {
          return (
            <>
              <Space>
                <div>
                  {record?.mode === 1 ? (
                    <MyIcon type="icon-kongyun" />
                  ) : (
                    <MyIcon type="icon-haihang" />
                  )}
                </div>
                <div>{record?.departsCity}</div>
                <SwapRightOutlined />
                <div>{record?.deliveryCity}</div>
              </Space>
            </>
          );
        },
      },
      {
        title: '提单号',
        dataIndex: 'blno',
        align: 'center',
        width: 200,
        hideInSearch: true,
      },
      {
        title: '配舱率（m³）',
        hideInSearch: true,
        dataIndex: 'number',
        align: 'center',
        width: 220,
        render: (_: any, recode: any) => {
          if ((recode?.configure && recode.configure.info)) {
            const num = (recode?.configure?.volume / recode?.configure?.info?.volume) * 100;
            return (
              <div>
                {`${recode?.configure?.volume}/${recode?.configure?.info?.volume}`}&nbsp;&nbsp;
                <Progress percent={num} size="small" showInfo={false} />
              </div>
            );
          } else {
            return ''
          }

        },
      },
      {
        title: '航线',
        dataIndex: 'vesselName',
        align: 'center',
        width: 200,

        hideInSearch: true,
      },
      // {
      //     title: '类型',
      //     dataIndex: 'mode',
      //     align: 'center',
      //     width: 100,
      //     hideInSearch: true,
      //     render: (_: any, recode: any) => {
      //         return recode?.mode == 1 ? '空运' : '海运'
      //     }

      // },
      {
        title: '船名航次/航班',
        dataIndex: 'voyage',
        align: 'center',
        hideInSearch: true,
        width: 250,
        render: (_: any, recode: any) => {
          if (recode?.mode == 1) {
            return recode?.airCode;
          } else if (recode?.mode == 2) {
            return (
              <div>
                {`${recode?.shipName}/${recode?.voyageCode}`}
                <EditvoyageModel recode={recode} fresh={fresh} />
              </div>
            );
          }
        },
      },
      // airLicenseNo
      {
        title: '体积(m³)',
        dataIndex: 'volume',
        align: 'center',
        hideInSearch: true,
        width: 120,
        render: (_: any, recode: any) => {
          // 空运体积
          if (recode?.mode == 1) {
            return recode?.airVolume;
          } else {
            return recode?.volume;
          }
        },
      },
      {
        title: '规格',
        dataIndex: 'model',
        align: 'center',
        hideInSearch: true,
        width: 120,
      },

      {
        title: '进仓编号',
        dataIndex: 'airLicenseNo',
        align: 'center',
        hideInSearch: true,
        width: 120,
      },
      // {
      //     title: '订舱费',
      //     dataIndex: 'fee',
      //     align: 'center',
      //     hideInSearch: true,
      //     width: 120,
      // },

      {
        title: '出发地',
        dataIndex: 'departsCity',
        align: 'center',
        hideInSearch: true,
        width: 160,
      },
      {
        title: '到达地',
        dataIndex: 'arrivesCity',
        align: 'center',
        hideInSearch: true,
        width: 160,
      },
      {
        title: '开船/起飞时间',
        dataIndex: 'estimatedDepartTime',
        align: 'center',
        hideInSearch: true,
        width: 160,
        render: (_: any, text: any) => {
          if (text?.mode == 1) {
            return text?.airDepartTime ? formatTime(text?.airDepartTime) : '';
          } else {
            return text?.estimatedDepartTime
              ? formatTime(text?.estimatedDepartTime)
              : '';
          }
        },
      },
      {
        title: '操作',
        width: 270,
        key: 'option',
        valueType: 'option',
        align: 'center',
        hideInSearch: true,
        fixed: 'right',
        render: (_: any, record: any) => <>
          <AccessCard accessible={[':Transport:SpacePurchaseAccess']}>
            <Space key="operation">
              <Popconfirm
                key="delete"
                title="退订舱位"
                description="想好了吗，退订舱位?"
                onConfirm={() => {
                  confirm(record.id);
                }}
                onCancel={() => {
                  message.info('已取消操作');
                }}
                okText="确认"
                cancelText="再想想"
              >
                {[1, 10, 20].includes(record?.state) && (
                  <a style={{ color: '#4071ff' }}>
                    {record?.mode == 1 ? '撤销' : '退订'}
                  </a>
                )}
              </Popconfirm>
              {record?.mode == 1 && [1, 10, 20].includes(record?.state) && (
                <a
                  key="modification"
                  style={{ color: '#4071ff' }}
                  onClick={() => {
                    setAirId(record);
                    setIsModalOpen(true);
                  }}
                >
                  修改空运舱位
                </a>
              )}
              {/* <Popconfirm
                key="delete"
                title="舱位转卖"
                description="确定舱位转卖?"
                onConfirm={async () => {
                  const res = await cabinResale({ id: record.id });
                  if (res?.status) {
                    message.success('转卖成功');
                    actionRef?.current.reload();
                  }
                }}
                onCancel={() => {
                  message.info('已取消操作');
                }}
                okText="确认"
                cancelText="再想想"
              > */}

              {/* </Popconfirm> */}
              <Popconfirm
                key="delete"
                title="舱位撤销转卖"
                description="确定舱位转卖?"
                onConfirm={async () => {
                  const res = await CancelcabinResale({ id: record.id });
                  if (res?.status) {
                    message.success('撤销转卖成功');
                    actionRef?.current.reload();
                  }
                }}
                onCancel={() => {
                  message.info('已取消操作');
                }}
                okText="确认"
                cancelText="再想想"
              >
                {[-1].includes(record?.state) && (
                  <a style={{ color: '#4071ff' }}>撤销卖柜</a>
                )}
              </Popconfirm>
            </Space>
          </AccessCard>
          <AccessCard accessible={[':Transport:SpaceSalesFullAccess']}>  {[1, 10, 20].includes(record?.state) && (
            <a style={{ color: '#4071ff' }} onClick={() => {
              setBlno({ blno: record?.blno, id: record?.id })
              setCabinetOpen(true)
            }}>卖柜</a>
          )}</AccessCard>
          <LogDashboard extraId_1={record?.id} btnType="link" btnText="日志" key="journal" />
        </>,
      },
    ];
  }, []);

  const option = useMemo(() => {
    return toList([
      {
        label: '空运',
        value: '1',
        accessible: [':Transport:SpaceReadOnlyAccess'],
        dataValidator: (path: any, data: any) => {
          return /[01]/.test(data.type.value);
        },
      },
      {
        label: '海运',
        value: '2',
        accessible: [':Transport:SpaceReadOnlyAccess'],
        dataValidator: (path: any, data: any) => {
          return /[02]/.test(data.type.value);
        },
      },
    ]);
  }, []);
  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    shippingAgencyList();
    shippingList();
    airLineList();
  }, []);
  useEffect(() => {
    if (airId?.id && isModalOpen) {
      warehouses(airId?.agentId);
      setCheck(airId?.fee2 > 0 ? true : false);
      const labelData = airId?.airWarehouse;
      form.setFieldValue(
        'detailSite',
        `${labelData.country === 'CN' ? '中国' : labelData.country} - ${labelData.provinceShortName
        } / ${labelData.province} - ${labelData.city} -${labelData.county}/ ${labelData.zipCode
        }`,
      );
      form.setFieldsValue({
        ...airId,
        airDepartTime: airId?.airDepartTime ? moment(airId?.airDepartTime) : '',
        airLicenseTime: airId?.airLicenseTime ? moment(airId?.airLicenseTime) : '',
        check: 'on',
        airWarehouse: airId?.airWarehouse?.id,
      });
    }
  }, [airId?.id, isModalOpen]);
  // 船司代理
  const shippingAgencyList = async (keyword?: string) => {
    try {
      const { status, data } = await shippingAgencyListAPI({
        start: 0,
        len: 20,
        keyword,
        type: ProviderType.AirTransport.getCode(),
      });
      if (status) {
        setAgency(data?.list);
      }
    } catch (error) { }
  };

  // 船司代理搜索防抖
  const hadSearch = _.debounce((e: any) => {
    shippingAgencyList(e);
  }, 100);
  // 船司搜索防抖
  const shippingSearch = _.debounce((e: any) => {
    shippingList(e);
  }, 100);
  // 航线搜索防抖
  const airLineSearch = _.debounce((e: any) => {
    airLineList(e);
  }, 100);
  // 航司
  const shippingList = async (keyword?: string) => {
    try {
      const { status, data } = await shippingListAPI({
        start: 0,
        len: 20,
        keyword,
        type: 1,
      });
      if (status) {
        setShipping(data?.list);
      }
    } catch (error) { }
  };
  // 航线
  const airLineList = async (keyword?: string) => {
    try {
      const { status, data } = await airLineListAPI({
        start: 0,
        len: 20,
        keyword,
        type: 1,
      });
      if (status) {
        setAirLine(data?.list);
      }
    } catch (error) { }
  };
  // 船司代理查仓库
  const warehouses = async (providerId: any, keyword?: string) => {
    try {
      const { status, data } = await getWarehouseListByProviderIdAPI({
        start: 0,
        len: 20,
        keyword,
        providerId,
      });
      if (status) {
        setWarehouse(data?.list);
      }
    } catch (error) { }
  };
  const updateTrailerInfo = async (value: any) => {
    if (!value?.fee) return message.warning('价格不能为空');
    const newValue = {
      ...value,
      check: value?.check ? true : false,
      airDepartTime: value?.airDepartTime ? formatTimes(value?.airDepartTime) : '',
      airLicenseTime: value?.airLicenseTime ? formatTimes(value?.airLicenseTime) : '',
      airWarehouse: {
        ...(warehouse?.filter(
          (ele: any) => ele?.id == value?.airWarehouse,
        )[0] as any),
      },
    };
    if (airId?.id) {
      newValue['id'] = airId?.id;
    }
    for (const key in newValue) {
      if (!newValue[key]) delete newValue[key];
      delete newValue?.detailSite;
    }
    try {
      const { status } = await transportationAPI(newValue);
      if (status) {
        message.success(airId?.id ? '修改成功' : '新建成功');
        setIsModalOpen(false);
        setCheck(false);
        setAirId({});
        actionRef?.current.reload();
      }
    } catch (error) { }
    // console.log(value, 'value');
  };
  // 卖柜
  const getSellCabinet = async () => {
    if (!blno?.clientId) return message.warning('请选择客户')
    const res = await cabinResale({ id: blno.id, clientId: blno?.clientId });
    if (res?.status) {
      message.success('转卖成功');
      setCabinetOpen(false)
      actionRef?.current.reload();
    }
  }
  const inputStyle = {
    backgroundColor: '#FBFBFB',
    width: '160px',
    border: 'none',
  };
  const filters = {
    "keyword": {
      "type": "search",
      "value": '',
      "termQuery": false,
      tabs: true

    },

    "type": {
      "desc": "类型",
      "type": "select",
      "range": option,
      value: ''
    },
    "state": {
      "desc": "状态",
      "type": "select",
      multi: true,
      "range": [
        { label: '空闲', value: '1' },
        { label: '待出库', value: '10' },
        { label: '已提柜', value: '20' },
        { label: '已出库', value: '30' },
        { label: '已进港', value: '40' },
        { label: '已开船', value: '50' },
        { label: '到达目的港', value: '60' },
        { label: '清关查验', value: '70' },
        { label: '海外提柜', value: '80' },
        { label: '已还柜', value: '90' },
        { label: '已转卖', value: '-1' },
        { label: '已退订', value: '-2' },
      ],
      value: ''
    },
    "departTime": {
      "desc": "开船/起飞",
      "type": "dateTimeRange",
      "endTime": "",
      "startTime": "",
    },
  }
  return (
    <div className={styles.modalSelet}>
      {/* <TablePage
        columns={columns}
        optionRender={false}
        scroll={{ x: 1200 }}
        formRef={ref}
        actionRef={actionRef}
        request={async (params: { current: number; pageSize: number }) => {
          const { current: start, pageSize: len } = params;
          const res = await bookingListAPI({
            ...param,
            start: (start - 1) * len,
            len,
            keyword,
            type,
            state
          });
          return {
            data: res.data.list || [],
            success: res.status,
            total: res.data.total,
          };
        }}
      // toolBar={<Button type="primary" onClick={() => setIsModalOpen(true)}>新建空运舱位</Button>}
      /> */}
      <MrTable
        columns={columns}
        keyID={'Booking/cabin/space'}
        request={async (params: any, action: any) => {
          actionRef.current = action
          const res = await bookingListAPI({
            ...params
          })

          return {
            data: res.data.list || [],
            success: res.status,
            total: res.data?.total,
          };
        }}
        filters={filters}
        toolBarRender={<AccessCard
          accessible={[':Transport:SpacePurchaseAccess']}
          dataValidator={(path: any, data: any) => {
            return /[01]/.test(data.type.value);
          }}
        >
          <Button
            type="primary"
            onClick={() => setIsModalOpen(true)}
            style={{ width: '130px' }}
          >
            新建空运舱位
          </Button>
        </AccessCard>} />
      <Modal
        title={airId?.id ? '修改空运舱位' : '新建空运舱位'}
        destroyOnClose
        afterClose={() => {
          form?.resetFields();
          setAirId({});
        }}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        style={{ minWidth: '880px', top: '8%' }}
        footer={[
          <div
            style={{ display: 'flex', justifyContent: 'center' }}
            key="footer"
          >
            <Button
              key="link"
              onClick={() => {
                setIsModalOpen(false);
              }}
              style={{ marginRight: '20px' }}>
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              onClick={() => {
                form?.submit();
              }}
            >
              确定
            </Button>
          </div>,
        ]}
      >
        <div className={styles.modal_box}>
          <Form
            name="basic"
            form={form}
            layout="vertical"
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 16 }}
            initialValues={{ remember: true }}
            onFinish={updateTrailerInfo}
            autoComplete="off"
            className={styles.shipping_arrange_form}
          >
            <Row>
              <Col span={8}>
                <Form.Item label="提单号" name="blno" labelCol={{ span: 9 }}>
                  <Input placeholder="请输入" style={inputStyle} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="航司代理"
                  name="agentId"
                  labelCol={{ span: 9 }}
                  rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Select
                    showSearch
                    filterOption={(input, option: any) => true}
                    className={styles.waybillNumber}
                    style={{ width: '160px' }}
                    placeholder="请选择"
                    onSearch={(e) => {
                      hadSearch(e);
                    }}
                    onChange={(e) => {
                      warehouses(e);
                    }}
                    options={agency?.map((item: any) => ({
                      label: item?.name,
                      value: item?.id,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="航司"
                  labelCol={{ span: 9 }}
                  name="providerId"
                // rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Select
                    showSearch
                    filterOption={(input, option: any) => true}
                    className={styles.waybillNumber}
                    style={{ width: '160px' }}
                    placeholder="请选择"
                    options={shipping?.map((item: any) => ({
                      label: item?.name,
                      value: item?.provider?.id,
                    }))}
                    onSearch={(e) => {
                      shippingSearch(e);
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="航线"
                  name="vesselId"
                  labelCol={{ span: 9 }}
                  rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Select
                    showSearch
                    className={styles.waybillNumber}
                    style={{ width: '160px' }}
                    placeholder="请选择"
                    filterOption={(input, option: any) => true}
                    options={airLine?.map((item: any) => ({
                      label: item?.name,
                      value: item?.id,
                    }))}
                    onSearch={(e) => {
                      airLineSearch(e);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <Form.Item
                  label="航班"
                  name="airCode"
                  labelCol={{ span: 9 }}
                // rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Input placeholder="请输入" style={inputStyle} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="起飞时间"
                  name="airDepartTime"
                  labelCol={{ span: 9 }}
                // rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <DatePicker style={{ width: '160px' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="时效"
                  labelCol={{ span: 9 }}
                  name="airPeriod"
                // rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Select
                    showSearch
                    className={styles.waybillNumber}
                    style={{ width: '160px' }}
                    placeholder="请选择"
                    options={[
                      { value: 0, label: '当日达' },
                      { value: 1, label: '次日达' },
                      { value: 2, label: '三天后达' },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <Form.Item
                  label="代理仓库"
                  name="airWarehouse"
                  labelCol={{ span: 9 }}
                  rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Select
                    showSearch
                    className={styles.waybillNumber}
                    style={{ width: '160px' }}
                    placeholder="请选择"
                    options={warehouse?.map((item: any) => ({
                      value: item?.id,
                      label: item?.name,
                    }))}
                    onChange={(e) => {
                      const labelData =
                        warehouse?.filter((item: any) => item?.id == e)
                          ?.length > 0
                          ? warehouse?.filter((item: any) => item?.id == e)[0]
                          : ({} as any);
                      form.setFieldValue(
                        'detailSite',
                        `${labelData.country === 'CN'
                          ? '中国'
                          : labelData.country
                        } - ${labelData.provinceShortName} / ${labelData.province
                        } - ${labelData.city} -${labelData.county}/ ${labelData.zipCode
                        }`,
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col
                span={8}
                style={{ boxSizing: 'border-box', paddingTop: '30px' }}
              >
                <Form.Item name="detailSite" labelCol={{ span: 9 }}>
                  <Input
                    placeholder="请输入"
                    style={{ ...inputStyle, width: '350px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <Form.Item
                  label="体积(m³)"
                  name="airVolume"
                  labelCol={{ span: 9 }}
                  rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Input placeholder="请输入" style={inputStyle} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="进仓编号"
                  name="airLicenseNo"
                  labelCol={{ span: 9 }}
                  rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <Input placeholder="请输入" style={inputStyle} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="进仓时间（代理仓库）"
                  labelCol={{ span: 22 }}
                  name="airLicenseTime"
                  rules={[{ required: true, message: '必填项不能为空' }]}
                >
                  <DatePicker style={{ width: '160px' }} />
                </Form.Item>
              </Col>
              <Col span={8} style={{ height: '74px' }}>
                <div style={{ display: 'flex', width: '220px' }}>
                  <Form.Item label="价格" name="fee" labelCol={{ span: 9 }}>
                    <Input
                      placeholder="请输入"
                      style={inputStyle}
                      type="number"
                    />
                  </Form.Item>
                  <span
                    style={{
                      display: 'inline-block',
                      lineHeight: '86px',
                      color: '#AEAEAE',
                    }}
                  >
                    &nbsp;&nbsp;元/公斤
                  </span>
                </div>
              </Col>
            </Row>
            <Row>
              {/* <Col span={6}>
                                <Form.Item
                                    name="check"
                                    labelCol={{ span: 9 }}>
                                    <div className={styles.checkbox}>平重同价<Checkbox checked={check} onChange={(e) => {
                                        setCheck(e?.target?.checked)
                                    }}></Checkbox></div>
                                </Form.Item>
                            </Col> */}
              {/* <Col span={6} style={{ height: '74px' }}>
                              <div style={{display:'flex',width:'220px'}}>
                              <Form.Item
                                    label="价格"
                                    name="fee"
                                    labelCol={{ span: 9 }}>
                                    <Input placeholder="请输入" style={inputStyle} type="number" /> 
                                </Form.Item><span style={{display:'inline-block',lineHeight:'86px',color:'#AEAEAE'}}>&nbsp;&nbsp;元/公斤</span>
                              </div>
                            </Col> */}
            </Row>
            {/* {!check && <Row>
                            <Col span={12} >
                               <div style={{display:'flex',}}>
                               <Form.Item
                                    name="fee2">
                                    <Input placeholder="请输入" style={{ ...inputStyle, marginLeft: '216px', marginTop: '-20px' }} type="number" />
                                </Form.Item>
                               <div style={{marginLeft:'10px',color:'#AEAEAE'}}><span style={{display:'inline-block',width:'200px'}}>元/公斤&nbsp;&nbsp;&nbsp;kg/m³&nbsp;{`>= 1/200`}</span></div>
                               </div>
                            </Col>
                        </Row>} */}
          </Form>
        </div>
      </Modal>
      <Modal title='卖柜' open={cabinetOpen}
        destroyOnClose
        onCancel={() => setCabinetOpen(false)}
        footer={[
          <div key='btn' style={{ display: 'flex', justifyContent: 'center' }}>
            <Button key="back" onClick={() => setCabinetOpen(false)} style={{ marginRight: '12px' }}>取消</Button>
            <Button key="submit" type="primary" onClick={getSellCabinet} >确认</Button>
          </div>
        ]}>
        <div className={styles.billNumber}>
          <div><span>提单号</span><span>{blno?.blno}</span></div>

        </div>
        <div className={styles.clientName}>
          <ProFormSelect
            name="clientName"
            label="客户名称"
            showSearch
            onChange={(e) => {
              setBlno({ ...blno, clientId: e })
            }}
            request={async ({ keyWords }) => {
              const { status, data } = await getClientListAPI({
                type: 0,
                len: 100,
                keyword: keyWords,
              });
              if (status) {
                return data.list.map((item: any) => {
                  return {
                    label: `【${item.name}】 - ${item.code}`,
                    value: item.id,
                  };
                });
              }
              return [];
            }}
          />
        </div>
      </Modal>
    </div>
  );
};
export default Shipping;
