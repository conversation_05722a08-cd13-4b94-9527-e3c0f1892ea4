.modalSelet {
    :global {

        .ant-select-selector,
        .ant-input {
            background-color: #FBFBFB !important;
            border: none !important;
        }

        // .ant-picker.ant-picker-range {
        //     border: none !important;
        // }
    }


}

.shipping_arrange_form {
    :global {
        .ant-select-selector {
            border: none !important;
            background-color: #FBFBFB !important;
        }

        .ant-form-item-label>label {
            color: #707070 !important;
        }

        .ant-picker {
            border: none;
        }

    }
}

.modal_box {
    background-color: #ebecee;
    margin-left: -24px;
    margin-right: -24px;
    padding-left: 30px;
    padding-top: 20px;
    padding-bottom: 20px;
}

.checkbox {
    display: flex;
    justify-content: space-between;
    // padding: 0 6px;
    padding: 0 15px 0 15px;
    width: 100px;
    height: 30px;
    line-height: 30px;
    background: #FBFBFB;
    border-radius: 24px;
    font-size: 12px;
}


.billNumber {
    width: 482px;
    height: 66px;
    background: url('../../../../assets//images/cabinet.png') no-repeat;
    background-size: cover;
    padding-left: 25px;
    box-sizing: border-box;

    >div {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;

        :first-child {
            font-weight: 400;
            font-size: 16px;
            color: #707070;
            margin-right: 16px;
        }

        :nth-child(2) {
            font-weight: 600;
            font-size: 20px;
            color: #333333;
        }
    }

    margin-bottom: 31px;
}

.clientName {
    :global {
        .ant-select-selector {
            border: none !important;
        }

        .ant-btn {
            background: #fff;
            color: #4071ff;
            border: 1px solid #d9d9d9;
        }


        .ant-form-item-label {
            border: 1px solid #d9d9d9;
            padding-left: 10px;
            border-radius: 4px 0 0 4px;
            border-right: none;
        }

        .ant-form-item-no-colon {
            color: #707070;
        }

        .ant-form-item-control {
            border: 1px solid #d9d9d9;
            border-radius: 0 4px 4px 0;
        }

        .ant-select-selector {
            border: none;
        }

        .ant-picker-outlined {
            border: none;
        }

        .ant-input-outlined {
            border: none;
        }

    }

}