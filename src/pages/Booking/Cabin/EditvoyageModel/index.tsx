import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal, Space, DatePicker, Tag, message } from 'antd';

import { EditOutlined } from '@ant-design/icons';

import MySearch from '@/components/MyProTable/MySearch';
import { formatTimes } from '@/utils/format';

import { editVoyage, voyageListAPI } from '@/services/booking';
import Selects from '@/pages/Booking/components/Select';
import Time from '@/pages/Booking/components/Time';
import AccessCard from '@/AccessCard';
import locale from 'antd/es/date-picker/locale/zh_CN';
import moment from 'moment';
import { ProTable } from '@ant-design/pro-components';
import VoyageModel from '@/pages/Booking/Flight/Voyage/modal/VoyageModel';
import { history } from '@@/core/history';

const EditvoyageModel = (props: any) => {
  const { onSelectWaybill, type, DefaultSelected, recode, fresh } = props;
  console.log('record', recode);
  const ref = useRef<any>(null);
  const actionRef = useRef<any>(null);
  const [param, setParam] = useState({});
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的信息
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  // 被选中的信息
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [keyword, setKeyword] = useState<any>('');
  const rowSelection: any = {
    type: 'radio',
    selectedRowKeys,
    onChange: (keys: any, row: any) => {
      console.log('keys: ', keys);
      setSelectedRowKeys(keys);
      setSelectedRows(row);
    },
  };
  useEffect(() => {
    /*设置回显选中项目*/
    if (isModalOpen && DefaultSelected) {
      setSelectedRowKeys(DefaultSelected);
    }
  }, [isModalOpen]);
  /* 获取常用地址库地址 */

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    const param = { voyageId: selectedRowKeys[0], id: recode.id };
    const res = await editVoyage(param);
    if (res?.status) {
      message.success('修改成功');
      fresh();
    }
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const columns: any = useMemo(() => {
    return [
      {
        hideInTable: true,
        fieldProps: {
          placeholder: '请输入企业名称、ID搜索',
        },
        renderFormItem: () => {
          return (
            <MySearch
              placeholder="请输入关键字搜索"
              allowClear
              enterButton="搜索"
              size="large"
              style={{ width: '300px', background: '#FBFBFB' }}
              onSearch={(e: any) => {
                setKeyword(e);
              }}
            />
          );
        },
      },

      {
        title: '开船时间',
        // dataIndex: 'keywords',
        hideInTable: true,
        // hideInSearch: true,
        renderFormItem: () => {
          /* return <Time changeTime={changeTime} />;*/
        },
      },
      {
        title: '航线',
        align: 'center',
        width: 240,
        // fixed: 'left',
        hideInSearch: true,
        // render: (recode: any) => {
        //   return recode?.vessel?.name;
        // },
        render: (_: any, recode: any) => {
          return recode?.vessel?.name;
        },
      },
      {
        title: '船司',
        dataIndex: 'providerName',
        align: 'center',
        width: 200,

        hideInSearch: true,
      },
      {
        title: '船名/航次',
        dataIndex: '',
        align: 'center',
        width: 400,
        hideInSearch: true,
        // render: (recode: any) => {
        //   return `${recode?.shipName}/${recode?.code}`;
        // },
        render: (_: any, recode: any) => {
          return `${recode?.shipName}/${recode?.code}`;
        },
      },
      {
        title: '始发港',
        dataIndex: 'vessel',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any) => {
          return recode?.vesselProperties?.departsCity;
        },
      },
      {
        title: '到达港',
        width: 200,

        dataIndex: 'vessel',
        align: 'center',
        hideInSearch: true,
        render: (recode: any) => {
          return recode?.vesselProperties?.arrivesCity;
        },
      },
      {
        title: '最晚预报',
        dataIndex: 'forecastLimitTime',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any, _: any) => {
          return formatTimes(recode);
        },
      },
      {
        title: '最晚到货',
        dataIndex: 'cargoLimitTime',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any, _: any) => {
          return formatTimes(recode);
        },
      },
      {
        title: '预计开船',
        dataIndex: 'estimatedDepartTime',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any, _: any) => {
          return formatTimes(recode);
        },
      },
      {
        title: '预计到港',
        dataIndex: 'estimatedArrivalTime',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any, _: any) => {
          return formatTimes(recode);
        },
      },
      {
        title: '航程',
        dataIndex: 'vessel',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any, text: any) => {
          return recode?.vesselProperties?.period;
        },
      },
      {
        title: '预计提柜',
        dataIndex: 'estimatedDeliveryTime',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any, _: any) => {
          return formatTimes(recode);
        },
      },
      {
        title: '开船后派送时效',
        dataIndex: 'vessel',
        width: 200,
        align: 'center',
        hideInSearch: true,
        render: (recode: any) => {
          return `${recode?.vesselProperties?.leastDays}~${recode?.vesselProperties?.mostDays}`;
        },
      },
      {
        title: '装柜地',
        dataIndex: 'vessel',
        valueType: 'option',
        key: 'option',
        align: 'center',
        width: 180,
        fixed: 'right',
        render: (text: any, recode: any) => {
          return (
            recode?.vessel?.cabinetCityList &&
            recode?.vessel?.cabinetCityList
              .map((item: any) => item.name)
              ?.join('、')
          );
          // return recode?.cabinetCity
        },
      },
    ];
  }, []);
  return (
    <>
      <div
        onClick={showModal}
        style={{ display: 'inline-block', cursor: 'pointer' }}
      >
        {/* <IconItem name="#icon-dizhibao" style={{ marginRight: '6px' }} /> */}
        <EditOutlined style={{ marginLeft: '5px' }} />
      </div>
      <Modal
        width="1800px"
        title="选择船名船次"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        footer={[
          <Space key="btn">
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        <div style={{ maxHeight: '70vh', overflow: 'auto' }}>
          {/* 地址选项列表 */}
          <ProTable
            columns={columns}
            formRef={actionRef}
            scroll={{ x: 1200 }}
            cardBordered
            rowSelection={rowSelection}
            request={async (params: any) => {
              const { current: start, pageSize: len } = params;
              const res = await voyageListAPI({
                start: (start - 1) * len,
                len,
                keyword: keyword,
                type: 2,
                vesselId: recode?.vesselId,
              });
              return {
                data: res.data.list || [],
                success: res.status,
                total: res.data.total,
              };
            }}
            params={{ keyword }}
            rowKey={(record) => {
              console.log('recordaa', record);
              return record?.voyage?.id;
            }}
            options={{
              fullScreen: true,
            }}
            dateFormatter="string"
            style={{ marginBottom: 20 }}
            search={{
              labelWidth: 'auto',
              collapsed: false,
              optionRender: false,
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default React.memo(EditvoyageModel);
