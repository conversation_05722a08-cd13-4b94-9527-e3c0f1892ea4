import React, { useMemo, useState } from "react"
import { Tabs } from 'antd';
import Statistics from "./Statistics";
import Shipping from "./Shipping";
import ShippingPlan from "./ShippingPlan";
import usePermissionFiltering from "@/hooks/usePermissionFiltering";
import TabsType from "@/components/TabsType";
interface EditableRowProps {
    index?: number
}
const Cabin: React.FC<EditableRowProps> = () => {
    const [toList] = usePermissionFiltering()
    const itemse = useMemo(() => {
        return toList([
            {
                label: '统计',
                key: '0',
                accessible: [':Transport:SpaceStatisticsAccess']
            },
            {
                label: '舱位',
                key: '1',
                accessible: [':Transport:SpaceReadOnlyAccess']
            },
            {
                label: '订舱计划',
                key: '2',
                accessible: [':Transport:SpacePlanFullAccess', ':Transport:SpacePurchaseAccess'],
                dataValidator: (path: any, data: any) => {
                    if (path === ':Transport:SpacePurchaseAccess')
                        return /[02]/.test(data.type.value)
                    return true;
                }
            },
            // {
            //     label: '卖柜订单',
            //     key: '3',
            //     // accessible: [':Transport:SpaceStatisticsAccess']
            // },
        ])
    }, [])
    const [activeKey, setActiveKey] = useState<string>(itemse[0]?.key);
    const onChange = (key: any) => {
        setActiveKey(key);
    };
    const tabsProps = useMemo(
        () => ({ activeKey, items: toList(itemse), onChange }),
        [activeKey],
    );
    const renderComponents: any = {
        '0': <Statistics />,
        '1': <Shipping />,
        '2': <ShippingPlan />,
        // '3': <SellCabinet />
    }
    return (
        <>
            <TabsType {...tabsProps} />
            <div style={{ height: 'calc(100vh - 115px)' }}>
                {renderComponents[activeKey]}
            </div>
        </>
    );
}
export default Cabin
