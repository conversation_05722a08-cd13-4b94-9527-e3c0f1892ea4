.shippingParticulars {

    .ant-table-footer {
        background-color: #fff;
    }

    .new-btn {
        width: 130px;
        // flex: 1;
        border-radius: 4px;
        border: 1px dashed #4071FF;
        padding: 5px 10px;
        background: #fff;
        cursor: pointer;
        font-size: 14px;
        color: #4071FF;
        margin-right: 20px;
        
    }

    .footer-box {
        background: #fff;
        display: flex;
        justify-content: space-around;
        // padding-left: 20%;
        padding-left: 180px;
        .ant-table-footer {
            background: #fff;
        }

    }

    .shipping {
        width: 100%;
        height: 328px;
        box-sizing: border-box;
        background-color: #fff;
        margin-bottom: 30px;
        border-radius: 10px;

        .shippingTop {
            display: flex;
            padding-left: 20px;
            box-sizing: border-box;
            height: 78px;
            line-height: 78px;
        }

        .shippingCen {
            height: 74px;
            padding-left: 20px;

            line-height: 74px;

            svg {
                width: 100px;
                color: #4071FF;
                transform: scale(3.0);
            }

        }

        .shippingScheduleCen {
            padding-left: 20px;
            .shippingSchedule {
                display: flex;
                width: 90%;
                height: 16px;
                background-color: #ccc;
                border-radius: 8px;
            }

            .shippingScheduleTitle {
                display: flex;
                justify-content: space-between;
                width: 40%;
                // background-color: red;
                border-radius: 0 0 10px 10px;
                margin-top: 10px;

                >div {
                    >i {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border-radius: 50%;
                        margin-right: 5px;
                    }
                }

                >div:nth-child(1) {
                    >i {
                        background: #3751FE;
                    }
                }

                >div:nth-child(2) {
                    >i {
                        background: #7B8EFF;
                    }
                }

                >div:nth-child(3) {
                    >i {
                        background: #05E48A;
                    }
                }

                >div:nth-child(4) {
                    >i {
                        background: #9DFD83;
                    }
                }

                >div:nth-child(5) {
                    >i {
                        background: #FFE68A;
                    }
                }
            }
        }

        .shippBoot {
            height: 100px;
            background: #F8FCFF;
            border-radius: 0 0 10px 10px;
            margin-top: 32px;

            ul {
                display: flex;
                height: 100px;

                li {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    list-style: none;
                    width: 12%;
                    height: 100%;
                    // background-color: red;
                }

                li:nth-child(1) {
                    margin-left: -5%;
                }
            }
        }
    }
}