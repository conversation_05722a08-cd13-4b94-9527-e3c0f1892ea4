import { MinusOutlined, PlusOutlined, SwapRightOutlined } from "@ant-design/icons"
import { Button, Input, Modal, Select, Table, message } from 'antd';
import './index.less'
import { useContext, useEffect, useState } from "react";
import { useLocation } from "@umijs/max";
import { cabinGetDetailAPI, fastBookAPI } from "@/services/booking";
import { formatTimes } from "@/utils/format";
import { KeepAliveTabContext } from '@/layouts/context';
const Particulars = () => {
    const enumeration: any = {
        1: '预定',
        2: '使用',
        3: '预配',
        4: '销售预报',
        5: '退订',
        6: '剩余',
    }
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { closeTab, refreshTab } = useContext(KeepAliveTabContext);
    const [fastBook, setFastBook] = useState<any>({})
    const [particulars, setParticulars] = useState<any>()
    const { state }: any = useLocation()
    const id = state?.id
    const [columns, setColumns] = useState<any>()
    // const priIdRef = useRef<any>(id)
    const newlyAdded = () => {
        // setFastBook({ ...fastBook, model: type })
        setIsModalOpen(true)
    }
    useEffect(() => {
        cabinGetDetail(id)
    }, [id])
    // 舱位统计详情
    const cabinGetDetail = async (id: string) => {
        if (id) {
            try {
                const { status, data } = await cabinGetDetailAPI({ id })
                if (status) {
                    data?.cabinRecords.map((item: any, index: number) => {
                        item[item.model] = item.number
                        item[item.id] = index
                    })
                    // const newColumns = columns.map((item: any, index: any) => {
                    //     if (index !== 0) {
                    //         return {
                    //             ...item,
                    //             title: data?.models[index - 1],
                    //             dataIndex: data?.models[index - 1],
                    //         }
                    //     }
                    //     return item
                    // })
                    // setColumns(newColumns)
                    setParticulars(data)
                    setFastBook({
                        name: data?.voyage?.vessel?.name,
                        shipsVoyage: `${data?.voyage?.voyageProperties?.shipName}/${data?.voyage?.voyageProperties?.code}`,
                        voyageId: data?.voyage?.id,
                        model: '',
                        number: 0,
                        agents: data?.voyage?.vessel?.agents,
                        models: data?.models
                    })

                }
            } catch (error) {

            }
        }
    }
    const dynamicColumns = (cabinRecords: any) => {
        const array: any = [
            {
                title: '订舱记录',
                dataIndex: 'state',
                align: 'center',
                width: 200,
                render: (recode: any) => {
                    return <a style={{ color: '#ccc' }}>{recode && enumeration[recode]}</a>
                }
            },
        ]
        cabinRecords?.forEach((ele: any, i: number) => {
            array.splice(1, 0, {
                title: ele,
                dataIndex: ele,
                align: 'center',
                // width: 167,
            });
        })
        return array
    }

    // 快捷订舱
    const getFastBook = async () => {
        if (!fastBook?.agentId) return message.warning('请选择船司代理！')
        if (!fastBook?.model) return message.warning('规格不能为空！')
        if (fastBook?.number == 0) return message.warning('数量不能为0')
        try {
            const { status } = await fastBookAPI({
                cabinPlans: [
                    {
                        voyageId: fastBook.voyageId,
                        agentId: fastBook.agentId,
                        planModelList: [
                            {
                                model: fastBook.model,
                                planNumber: fastBook.number
                            }
                        ]
                    }
                ]
            })
            if (status) {
                message.success('快捷订舱发布成功')
                setIsModalOpen(false)
                cabinGetDetail(id)
                refreshTab('/Booking/cabin')
            }
        } catch (error) {
            console.log(JSON.stringify(error), 'error');

        }
    }
    return (
        <div className='shippingParticulars'>
            <div className="shipping">
                <div className="shippingTop">
                    <h3 style={{ marginRight: 40 }}>{particulars?.voyage?.vessel?.name || '-'}</h3>
                    <h3 style={{ marginRight: 40 }}>{particulars?.voyage?.vessel?.vesselProperties?.code || '-'}</h3>
                    <h3 style={{ marginRight: 40 }}>{`${particulars?.voyage?.voyageProperties?.shipName || ''}/${particulars?.voyage?.voyageProperties?.code || ''}`}</h3>
                </div>
                <Button style={{ position: 'absolute', top: 20, right: 20 }} type="primary" onClick={() => {
                    history.back()

                }}>返回</Button>
                <div className="shippingCen">
                    <span>出发港 </span>
                    <span style={{ fontWeight: 700 }}>{
                        particulars?.voyage?.vessel?.vesselProperties?.departsCity || '-'
                    }</span>
                    <SwapRightOutlined rev={undefined} />
                    <span>目的地 </span>
                    <span style={{ fontWeight: 700 }}>{
                        particulars?.voyage?.vessel?.vesselProperties?.arrivesCity || '-'

                    }</span>
                </div>
                <div className="shippingScheduleCen">
                    {/* 进度 */}
                    <div className="shippingSchedule">
                        <div style={{ backgroundColor: '#3751FE', width: '10%', borderRadius: '8px 0px 0px 8px' }}></div>
                        <div style={{ backgroundColor: '#7B8EFF', width: '20%' }}></div>
                        <div style={{ backgroundColor: '#05E48A', width: '25%' }}></div>
                        <div style={{ backgroundColor: '#9DFD83', width: '25%' }}></div>
                        <div style={{ backgroundColor: '#FFE68A', width: '20%', borderRadius: '0px 8px 8px 0px' }}></div>

                    </div>
                    {/* 标题 */}
                    <div className="shippingScheduleTitle">
                        <div><i></i><span>已装货</span></div>
                        <div><i></i><span>销售预报</span></div>
                        <div><i></i><span>预配置</span></div>
                        <div><i></i><span>空余</span></div>
                        <div><i></i><span>短缺</span></div>
                    </div>
                </div>
                <div className="shippBoot">
                    <ul>
                        <li>
                            <span>最晚预报</span>
                            <span>{
                                particulars?.voyage?.voyageProperties?.forecastLimitTime ? formatTimes(particulars?.voyage?.voyageProperties?.forecastLimitTime) : '-'
                            }</span>
                        </li>
                        <li>
                            <span>最晚到货</span>
                            <span>{
                                particulars?.voyage?.voyageProperties?.cargoLimitTime ? formatTimes(particulars?.voyage?.voyageProperties?.cargoLimitTime) : '-'

                            }</span>
                        </li>
                        <li>
                            <span>预计开船</span>
                            <span>{
                                particulars?.voyage?.voyageProperties?.estimatedDepartTime ? formatTimes(particulars?.voyage?.voyageProperties?.estimatedDepartTime) : '-'

                            }</span>

                        </li>
                        <li>
                            <span>预计到港</span>
                            <span>{
                                particulars?.voyage?.voyageProperties?.estimatedArrivalTime ? formatTimes(particulars?.voyage?.voyageProperties?.estimatedArrivalTime) : '-'
                            }</span>
                        </li>
                        <li>
                            <span>航程</span>
                            <span>{
                                particulars?.voyage?.vessel?.vesselProperties?.period || '-'}</span>
                        </li>
                        <li>
                            <span>预计提柜</span>
                            <span>{
                                particulars?.voyage?.voyageProperties?.estimatedDeliveryTime ? formatTimes(particulars?.voyage?.voyageProperties?.estimatedDeliveryTime) : '-'
                            }</span>
                        </li>
                        <li>
                            <span>开船后派送时效</span>
                            <span>{
                                `${particulars?.voyage?.vessel?.vesselProperties?.leastDays || ''} -  ${particulars?.voyage?.vessel?.vesselProperties?.mostDays || ''}`
                            }</span>
                        </li>
                        <li>
                            <span>装柜地</span>
                            <span>{
                                particulars?.voyage?.vessel?.cabinetCityList?.map((item: any) => item.name)?.join('、') || '-'
                            }</span>
                        </li>

                    </ul>
                </div>
            </div>
            <Table columns={dynamicColumns(particulars?.models)}
                rowKey='id'
                bordered
                footer={(e) => {
                    return (
                        <div className={'footer-box'}>
                            {particulars?.models?.map((ele: any) => <div className={'new-btn'}
                                onClick={() => {
                                    newlyAdded()
                                }}
                            >
                                + 订舱
                            </div>)}

                            {/* <div className={'new-btn'}
                                onClick={() => {
                                    newlyAdded()
                                }}

                            >
                                + 订舱
                            </div>
                            <div className={'new-btn'}
                                onClick={() => newlyAdded()}
                            >
                                + 订舱
                            </div>
                            <div className={'new-btn'}
                                onClick={() => newlyAdded()}
                            >
                                + 订舱
                            </div> */}
                        </div>
                    );
                }}
                pagination={false} dataSource={particulars?.cabinRecords?.map((item: any) => ({ ...item, id: item.model })) || []} />
            <Modal style={{ minWidth: 900 }} onOk={getFastBook} destroyOnClose title="快捷订舱" open={isModalOpen} onCancel={() => {
                setFastBook({
                    ...fastBook,
                    number: 0
                })
                setIsModalOpen(false)
            }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', margin: '20px 0 20px 0' }}>
                    <span>{fastBook?.name}</span>
                    <span>{fastBook?.shipsVoyage}</span>
                    <Select
                        style={{ width: 120 }}
                        // defaultValue={fastBook.model}
                        placeholder="请选择规格"
                        onChange={(e) => {
                            setFastBook({ ...fastBook, model: e })
                        }}
                        options={fastBook?.models?.map((item: any) => ({ label: item, value: item }))}
                    />
                    <Select
                        style={{ width: 200 }}
                        onChange={(e) => {
                            setFastBook({ ...fastBook, agentId: e })
                        }}
                        fieldNames={{
                            label: 'name',
                            value: 'id'
                        }}
                        placeholder="请选择船司代理"
                        // defaultValue={fastBook?.agentId}
                        options={fastBook?.agents}
                    />
                </div>
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <MinusOutlined style={{ cursor: 'pointer' }}
                        onClick={() => {
                            setFastBook(() => {
                                if (fastBook.number == 0) {
                                    return {
                                        ...fastBook,
                                        number: 0
                                    };
                                }
                                return {
                                    ...fastBook,
                                    number: fastBook.number - 1
                                };

                            });
                        }} rev={undefined} /> <div style={{ position: 'relative' }}>
                        <Input value={fastBook.number} type='number' style={{ width: '90%', margin: '0 10px 0 10px' }} onChange={(e) => {
                            setFastBook({ ...fastBook, number: Number(e?.target?.value || 0) })
                        }} />
                        <span style={{ display: 'inline-block', position: 'absolute', bottom: 2, right: 20, width: 20, height: 28, backgroundColor: '#fff' }}></span>
                    </div>
                    <PlusOutlined style={{ cursor: 'pointer' }}
                        onClick={() => {
                            setFastBook({ ...fastBook, number: fastBook.number + 1 })
                        }} rev={undefined} />
                </div>
            </Modal>
        </div>
    )
}
export default Particulars