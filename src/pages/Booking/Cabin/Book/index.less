// .search {
//     .searchTop {
//         // height: 140px;
//         border-radius: 5px;
//         background-color: #fff;
//         box-sizing: border-box;
//         padding-left: 20px;
//         position: relative;
//         .search_top {
//             width: 100%;
//             height: 138px;
//             background-color: #fff;
//             box-sizing: border-box;
//             padding: 24px;
//             :global {
//                 .ant-select-selector {
//                     border: none !important;
//                     background-color: #fafafa !important;
//                 }
//             }
//             .criteria {
//                 display: flex;
//                 flex-wrap: wrap;
//                 // justify-content: space-between;
//                 // width: 700px;
//                 height: 60px;
//                 >div {
//                     // margin-bottom: 20px;
//                     // margin-right: 20px;
//                     margin: 0 20px 20px 0;
//                 }
//                 :global {
//                     .ant-picker-range {
//                         border: none;
//                         background-color: #fafafa !important;
//                     }
//                 }
//             }
//             .service {
//                 display: flex;
//                 justify-content: space-between;
//                 width: 700px;
//                 height: 75px;

//             }
//             .search_title {
//                 color: #707070;
//             }
//         }
//         :global {
//             .ant-select-selector,.ant-input {
//                 background-color: #FBFBFB !important;
//                 border: none !important;
//             }
//             .ant-picker.ant-picker-range{
//                 border: none !important;
//             }
//         }
//     }  
//     :global {
//         .ant-pro-query-filter{
//             padding: 0 !important;
//         }
//     }

// }
.warp-book {
    :global {
        .rc-virtual-list-scrollbar-thumb {
            background: #d8e1ee !important;
        }

        // .rc-virtual-list-scrollbar {
        //   visibility: visible !important;
        // }

        .ant-pagination {
            background: #fbfbfd;
            padding: 12px 14px 12px 4px;
            border: 1px solid #E2E4E6;
            border-top: none;
            border-radius: 0 0 4px 4px;
            margin: 0 !important;
            color: #707070;
        }
    }

    .footer-box-select {
        position: relative;
        bottom: 52px;
        z-index: 10;
        left: 35px;
        display: inline-block;
        // padding: 12px 14px 12px 4px;
        // border-right: 1px solid #E2E4E6;
    }

    .footer-box-select::after {
        content: '';
        position: absolute;
        width: 1px;
        height: 48px;
        background: #E2E4E6;
        right: -12px;
        top: -12px;
        z-index: 11;

    }
}