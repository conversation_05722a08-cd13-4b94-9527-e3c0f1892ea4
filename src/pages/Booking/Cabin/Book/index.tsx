
// import { Row, Col, Form, Input, Select, DatePicker, Button, message } from 'antd'

// import { ProTable } from '@ant-design/pro-components';
// import { useContext, useEffect, useRef, useState } from 'react';
// const { RangePicker } = DatePicker;
// import { fastBookAPI, previewAPI, shippingListAPI } from '@/services/booking';
// import { history } from '@umijs/max';
// import { KeepAliveTabContext } from '@/layouts/context';
// import MySearch from '@/components/MyProTable/MySearch';
// import { getLastLastLastWeekRange, getLastLastWeekRange, getLastWeekRange, formatTimes } from '@/utils/format';
// import _ from 'lodash'
// import 'moment/locale/zh-cn';
// import moment from 'moment';
// import MrTable from '@/components/MrTable';
// import styles from '@/components/MrTable/index.less'
// moment.locale('zh-cn');
// const Book: React.FC = () => {
//     // 刷新 url
// const { closeTab, refreshTab } = useContext(KeepAliveTabContext);
//     // 获取 Table 
//     const actionRef = useRef<any>(null)
//     const [preview, setPreview] = useState<any>([])
//     const [bookSked, setBookSked] = useState(false)
//     const [keyword, setKeyeyword] = useState('')
//     const ref = useRef<any>(null)
//     const [type, setType] = useState('')
//     const [previous, setPrevious] = useState<any>([])
//     // 往期参数
//     const [previousDate, setPreviousData] = useState<any>({})
//     const [time, setTime] = useState<any>({})
//     // 船司
//     const [shipping, setShipping] = useState([])
// const columns: any = [
//     {
//         title: '航线',
//         align: 'center',
//         hideInSearch: true,
//         width: 300,
//         fixed: 'left',
//         render: (_: any, recode: any) => {
//             return <div>
//                 <span style={{
//                     display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
//                     transform: 'translateX(-50%)'
//                 }}>{recode?.voyage?.vessel?.name}</span>
//             </div>
//         }
//     },

//     {
//         title: '航线代码',
//         dataIndex: 'vessel',
//         align: 'center',
//         hideInSearch: true,
//         width: 160,
//         render: (_: any, recode: any) => {
//             return <div>
//                 <span style={{
//                     display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
//                     transform: 'translateX(-50%)'
//                 }}>{recode?.voyage?.vessel?.vesselProperties?.code}</span>
//             </div>

//         }

//     },
//     {
//         title: '船司',
//         dataIndex: 'vessel',
//         align: 'center',
//         hideInSearch: true,
//         width: 160,
//         render: (_: any, recode: any) => {
//             return <div>
//                 <span style={{
//                     display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
//                     transform: 'translateX(-50%)'
//                 }}>{recode?.voyage?.vessel?.vesselProperties?.providerName}</span>
//             </div>

//         }

//     },
//     {
//         title: '船名航次',
//         dataIndex: 'voyage',
//         align: 'center',
//         hideInSearch: true,
//         width: 300,
//         render: (_: any, recode: any) => {
//             return <div>
//                 <span style={{
//                     display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
//                     transform: 'translateX(-50%)'
//                 }}>{`${recode?.voyage?.voyageProperties?.shipName}/${recode?.voyage?.voyageProperties?.code}`}</span>
//             </div>
//         }


//     },
//     {
//         title: '开船时间',
//         align: 'center',
//         hideInSearch: true,
//         width: 160,
//         render: (_: any, recode: any) => {
//             return <div>
//                 <span style={{
//                     display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
//                     transform: 'translateX(-50%)'
//                 }}>{recode?.voyage?.voyageProperties?.estimatedDepartTime && formatTimes(recode.voyage.voyageProperties.estimatedDepartTime)}</span>
//             </div>
//         }

//     },
//     {
//         title: '船司代理',
//         align: 'center',
//         hideInSearch: true,
//         selectable: 'none',
//         width: 320,
//         render: (_: any, data: any) => {
//             const option = data?.voyage?.vessel?.agents
//             const value = data?.voyage?.vessel?.newData?.map((_: any, index: any) => ({
//                 ..._
//             }));
//             return value?.map((item: any, index: number) => {
//                 return <Select value={item.id} key={`${item.key}_${index}`}
//                     placeholder="请选择船司"
//                     style={{
//                         width: '300px',
//                         marginBottom: 10
//                     }}
//                     options={option?.map((ele: any) => { return { label: ele.name, value: ele.id } })}
//                     onChange={(e) => {
//                         data.voyage.vessel.newData[index].id = e
//                         data.voyage.vessel['models'] = option?.filter((fid: any) => fid?.id === e)?.length ? option?.filter((fid: any) => fid?.id === e)[0].modelList : []
//                         setBookSked(!bookSked)
//                         // setPreview([...preview])
//                     }} />
//             })

//         }
//     },
//     {
//         title: '规格',
//         align: 'center',
//         hideInSearch: true,
//         width: 180,
//         selectable: 'none',
//         render: (_: any, data: any, i: number) => {
//             // const option = data?.models
//             const option = data?.voyage?.vessel?.models
//             const value = data?.voyage?.vessel?.newData?.map((_: any, index: any) => ({
//                 ..._
//             }));
//             return value?.map((item: any, index: number) => {
//                 return <Select value={item.model} key={`${item.key}_${index}`}
//                     placeholder="请选择规格"
//                     style={{ width: '160px', marginBottom: 10 }}
//                     options={option?.map((ele: any) => { return { label: ele, value: ele } })}
//                     onChange={(e) => {
//                         data.voyage.vessel.newData[index].model = e
//                         setBookSked(!bookSked)
//                         // setPreview([...preview])
//                     }} />
//             })
//         }
//     },
//     {
//         title: '数量',
//         align: 'center',
//         hideInSearch: true,
//         width: 200,
//         selectable: 'none',
//         render: (_: any, data: any, i: number) => {
//             const value = data?.voyage?.vessel?.newData?.map((_: any, index: any) => ({
//                 ..._
//             }));
//             return value?.map((item: any, index: number) => {
//                 return <div key={`${item.key}_${index}`}>
//                     <Input type='number' value={item.successNumber} key={`${item.key}_${index}`} placeholder='请输入数量' style={{ width: 140, marginBottom: 10, marginRight: 10 }}
//                         onChange={(e) => {
//                             const num = Number(e.target.value) <= 0 ? 0 : Number(e.target.value)
//                             data.voyage.vessel.newData[index].successNumber = num
//                             setBookSked(!bookSked)
//                             // setPreview([...preview])
//                         }}
//                     />
//                     {value?.length >= 2 ? <a key={2} style={{ color: '#FF5840' }} onClick={() => {
//                         const newData = [...preview];
//                         newData[i].voyage.vessel.newData.splice(index, 1)
//                         setBookSked(!bookSked)
//                         // setPreview([...preview])
//                     }}>删除</a> : ''}
//                 </div>
//             })

//         }
//     },
//     {
//         title: '操作',
//         width: 180,
//         key: 'option',
//         valueType: 'option',
//         align: 'center',
//         hideInSearch: true,
//         fixed: 'right',
//         selectable: 'none',

//         render: (text: any, recode: any, index: number) => {
//             return <a key={1} onClick={() => {
//                 const newData = [...preview];
//                 const indexLength = newData[index].voyage.vessel.newData.length
//                 newData[index].voyage.vessel.newData.splice(indexLength + 1, 0, {
//                     agentPropertiesId: undefined,
//                     model: undefined,
//                     name: "",
//                     successNumber: 0,
//                     voyageId: recode.voyage.id,
//                     key: Math.floor(Math.random() * 100)
//                 });
//                 setBookSked(!bookSked)
//                 // setPreview([...preview])
//             }}>添加</a>
//         }
//     }
// ];
// useEffect(() => {
//     setPrevious([
//         { label: `${getLastLastLastWeekRange().startWeek}`, value: `${getLastLastLastWeekRange().startWeek}` },
//         { label: `${getLastLastWeekRange().startWeek}`, value: `${getLastLastWeekRange().startWeek}` },
//         { label: '上周', value: '上周' },
//     ])
//     // setPreview([{}, {}])
//     shippingData()
// }, [])
// const handleChange = (e: any) => {
//     if (e == '上周') {
//         setPreviousData({
//             historyStartDate: formatTimes(getLastWeekRange().startDate),
//             historyEndDate: formatTimes(getLastWeekRange().endDate)
//         })
//     } else if (e == getLastLastWeekRange().startWeek) {
//         setPreviousData({
//             historyStartDate: formatTimes(getLastLastWeekRange().startDate),
//             historyEndDate: formatTimes(getLastLastWeekRange().endDate)
//         })
//     } else if (e == getLastLastLastWeekRange().startWeek) {
//         setPreviousData({
//             historyStartDate: formatTimes(getLastLastLastWeekRange().startDate),
//             historyEndDate: formatTimes(getLastLastLastWeekRange().endDate)
//         })
//     } else {
//         setPreviousData({})
//     }
//     ref?.current?.submit()
// }
// const transformData = (data: any) => {
//     // const agents = ele.voyage.vessel.agents;
//     const newData = data?.map((ele: any) => {
//         if (previousDate?.historyStartDate && previousDate?.historyEndDate) {
//             const agents = ele.voyage.vessel.agents;
//             // 用于后期往期复制的时候，处理数据用
//             const onlyObj: Record<string, any> = {};
//             agents.forEach((item: any) => {
//                 item.models.forEach((element: any) => {
//                     const key = (item.name + element.model) as string;
//                     const currentItem = onlyObj[key] || {};
//                     onlyObj[key] = {
//                         ...item,
//                         model: element.model,
//                         // voyageId: ele.voyage.vessel.id,
//                         voyageId: ele.voyage.id,
//                         planNumber: Number(currentItem.planNumber || 0) + element.planNumber,
//                         successNumber:
//                             // (Number(currentItem?.historySuccessNumber || 0) + element?.historySuccessNumber) || 0,
//                             (Number(currentItem?.successNumber || 0) + element?.successNumber) || 0,

//                     };
//                 });
//             });
//             const newData = Object.values(onlyObj).map((ele) => ele);
//             ele.voyage.vessel.newData = newData;
//         } else {
//             const agents = ele.voyage.vessel;
//             // 默认就是一行
//             agents['newData'] = [{
//                 "agentPropertiesId": "",
//                 "code": "",
//                 "id": undefined,
//                 "mode": 2,
//                 "nameEn": "COSCO SHIPPING",
//                 "model": undefined,
//                 "voyageId": ele.voyage.id,
//                 "planNumber": 24,
//                 "successNumber": 0
//             },]
//         }
//         return {
//             ...ele,
//             voyage: {
//                 ...ele?.voyage,
//                 vessel: {
//                     ...ele?.voyage?.vessel,
//                     models: []
//                 }
//             }
//         };
//     });
//     return newData;
// }
// // 将数据处理成后端需要的传
// const releaseData = () => {
//     const newData = preview?.map((item: any) => item.voyage.vessel.newData).flat()
//     const processedData: any = [];
//     newData.forEach((item: any) => {
//         const existingItem = processedData.find((obj: any) => obj.agentId === item.id);
//         if (existingItem) {
//             const existingModel = existingItem.planModelList.find((model: any) => model.model === item.model);
//             if (existingModel) {
//                 existingModel.planNumber += item.successNumber;
//             } else {
//                 existingItem.planModelList.push({
//                     model: item.model,
//                     planNumber: item.successNumber
//                 });
//             }
//         } else {
//             processedData.push({
//                 agentId: item.id,
//                 voyageId: item.voyageId,
//                 planModelList: [
//                     {
//                         model: item.model,
//                         planNumber: item.successNumber
//                     }
//                 ]
//             });
//         }
//     });
//     fastBook(processedData)
// }
// // 订舱发布
// const fastBook = async (data: any) => {
//     let shouldStop = false; // 标志变量，初始值为false
//     const arr = data?.filter((item: any) => item.agentId != undefined)
//     if (arr.length <= 0) return message.warning('船司代理不能为空！');
//     arr.forEach((v: any) => {
//         v?.planModelList?.forEach((fid: any) => {
//             if (shouldStop) return
//             if (fid.model == undefined) {
//                 message.warning('规格不能为空！');
//                 shouldStop = true
//                 return
//             }
//             if (fid.planNumber == 0) {
//                 message.warning('数量不能为0！');
//                 shouldStop = true
//                 return
//             }
//         })

//     })
//     if (!shouldStop && arr?.length >= 1) {
//         try {
//             const { status } = await fastBookAPI({
//                 cabinPlans: arr,
//             });
//             if (status) {
//                 message.success('订舱成功')
//                 setPreviousData({})
//                 history.back()
//                 closeTab()
//                 refreshTab('/Booking/cabin')
//             }
//         } catch (error) {
//             console.log(JSON.stringify(error), 'error');
//         }
//     }

// }
//     const rangePicker = (date: any) => {
//         setTime({
//             startDate: date ? formatTimes(date[0].$d) : '',
//             endDate: date ? formatTimes(date[1].$d) : ''
//         })
//         ref?.current?.submit()
//     }
//     //   船司
//     const shippingData = async (keyword?: string) => {
//         try {
//             const { status, data } = await shippingListAPI({
//                 start: 0,
//                 len: 20,
//                 keyword,
//                 type: 2
//             })
//             if (status) {
//                 setShipping(data?.list)
//             }
//         } catch (error) {

//         }
//     }
//     const shippingSearch = _.debounce((e: any) => {
//         shippingData(keyword)
//     })
// const refresh = () => {
//     actionRef.current?.reload();
// };
//     const filters = {
//         "keyword": {
//             "type": "keyword",
//             "value": '',
//         },
//         "providerId": {
//             "desc": "船司",
//             "type": "shipping",
//             "value": ""
//         },
//         "departTime": {
//             "desc": "开船时间",
//             "type": "dateTimeRange",
//             "startTime": "",
//             "endTime": "",
//         },
//         "historyDepartTime": {
//             "desc": "复制往期",
//             "type": "select",
//             // getLastWeekRange
// "range": [
//     { label: `${getLastLastLastWeekRange().startWeek}`, value: `${formatTimes(getLastLastLastWeekRange().startDate)} 00:00:00,${formatTimes(getLastLastLastWeekRange().endDate)} 23:59:59` },
//     { label: `${getLastLastWeekRange().startWeek}`, value: `${formatTimes(getLastLastWeekRange().startDate)} 00:00:00,${formatTimes(getLastLastWeekRange().endDate)} 23:59:59` },
//     { label: '上周', value: `${formatTimes(getLastWeekRange().startDate)} 00:00:00,${formatTimes(getLastWeekRange().endDate)} 23:59:59` },
// ],
//             "value": ""
//         },
//     }
//     // useEffect(() => {
//     //     setPreview([{ label: '3232', text: '232' }])
//     // }, [])
//     return (
//         // <div className={styles.search}>
//         //     <div className={styles.searchTop}>
//         //         <h3 style={{ height: 30, paddingTop: 20 }}>订舱计划</h3>
//         //         {/* <Form
//         //             name="basic"
//         //             labelCol={{ span: 8 }}
//         //             wrapperCol={{ span: 16 }}
//         //             style={{ maxWidth: 1000 }}
//         //             initialValues={{ remember: true }}
//         //             autoComplete="off"
//         //         >
//         //             <Row>
//         //                 <Col span={7}>
//         //                     <Form.Item
//         //                     >
//         //                         <MySearch
//         //                             placeholder="请输入船司、航司、航线搜索"
//         //                             allowClear
//         //                             enterButton="搜索"
//         //                             size="large"
//         //                             style={{ width: '300px', background: '#FBFBFB' }}
//         //                             onSearch={(e: any) => {
//         //                                 setKeyeyword(e)
//         //                                 ref?.current?.submit()
//         //                             }}
//         //                         />
//         //                     </Form.Item>
//         //                 </Col>
//         //                 <Col span={4}>
//         //                     <Form.Item
//         //                         label="船司"
//         //                         name="username"
//         //                     >
//         //                         <Select
//         //                             placeholder="请选择"
//         //                             style={{ width: 140 }}
//         //                             allowClear
//         //                             onChange={(e) => {
//         //                                 setType(e)
//         //                                 ref?.current?.submit()
//         //                             }}
//         //                             showSearch
//         //                             options={shipping?.map((item:any) => ({value:item?.provider?.id,label:item?.name}))}
//         //                             onSearch={(e) => {
//         //                                 shippingSearch(e)
//         //                             }}
//         //                         />
//         //                     </Form.Item>
//         //                 </Col>
//         //                 <Col span={8}>
//         //                     <Form.Item
//         //                         label="开船时间"
//         //                         name="day"
//         //                     >
//         //                         <RangePicker locale={moment.locale('zh-cn') as any} style={{ width: 200 }} onChange={rangePicker} />
//         //                     </Form.Item>
//         //                 </Col>
//         //                 <Col span={5}>
//         //                     <Form.Item
//         //                         label="复制往期"
//         //                         name="come"
//         //                     >
//         //                         <Select
//         //                             placeholder="请选择"
//         //                             allowClear
//         //                             style={{ width: 140 }}
//         //                             onChange={handleChange}
//         //                             options={previous}
//         //                         />
//         //                     </Form.Item>
//         //                 </Col>
//         //             </Row>
//         //         </Form> */}
//         //         <div className={styles.search_top}>
//         //             <div className={styles.criteria}>
//         //                 <MySearch
//         //                     placeholder="请输入船司、航司、航线搜索"
//         //                     allowClear
//         //                     enterButton="搜索"
//         //                     size="large"
//         //                     style={{ width: '300px', background: '#FBFBFB' }}
//         //                     onSearch={(e: any) => {
//         //                         setKeyeyword(e)
//         //                         ref?.current?.submit()
//         //                     }}
//         //                 />
//         //                 <div>
//         //                     <span className={styles.search_title}>船司：</span>
//         //                     <Select
//         //                         placeholder="请选择"
//         //                         style={{ width: 220 }}
//         //                         allowClear
//         //                         onChange={(e) => {
//         //                             setType(e)
//         //                             ref?.current?.submit()
//         //                         }}
//         //                         showSearch
//         //                         options={shipping?.map((item: any) => ({ value: item?.provider?.id, label: item?.name }))}
//         //                         onSearch={(e) => {
//         //                             shippingSearch(e)
//         //                         }}
//         //                     />
//         //                 </div>
//         //                 <div>
//         //                     <span className={styles.search_title}>开船时间：</span>
//         //                     <RangePicker locale={moment.locale('zh-cn') as any} style={{ width: 320 }} onChange={rangePicker} />


//         //                 </div>
//         //                 <div>
//         //                     <span className={styles.search_title}>复制往期：</span>
//         //                     <Select
//         //                         placeholder="请选择"
//         //                         allowClear
//         //                         style={{ width: 220 }}
//         //                         onChange={handleChange}
//         //                         options={previous}
//         //                     />
//         //                 </div>
//         //             </div>
//         //             {/* <div className={styles.service}>
//         //         <div>
//         //             <span className={styles.search_title}>服务商：</span>
//         //             <Select
//         //                 placeholder="请选择"
//         //                 style={{ width: 140, marginRight: 20 }}
//         //                 options={[
//         //                     { value: 'Declaration', label: '报关' },
//         //                     { value: 'Clearance', label: '清关' },
//         //                     { value: 'Trailer', label: '拖车' },
//         //                     { value: 'AirTransport', label: '空运' },
//         //                     { value: 'ShipTransport', label: '海运' },
//         //                     { value: 'TailCHannel', label: '尾程渠道' },
//         //                     { value: 'TransChannel', label: '卖货应付' },
//         //                     { value: 'AbroadOperate', label: '后端操作' },
//         //                     { value: 'ContainerLoading', label: '装柜' },
//         //                     { value: 'Logistics', label: '国内物流' },
//         //                     { value: 'Truck', label: '卡派' }
//         //                 ]}
//         //                 onChange={(e) => {
//         //                     // getlistProviderByType(e)
//         //                 }} />

//         //         </div>
//         //         <div>
//         //             <span className={styles.search_title}>状态：</span>
//         //             <Select
//         //                 allowClear
//         //                 placeholder="请选择"
//         //                 style={{ width: 160 }}
//         //                 options={[
//         //                     { value: 10, label: '待审批' },
//         //                     { value: 20, label: '已审核' },
//         //                     { value: 40, label: '已做废' },
//         //                 ]}
//         //                 onChange={(e) => {
//         //                     // setSearchData({ ...searchData, state: e })
//         //                     // formSubmit()
//         //                 }} />
//         //         </div>
//         //     </div> */}
//         //         </div>
//         //     </div>
//         //     {/* <ProTable<any>
//         //         scroll={{ x: 1600 }}
//         //         columns={columns}
//         //         actionRef={actionRef}
//         //         rowKey='id'
//         //         dataSource={preview}
//         //         formRef={ref}
//         request={async (params: any) => {
//             const { current: start, pageSize: len } = params;
//             const msg = await previewAPI({
//                 start: (start - 1) * len,
//                 len,
//                 keyword,
//                 // type,
//                 ...previousDate,
//                 ...time
//             });
// const newData = transformData(msg.data.list)
// setPreview(newData.map((item: any, index: number) => { return { ...item, id: index } }))
//             return {
//                 data: preview || [],
//                 success: msg.status,
//                 total: msg.data.total,
//             };
//         }}
//         //         editable={{
//         //             type: 'multiple',

//         //         }}

//         //         search={{
//         //             labelWidth: 'auto',
//         //             collapsed: false,
//         //             optionRender: false

//         //         }}
//         //         options={{
//         //             fullScreen: true,
//         //         }}
//         //         dateFormatter="string"
//         //         style={{ marginBottom: 20 }}
//                 footer={() => {
//                     return (
// (<Button
//     type="primary"
//     onClick={releaseData}>
//     发布
// </Button >)

//         //             );
//         //         }}
//         //     /> */}


//         // </div>
//         <>
//             <MrTable
//                 columns={columns}
//                 keyID={'Booking/cabin/book'}
//                 request={async (params: any, action: any) => {
//                     actionRef.current = action
//                     const res = await previewAPI({
//                         ...params,
//                     });
//                     const newData = transformData(res.data.list).map((item: any, index: number) => { return { ...item, id: index } })
//                     setPreview(newData.map((item: any, index: number) => { return { ...item, id: index } }))
//                     return {
//                         data: newData || [],
//                         success: res.status,
//                         total: res.data.total,
//                     };
//                 }}
//                 filters={filters}
//                 toolBarRender={<Button
//                     type="primary"
//                     onClick={releaseData}>
//                     发布
//                 </Button >}
//                 bookSked={bookSked} /></>
//     )
// }
// export default Book
import { Row, Col, Form, Input, Select, DatePicker, Button, message, ConfigProvider } from 'antd'
import { ProFormDateTimeRangePicker, ProFormSelect, ProTable } from '@ant-design/pro-components';
import { useContext, useEffect, useRef, useState } from 'react';
const { RangePicker } = DatePicker;
import { fastBookAPI, getWarehouseListAPI, previewAPI, shippingListAPI } from '@/services/booking';
import { history } from '@umijs/max';
import { KeepAliveTabContext } from '@/layouts/context';
import MySearch from '@/components/MyProTable/MySearch';
import { getLastLastLastWeekRange, getLastLastWeekRange, getLastWeekRange, formatTimes } from '@/utils/format';
import _ from 'lodash'
import 'moment/locale/zh-cn';
import moment from 'moment';
import styles from './index.less';
import dayjs from 'dayjs';
import SearchCardNew from '@/components/SearchCardNew';
// const textEllipsis = (text: any) => {
//   return (
//     <div className="truncate">
//       <Tooltip title={text}>
//         <span>{text}</span>
//       </Tooltip>
//     </div>
//   );
// };
// const renderStatefn = (text: any, record: any) => {
//   return (
//     <Tag
//       style={{
//         ...waybillStatusEnum[record?.waybillState]?.styles,
//         color: waybillStatusEnum[record?.waybillState]?.styles?.fontSizeColor,
//       }}
//       color={waybillStatusEnum[record?.waybillState]?.color}
//     >
//       {waybillStatusEnum[record?.waybillState]?.text}
//     </Tag>
//   );
// };


const Book = () => {
    const actionRef = useRef<any>();
    const [searchParams, setSearchParams] = useState<any>({});
    const [keyword, setKeyword] = useState<string>('');
    const [selectedRows, setSelectedRows] = useState<any>([]);
    const [windowHeight, setWindowHeight] = useState(window.innerHeight);
    const [preview, setPreview] = useState<any>([])
    const [previousDate, setPreviousData] = useState<any>({})
    const [previous, setPrevious] = useState<any>([])
    const { closeTab, refreshTab } = useContext(KeepAliveTabContext);
    const [key, setKey] = useState(1)
    const columns: any = [
        {
            title: '航线',
            align: 'center',
            hideInSearch: true,
            width: 300,
            fixed: 'left',
            render: (_: any, recode: any) => {
                return <div>
                    <span style={{
                        display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
                        transform: 'translateX(-50%)'
                    }}>{recode?.voyage?.vessel?.name}</span>
                </div>
            }
        },

        {
            title: '航线代码',
            dataIndex: 'vessel',
            align: 'center',
            hideInSearch: true,
            width: 160,
            render: (_: any, recode: any) => {
                return <div>
                    <span style={{
                        display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
                        transform: 'translateX(-50%)'
                    }}>{recode?.voyage?.vessel?.vesselProperties?.code}</span>
                </div>

            }

        },
        {
            title: '船司',
            dataIndex: 'vessel',
            align: 'center',
            hideInSearch: true,
            width: 160,
            render: (_: any, recode: any) => {
                return <div>
                    <span style={{
                        display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
                        transform: 'translateX(-50%)'
                    }}>{recode?.voyage?.vessel?.vesselProperties?.providerName}</span>
                </div>

            }

        },
        {
            title: '船名航次',
            dataIndex: 'voyage',
            align: 'center',
            hideInSearch: true,
            width: 300,
            render: (_: any, recode: any) => {
                return <div>
                    <span style={{
                        display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
                        transform: 'translateX(-50%)'
                    }}>{`${recode?.voyage?.voyageProperties?.shipName}/${recode?.voyage?.voyageProperties?.code}`}</span>
                </div>
            }


        },
        {
            title: '开船时间',
            align: 'center',
            hideInSearch: true,
            width: 160,
            render: (_: any, recode: any) => {
                return <div>
                    <span style={{
                        display: 'inline-block', width: '100%', position: 'absolute', top: 15, left: '50%',
                        transform: 'translateX(-50%)'
                    }}>{recode?.voyage?.voyageProperties?.estimatedDepartTime && formatTimes(recode.voyage.voyageProperties.estimatedDepartTime)}</span>
                </div>
            }

        },
        {
            title: '船司代理',
            align: 'center',
            hideInSearch: true,
            selectable: 'none',
            width: 320,
            render: (_: any, data: any) => {
                const option = data?.voyage?.vessel?.agents
                const value = data?.voyage?.vessel?.newData?.map((_: any, index: any) => ({
                    ..._
                }));
                return value?.map((item: any, index: number) => {
                    return <Select value={item.id} key={`${item.key}_${index}`}
                        placeholder="请选择船司"
                        style={{
                            width: '300px',
                            marginBottom: 10
                        }}
                        options={option?.map((ele: any) => { return { label: ele.name, value: ele.id } })}
                        onChange={(e) => {
                            data.voyage.vessel.newData[index].id = e
                            data.voyage.vessel['models'] = option?.filter((fid: any) => fid?.id === e)?.length ? option?.filter((fid: any) => fid?.id === e)[0].modelList : []
                            // setBookSked(!bookSked)
                            setPreview([...preview])
                        }} />
                })

            }
        },
        {
            title: '规格',
            align: 'center',
            hideInSearch: true,
            width: 180,
            selectable: 'none',
            render: (_: any, data: any, i: number) => {
                // const option = data?.models
                const option = data?.voyage?.vessel?.models
                const value = data?.voyage?.vessel?.newData?.map((_: any, index: any) => ({
                    ..._
                }));
                return value?.map((item: any, index: number) => {
                    return <Select value={item.model} key={`${item.key}_${index}`}
                        placeholder="请选择规格"
                        style={{ width: '160px', marginBottom: 10 }}
                        options={option?.map((ele: any) => { return { label: ele, value: ele } })}
                        onChange={(e) => {
                            data.voyage.vessel.newData[index].model = e
                            // setBookSked(!bookSked)
                            setPreview([...preview])
                        }} />
                })
            }
        },
        {
            title: '数量',
            align: 'center',
            hideInSearch: true,
            width: 200,
            selectable: 'none',
            render: (_: any, data: any, i: number) => {
                const value = data?.voyage?.vessel?.newData?.map((_: any, index: any) => ({
                    ..._
                }));
                return value?.map((item: any, index: number) => {
                    return <div key={`${item.key}_${index}`}>
                        <Input type='number' value={item.successNumber} key={`${item.key}_${index}`} placeholder='请输入数量' style={{ width: 140, marginBottom: 10, marginRight: 10 }}
                            onChange={(e) => {
                                const num = Number(e.target.value) <= 0 ? 0 : Number(e.target.value)
                                data.voyage.vessel.newData[index].successNumber = num
                                // setBookSked(!bookSked)
                                setPreview([...preview])
                            }}
                        />
                        {value?.length >= 2 ? <a key={2} style={{ color: '#FF5840' }} onClick={() => {
                            const newData = [...preview];
                            newData[i].voyage.vessel.newData.splice(index, 1)
                            // setBookSked(!bookSked)
                            setPreview([...preview])
                        }}>删除</a> : ''}
                    </div>
                })

            }
        },
        {
            title: '操作',
            width: 180,
            key: 'option',
            valueType: 'option',
            align: 'center',
            hideInSearch: true,
            fixed: 'right',
            selectable: 'none',

            render: (text: any, recode: any, index: number) => {
                return <a key={1} onClick={() => {
                    const newData = [...preview];
                    const indexLength = newData[index].voyage.vessel.newData.length
                    newData[index].voyage.vessel.newData.splice(indexLength + 1, 0, {
                        agentPropertiesId: undefined,
                        model: undefined,
                        name: "",
                        successNumber: 0,
                        voyageId: recode.voyage.id,
                        key: Math.floor(Math.random() * 100)
                    });
                    // setBookSked(!bookSked)
                    setPreview([...preview])
                    // setKey(2)
                }}>添加</a>
            }
        }
    ];
    useEffect(() => {
        setPrevious([
            { label: `${getLastLastLastWeekRange().startWeek}`, value: `${getLastLastLastWeekRange().startWeek}` },
            { label: `${getLastLastWeekRange().startWeek}`, value: `${getLastLastWeekRange().startWeek}` },
            { label: '上周', value: '上周' },
        ])
        // setPreview([{}, {}])
        // shippingData()
    }, [])
    const handleChange = (e: any) => {
        if (e == '上周') {
            setPreviousData({
                historyStartDate: formatTimes(getLastWeekRange().startDate),
                historyEndDate: formatTimes(getLastWeekRange().endDate)
            })
        } else if (e == getLastLastWeekRange().startWeek) {
            setPreviousData({
                historyStartDate: formatTimes(getLastLastWeekRange().startDate),
                historyEndDate: formatTimes(getLastLastWeekRange().endDate)
            })
        } else if (e == getLastLastLastWeekRange().startWeek) {
            setPreviousData({
                historyStartDate: formatTimes(getLastLastLastWeekRange().startDate),
                historyEndDate: formatTimes(getLastLastLastWeekRange().endDate)
            })
        } else {
            setPreviousData({})
        }
        actionRef.current?.reload();
    }
    const transformData = (data: any) => {
        // const agents = ele.voyage.vessel.agents;
        const newData = data?.map((ele: any) => {
            // if (previousDate?.historyStartDate && previousDate?.historyEndDate) {
            if (searchParams?.historyDepartTime) {
                const agents = ele.voyage.vessel.agents;
                // 用于后期往期复制的时候，处理数据用
                const onlyObj: Record<string, any> = {};
                agents.forEach((item: any) => {
                    item.models.forEach((element: any) => {
                        const key = (item.name + element.model) as string;
                        const currentItem = onlyObj[key] || {};
                        onlyObj[key] = {
                            ...item,
                            model: element.model,
                            // voyageId: ele.voyage.vessel.id,
                            voyageId: ele.voyage.id,
                            planNumber: Number(currentItem.planNumber || 0) + element.planNumber,
                            successNumber:
                                // (Number(currentItem?.historySuccessNumber || 0) + element?.historySuccessNumber) || 0,
                                (Number(currentItem?.successNumber || 0) + element?.successNumber) || 0,

                        };
                    });
                });
                const newData = Object.values(onlyObj).map((ele) => ele);
                ele.voyage.vessel.newData = newData;
            } else {
                const agents = ele.voyage.vessel;
                // 默认就是一行
                agents['newData'] = [{
                    "agentPropertiesId": "",
                    "code": "",
                    "id": undefined,
                    "mode": 2,
                    "nameEn": "COSCO SHIPPING",
                    "model": undefined,
                    "voyageId": ele.voyage.id,
                    "planNumber": 24,
                    "successNumber": 0
                },]
            }
            return {
                ...ele,
                // voyage: {
                //     ...ele?.voyage,
                //     vessel: {
                //         ...ele?.voyage?.vessel,
                //         models: []
                //     }
                // }
            };
        });
        return newData;
    }
    // 将数据处理成后端需要的传
    const releaseData = () => {
        const newData = preview?.map((item: any) => item.voyage.vessel.newData).flat()
        const processedData: any = [];
        newData.forEach((item: any) => {
            const existingItem = processedData.find((obj: any) => obj.agentId === item.id);
            if (existingItem) {
                const existingModel = existingItem.planModelList.find((model: any) => model.model === item.model);
                if (existingModel) {
                    existingModel.planNumber += item.successNumber;
                } else {
                    existingItem.planModelList.push({
                        model: item.model,
                        planNumber: item.successNumber
                    });
                }
            } else {
                processedData.push({
                    agentId: item.id,
                    voyageId: item.voyageId,
                    planModelList: [
                        {
                            model: item.model,
                            planNumber: item.successNumber
                        }
                    ]
                });
            }
        });
        fastBook(processedData)
    }
    // 订舱发布
    const fastBook = async (data: any) => {
        let shouldStop = false; // 标志变量，初始值为false
        const arr = data?.filter((item: any) => item.agentId != undefined)
        if (arr.length <= 0) return message.warning('船司代理不能为空！');
        arr.forEach((v: any) => {
            v?.planModelList?.forEach((fid: any) => {
                if (shouldStop) return
                if (fid.model == undefined) {
                    message.warning('规格不能为空！');
                    shouldStop = true
                    return
                }
                if (fid.planNumber == 0) {
                    message.warning('数量不能为0！');
                    shouldStop = true
                    return
                }
            })

        })
        if (!shouldStop && arr?.length >= 1) {
            try {
                const { status } = await fastBookAPI({
                    cabinPlans: arr,
                });
                if (status) {
                    message.success('订舱成功')
                    setPreviousData({})
                    history.back()
                    closeTab()
                    refreshTab('/Booking/cabin')
                }
            } catch (error) {
                console.log(JSON.stringify(error), 'error');
            }
        }

    }
    useEffect(() => {
        const handleResize = () => setWindowHeight(window.innerHeight);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    //   const FooterBox = () => {
    //     return (
    //       <Space>
    //         <div className="color-#707070  ">
    //           已选
    //           {/* color-#4071FF font-600 */}
    //           <span className=""> {selectedRows.length} </span>项
    //         </div>
    //       </Space>
    //     );
    //   };

    return (
        <div className={styles['warp-book']}>
            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            headerColor: '#8098AB',
                            headerBorderRadius: 4,
                            borderColor: '#E2E4E6',
                        },
                    },
                }}
            >
                <SearchCardNew
                    onSearch={(e) => {
                        setKeyword(e);
                        actionRef.current.reloadAndRest();
                    }}
                    placeholder="请输入关键字搜索"
                    onCardValuesChange={(val, vals) => {
                        console.log(vals, 'valsvals');

                        setSearchParams(vals);
                        actionRef.current.reloadAndRest();
                    }}
                    initialValues={{
                        beginTime: [],
                    }}
                    isBatch={true}
                >
                    <ProFormSelect
                        name="providerId"
                        label="船司"
                        style={{ width: '100%' }}
                        showSearch
                        request={async ({ keyWords }) => {
                            const { status, data } = await shippingListAPI({
                                start: 0,
                                len: 20,
                                keyword,
                                type: 2
                            });
                            if (status) {
                                return data.list.map((item: any) => ({ value: item?.provider?.id, label: item?.name }));
                            }
                            return [];
                        }}
                    />
                    <ProFormDateTimeRangePicker
                        name="beginTime"
                        label="开船时间"
                        dataFormat="YYYY-MM-DD HH:mm:ss"
                        fieldProps={{
                            showTime: {
                                hideDisabledOptions: true,
                                defaultValue: [
                                    dayjs('00:00:00', 'HH:mm:ss'),
                                    dayjs('23:59:59', 'HH:mm:ss'),
                                ],
                            },
                        }}
                        transform={(value) => {
                            // console.log('value: ', value);
                            if (!value?.length) return;
                            if (typeof value[0] === 'string') {
                                return {
                                    beginTime: value[0],
                                    endTime: value[1],
                                };
                            } else {
                                return {
                                    beginTime: dayjs(value[0]?.$d).format('YYYY-MM-DD HH:mm:ss'),
                                    endTime: dayjs(value[1]?.$d).format('YYYY-MM-DD HH:mm:ss'),
                                };
                            }
                        }}
                    />
                    <ProFormSelect
                        name="historyDepartTime"
                        label="复制往期"
                        showSearch
                        // mode="multiple"
                        fieldProps={{
                            maxTagCount: 1,
                        }}
                        options={[
                            { label: `${getLastLastLastWeekRange().startWeek}`, value: `${formatTimes(getLastLastLastWeekRange().startDate)} 00:00:00,${formatTimes(getLastLastLastWeekRange().endDate)} 23:59:59` },
                            { label: `${getLastLastWeekRange().startWeek}`, value: `${formatTimes(getLastLastWeekRange().startDate)} 00:00:00,${formatTimes(getLastLastWeekRange().endDate)} 23:59:59` },
                            { label: '上周', value: `${formatTimes(getLastWeekRange().startDate)} 00:00:00,${formatTimes(getLastWeekRange().endDate)} 23:59:59` },
                        ]}
                    />
                </SearchCardNew>
                <ProTable
                    // rowSelection={{
                    //     onChange: (_, selectedRows) => {
                    //         setSelectedRows(selectedRows);
                    //     },
                    //     columnWidth: 40,
                    // }}
                    bordered
                    footer={() => <Button
                        type="primary"
                        onClick={releaseData}>
                        发布
                    </Button >}
                    //   tableAlertRender={false}
                    columns={columns}
                    actionRef={actionRef}
                    // dataSource={tableData}
                    key={key}
                    request={async (params: any) => {
                        const { current: start, pageSize: len } = params;
                        const msg = await previewAPI({
                            start: (start - 1) * len,
                            len,
                            condition: {
                                keyword: {
                                    value: keyword,
                                },
                                providerId: {
                                    value: searchParams?.providerId,
                                },
                                departTime: {
                                    // value: searchParams?.recordType,
                                    startTime: searchParams?.beginTime,
                                    endTime: searchParams?.endTime
                                },
                                historyDepartTime: {
                                    value: searchParams?.historyDepartTime,
                                }
                            }

                            // keyword,
                            // type,
                            // ...previousDate,
                            // ...time
                        });
                        const newData = transformData(msg.data.list)
                        setPreview(newData.map((item: any, index: number) => { return { ...item, id: index } }))
                        return {
                            data: newData || [],
                            success: msg.status,
                            total: msg.data.total,
                        };
                    }}
                    rowKey="id"
                    search={false}
                    options={{
                        fullScreen: true,
                    }}
                    // virtual
                    pagination={{
                        defaultPageSize: 100,
                        pageSizeOptions: [20, 100, 200, 500, 1000, 2000, 5000],
                    }}
                    scroll={{ x: 1200, y: windowHeight - 455 }}
                />
                <div>
                    {selectedRows.length > 0 ? (
                        <div className={styles['footer-box-select']}>
                            {/* <FooterBox /> */}
                        </div>
                    ) : null}
                </div>
            </ConfigProvider>
        </div>
    );
};

export default Book;
