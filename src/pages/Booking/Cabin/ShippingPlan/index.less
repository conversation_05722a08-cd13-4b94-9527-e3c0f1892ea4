.uploadStyle {
    display: flex;
    justify-content: space-between;
    width: 275px;
    height: 22px;
    background-color: #fff;
    margin-top: 2px;
    padding-right: 4px;
}

.uploadStyle:hover {
    background-color: rgba(0, 0, 0, 0.04);
    transition: all 0.3s;
}

.shippingSelect {
    :global {
        // .ant-select-selector,.ant-input {
        //     background-color: #FBFBFB !important;
        //     border: none !important;
        // }
        // .ant-picker.ant-picker-range {
        //     border: none !important;
        // }
    }
}