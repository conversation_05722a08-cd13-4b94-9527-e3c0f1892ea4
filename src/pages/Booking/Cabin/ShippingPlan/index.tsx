import { Button, Modal, Input, Upload, Tooltip, message, Tag } from 'antd';
import { useMemo, useRef, useState } from 'react';
import Selects from '../../components/Select';
import TablePage from '../../components/TablePage';
import { history } from '@umijs/max';
import { authorizationLetterAPI, planAPI, shippingPlanAPI, soFileUploadAPI } from '@/services/booking';
import { CheckOutlined, CloudUploadOutlined, DeleteOutlined, EditOutlined, LinkOutlined } from '@ant-design/icons';
import { saveAs } from 'file-saver';
import _ from 'lodash'
import styles from './index.less'
import MySearch from '@/components/MyProTable/MySearch';
import AccessCard from '@/AccessCard';
import MrTable from '@/components/MrTable';
import LogDashboard from '@/components/LogDashboard';
const { TextArea } = Input
const ShippingPlan = () => {
    const ref = useRef<any>();
    const actionRef = useRef<any>(null)
    const [keyword, setKeyword] = useState('')
    const [state, setState] = useState('')
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isModalOpenBill, setIsModalOpenBill] = useState(false);
    const [shippingText, setShippingText] = useState<any>()
    const [upData, setUpData] = useState<any>([])
    const [blnoModels, setBlnoModels] = useState<any>([])
    const [loading, setLoading] = useState(false)
    const [loadingBill, setLoadingBill] = useState(false)
    const options = useMemo(() => {
        return [
            // { label: '空闲', value: '1' },
            // { label: '已装柜', value: '2' },
            // { label: '已开船', value: '3' },
            // { label: '已转卖', value: '4' },
            // { label: '已退订', value: '5' },
            { label: '未完成', value: '1' },
            { label: '部分完成', value: '2' },
            { label: '已完成', value: '3' },
            { label: '已撤销', value: '4' },
        ]
    }, [])
    const shippingPlanState: any = {
        1: {
            text: '未完成',
            color: '#f50'
        },
        2: {
            text: '部分完成',
            color: '#2db7f5'
        },
        3: {
            text: '已完成',
            color: '#87d068'
        }
    }
    const columns = useMemo(() => {
        return [

            {
                title: '流水号',
                // align: 'center',
                dataIndex: 'id',
                width: 200,
                hideInSearch: true,
            },
            {
                title: '状态',
                dataIndex: 'state',
                // hideInTable: true,
                hideInSearch: true,
                // align: 'center',
                width: 100,
                render: (_: any, text: any) => {
                    return <Tag color={shippingPlanState[text?.state]?.color}>{shippingPlanState[text?.state]?.text}</Tag>
                }
            },
            {
                title: '船司',
                dataIndex: 'provider',
                hideInSearch: true,
                // align: 'center',
                width: 160,
                render: (_: any, text: any) => {
                    return text?.provider?.properties?.name
                }
            },
            {
                title: '航线名称',
                hideInSearch: true,
                dataIndex: 'vesselName',
                // align: 'center',
                width: 160,
                render: (_: any, recode: any) => {
                    return recode?.voyage?.vessel?.name
                }
            },
            {
                title: '航线代码',
                hideInSearch: true,
                dataIndex: 'voyage',
                // align: 'center',
                width: 160,
                render: (_: any, recode: any) => {
                    return recode?.voyage?.vessel?.vesselProperties?.code
                }
            },
            {
                title: '船名航次',
                hideInSearch: true,
                dataIndex: 'voyageProperties',
                // align: 'center',
                width: 260,
                render: (text: any, recode: any) => {
                    return `${recode?.voyage?.voyageProperties?.shipName}/${recode?.voyage?.voyageProperties?.code}`
                }
            },
            {
                title: '船司代理',
                hideInSearch: true,
                // align: 'center',
                dataIndex: 'Properties',

                width: 400,
                render: (_: any, recode: any) => {
                    return <span>{recode?.agent?.name}</span>
                }
            },
            {
                hideInSearch: true,
                title: '预定舱位',
                // align: 'center',
                dataIndex: 'cwyd',
                width: 160,
                render: (_: any, recode: any) => {
                    const newData = recode?.planModelList
                    return newData?.map((item: any, index: number) => {
                        return <div key={index} style={{ color: '#49B000', marginBottom: 10 }} >{item.model} × {item.planNumber}</div>
                    })
                }

            },
            {
                hideInSearch: true,
                title: '制定人',
                dataIndex: 'planner',
                // align: 'center',

                width: 160,
                render: (_: any, text: any) => {
                    return (
                        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                            <img style={{ width: 30, height: 30, marginRight: 10, borderRadius: '50%' }}
                                src={
                                    text?.planner?.avatar.startsWith('https')
                                        ? text?.planner?.avatar
                                        : `https://static.kmfba.com/${text?.planner?.avatar}` ||
                                        ''
                                }
                            />
                            <span>
                                {text?.planner?.name}
                            </span>
                        </div>
                    )
                }
            },
            {
                hideInSearch: true,
                title: '执行人',
                dataIndex: 'operator',
                // align: 'center',
                width: 160,
                render: (_: any, text: any) => {
                    return (
                        text?.operator ? <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                            <img style={{ width: 30, height: 30, marginRight: 10, borderRadius: '50%' }}
                                src={
                                    text?.operator?.avatar?.startsWith('https')
                                        ? text?.operator?.avatar
                                        : `https://static.kmfba.com/${text?.operator?.avatar}` ||
                                        ''
                                }
                            />
                            <span>
                                {text?.operator?.name}
                            </span>
                        </div> : ''
                    )
                }
            },
            {
                title: '操作',
                valueType: 'option',
                key: 'option',
                width: 200,
                // align: 'center',
                fixed: 'right',
                render: (text: any, recode: any) => {
                    if (recode?.agent?.supportAuthorizationLetterUrl == 1 && (!recode?.downloadTime && !recode?.completionTime)) {
                        return <AccessCard accessible={[':Transport:SpacePurchaseAccess']} dataValidator={(path: any, data: any) => {
                            return /[02]/.test(data.type.value)
                        }}>
                            <a
                                style={{ width: '40%' }}
                                key="editable"
                                onClick={() => {
                                    authorizationLetter(recode.id)
                                }}
                            >
                                下载托书
                            </a>
                            <LogDashboard extraId_1={recode?.id} btnType="link" btnText="日志" key="journal" />
                        </AccessCard>
                    } else if (!recode?.completionTime) {

                        return <AccessCard accessible={[':Transport:SpacePurchaseAccess']}>
                            <a
                                style={{ width: '40%' }}
                                key="s"
                                onClick={() => {
                                    setShippingText({
                                        // 流水号
                                        id: recode?.id,
                                        type: recode?.provider?.properties?.suppliedSo,
                                        // 船司
                                        name: recode?.provider?.properties?.name,
                                        // 柜子信息 
                                        blnoModels: recode.planModelList.map((item: any) => ({ ...item, control: false, fee: item?.price ?? 200, blnoStr: '' }))?.filter((ele: any) => ele?.planNumber !== ele?.successNumber),
                                        // 航线代码
                                        vessel: `${recode?.voyage?.vessel?.vesselProperties?.name}-${recode?.voyage?.vessel?.vesselProperties?.code}`,
                                        uid: Math.floor(Math.random() * 100)
                                    })
                                    setIsModalOpen(true)
                                }}
                            >
                                完成订舱
                            </a>
                            <LogDashboard extraId_1={recode?.id} btnType="link" btnText="日志" key="journal" />
                        </AccessCard>
                    } else if (recode?.completionTime) {
                        return ''
                    }

                }
            },
        ];
    }, [])
    // 下载托书
    const authorizationLetter = async (id: string) => {
        try {
            const response: any = await authorizationLetterAPI({ id });
            const fileName = decodeURI(
                response.headers['content-disposition'].match(/filename=(.*)/)[1],
            );
            const newFileName = Buffer.from(fileName, 'base64').toString('utf8');
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, newFileName);
            message.success('下载托书成功')
            actionRef?.current.reload()

        } catch (err) {
            console.log('下载托书: ', err);
            message.warning('服务器异常')
        }
    }
    // 选文件
    const newCustomRequest: any = async (fileData: any, index: number, item: any) => {
        const existingModel = upData?.find((v: any) => v.model.model === item.model);
        if (existingModel) {
            existingModel.file.push(fileData.file);
        } else {
            upData.push({
                file: [fileData.file],
                model: item
            },)
        }
        setUpData([...upData])
    }
    // 上传提单号
    const billof = async (boolean?: any) => {
        if (blnoModels?.length == 0) return message.warning('请粘贴提单号！')
        if (blnoModels?.length !== shippingText?.blnoModels?.length) return message.warning('还有规格未粘贴提单号！')
        setLoading(true)
        try {
            const { status } = await planAPI({
                id: shippingText?.id,
                blnoModels
            })
            if (status) {
                message.success('上传成功')
                setLoadingBill(false)
                setIsModalOpenBill(false)
                setLoading(false)
                setIsModalOpen(false)
                setBlnoModels([])
                actionRef?.current.reload()
            }
            setLoading(false)
        } catch (error) {
            setLoading(false)
            console.log(JSON.stringify(error), 'error');

        }
    }
    // 并发请求
    function fetchData(newData: any) {
        return new Promise((resolve, reject) => {
            if (newData) {
                const formData = new FormData()
                newData.file.forEach((item: any) => {
                    formData.append('soFiles', item)
                })
                formData.append('id', shippingText.id)
                formData.append('model', newData.model.model)
                formData.append('fee', newData.model.fee)
                // 本地测试
                // fetch('/api/waybill/tms/transport/cabin/plan/soFile/upload', {
                //     method: 'POST',
                //     body: formData,
                //     headers: {
                //         token: '/iiLhLyMmiNvrs43+vdTIczHlDKyNKd2p5xHnX1wXvU=',
                //     },
                // });
                soFileUploadAPI(formData).then((response) => {
                    if (response.status) {
                        return response;
                    } else {
                        throw new Error('Request failed');
                    }
                })
                    .then((data) => {
                        resolve(data);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    }
    function fetchAll(data: any) {
        const promises = data.map((url: any) => fetchData(url));
        return Promise.all(promises);
    }
    const trigger = async () => {
        if (upData?.length == 0) return message.warning('请上传SO文件！')
        if (upData?.length !== shippingText?.blnoModels?.length) return message.warning('还有规格未上传SO文件！')
        setLoading(true)
        fetchAll(upData)
            .then((results) => {
                console.log(results, 'results'); // 包含每个请求的结果数组
                message.success('上传SEO文件成功')
                setLoading(false)
                setIsModalOpen(false)
                setUpData([])
                actionRef?.current.reload()
            })
            .catch((error) => {
                setLoading(false)
                console.error(error, 'error'); // 捕获任何一个请求失败的错误

            });
    }
    // 上传提单号
    const modification = (val: any, data: any, index: number) => {
        const existingData = blnoModels.find((item: any) => item.model === data.model);
        if (existingData) {
            existingData.blnoStr = val.target.value;
        } else {
            blnoModels.push({
                model: data.model,
                fee: data.fee,
                blnoStr: val.target.value
            });
        }
        const newData = blnoModels?.filter((ele: any) => ele?.blnoStr !== '')
        setBlnoModels([...newData])
    }
    const filters = {
        "keyword": {
            "type": "keyword",
            "value": '',
            tabs: true

        },
        "state": {
            "desc": "状态",
            "type": "select",
            "range": options,
            value: ''
        },
    }
    return <div className={styles.shippingSelect}>
        {/* <TablePage columns={columns}
            scroll={{ x: 1400 }}
            actionRef={actionRef}
            optionRender={false}
            formRef={ref}
            request={async (params: { current: number, pageSize: number }) => {
                const { current: start, pageSize: len } = params;
                const res = await shippingPlanAPI({
                    start: (start - 1) * len,
                    len,
                    keyword,
                    state
                })
                return {
                    data: res?.data?.list || [],
                    success: res?.status,
                    total: res?.data?.total,
                };
            }}
            toolBar={
                <AccessCard key={1} accessible={[':Transport:SpacePlanFullAccess']}>
                    <Button
                        key="button"
                        onClick={() => {
                            history.push('/Booking/cabin/book')
                        }}
                        type="primary"
                    >
                        新建计划
                    </Button>
                </AccessCard>
            } /> */}
        <MrTable
            columns={columns}
            keyID={'Booking/cabin/bookingPlan'}
            request={async (params: any, action: any) => {
                actionRef.current = action
                const res = await shippingPlanAPI({
                    ...params
                })

                return {
                    data: res.data.list || [],
                    success: res.status,
                    total: res.data?.total,
                };
            }}
            filters={filters}
            toolBarRender={<AccessCard key={1} accessible={[':Transport:SpacePlanFullAccess']}>
                <Button
                    key="button"
                    onClick={() => {
                        history.push('/Booking/cabin/book')
                    }}
                    type="primary"
                >
                    新建计划
                </Button>
            </AccessCard>} />
        {/* <Modal title={shippingText?.type == 1 ? '上传SEO文件' : '上传提单号'}
            confirmLoading={loading} destroyOnClose style={{ minWidth: 700 }} open={isModalOpen} onOk={shippingText?.type == 1 ? trigger : billof} onCancel={() => {
                setUpData([])
                setLoading(false)
                setIsModalOpen(false)
            }}>
            <div style={{ display: 'flex', marginTop: 30, justifyContent: 'space-between' }}>
                <span>{shippingText?.id}</span>
                <span>{shippingText?.name}</span>
                <span>{shippingText?.vessel}</span>
            </div>
            {shippingText?.type == 1 ? shippingText?.blnoModels.map((item: any, index: number) => {
                return <div key={index} style={{ display: 'flex', justifyContent: 'space-between', margin: '40px 0' }}>
                    <span>{item.model}</span>
                    <div>
                        <Upload
                            name='soFiles'
                            withCredentials
                            showUploadList={false}
                            customRequest={(file: any) => newCustomRequest(file, index, item)}
                        >
                            <span style={{ cursor: 'pointer', color: '#1677ff' }}>上传SO文件&nbsp;<CloudUploadOutlined rev={undefined} /> </span>&nbsp;&nbsp;&nbsp;&nbsp;
                        </Upload>
                        <a>上传提单号</a>
                        {upData.length >= 1 && upData[index]?.file?.map((v: any, i: number) => {
                            return <div key={v.uid} className={styles.uploadStyle}>
                                <div> <LinkOutlined rev={undefined} />{v?.name}</div>
                                <DeleteOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => {
                                    const newData = upData.map((fid: any) => {
                                        if (fid?.file[i]?.uid == v.uid) {
                                            return {
                                                ...fid,
                                                file: fid.file?.filter((ele: any) => ele?.uid !== v?.uid)
                                            }
                                        } else {
                                            return fid
                                        }
                                    })
                                    const arr = newData.filter((ele: any) => ele?.file?.length >= 1)
                                    setUpData(arr)
                                }} />
                            </div>
                        })}
                    </div>
                    <div style={{ display: 'flex' }}>
                        <span style={{ color: '#999' }}>
                            单价（美元）&nbsp;&nbsp;
                            {item.control ? <>
                                <Input value={item.fee} style={{ width: 80, marginRight: 10 }} type="number" onChange={(e) => {
                                    const newData = upData.map((fid: any) => {
                                        if (fid.model.model == item.model) {
                                            return {
                                                ...fid,
                                                model: {
                                                    ...fid.model,
                                                    fee: Number(e.target.value) <= 0 ? 0 :  Number(e.target.value) 
                                                }
                                            }
                                        }
                                        return fid
                                    })
                                    setUpData([...newData])
                                    setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    fee: Number(e.target.value) <= 0 ? 0 :  Number(e.target.value) 
                                                }
                                            }
                                            return item
                                        })
                                    })
                                }} />
                                <Tooltip title="完成">
                                    <CheckOutlined rev={undefined} onClick={() => {
                                        setShippingText({
                                            ...shippingText,
                                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                                if (index == i) {
                                                    return {
                                                        ...item,
                                                        control: false,

                                                    }
                                                }
                                                return item
                                            })
                                        })
                                    }} />
                                </Tooltip>
                            </> : <>
                                <span style={{ width: 80, marginRight: 10, display: 'inline-block' }}>{item.fee}</span>
                                <Tooltip title="编辑">
                                    <EditOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    control: true
                                                }
                                            }
                                            return item
                                        })

                                    })} />
                                </Tooltip>
                            </>}
                        </span></div>
                </div>

            }) : shippingText?.blnoModels.map((item: any, index: number) => {
                return <div key={index} style={{ display: 'flex', justifyContent: 'space-between', margin: '40px 0' }}>
                    <span>{item.model}</span>
                    <TextArea style={{ width: '60%' }} placeholder='请粘贴提单号' autoSize={{ minRows: 4, maxRows: 8 }} value={item.blnoStr} onChange={(e) => {
                        modification(e, item, index)
                        setShippingText({
                            ...shippingText,
                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                if (index == i) {
                                    return {
                                        ...item,
                                        blnoStr: e.target.value
                                    }
                                }
                                return item
                            })
                        })

                    }} />
                    <span style={{ display: 'flex' }}>
                        <span style={{ color: '#999' }}>
                            单价（美元）&nbsp;&nbsp;
                            {item.control ? <>
                                <Input value={item.fee} style={{ width: 80, marginRight: 10 }} onChange={(e) => {
                                    const newData = blnoModels.map((v: any) => {
                                        if (v.model == item.model) {
                                            return {
                                                ...v,
                                                fee: Number(e.target.value)
                                            }

                                        }
                                        return v
                                    })
                                    setBlnoModels([...newData])
                                    setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    fee: + e.target.value
                                                }
                                            }
                                            return item
                                        })
                                    })
                                }} />
                                <Tooltip title="完成">
                                    <CheckOutlined rev={undefined} onClick={() => {
                                        setShippingText({
                                            ...shippingText,
                                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                                if (index == i) {
                                                    return {
                                                        ...item,
                                                        control: false,

                                                    }
                                                }
                                                return item
                                            })
                                        })
                                    }} />
                                </Tooltip>
                            </> : <>
                                <span style={{ width: 80, marginRight: 10, display: 'inline-block' }}>{item.fee}</span>
                                <Tooltip title="编辑">
                                    <EditOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    control: true
                                                }
                                            }
                                            return item
                                        })

                                    })} />
                                </Tooltip>
                            </>}
                        </span></span>
                </div>
            })}
        </Modal> */}

        <Modal title='上传提单号'
            confirmLoading={loadingBill} destroyOnClose style={{ minWidth: 700 }} open={isModalOpenBill} onOk={billof} onCancel={() => {
                setUpData([])
                setLoadingBill(false)
                setIsModalOpenBill(false)
            }}>
            <div style={{ display: 'flex', marginTop: 30, justifyContent: 'space-between' }}>
                <span>{shippingText?.id}</span>
                <span>{shippingText?.name}</span>
                <span>{shippingText?.vessel}</span>
            </div>
            {shippingText?.blnoModels.map((item: any, index: number) => {
                return <div key={index} style={{ display: 'flex', justifyContent: 'space-between', margin: '40px 0' }}>
                    <span>{item.model}</span>
                    <TextArea style={{ width: '60%' }} placeholder='请粘贴提单号' autoSize={{ minRows: 4, maxRows: 8 }} value={item.blnoStr} onChange={(e) => {
                        modification(e, item, index)
                        setShippingText({
                            ...shippingText,
                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                if (index == i) {
                                    return {
                                        ...item,
                                        blnoStr: e.target.value
                                    }
                                }
                                return item
                            })
                        })

                    }} />
                    <span style={{ display: 'flex' }}>
                        <span style={{ color: '#999' }}>
                            单价（美元）&nbsp;&nbsp;
                            {item.control ? <>
                                <Input value={item.fee} style={{ width: 80, marginRight: 10 }} onChange={(e) => {
                                    const newData = blnoModels.map((v: any) => {
                                        if (v.model == item.model) {
                                            return {
                                                ...v,
                                                fee: Number(e.target.value)
                                            }

                                        }
                                        return v
                                    })
                                    setBlnoModels([...newData])
                                    setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    fee: + e.target.value
                                                }
                                            }
                                            return item
                                        })
                                    })
                                }} />
                                <Tooltip title="完成">
                                    <CheckOutlined rev={undefined} onClick={() => {
                                        setShippingText({
                                            ...shippingText,
                                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                                if (index == i) {
                                                    return {
                                                        ...item,
                                                        control: false,

                                                    }
                                                }
                                                return item
                                            })
                                        })
                                    }} />
                                </Tooltip>
                            </> : <>
                                <span style={{ width: 80, marginRight: 10, display: 'inline-block' }}>{item.fee}</span>
                                <Tooltip title="编辑">
                                    <EditOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    control: true
                                                }
                                            }
                                            return item
                                        })

                                    })} />
                                </Tooltip>
                            </>}
                        </span></span>
                </div>
            })}
        </Modal>



        <Modal title={shippingText?.type == 1 ? '上传SEO文件' : '上传提单号'}
            confirmLoading={loading} destroyOnClose style={{ minWidth: 700 }} open={isModalOpen} onOk={shippingText?.type == 1 ? trigger : billof} onCancel={() => {
                setUpData([])
                setLoading(false)
                setIsModalOpen(false)
            }}>
            <div style={{ display: 'flex', marginTop: 30, justifyContent: 'space-between' }}>
                <span>{shippingText?.id}</span>
                <span>{shippingText?.name}</span>
                <span>{shippingText?.vessel}</span>
            </div>
            {shippingText?.type == 1 ? shippingText?.blnoModels.map((item: any, index: number) => {
                return <div key={index} style={{ display: 'flex', justifyContent: 'space-between', margin: '40px 0' }}>
                    <span>{item.model}</span>
                    <div>
                        <Upload
                            name='soFiles'
                            withCredentials
                            showUploadList={false}
                            customRequest={(file: any) => newCustomRequest(file, index, item)}
                        >
                            <span style={{ cursor: 'pointer', color: '#1677ff' }}>上传SO文件&nbsp;<CloudUploadOutlined rev={undefined} /> </span>&nbsp;&nbsp;&nbsp;&nbsp;
                        </Upload>
                        <a onClick={() => setIsModalOpenBill(true)}>上传提单号</a>
                        {upData.length >= 1 && upData[index]?.file?.map((v: any, i: number) => {
                            return <div key={v.uid} className={styles.uploadStyle}>
                                <div> <LinkOutlined rev={undefined} />{v?.name}</div>
                                <DeleteOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => {
                                    const newData = upData.map((fid: any) => {
                                        if (fid?.file[i]?.uid == v.uid) {
                                            return {
                                                ...fid,
                                                file: fid.file?.filter((ele: any) => ele?.uid !== v?.uid)
                                            }
                                        } else {
                                            return fid
                                        }
                                    })
                                    const arr = newData.filter((ele: any) => ele?.file?.length >= 1)
                                    setUpData(arr)
                                }} />
                            </div>
                        })}
                    </div>
                    <div style={{ display: 'flex' }}>
                        <span style={{ color: '#999' }}>
                            单价（美元）&nbsp;&nbsp;
                            {item.control ? <>
                                <Input value={item.fee} style={{ width: 80, marginRight: 10 }} type="number" onChange={(e) => {
                                    const newData = upData.map((fid: any) => {
                                        if (fid.model.model == item.model) {
                                            return {
                                                ...fid,
                                                model: {
                                                    ...fid.model,
                                                    fee: Number(e.target.value) <= 0 ? 0 : Number(e.target.value)
                                                }
                                            }
                                        }
                                        return fid
                                    })
                                    setUpData([...newData])
                                    setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    fee: Number(e.target.value) <= 0 ? 0 : Number(e.target.value)
                                                }
                                            }
                                            return item
                                        })
                                    })
                                }} />
                                <Tooltip title="完成">
                                    <CheckOutlined rev={undefined} onClick={() => {
                                        setShippingText({
                                            ...shippingText,
                                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                                if (index == i) {
                                                    return {
                                                        ...item,
                                                        control: false,

                                                    }
                                                }
                                                return item
                                            })
                                        })
                                    }} />
                                </Tooltip>
                            </> : <>
                                <span style={{ width: 80, marginRight: 10, display: 'inline-block' }}>{item.fee}</span>
                                <Tooltip title="编辑">
                                    <EditOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    control: true
                                                }
                                            }
                                            return item
                                        })

                                    })} />
                                </Tooltip>
                            </>}
                        </span></div>
                </div>

            }) : shippingText?.blnoModels.map((item: any, index: number) => {
                return <div key={index} style={{ display: 'flex', justifyContent: 'space-between', margin: '40px 0' }}>
                    <span>{item.model}</span>
                    <TextArea style={{ width: '60%' }} placeholder='请粘贴提单号' autoSize={{ minRows: 4, maxRows: 8 }} value={item.blnoStr} onChange={(e) => {
                        modification(e, item, index)
                        setShippingText({
                            ...shippingText,
                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                if (index == i) {
                                    return {
                                        ...item,
                                        blnoStr: e.target.value
                                    }
                                }
                                return item
                            })
                        })

                    }} />
                    <span style={{ display: 'flex' }}>
                        <span style={{ color: '#999' }}>
                            单价（美元）&nbsp;&nbsp;
                            {item.control ? <>
                                <Input value={item.fee} style={{ width: 80, marginRight: 10 }} onChange={(e) => {
                                    const newData = blnoModels.map((v: any) => {
                                        if (v.model == item.model) {
                                            return {
                                                ...v,
                                                fee: Number(e.target.value)
                                            }

                                        }
                                        return v
                                    })
                                    setBlnoModels([...newData])
                                    setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    fee: + e.target.value
                                                }
                                            }
                                            return item
                                        })
                                    })
                                }} />
                                <Tooltip title="完成">
                                    <CheckOutlined rev={undefined} onClick={() => {
                                        setShippingText({
                                            ...shippingText,
                                            blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                                if (index == i) {
                                                    return {
                                                        ...item,
                                                        control: false,

                                                    }
                                                }
                                                return item
                                            })
                                        })
                                    }} />
                                </Tooltip>
                            </> : <>
                                <span style={{ width: 80, marginRight: 10, display: 'inline-block' }}>{item.fee}</span>
                                <Tooltip title="编辑">
                                    <EditOutlined rev={undefined} style={{ cursor: 'pointer' }} onClick={() => setShippingText({
                                        ...shippingText,
                                        blnoModels: shippingText.blnoModels.map((item: any, i: number) => {
                                            if (index == i) {
                                                return {
                                                    ...item,
                                                    control: true
                                                }
                                            }
                                            return item
                                        })

                                    })} />
                                </Tooltip>
                            </>}
                        </span></span>
                </div>
            })}
        </Modal>
    </div>
}
export default ShippingPlan
