import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, Col, Form, Input, message, Row, Select } from 'antd';
import React, { useState } from 'react';
import styles from './index.less';
import { AddVesselProperties, shippingListAPI } from '@/services/booking';
const AirLineModel = (props: any) => {
  const [params, setParams] = useState<any>({});
  const { record, btnTitle, refresh } = props;
  console.log('record: ', record);
  // console.log('dataList: ', dataList);
  const [form] = Form.useForm<any>();
  const [modalVisit, setModalVisit] = useState(false);
  /* 提交 */
  const onFinish = async () => {
    // message.success('提交成功');
    if (
      params?.name &&
      params?.mode &&
      params?.code &&
      params?.providerId &&
      params?.period &&
      params?.arrivesCity &&
      params?.mostDays &&
      params?.deliveryCity &&
      params?.leastDays &&
      params?.departsCity
    ) {
      const { status } = await AddVesselProperties(params);
      if (status) {
        message.success('提交成功');
        refresh();
        // props.refreshTable();
        setModalVisit(false);
        return true;
      } else {
        setModalVisit(false);
        // message.error('提交失败');
        return false;
      }
    } else {
      message.error('航线所有信息不能为空!');
      return;
    }
  };
  const showModal = () => {
    setModalVisit(true);
  };
  const setNewParams = (key: any, value: any) => {
    setParams({ ...params, [key]: value });
  };
  return (
    <>
      <Button type={'primary'} onClick={showModal}>
        {btnTitle}
      </Button>
      <ModalForm
        title={btnTitle}
        // labelCol={{ span: 3 }}
        // wrapperCol={{ span: 21 }}
        open={modalVisit}
        form={form}
        labelWrap={true}
        autoComplete="off"
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setModalVisit(false),
        }}
        layout="horizontal"
        onFinish={onFinish}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>名称</span>
              <Input
                placeholder="请输入"
                style={{ display: 'flex' }}
                onChange={(e) => {
                  console.log('e');
                  setNewParams('name', e.target.value);
                }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>类型</span>
              <Select
                style={{ width: 146 }}
                placeholder="请选择"
                options={[
                  { label: '空运', value: 1 },
                  { label: '海运', value: 2 },
                ]}
                onChange={(e) => {
                  setNewParams('providerId', '');
                  form.setFieldValue('providerId', '');
                  setNewParams('mode', e);
                }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>航/船司</span>
              <ProFormSelect
                name={['providerId']}
                rules={[{ required: true, message: '必填项不能为空' }]}
                noStyle
                //showSearch
                request={async ({ keyWords }) => {
                  const res = await shippingListAPI({
                    start: 0,
                    len: 300,
                    keyword: keyWords,
                    type: params?.mode,
                  });
                  const { data, status } = res;
                  if (status) {
                    return data?.list?.map((item: any) => {
                      return { label: item.name, value: item.id };
                    });
                  }
                  return [];
                }}
                params={params?.mode}
                fieldProps={{
                  onChange: (value) => {
                    setNewParams('providerId', value);
                  },
                  filterOption: false,
                }}
              />
            </div>
          </Col>

          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>代码</span>
              <Input
                placeholder="请输入"
                style={{ display: 'flex' }}
                onChange={(e) => {
                  setNewParams('code', e.target.value);
                }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>航程(天)</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  setNewParams('period', e.target.value);
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>

          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>目的港</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  setNewParams('arrivesCity', e.target.value);
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>

          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>最少天数</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  setNewParams('leastDays', e.target.value);
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>

          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>出发港</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  setNewParams('departsCity', e.target.value);
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>最大天数</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  setNewParams('mostDays', e.target.value);
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.clientele}>
              <span className={styles.organization}>提货城市</span>
              <Input
                placeholder="请输入"
                onChange={(e) => {
                  setNewParams('deliveryCity', e.target.value);
                }}
                style={{ width: '100%' }}
              />
            </div>
          </Col>
        </Row>
      </ModalForm>
    </>
  );
};
export default AirLineModel;
