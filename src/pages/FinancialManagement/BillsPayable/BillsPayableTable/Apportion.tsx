import { divideCostAPI, getDivideMethodAPI } from '@/services/financeApi';
import { Button, message, Modal, Select } from 'antd';
import { useEffect, useState } from 'react';

const Apportion = ({ btnType, btnText, selectRow, refresh }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [apportionWay, setApportionWay] = useState([]);
  const [method, setMethod] = useState(undefined);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (isModalOpen) {
      getApportionWay();
    }
  }, [isModalOpen]);
  const handleOpenModal = () => {
    if (!selectRow?.length) return message.error('请选择');
    setIsModalOpen(true);
  };
  // 分摊方式数据来源
  const getApportionWay = async () => {
    try {
      const { status, data } = await getDivideMethodAPI({});
      if (status) {
        setApportionWay(
          data?.list?.map((item: any) => ({
            label: item?.desc,
            value: item?.code,
          })),
        );
      }
    } catch (error) {}
  };
  const handleOk = async () => {
    if (method == undefined) return message.error('请选择分摊方式');
    setLoading(true);
    try {
      const { status } = await divideCostAPI({
        id: selectRow?.map((item: any) => item?.id)?.join(','),
        method,
      });
      if (status) {
        message.success('操作成功');
        setLoading(false);
        setIsModalOpen(false);
        if (refresh) refresh();
      }
    } catch (error) {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type={btnType} onClick={handleOpenModal}>
        {btnText}
      </Button>
      <Modal
        title="分摊成本"
        open={isModalOpen}
        width={550}
        destroyOnClose
        onOk={handleOk}
        onCancel={handleCancel}
        afterClose={() => {
          setMethod(undefined);
        }}
        footer={[
          <div key="btn">
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              onClick={handleOk}
              loading={loading}
              style={{ marginLeft: '10px' }}
            >
              确认
            </Button>
          </div>,
        ]}
      >
        分摊方式：
        <Select
          placeholder="请选择分摊方式"
          options={apportionWay}
          style={{ width: '320px' }}
          allowClear
          onChange={(e) => setMethod(e)}
        />
      </Modal>
    </>
  );
};
export default Apportion;
