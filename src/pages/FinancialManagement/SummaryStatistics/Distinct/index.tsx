import { forecasterState } from '@/shared/Enumeration';
import { formatTime, formatTimes } from '@/utils/format';
import { Tag, Image } from 'antd';
import smallLogoImg from '@/assets/yu.svg';
import { useRef } from 'react';
import SuperTables from '@/components/SuperTables';
import { listWMSBlnoSummaryAPI } from '@/services/carriers';
import { ProgressFilterStateType } from '@/utils/constant';
import { history } from 'umi';

const Distinct = () => {
  const actionRef = useRef<any>();

  const refreshTable = () => {
    actionRef?.current?.refresh();
  };
  const auditState: any = {
    '0': {
      text: '待审核',
      color: 'geekblue',
    },
    '1': {
      text: '审核通过',
      color: 'green',
    },
    '-1': {
      text: '待审核',
      color: 'magenta',
    },
  };
  const columns: any = [
    {
      title: '单号',
      dataIndex: 'no',
      width: 135,
    },
    {
      title: '柜号',
      dataIndex: 'containerNo',
      width: 140,
    },
    {
      title: '客户订单号',
      dataIndex: 'outOrderId',
      width: 135,
    },
    {
      title: '提单号',
      dataIndex: 'blno',
      width: 135,
    },
    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      width: 60,
      fieldFormat: (value: any) => {
        return forecasterState[value.state]?.text;
      },
      render: (_: any, recode: any) => {
        return (
          <Tag color={forecasterState[recode?.state]?.color}>
            {forecasterState[recode?.state]?.text}
          </Tag>
        );
      },
    },
    {
      title: '审核状态',
      dataIndex: 'approvalState',
      hideInSearch: true,
      width: 100,
      fieldFormat: (value: any) => {
        return auditState[value.approvalState]?.text;
      },
      render: (_: any, recode: any) => {
        return (
          <Tag color={auditState[recode?.approvalState]?.color}>
            {auditState[recode?.approvalState]?.text}
          </Tag>
        );
      },
    },
    {
      title: '交货仓库',
      dataIndex: 'r',
      width: 200,
      fieldFormat: (recode: any) => {
        return recode?.warehouse?.name;
      },
    },
    {
      title: '客户名称',
      dataIndex: 'clientName',
      width: 200,
    },
    {
      title: '销售产品',
      dataIndex: 'productName',
      width: 200,
    },
    {
      title: '柜型',
      dataIndex: 'containerMode',
      width: 80,
    },

    {
      title: '总票数',
      dataIndex: 'waybillNum',
      width: 80,
    },
    {
      title: '打托/实收/预报（件数）',
      dataIndex: 'official',
      width: 200,
      fieldFormat: (recode: any) => {
        return `${recode?.palletsPieceNum} / ${recode?.checkinPieceNum} / ${recode?.pieceNum}`;
      },
    },
    {
      title: '应收(美元)',
      dataIndex: 'shouldChargeAmount',
      width: 120,
      sorter: true,
      // render: (_: any, text: any) => {
      //   return toNumber(text?.shouldChargeAmount);
      // },
      style: (value: any, record: any) => {
        if (record?.shouldChargeAmount > 0) {
          return { color: 'green' };
        } else if (record?.shouldChargeAmount < 0) {
          return { color: 'red' };
        } else {
          return { color: '#64748B' };
        }
      },
      aggregation: {
        aggregationType: 'SUM',
        formatFun: (value: any) => {
          return Number(value).toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 5,
          });
        },
      },
    },
    {
      title: '应付(美元)',
      dataIndex: 'shouldPayAmount',
      width: 120,
      sorter: true,
      style: (value: any, record: any) => {
        if (record?.shouldPayAmount > 0) {
          return { color: 'green' };
        } else if (record?.shouldPayAmount < 0) {
          return { color: 'red' };
        } else {
          return { color: '#64748B' };
        }
      },
      accessible: ['TMS:Finance:StatisticsAccess'],
      dataValidator: (path: any, data: any) => {
        return /all|payments/.test(data.waybill.value);
      },
      aggregation: {
        aggregationType: 'SUM',
        formatFun: (value: any) => {
          return Number(value).toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 5,
          });
        },
      },
    },
    {
      title: '实收(美元)',
      dataIndex: 'actualChargeAmount',
      width: 150,
      sorter: true,
      // render: (_: any, text: any) => {
      //   return toNumber(text?.actualChargeAmount);
      // },
      style: (value: any, record: any) => {
        if (record?.actualChargeAmount > 0) {
          return { color: 'green' };
        } else if (record?.actualChargeAmount < 0) {
          return { color: 'red' };
        }
      },
      aggregation: {
        aggregationType: 'SUM',
        formatFun: (value: any) => {
          return Number(value).toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 5,
          });
        },
      },
    },
    {
      title: '实付(美元)',
      dataIndex: 'actualPayAmount',
      width: 120,
      sorter: true,
      // render: (_: any, text: any) => {
      //   return toNumber(text?.actualPayAmount);
      // },
      style: (value: any, record: any) => {
        if (record?.actualPayAmount > 0) {
          return { color: 'green' };
        } else if (record?.actualPayAmount < 0) {
          return { color: 'red' };
        }
      },
      accessible: ['TMS:Finance:StatisticsAccess'],
      dataValidator: (path: any, data: any) => {
        return /all|payments/.test(data.waybill.value);
      },
      aggregation: {
        aggregationType: 'SUM',
        formatFun: (value: any) => {
          return Number(value).toLocaleString(undefined, {
            minimumFractionDigits: 0,
            maximumFractionDigits: 5,
          });
        },
      },
    },
    {
      title: '总重量',
      dataIndex: 'weight',
      width: 80,
    },
    {
      title: '托数',
      dataIndex: 'palletsNum',
      width: 80,
      fieldFormat: (record: any) => {
        return `${
          (record?.palletsNum ? record?.palletsNum : 0) -
          (record?.unStoredPalletsNum ? record?.unStoredPalletsNum : 0)
        }/${record?.palletsNum}`;
      },
    },
    {
      title: '到港日期',
      dataIndex: 'estimateArriveTime',
      width: 200,
      sorter: true,
      renderType: 'text',
      fieldFormat: (record: any) => {
        return `${formatTimes(record?.estimateArriveTime)}`;
      },
      render: (text: any, record: any) => {
        return (
          <>
            {record?.state < 1 && (
              <Image
                width={16}
                height={16}
                src={smallLogoImg}
                preview={false}
              />
            )}
          </>
        );
      },
    },
    {
      title: '提柜日期',
      dataIndex: 'estimatePickupTime',
      width: 200,
      renderType: 'text',
      sorter: true,
      fieldFormat: (record: any) => {
        return `${formatTimes(record?.estimatePickupTime)}`;
      },
      render: (text: any, record: any) => {
        return (
          <>
            {record?.state < 3 && (
              <Image
                width={16}
                height={16}
                src={smallLogoImg}
                preview={false}
              />
            )}
          </>
        );
      },
    },
    {
      title: '提柜代理',
      dataIndex: 'pickupProviderName',
      width: 200,
      fieldFormat: (record: any) => {
        return record?.pickupProviderName;
      },
    },
    {
      title: '清关行',
      dataIndex: 'clearanceProviderName',
      width: 200,
      fieldFormat: (record: any) => {
        return record?.clearanceProviderName;
      },
    },
    {
      title: '清关时间',
      dataIndex: 'estimatePickupTime2',
      width: 200,
      fieldFormat: (record: any) => {
        return formatTime(record?.estimatePickupTime2);
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      render: (_: any, recode: any) => {
        return (
          <a
            key={'details'}
            onClick={() => {
              history.push('/BringGroup/FinanceDetail', {
                financeId: recode?.id,
              });
            }}
          >
            详情
          </a>
        );
      },
    },
  ];
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    clientId: {
      desc: '客户',
      type: 'client',
      value: '',
    },
    estimateArriveTime: {
      desc: '到港日期',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    estimatePickupTime: {
      desc: '提柜日期',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    form: {
      desc: '交货方式',
      type: 'select',
      multi: true,
      range: [
        { label: '整柜快递', value: 0 },
        { label: '整柜卡派', value: 1 },
        { label: '拼柜散货', value: 2 },
      ],
    },
    warehouseId: {
      desc: '交货仓库',
      type: 'overseas',
      value: '',
    },
    containerNo: {
      desc: '柜号',
      type: 'text',
      value: '',
    },
    state: {
      desc: '状态',
      type: 'select',
      multi: true,
      range: ProgressFilterStateType,
      value: '',
    },
  };
  return (
    <>
      <SuperTables
        columns={columns}
        // field={'holdFlag'}
        isWarpTabs={true}
        request={async (params: any) => {
          const msg = await listWMSBlnoSummaryAPI({
            start: (params.current - 1) * params.pageSize,
            len: params.pageSize,
            columnsFilter: params.columnsFilter || {},
            condition: { ...params.condition },
          });
          return {
            data: msg?.data?.list || [],
            success: msg.status,
            total: msg?.data?.total,
          };
        }}
        // rowSelection={(selectedRows: any) => {
        //   setSelectedRows(selectedRows);
        // }}
        bottomFrozenRowCount={1}
        filters={filters}
        ref={actionRef}
        instanceId={'finance_QingtiList'}
      />
    </>
  );
};
export default Distinct;
