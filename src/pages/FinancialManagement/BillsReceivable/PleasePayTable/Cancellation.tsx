import { invalidRequirePaymentAPI } from '@/services/productAndPrice/api';
import { Button, message, Modal } from 'antd';
import { useState } from 'react';

const Cancellation = ({ btnText, btnType, refresh, selectedRow }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    if (!selectedRow?.length) return message.error('请选择请款单');
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    setIsModalOpen(false);
    try {
      const { status } = await invalidRequirePaymentAPI({
        requireId: selectedRow?.map((item: any) => item?.id)?.join(','),
      });
      if (status) {
        message.success('操作成功');
        refresh();
      }
    } catch (error) {}
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={btnText}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
      >
        <span
          style={{
            fontSize: '16px',
            fontWeight: 600,
            display: 'flex',
            // justifyContent: 'center',
          }}
        >
          {selectedRow?.length === 1
            ? '作废该请款单'
            : `已选中 ${selectedRow?.length} 个请款单，请确认作废`}
        </span>
      </Modal>
    </>
  );
};
export default Cancellation;
