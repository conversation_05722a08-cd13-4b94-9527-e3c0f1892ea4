import { history } from 'umi';
import { message, Radio, Space, Tag } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { cancelAPI, getDistanceList } from '@/services/overview';
import { forecasterState } from '@/shared/Enumeration';
import { formatTime, formatTimes } from '@/utils/format';
import LogDashboard from '@/components/LogDashboard';
import SuperTableMax from '@/components/SuperTableMax';

const DistanceManagement = () => {
  const actionRef = useRef<any>();
  // const [activeKey, setActiveKey] = useState<any>('0,1,-1');
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [State, setState] = useState('');
  const refreshTable = () => {
    actionRef?.current?.refresh();
  };

  const auditState: any = {
    '0': {
      text: '待审核',
      color: 'geekblue',
    },
    '1': {
      text: '审核通过',
      color: 'green',
    },
    '-1': {
      text: '待审核',
      color: 'magenta',
    },
  };

  const handleMenuClick = async (id: string) => {
    try {
      const { status } = await cancelAPI({
        id,
      });
      if (status) {
        message.success('操作成功');
        refreshTable();
      }
    } catch (error) {}
  };

  const columns = useMemo(() => {
    return [
      {
        title: '海外仓',
        dataIndex: 'no',
        width: 135,
      },
      {
        title: '目的地',
        dataIndex: 'outOrderId',
        width: 135,
      },
      {
        title: '距离',
        dataIndex: 'blno',
        width: 135,
      },
      {
        title: '状态',
        dataIndex: 'state',
        width: 120,
        fieldFormat: (value: any) => {
          return forecasterState[value?.state]?.text;
        },
        render: (_: any, recode: any) => {
          return (
            <Tag color={forecasterState[recode?.state]?.color}>
              {forecasterState[recode?.state]?.text}
            </Tag>
          );
        },
      },
      {
        title: '创建人',
        dataIndex: 'approvalState',
        width: 120,
        fieldFormat: (value: any) => {
          return auditState[value?.approvalState]?.text;
        },
        render: (_: any, recode: any) => {
          return (
            <Tag color={auditState[recode?.approvalState]?.color}>
              {auditState[recode?.approvalState]?.text}
            </Tag>
          );
        },
      },
      {
        title: '创建时间',
        dataIndex: 'checkinTime',
        width: 200,
        fieldFormat: (record: any) => {
          return formatTime(record?.checkinTime);
        },
      },
      {
        title: '生效时间',
        dataIndex: 'estimateArriveTime',
        width: 200,
        sorter: true,
        renderType: 'text',
        fieldFormat: (record: any) => {
          return `${formatTimes(record?.estimateArriveTime)}`;
        },
      },
      {
        title: '失效时间',
        dataIndex: 'estimateArriveTime',
        width: 200,
        sorter: true,
        renderType: 'text',
        fieldFormat: (record: any) => {
          return `${formatTimes(record?.estimateArriveTime)}`;
        },
      },
      {
        title: '操作',
        width: 180,
        fixed: 'right',
        render: (text: any, record: any) => {
          return (
            <Space>
              <a
                key={'examine'}
                onClick={() => {
                  history.push('/BringGroup/detail', {
                    qingtiId: record?.id,
                  });
                }}
              >
                查看
              </a>
              <LogDashboard
                extraId_1={record?.id}
                btnType="link"
                btnText="日志"
              />
            </Space>
          );
        },
      },
    ];
  }, []);

  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    state: {
      desc: '状态',
      type: 'select',
      range: [
        { label: '生效', value: 1 },
        { label: '不生效', value: 0 },
      ],
    },
    time: {
      desc: '创建时间',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
  };

  return (
    <SuperTableMax
      columns={columns}
      request={async (params: any) => {
        const msg = await getDistanceList({
          start: (params.current - 1) * params.pageSize,
          len: params.pageSize,
          columnsFilter: params.columnsFilter || {},
          condition: { ...params.condition, approvalState: { value: State } },
        });
        return {
          data: msg?.data?.list || [],
          success: msg.status,
          total: msg?.data?.total,
        };
      }}
      rowSelection={(selectedRows: any) => {
        setSelectedRows(selectedRows);
      }}
      filters={filters}
      ref={actionRef}
      toolBarRender={() => {
        return <div></div>;
      }}
      toolBarRenderRight={() => (
        <div>
          距离单位：
          <Radio.Group
            buttonStyle="solid"
            optionType="button"
            defaultValue={1}
            options={[
              { value: 1, label: '英里' },
              { value: 2, label: '公里' },
            ]}
          />
          {/* <Button style={{ marginRight: '10px' }}>批量导出拆柜清单</Button>*/}
          {/* <Button style={{ marginLeft: '10px' }}>批量备注</Button> */}
        </div>
      )}
      // toolbar={{
      //   menu: {
      //     type: 'tab',
      //     activeKey: activeKey,
      //     items: [],
      //     onChange: (key: any) => {
      //       console.log('key', key);
      //       setActiveKey(key as string);
      //       refreshTable();
      //     },
      //   },
      // }}
      instanceId={'QingtiListV4'}
    />
  );
};
export default DistanceManagement;
