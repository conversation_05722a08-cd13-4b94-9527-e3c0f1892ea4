.warp-from{
  z-index: 100;
  :global{
    .ant-input-affix-wrapper{
      border-color: #fafafa;
      // background: #fafafa;
    }

    .ant-input-affix-wrapper:hover{
      border-color: #fafafa;
      // background: #fafafa;
    }

    .ant-input{
      // background: #fafafa;
      border: 1px solid #fafafa;
    }

    .ant-select-selector{
      // background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-picker-range{
      // background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-input-number-input{
      // background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-input-number{
      border: none;
    }

    .ant-picker{
      // background: #fafafa !important;
      border: none;
    }

    .ant-input-number-group-addon{
      // background: #fafafa !important;
      border: none;
    }

    .ant-form-item-label{
      border:1px solid #d9d9d9 ;
      padding-left: 10px;
      border-radius: 4px 0 0 4px;
      border-right: none;
      color:#707070 !important;
      background: #fff;
    }
    
    .ant-form-item-no-colon{
      color: #707070 !important; 
    }

    .ant-form-item-required{
      color:#707070 !important;
    }

    .ant-form-item-control{
      border:1px solid #d9d9d9;
      border-radius: 0 4px 4px 0;
    }
    
    .ant-select-selector{
      border:none;
    }

    .ant-picker-outlined{
      border:none;
    }

    .ant-input-outlined{
      border:none;
    }


    // .ant-form-item-control div:nth-child(2){
    //   position: absolute;
    // }

  }

  .isfba{
    width: 100%;
    margin-top: 10px;
    :global{
      .ant-form-item-control{
        border:1px solid #F7F8F9 !important;
        border-radius: 0 4px 4px 0;
      }

      .ant-segmented{
        border:1px solid #d9d9d9;
        border-radius:4px;
      }
    }
  }
}

// .warp-from::before {
//   content: '';
//   position: absolute;
//   top: 105px;
//   left: 0;
//   width: 100%;
//   height: 326px;
//   background-color: #F7F8F9;
// }

// ::after{
//   content: '';
//   display: block;
//   clear: both;
//   width: 100%;
//   height:200px;
//   background: #F7F8F9;
//   position: absolute;
//   bottom: 0;
//   left: 0;
// }