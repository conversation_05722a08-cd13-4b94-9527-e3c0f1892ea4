.warp-from {
  z-index: 100;
}
.warp-from :global .ant-input-affix-wrapper {
  border-color: #fafafa;
}
.warp-from :global .ant-input-affix-wrapper:hover {
  border-color: #fafafa;
}
.warp-from :global .ant-input {
  border: 1px solid #fafafa;
}
.warp-from :global .ant-select-selector {
  border: 1px solid #fafafa !important;
}
.warp-from :global .ant-picker-range {
  border: 1px solid #fafafa !important;
}
.warp-from :global .ant-input-number-input {
  border: 1px solid #fafafa !important;
}
.warp-from :global .ant-input-number {
  border: none;
}
.warp-from :global .ant-picker {
  border: none;
}
.warp-from :global .ant-input-number-group-addon {
  border: none;
}
.warp-from :global .ant-form-item-label {
  border: 1px solid #d9d9d9;
  padding-left: 10px;
  border-radius: 4px 0 0 4px;
  border-right: none;
  color: #707070 !important;
  background: #fff;
}
.warp-from :global .ant-form-item-no-colon {
  color: #707070 !important;
}
.warp-from :global .ant-form-item-required {
  color: #707070 !important;
}
.warp-from :global .ant-form-item-control {
  border: 1px solid #d9d9d9;
  border-radius: 0 4px 4px 0;
}
.warp-from :global .ant-select-selector {
  border: none;
}
.warp-from :global .ant-picker-outlined {
  border: none;
}
.warp-from :global .ant-input-outlined {
  border: none;
}
.warp-from .isfba {
  width: 100%;
  margin-top: 10px;
}
.warp-from .isfba :global .ant-form-item-control {
  border: 1px solid #F7F8F9 !important;
  border-radius: 0 4px 4px 0;
}
.warp-from .isfba :global .ant-segmented {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}
