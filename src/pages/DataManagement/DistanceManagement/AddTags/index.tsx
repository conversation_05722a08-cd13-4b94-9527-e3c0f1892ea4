import React, { useState } from 'react';
import { Button, Col, Form, Input, Modal, Row, Select } from 'antd';
import styles from './index.less';
import { useRequest } from 'ahooks';
import { modifyServiceTagAPI } from '@/services/financeApi';
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
}
const AddTags = ({ btnText, btnType,refresh,record }: Props) => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { runAsync: modifyFn } = useRequest(modifyServiceTagAPI, {
    manual: true,
  });
  const showModal = () => {
    setIsModalOpen(true);
    if(record?.id){
      form.setFieldsValue(record)
    }
  };
  const handleOk = () => {
    // setIsModalOpen(false);
    form.validateFields().then((values) => {
      modifyFn({
        ...values,
        id: record?.id
      }).then(() => {
        setIsModalOpen(false);
        form.resetFields();
        refresh();
      })
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields();
  };
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="添加服务标签"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className={styles['warp-from']}>
          <Form className="mt-20px" form={form}>
            <Row>
              <Col span={24}>
                <Form.Item
                  label="标&nbsp;签&nbsp;名&nbsp;"
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: '必填项不能为空',
                    },
                  ]}
                >
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="分&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;组"
                  name="groupName"
                  rules={[
                    {
                      required: true,
                      message: '必填项不能为空',
                    },
                  ]}
                >
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="是否常用"
                  name="commonFlag"
                  rules={[
                    {
                      required: true,
                      message: '必填项不能为空',
                    },
                  ]}
                >
                  <Select
                    allowClear
                    options={[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </>
  );
};
export default AddTags;
