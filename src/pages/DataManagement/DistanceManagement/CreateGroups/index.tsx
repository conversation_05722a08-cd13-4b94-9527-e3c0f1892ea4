import React, { useState } from 'react';
import { Button, Col, Form, Input, Modal, Row } from 'antd';
import styles from '../AddTags/index.less';

const CreateGroups = () => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type="primary" onClick={showModal} ghost>
        创建新分组
      </Button>
      <Modal title="创建分组" open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
      <div className={styles['warp-from']}>
          <Form className="mt-20px" form={form} >
            <Row>
              <Col span={24}>
                <Form.Item
                  label="分组名"
                  name="tagName"
                  rules={[
                    {
                      required: true,
                      message: '必填项不能为空',
                    },
                  ]}
                >
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
            
            </Row>
          </Form>
        </div>
      </Modal>
    </>
  );
};
export default CreateGroups;