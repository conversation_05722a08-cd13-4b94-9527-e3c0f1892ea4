import { message, Image } from 'antd';
import * as clipboard from 'clipboard-polyfill';
import styles from './index.less'
import { memo, useEffect, useRef, useState } from 'react';
import { CopyOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import ReactDom from 'react-dom'
import _ from "lodash";
import { coordinatePag, onRowMouses, paintShape, setColumnsDatasource, setSite, toggleCopy } from '@/store/actions/waybill';
import selectImg from '@/assets/images/选择.png'
import reverseImg from '@/assets/icon/InvertSelection.png'
import arrayImg from '@/assets/images/整列复制@2x.png'
import copyImg from '@/assets/icon/copy.png'
const CopyComponent = memo((props: any) => {
    const { columnsList, getDragCheckData, dataSource, rowSelectionCablck, modalCopy, keyID } = props
    const movingRef = useRef<any>(false)
    const originRef = useRef<any>(null)
    const curRef = useRef<any>(null)
    const spRef = useRef<any>(null)
    const isSelected = useRef<any>(false);
    const x0 = useRef<any>(-1)
    const x1 = useRef<any>(-1)
    const y0 = useRef<any>(-1)
    const y1 = useRef<any>(-1)
    const dataSet = useRef<any>(null);
    const columnsSet = useRef<any>(null);
    const columnsData = useRef<any>(null);
    const dispatch = useDispatch();
    const Page = useSelector((state: any) => state?.tableFilter?.coordinate);
    const columnsDatasource = useSelector((state: any) => state?.tableFilter?.getColumnsDatasource);
    // const indexs = useRef<any>(1)
    const site = useSelector((state: any) => state?.tableFilter?.site);
    const keydownData = useRef<any>({})
    // const [modal,setModal] = useState()
    columnsData.current = columnsDatasource
    dataSet.current = dataSource;
    // columnsSet.current = columnsList;
    useEffect(() => {
        dispatch(onRowMouses(onRowMouse))
        dispatch(paintShape(paint))
        dispatch(toggleCopy(toggleCopyPanel))
    }, [])
    useEffect(() => {
        dispatch(setColumnsDatasource({
            [keyID]: {
                columns: columnsList,
                source: dataSource
            }
        }))
    }, [dataSource?.length])
    const scrolling = () => {
        // if (isSelected.current)
        paint();
    }
    const onRowMouse = (e: MouseEvent, record: any, key: number, data: any) => {
        columnsSet.current = data?.columns
        dataSet.current = data?.source
        if (e.button != 0 || e.buttons != 1)
            return;
        if (movingRef.current) return;
        movingRef.current = true;
        clear(true);
        let a: any = e.target;
        while (a && a.parentNode && (!Array.from(a.classList).includes('rc-virtual-list-holder'))) {
            a = a.parentNode;
        }
        let cell: any = e.target;
        while (cell && cell.parentNode && (!Array.from(cell.classList).includes('ant-table-cell'))) {
            cell = cell.parentNode;
        }
        try {
            if (!Array.from(a.classList).includes('rc-virtual-list-holder')) return
        } catch (error) {
            return
        }
        spRef.current = a;
        spRef.current.addEventListener("scroll", scrolling);
        document.addEventListener('mousemove', Move)
        document.addEventListener('mouseup', Up)
        e.stopPropagation();
        document.addEventListener("mousedown", Down);
        if (originRef.current == null) {
            try {
                x0.current = x1.current = y0.current = y1.current = -1;
                y0.current = rowIndex(e.currentTarget);
                x0.current = childIndex(cell).x;
                // keydownData.current = {
                //     ...keydownData.current,
                //     y0: rowIndex(e.currentTarget),
                //     x0: childIndex(cell).x
                // }
                dispatch(setSite({
                    y0: rowIndex(e.currentTarget),
                    x0: childIndex(cell).x
                }))
                originRef.current = {
                    x: e?.clientX + spRef?.current?.getBoundingClientRect()?.left - spRef.current.querySelector('.rc-virtual-list-holder-inner').getBoundingClientRect().left,
                    y: e?.clientY + spRef.current?.scrollTop - spRef?.current?.getBoundingClientRect()?.top
                }
                curRef.current = originRef.current;
                // curRef.current.x = originRef.current.x;
                // curRef.current.y = originRef.current.y; 
                paint();
            } catch (error) {
                console.log(error);

            }
        }
    }
    let getTransformedElementPosition = function (element: any, t: Number, l: Number) {
        var rect = element?.getBoundingClientRect();
        return {
            x: rect.left + l,
            y: rect.top + t,
            width: rect.width,
            height: rect.height
        };
    }
    const Move = (e: any, record?: any) => {
        let rect = spRef.current.getBoundingClientRect();
        curRef.current = {
            x: e?.clientX + spRef?.current?.getBoundingClientRect()?.left - spRef.current.querySelector('.rc-virtual-list-holder-inner').getBoundingClientRect().left,
            y: e.clientY + spRef.current.scrollTop - spRef.current?.getBoundingClientRect().top
        }
        if (e.clientY > (rect.top + rect.height)) {
            let step = 50;
            if (!spRef.current.timer)
                spRef.current.timer = setInterval(() => {
                    spRef.current.scrollTop = spRef.current.scrollTop + step;
                    curRef.current.y += step;
                    paint();
                }, 10);
        } else {
            clearInterval(spRef.current.timer);
            spRef.current.timer = null;
        }
        if (e.clientY < (rect.top)) {
            let step = (rect.top - e.clientY) * 2;
            if (!spRef.current.topTimer)
                spRef.current.topTimer = setInterval(() => {
                    spRef.current.scrollTop = spRef.current.scrollTop - 10//step;
                    curRef.current.y -= 10;
                    paint();
                }, 10);
        } else {
            clearInterval(spRef.current.topTimer);
            spRef.current.topTimer = null;
        }
        paint()
    }
    const Up = (e: any) => {
        document.removeEventListener('mousemove', Move);
        document.removeEventListener('mouseup', Up);
        clearInterval(spRef.current.timer);
        clearInterval(spRef.current.topTimer);
        if (spRef.current.querySelectorAll('[selectState]').length == 0) {
            clear(true);
        }
        if (spRef.current) {
        }
    }
    const Down = (event: MouseEvent) => {
        if (event.button != 0 || event.buttons != 1)
            return;
        clear(true);
        document.removeEventListener("mousedown", Down);
    }
    const childIndex = (cell: any): any => {
        try {
            return {
                x: Array.from(cell.parentNode.childNodes).indexOf(cell),
                y: rowIndex(cell.parentNode)
            };
        } catch (error) {

        }
    }
    const rowIndex = (node: any) => {
        return Number(node.className.replaceAll(/^.*luofeng\-(\d+).*$/g, '$1'));
    }
    const paint = () => {
        if (!originRef.current)
            return;
        clear();
        let cells = spRef.current.querySelectorAll('.ant-table-cell');
        //        let cx = spRef?.current?.getBoundingClientRect()?.left-spRef.current.querySelector('.rc-virtual-list-holder-inner').getBoundingClientRect().left
        var scope = {
            left: Math.min(originRef.current.x, curRef.current.x),
            top: Math.min(originRef.current.y, curRef.current.y),
            bottom: Math.max(originRef.current.y, curRef.current.y),
            right: Math.max(originRef.current.x, curRef.current.x)
        }
        var L = - spRef.current.querySelector('.rc-virtual-list-holder-inner').getBoundingClientRect().left;
        var spT = spRef.current.scrollTop - spRef.current.getBoundingClientRect().top;
        var spL = L + spRef.current.getBoundingClientRect().left;//-spRef.current.getBoundingClientRect().left-;//L;//spRef.current.scrollLeft - spRef.current.getBoundingClientRect().left;
        var hasSelection = spRef.current.querySelector(".ant-table-selection-column") != null;
        var tempx = -1, tempy = -1;
        for (var i = 0; i < cells.length; i++) {
            let cell = cells[i];
            let e = getTransformedElementPosition(cell, spT, spL);
            let attr = "";
            // let y = parseInt((e?.y / e?.height) + "");
            let coor = childIndex(cell);
            try {
                if (hasSelection && coor.x == 0)
                    continue;
                if (columnsList[coor.x - 1].selectable == 'none') {
                    // console.log("columnsList[coor.x]=", columnsList[coor.x], "x=", coor.x);
                    continue;
                }
            } catch (e) { }

            // dataMap.current[coor.x + '-' + coor.y] = cell.innerText;
            if ((e.x + e.width) > scope.left && e.x < scope.right && (e.y < scope.bottom) && (e.y + e.height) > scope.top) {
                // console.log(e, "e=");
                if (tempy == -1)
                    tempy = coor.y;
                if (coor.y < y0.current) {
                    tempy = Math.min(coor.y, tempy);
                } else {
                    tempy = Math.max(coor.y, tempy);
                }

                if (tempx == -1)
                    tempx = coor.x;
                if (coor.x < x0.current) {
                    tempx = Math.min(coor.x, tempx);
                } else {
                    tempx = Math.max(coor.x, tempx);
                }
                // if (y1.current == -1)
                //     y1.current = coor.y;
                // if (coor.y < y0.current) {
                //     y1.current = Math.min(coor.y, y1.current);
                // } else {
                //     y1.current = Math.max(coor.y, y1.current);
                // }

                // if (x1.current == -1)
                //     x1.current = coor.x;
                // if (coor.x < x0.current) {
                //     x1.current = Math.min(coor.x, x1.current);
                // } else {
                //     x1.current = Math.max(coor.x, x1.current);
                // }

                // x0.current = x0.current == -1 ? x : Math.min(x, x0.current);
                // x1.current = x1.current == -1 ? x : Math.max(x, x1.current);

                // x0.current = x0.current == -1 ? x : Math.min(x, x0.current);
                // x1.current = x1.current == -1 ? x : Math.max(x, x1.current);

                attr = 'sel';
                if (e.x < scope.left) {
                    attr += ' Left';
                }
                if (e.x + e.width >= scope.right) {
                    attr += ' Right';
                }
                if (e.y + e.height >= scope.bottom) {
                    attr += ' Bottom';
                }
                if (e.y < scope.top) {
                    attr += ' Top';
                }
                cell.setAttribute('selectState', attr);
            }
        }
        dispatch(setSite({
            x1: tempx,
            y1: tempy
        }))
        // keydownData.current = {
        //     ...keydownData.current,
        //     x1: tempx,
        //     y1: tempy
        // }
        x1.current = tempx;
        y1.current = tempy;
        if (x1.current != x0.current && y1.current != y0.current) {
            isSelected.current = true;
        } else
            isSelected.current = false;

        if (x1.current != -1 && y1.current != -1) {
            document.addEventListener('keydown', onKeydown);
        }
    }
    const clear = (force: boolean = false) => {
        toggleCopyPanel(null, false);
        if (force) {
            if (movingRef.current)
                movingRef.current = false
            if (originRef.current)
                originRef.current = null
            if (spRef.current)
                spRef.current.removeEventListener("scroll", scrolling);
            x0.current = -1;
        }
        document.removeEventListener("keydown", onKeydown);
        try {
            let lst = spRef.current.querySelectorAll('[selectState]');
            if (!lst || lst.length == 0)
                return;

            lst.forEach((ele: Element) => {
                ele.removeAttribute('selectState');
            });

        } catch (err) { }
    }
    function docClick(event: MouseEvent) {
        // toggleCopyPanel(null, false);
    }
    function toggleCopyPanel(event: any, visible: boolean) {
        // console.log(x0.current, 'toggleCopyPanel');
        // console.log(x1.current, 'toggleCopyPanel');
        // console.log(y0.current, 'toggleCopyPanel');
        // console.log(y1.current, 'toggleCopyPanel');
        if (visible) {
            // console.log(x0.current, 'x0.visible');
            // console.log(x1.current, 'x1.visible');
            // console.log(y0.current, 'y0.visible');
            // console.log(y1.current, 'y1.visible');
            // document.addEventListener('mousedown', docClick);
            dispatch(coordinatePag({
                x: event?.clientX - (modalCopy?.x ?? 223),
                y: event?.clientY - (modalCopy?.x ?? 46),
                show: 'inline'
            }))
        } else {
            // console.log(x0.current, 'x0.none');
            // console.log(x1.current, 'x1.none');
            // console.log(y0.current, 'y0.none');
            // console.log(y1.current, 'y1.none');
            dispatch(coordinatePag({
                ...Page,
                show: "none"
            }))

            // document.removeEventListener('mousedown', docClick);
        }

    }
    const Copy = (e: any) => {
        // console.log(x0.current, 'x0.Copy');
        // console.log(x1.current, 'x1.Copy');
        // console.log(y0.current, 'y0.Copy');
        // console.log(y1.current, 'y1.Copy');
        toggleCopyPanel(null, false);

        oncopy()
    }
    const CopyRow = () => {
        toggleCopyPanel(null, false);
        oncopyByRow()
    }
    const CopyArray = () => {
        toggleCopyPanel(null, false);
        oncopy(false, true);
        // oncopyByColumn();
    }
    // 复制整行
    async function oncopyByColumn() {
        let temp = document.querySelectorAll('[selectState~=sel]');
        var x = 0, y = 0;
        let p: any = null;

        temp.forEach((ele: any) => {
            if (p == null) {
                p = ele.parentNode;
                x = ele.cellIndex;
            }
            if (p != ele.parentNode)
                return;
            y = ele.cellIndex;
        })
        let parent = temp[0].parentNode?.parentNode;
        let trs = parent?.querySelectorAll('tr:not(.ant-table-measure-row)');

        let lst: any = [];

        trs?.forEach(elem => {
            let cs = elem.childNodes;
            for (var i = x; i <= y; i++) {
                lst.push(cs[i]);
            }
        })

        let text = '';
        p = null;
        lst.forEach((ele: any) => {
            if (text && (ele.parentNode == p)) {
                text += '\t';
            } else if (text) {
                text += '\n';
            }
            text += ele.innerText;
            p = ele.parentNode;
        });
        await clipboard.writeText(text);
        copyMessage()
    }
    // 复制整列
    async function oncopyByRow() {
        let lst = document.querySelectorAll('[selectState~=sel]') as any;
        let p: any = null;
        let tempList: any = [];
        lst.forEach((ele: Element) => {
            if (ele.parentNode != p) {
                p = ele.parentNode;
                tempList.push(ele.parentNode);
            }
            else
                return;
        })
        lst = [];
        tempList.forEach((ele: Element) => {
            lst = lst.concat(Array.from(ele.querySelectorAll('td:not(.ant-table-selection-column)')));
        })
        let text = '';
        p = null;
        lst.forEach((ele: any) => {
            if (text && (ele.parentNode == p)) {
                text += '\t';
            } else if (text) {
                text += '\n';
            }
            text += ele.innerText;
            p = ele.parentNode;
        });
        await clipboard.writeText(text);
        copyMessage()
    }

    function onKeydown(event: any) {
        // 判断操作系统类型  
        const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
        if ((isMac && event.metaKey && event.key === 'c') || // macOS 上的 Command+C  
            (!isMac && event.ctrlKey && event.key === 'c')) {  // Windows 上的 Ctrl+C  
            oncopy(true)
        }
    }

    function getCopyValue(x: any, y: any) {
        // console.log(columnsSet.current, 'columnsSet.current');
        // console.log(dataSet.current, 'dataSet.current');
        // console.log(x, 'x');
        // console.log(y, 'y');
        // const data = columnsSet.current?.s       ome((item: any) => item?.children) ? columnsSet.current?.map((item: any) => item?.children)?.flat()?.filter(Boolean) : columnsSet.current
        var co = columnsSet.current?.[x];
        var dataItem = dataSet.current[y];
        if (!co?.type) {
            return dataItem[co.dataIndex]
        } else {
            var div = document.createElement("div");

            document.body.append(div);
            console.log(co, 'co');
            console.log(dataItem, 'dataItem');

            ReactDom.render(co?.render({}, dataItem), div);
            let text = div.innerText || div.textContent;
            document.body.removeChild(div);
            return text;
        }
    }
    async function oncopy(flag?: boolean, isAllColumn?: boolean) {
        try {

            if (spRef.current != null) {
                let node = spRef.current;
                while (node.parentNode && node.tagName != 'BODY') {
                    node = node.parentNode;
                }
                if (node.tagName != 'BODY') {
                    return clear();
                }
            } else {
                clear();
            }
            // console.log(x0.current, 'x0.current');
            // console.log(x1.current, 'x1.current');
            // console.log(y0.current, 'y0.current');
            // console.log(y1.current, 'y1.current');
            // console.log(site, 'sitesite');

            // let m0 = Math.min(x0.current, x1.current), m1 = Math.max(x0.current, x1.current);
            // let n0 = Math.min(y0.current, y1.current), n1 = Math.max(y0.current, y1.current);
            // let m0 = Math.min(site.x0, site.x1), m1 = Math.max(site.x0, site.x1);
            // let n0 = Math.min(site.y0, site.y1), n1 = Math.max(site.y0, site.y1);

            let m0 = Math.min(flag ? x0.current : site.x0, flag ? x1.current : site.x1), m1 = Math.max(flag ? x0.current : site.x0, flag ? x1.current : site.x1);
            let n0 = Math.min(flag ? y0.current : site.y0, flag ? y1.current : site.y1), n1 = Math.max(flag ? y0.current : site.y0, flag ? y1.current : site.y1);
            let tempColumns = columnsSet.current.filter((x: any) => !x.hideInTable || x.title !== '序号').map((item: any) => {
                if (item?.title == '操作') {
                    return {
                        ...item,
                        selectable: 'none',
                        className: 'nonSelectable'
                    }
                }
                return item
            });
            let text = '';
            if (isAllColumn) {
                n0 = 0;
                n1 = dataSet.current?.length - 1;
            }
            for (var j = n0; j <= n1; j++) {
                if (text)
                    text += "\n";
                for (var i = m0; i <= m1; i++) {
                    // let x = rowSelectionCablck ? i - 2 : i - 1;
                    let x = rowSelectionCablck ? i - 1 : i;

                    if (tempColumns[x].selectable == 'none') {
                        continue;
                    }
                    if (i != m0)
                        text += "\t";
                    text += getCopyValue(x, j);
                }
            }
            await clipboard.writeText(text);
            copyMessage()
        } catch (error) {

        }
    }
    const copyMessage = () => message.info('复制成功');
    const selectionCheck = (check?: boolean) => {
        let startCheck = Math.min(y0.current, y1.current), endCheck = Math.max(y0.current, y1.current);
        getDragCheckData(check ? dataSource.slice(startCheck, endCheck + 1) : [])
        dispatch(coordinatePag({
            ...Page,
            show: "none"
        }))
    }
    return <div key={keyID} className={styles.prompt_box} id='prompt_box' style={{
        left: Page?.x, top: Page?.y,
        display: Page?.show
    }} onMouseDown={(event: any) => {
        event.preventDefault();
        event.stopPropagation();
    }}>
        <div onClick={Copy}><Image preview={false} width={22} height={22} src={copyImg} />复制&nbsp;&nbsp; <span style={{ color: '#ccc' }}>Ctrl+C</span></div>
        <div onClick={CopyArray}><Image preview={false} width={22} height={22} src={arrayImg} /> 整列复制</div>
        {rowSelectionCablck && <div onClick={() => selectionCheck(true)}><Image preview={false} width={22} height={22} src={selectImg} />选择</div>}
        {rowSelectionCablck && <div onClick={() => selectionCheck()}><Image preview={false} width={22} height={22} src={reverseImg} />反选</div>}
    </div>
})
export default CopyComponent



