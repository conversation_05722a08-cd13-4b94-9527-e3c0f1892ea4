import { getProviderType, getUserListAPI, shippingAgencyListAPI, shippingListAPI, voyageListAPI } from "@/services/booking";
import { carrierDriverAPI } from "@/services/carriers";
import { getCustomGroupAPI, getLabelListAPI } from "@/services/customer/CustomerHome";
import { getAddressInformationAPI, getClientListAPI, getSearchAddressAPI, getWarehouseListAPI } from "@/services/home/<USER>";
import { getBankAccountListAPI, getFinanceSubjectListAPI, getIDCodeRule, getProductList } from "@/services/productAndPrice/api";
import { ProviderType } from "@/shared/ProviderTypeFn";

export const columnsFilterData = async (ele: any, keyword?: string) => {
    if (ele?.type === 'address') {
        const { status, data } = await getAddressInformationAPI({
            token: keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: `${item.country.replaceAll('CN', '中国')} - ${item.provinceShortName
                        } / ${item.province} - ${item.city} ${item.county ? '-' : ''} ${item.county
                        } / ${item.zipCode}`,
                    value: item?.id,
                };
            });
        }
        // 仓库
    } else if (ele?.type === 'warehouse') {
        const { status, data } = await getWarehouseListAPI({
            keyword,
            type: 0
        });
        if (status) {
            return newData(data)
        }
        // 服务商
    } else if (ele?.type === 'provider') {
        const { status, data } = await shippingAgencyListAPI({
            type: ele?.providerType,
            keyword,
        });
        if (status) {
            return newData(data)
        }
        // 产品
    } else if (ele?.type === 'product') {
        const { status, data } = await getProductList({
            keyword,
        });
        if (status) {
            return newData(data)
        }
        // 客户
    } else if (ele?.type === 'client') {
        const { status, data } = await getClientListAPI({
            keyword,
        });
        if (status) {
            return newData(data)
        }
    } else if (ele?.type === 'clientGroup') {
        const { status, data } = await getCustomGroupAPI({
            keyword,
        });
        if (status) {
            return newData(data)
        }

    }
    // ID编码规则
    else if (ele?.type === 'idRule') {
        const { status, data } = await getIDCodeRule({
            keyword,
        });
        if (status) {
            return newData(data)
        }

    } else if (ele?.type === 'clientTags') {
        const { status, data } = await getLabelListAPI({
            keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.tag,
                    value: item?.tag,
                };
            });
        }

        // 城市
    } else if (ele?.type === 'city') {
        const { status, data } = await getSearchAddressAPI({
            keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: `${item.country === 'CN' ? '中国' : item.country} - ${item.provinceShortName
                        } / ${item.province} - ${item.city} -${item.county}/ ${item.zipCode
                        }`,
                    value: item?.id,
                };
            });
        }
        // 服务商类型
    } else if (ele?.type === 'providerType') {
        const { status, data } = await getProviderType({
            keyword,
        });
        if (status) {
            return newData(data?.list?.map((fid: any) => ({ ...fid, id: fid?.type + '', name: fid?.desc })))
        }
        // FBA仓库
    } else if (ele?.type === 'FBA') {
        const { status, data } = await getWarehouseListAPI({
            keyword,
            type: 1,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.name,
                    value: item?.name,
                };
            });
        }
        // 财务主体
    } else if (ele?.type === 'organization') {
        const { status, data } = await getFinanceSubjectListAPI({
            keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.name,
                    value: item?.id,
                };
            });
        }
        // 航次
    } else if (ele?.type === 'voyage') {
        const { status, data } = await voyageListAPI({
            keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.code,
                    value: item?.voyage?.id,
                };
            });
        }
        // 岗位人员
    } else if (ele?.type === 'user') {
        const { status, data } = await getUserListAPI({
            start: 0,
            len: 20,
            keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.user?.name,
                    value: item?.id,
                };
            });
        }
        // 客户代码
    } else if (ele?.type === 'clientCode') {
        const { status, data } = await getClientListAPI({
            keyword,
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.code,
                    value: item?.code,
                };
            })
        }
    }
    // 车队
    else if (ele?.type === 'fullName') {
        const { status, data } = await shippingAgencyListAPI({
            keyword,
            type: ProviderType.Trailer.getCode()
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: item?.fullName,
                    value: item?.id,
                };
            });
        }
        // 收款银行
    } else if (ele?.type === 'gatheringBank') {
        const { status, data } = await getBankAccountListAPI({
            keyword,
            enabled: 1,
            len: 200
        });
        if (status) {
            return data.list.map((item: any) => {
                return {
                    text: `${item?.accountCode} ${item?.account}`,
                    value: item?.id,
                };
            });
        }
    }
    // 船司 
    else if (ele?.type === 'shipping') {
        const { status, data } = await shippingListAPI({
            keyword,
            type: 2
        });
        if (status) {
            return data.list.map((item: any) => ({
                value: item?.provider?.id,
                text: item?.name
            }))
        }
    }
    else if (ele?.type === 'driver') {
        const { status, data } = await carrierDriverAPI({
            keyword,
        });
        if (status) {
            return data?.list?.map((item: any) => ({ value: item.uid, text: item?.user?.name }))
        }
    }
    return [];
}
// 一样的渲染字段，统一写
const newData = (data: any) => {
    return data?.list?.map((item: any) => {
        return {
            text: item?.name,
            value: item?.id,
        };
    });
}
// 后端要求特殊，数据处理成后端要的格式
export const extendData = (columnsList: any, sort: any, filter: any) => {
    let integrate: any = {}
    columnsList?.forEach((item: any) => {
        // 既操作了排序，又做了其他筛选
        if ((typeof sort === 'object' && Object.keys(sort).length !== 0) && (typeof filter === 'object' && Object.keys(filter).length !== 0)) {
            for (const key in filter) {
                if (item?.dataIndex == key) {
                    const val = item?.valueEnum?.filter((_: any, index: any) => filter[key]?.map((ele: any) => Number(ele)).includes(index))?.map((ele: any) => ele?.value)?.join(',')
                    integrate[item?.dataIndex] = {
                        ...item?.filter?.condition,
                        value: item?.valueEnum ? val : (processStringArray(filter[key]) || ''),
                        sort: rank(sort[key])
                    }
                }
            }
            for (const key in sort) {
                if (item.dataIndex == key) {
                    const val = item?.valueEnum?.filter((_: any, index: any) => filter[key]?.map((ele: any) => Number(ele)).includes(index))?.map((ele: any) => ele?.value)?.join(',')
                    integrate[item?.dataIndex] = {
                        ...item?.filter?.condition,
                        value: item?.valueEnum ? val : (processStringArray(filter[key]) || ''),
                        sort: rank(sort[key])
                    }
                }
            }
            // 只操作了排序
        } else if (typeof sort === 'object' && Object.keys(sort).length !== 0) {
            console.log(sort, 'sort');
            console.log(item, 'item');

            for (const key in sort) {
                if (item?.dataIndex === key) {
                    integrate[item?.dataIndex] = {
                        ...item?.filter?.condition,
                        sort: rank(sort[key])
                    }
                }
            }
            // 只操作其他筛选
        } else if (typeof filter === 'object' && Object.keys(filter).length !== 0) {
            for (const keys in filter) {
                if (item?.dataIndex === keys) {
                    const val = item?.valueEnum?.filter((_: any, index: any) => filter[keys]?.map((ele: any) => Number(ele)).includes(index))?.map((ele: any) => ele?.value)?.join(',')
                    integrate[item?.dataIndex] = {
                        ...item?.filter?.condition,
                        value: item?.valueEnum ? val : (processStringArray(filter[keys]) || '')
                    }
                }
            }
        }

    })
    return integrate
}
function processStringArray(array: any) {
    return array?.map((str: any) => {
        let processedStr = str.replace(/\s+/g, ',').replace(/\n/g, ',');
        if (!/ /.test(str)) {
            return processedStr?.split(',')?.filter(Boolean)?.join(',');
        }
    }).flat()?.join(',');
}
export function sumFields(data: any, fields: any) {
    fields.forEach((field: any) => {
        let sum = 0;
        function traverse(obj: any) {
            if (typeof obj === 'object' && obj !== null) {
                for (let key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        if (key === field.field) {
                            sum += Number(obj[key]);
                        }
                        else if (Array.isArray(obj[key])) {
                            obj[key].forEach(item => traverse(item));
                        }
                        else if (typeof obj[key] === 'object' && obj[key] !== null) {
                            traverse(obj[key]);
                        }
                    }
                }
            }
        }
        data?.forEach((item: any) => traverse(item));
        field.value = Math.round(sum * 100) / 100;
    });
    return fields;
}

const rank = (data: any) => {
    if (data === 'ascend') {
        return 1
    } else if (data === 'descend') {
        return -1
    }
    return 0
}

// 视图的整合编写逻辑
export const combineColumns = (cloudsColumns: any, columns: any) => {
    // 创建一个 Map 来存储 cloudsColumns 中的 dataIndex 和对应的对象，方便快速查找  
    // const cloudMap = new Map(cloudsColumns.map((item: any) => [item.dataIndex, item]));
    // 初始化一个新数组来存放结果  
    let res = columns.slice(0);
    columns = res;
    let indexMap: any = {};
    let keyMap: any = {};
    for (var i = 0; i < cloudsColumns.length; i++) {
        let c = cloudsColumns[i];
        indexMap[c.dataIndex] = i;
        keyMap[c.dataIndex] = c;
    }
    for (var i = 0; i < columns.length; i++) {
        let c = columns[i];
        if (indexMap.hasOwnProperty(c.dataIndex))
            continue;
        indexMap[c.dataIndex] = i - 0.1;
    }

    columns = columns.sort((a: any, b: any) => {
        try {
            return indexMap[a.dataIndex] - indexMap[b.dataIndex];
        } catch (e) {
            return 1;
        }
    })
    for (var co of columns) {
        var c = keyMap[co.dataIndex];
        if (c) {
            /**
             * 其他待覆盖的值；
             * 
             */
            // co.filteredValue = c.filteredValue;
            // co.sortOrder = c.sortOrder;
        }
    }
    return columns;

    // const result = [];
    // // 遍历 columns 数组  
    // for (const column of columns) {
    //     // 如果 column 的 dataIndex 在 cloudsColumns 中存在，则添加到结果数组的前面  
    //     if (cloudMap.has(column.dataIndex)) {
    //         result.unshift(column);
    //         // 可以选择从 Map 中删除已处理的 dataIndex，但在这里不需要  
    //     } else {
    //         // 如果不存在，则直接添加到结果数组的末尾  
    //         result.push(column);
    //     }
    // }
    // // 如果 cloudsColumns 中有 columns 中没有的 dataIndex，则添加到结果数组的末尾  
    // for (const [dataIndex, cloudColumn] of cloudMap) {
    //     if (!columns.some((column: any) => column.dataIndex === dataIndex)) {
    //         result.push(cloudColumn);
    //     }
    // }
    // return result;
}
// 统一添加序号

// const 


// 统计添加序号  
export const columnsSerial = (columns: any, dataSource: any) => {
    const bool = [...columns]?.some((item: any) => item?.dataIndex == 'index' || item?.title == '序号')
    if (bool) {
        return columns
    } else {
        return [
            {
                title: '序号',
                dataIndex: 'index',
                valueType: 'indexBorder',
                fixed: 'left',
                selectable: 'none',
                width: getNumberDigits(dataSource?.length),
                render: (_: any, recode: any, index: number) => {
                    return `${index + 1}`
                }
            },
            ...columns
        ]
    }
}
// 序号根据数据宽度
function getNumberDigits(num: any) {
    // 确保输入是数字  
    if (typeof num !== 'number' || isNaN(num) || !isFinite(num)) {
        return 68;
    }
    // 将数字转换为字符串并获取其长度  
    var strNum = num.toString();
    var length = strNum.length;
    // 根据长度返回描述  
    switch (length) {
        case 1:
            return 48;
        case 2:
            return 80;
        case 3:
            return 150;
        case 4:
            return 200;
        default:
            return 250;
    }
}


