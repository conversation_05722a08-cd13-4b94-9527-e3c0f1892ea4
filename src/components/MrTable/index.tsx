import {
  DownOutlined,
  DownSquareOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Dropdown,
  Form,
  Input,
  message,
  Modal,
  Pagination,
  Row,
  Space,
  Tooltip,
} from 'antd';
import React, { useCallback, useMemo } from 'react';
import { useEffect, useRef, useState } from 'react';
import type { FilterDropdownProps } from 'antd/es/table/interface';
import {
  columnsFilterData,
  columnsSerial,
  combineColumns,
  extendData,
  sumFields,
} from './Filter';
import { useSelector, useDispatch } from 'react-redux';
import { tableSearch } from '@/store/actions/waybill';
import CustomizeComponent from './SearchFilters';
import CopyComponent from './Copy';
import moment from 'moment';
import search_inco from '@/assets/icon/waybil_search.png';
import search_check from '@/assets/icon/search_check.png';
import time_icon from '@/assets/icon/waybil_time.png';
import time_check from '@/assets/icon/time_check.png';
import muns_icon from '@/assets/icon/muns.png';
import muns_check from '@/assets/icon/muns_check.png';
import _ from 'lodash';
// import useAntdResizableHeader from "use-antd-resizable-header";
import { useAntdResizableHeader } from 'use-antd-resizable-header';
import search_arrows from '@/assets/icon/箭头.png';
const { TextArea } = Input;
const { RangePicker } = DatePicker;
import Sortable from 'sortablejs';
import styles from './index.less';
import { getCreateAPI, getListAPI, getViewAPI } from '@/services/view';
interface ShareProTablePropsType {
  /* 表格唯一 key 后端需要 不能重复 */
  keyID: string;
  /* 表头，现在的表头项  */
  columns: any;
  /* 请求，原来接口如何传递，现在一样不变 */
  request?: any;
  /* 表头排序与查询条件配置 */
  filters?: any;
  /* 原来按钮的渲染，根原来的 toolBarRender 一样*/
  toolBarRender?: any;
  /* 是否在原来基础上添加额外按钮  */
  toolBar?: any;
  /* 是否需要导出功能 */
  exportCallback?: (data: any) => void;
  /* 勾选数据的回调 */
  rowSelectionCablck?: any;
  /* 底部要栏 */
  FooterBoxRender?: any;
  /* 表格嵌套表格的情况 */
  expandable?: any;
  /* 表格大小 不传默认 large */
  size?: 'middle' | 'small';
  /* Table 标识数据 唯一 字段 rowKey 不传则默认 id */
  rowKey?: string;
  /* scrollX  */
  scrollX?: number;
  /* toolbar 这里目前就地址库会用到 */
  toolbar?: Object;
  /* 到货总单需要的 */
  // summary?: any
  /*提单 - 运单，这里需求特殊，默认不走请求需要点搜索走 Search */
  // Search?: boolean
  modalCopy?: Object;
  headerTitle?: any;
  FooterTotal?: any;
  modalTableY?: any;
  options?: any;
  scrolly?: any;
  style?: any;
  titleChidren?: any;
  checkData?: any;
  field?: any;
  dataSource?: any;
  sTableY?: any;
  // 子单勾选回调
  bulletCheck?: any;
  tableAlertRender?: any;
}
export const textEllipsis = (text: any) => {
  return (
    <div className="text-ellipsis">
      <Tooltip title={text}>
        <span>{text}</span>
      </Tooltip>
    </div>
  );
};
const MrTable = (props: ShareProTablePropsType) => {
  const {
    keyID,
    columns,
    request,
    toolBarRender,
    filters,
    toolBar,
    exportCallback,
    rowSelectionCablck,
    FooterBoxRender,
    size = 'small',
    scrollX,
    rowKey,
    toolbar,
    // Search,
    modalCopy,
    modalTableY,
    headerTitle,
    FooterTotal,
    options,
    style,
    titleChidren,
    checkData,
    field,
    dataSource,
    sTableY,
    tableAlertRender,
    // scrolly
    // summary
  } = props;
  const [form] = Form.useForm();
  const tableFilter = useSelector((state: any) => state?.tableFilter[keyID]);
  const onRowMouse = useSelector(
    (state: any) => state?.tableFilter?.onRowMouseDown,
  );
  const toggleCopyPane = useSelector(
    (state: any) => state?.tableFilter?.toggleCopyPanel,
  );
  const dispatch = useDispatch();
  const actionRef = useRef<ActionType>();
  const [windowHeight, setWindowHeight] = useState(window.innerHeight);
  const [flag, setFlag] = useState(false);
  const [columnsList, setColumnsList] = useState<any>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const exportParams = useRef<any>(null);
  const [pagination, setPagination] = useState<any>({
    start: 0,
  });
  const [valueEnum, setValueEnum] = useState<any>({});
  const [footerData, setFooterData] = useState(FooterBoxRender);
  const [text, setText] = useState('本页总计');
  const [scrollY, setScrollY] = useState(0);
  const [viewFlag, setViewFlag] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [checked, setChecked] = useState(true);
  const [viewList, setViewList] = useState<any>([]);
  const tableRef = useRef<any>(null);
  const dragColumns = useRef<any>([]);
  // const [search, setSearch] = useState(false)
  const searchs = useRef<any>(false);
  const refresh = () => {
    actionRef.current?.reload();
    // setSelectedRows([]);
  };
  const clearSelectedRows = () => setSelectedRows([]);
  useEffect(() => {
    if (Array.isArray(checkData)) {
      setSelectedRows([...checkData]);
    }
  }, [checkData?.length]);
  useEffect(() => {
    const handleResize = () => {
      setWindowHeight(window.innerHeight);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [flag]);
  const renderHeader = (title: any, index: any) => {
    return (
      <div className="drag-handle" style={{ width: '100%', height: '100%' }}>
        {title}
      </div>
    );
  };
  useEffect(() => {
    for (const key in filters) {
      filters[key]['userSoleId'] = key;
    }
    if (keyID == 'waybillTable') {
      searchs.current = true;
    }
    dispatch(tableSearch(keyID, filters));
  }, [Object.keys(filters ?? {}).length, keyID]);
  useEffect(() => {
    dragColumns.current = columnsList;
  }, [columnsList?.length]);
  useEffect(() => {
    getView();
  }, [columns.length, Object.values(valueEnum).flat()?.length]);
  const getView = async (dataParmas?: any) => {
    const newColumns = getNewColumns(columns).map((colDef: any) => {
      if (colDef?.render) {
        return {
          ...colDef,
          title: renderHeader(colDef.title, colDef.key), // 使用自定义的表头
          type: true,
        };
      } else {
        return {
          ...colDef,
          title: renderHeader(colDef.title, colDef.key), // 使用自定义的表头
        };
      }
    });

    try {
      if (!dataParmas) {
        const { status, data } = await getViewAPI({
          key: keyID,
        });
        if (status && data?.view) {
          // data?.view?.columns?.forEach((item: any, index: number) => {
          //     newColumns?.forEach((ele: any, i: number) => {
          //         if (index == i) {
          //             item.title = ele?.title
          //             if (ele?.render) {
          //                 item['render'] = ele?.render
          //             }
          //         }

          //     })
          // })
          const combinedColumns = combineColumns(
            columnsSerial(data?.view?.columns, pagination?.dataSource),
            newColumns,
          );
          // dispatch(tableSearch(keyID, { ...viewCondition(data?.view?.condition, filters), view: {} }));
          setColumnsList([...combinedColumns]);
        } else {
          const combinedColumns = columnsSerial(
            newColumns,
            pagination?.dataSource,
          );
          setColumnsList([...combinedColumns]);
        }
      } else {
        const combinedColumns = combineColumns(
          columnsSerial(dataParmas?.columns, pagination?.dataSource),
          newColumns,
        );
        // dispatch(tableSearch(keyID, { ...viewCondition(dataParmas?.condition, filters), view: {} }));
        setColumnsList([...combinedColumns]);
      }
    } catch (error) {}
  };

  const updateColumnOrder = (evt: any) => {
    const { oldIndex, newIndex } = evt;
    const updatedColumns = columnsList;
    [updatedColumns[oldIndex], updatedColumns[newIndex]] = [
      updatedColumns[newIndex],
      updatedColumns[oldIndex],
    ];
    setColumnsList([...updatedColumns]);
    onRowMouse(_, _, _, {
      columns: [...updatedColumns].filter((a: any) => {
        return !a.hideInTable;
      }),
      source: pagination?.dataSource,
    });
  };
  useEffect(() => {
    if (tableRef.current) {
      const sortable = Sortable.create(
        tableRef.current.querySelector('thead tr'),
        {
          animation: 150,
          handle: '.drag-handle',
          onEnd: updateColumnOrder,
        },
      );
      return () => {
        sortable.destroy();
      };
    }
  }, [columnsList]);
  const hanDebounce = _.debounce((e: any, objType: any) => {
    asyncColumnsFilters(objType, e?.target?.value).then((res) => {
      setValueEnum((oldData: any) => ({
        ...oldData,
        [objType?.type]: res,
      }));
    });
  }, 500);
  const asyncColumnsFilters = (type: any, value?: any) => {
    return columnsFilterData(type, value);
  };
  useEffect(() => {
    if (pagination?.dataSource?.length && FooterBoxRender) {
      let result: any = [];
      if (text == '本页总计') {
        result = sumFields(pagination?.dataSource, FooterBoxRender);
      } else if (text == '选中总计') {
        result = sumFields(selectedRows, FooterBoxRender);
      } else if (text == '全部总计') {
        result = FooterTotal ?? [];
      }
      setFooterData([...result]);
    }
  }, [pagination?.dataSource, text, selectedRows.length]);
  const getNewColumns = (data: any, keyword?: string) => {
    return data
      .map((item: any, index: number) => {
        if (item?.children?.length) {
          return {
            ...item,
            children: item?.children.map((fid: any) => ({
              ...fid,
              ellipsis: true,
            })),
          };
        }
        if ([0, 1, -1].includes(item?.filter?.sort)) {
          return {
            ...item,
            ellipsis: true,
            // sorter: sorter(item, item?.filter?.sort)
            sorter: true,
          };
        }
        if (item?.title == '操作') {
          return {
            ...item,
            className: 'nonSelectable',
          };
        }
        return item;
      })
      ?.map((item: any) => {
        if (['search', 'dateTime'].includes(item?.filter?.condition?.type)) {
          return { ...item, ...getColumnSearchProps(item), ellipsis: true };
        } else if (item?.filter?.condition?.type === 'select') {
          return {
            ...item,
            valueEnum: item?.filter?.condition?.range,
            ellipsis: true,
          };
        } else if (
          [
            'address',
            'warehouse',
            'provider',
            'product',
            'client',
            'clientCode',
            'clientGroup',
            'idRule',
            'clientTags',
            'city',
            'providerType',
            'FBA',
            'organization',
            'voyage',
            'user',
            'gatheringBank',
          ].includes(item?.filter?.condition?.type)
        ) {
          const newItem = {
            ...item,
            ...getColumnDownProps(item),
            ellipsis: true,
          };
          return newItem;
        }
        return { ...item, ellipsis: true };
      });
  };
  const sorter = (item: any, sort: number) => {
    if (sort === 0) {
      return (a: any, b: any) => {
        return 0;
      };
      // sortByAge(a?.[item?.dataIndex], b?.[item?.dataIndex]);
    } else if (sort === 1) {
      // return {
      //     order: "ascend",
      // };
      return 1;
    } else if (sort === -1) {
      // return {
      //     order: "descend",
      // };
      return -1;
    }
  };
  useEffect(() => {
    getViewList();
  }, []);
  const getViewList = async () => {
    try {
      const { status, data } = await getListAPI({
        key: keyID,
        start: 0,
        len: 20,
      });
      if (status) {
        setViewList(data?.list);
      }
    } catch (error) {}
  };
  const getColumnSearchProps = (item: any) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }: any) => (
      <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
        {item?.filter?.condition?.type === 'search' ? (
          <>
            <TextArea
              placeholder={`请输入关键字搜索`}
              value={selectedKeys[0]}
              onChange={(e) =>
                setSelectedKeys(e.target.value ? [e.target.value] : [])
              }
              onPressEnter={() =>
                handleSearch(selectedKeys as string[], confirm, item?.dataIndex)
              }
              style={{ marginBottom: 8, display: 'block' }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  handleSearch(
                    selectedKeys as string[],
                    confirm,
                    item?.dataIndex,
                  );
                  getStart(0);
                }}
                icon={<SearchOutlined />}
                size="small"
                style={{ width: 90 }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  if (clearFilters) {
                    handleReset(clearFilters);
                    handleSearch(
                      selectedKeys as string[],
                      confirm,
                      item?.dataIndex,
                    );
                    getStart(0);
                  }
                }}
                size="small"
                style={{ width: 90 }}
              >
                重置
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  close();
                }}
              >
                关闭
              </Button>
            </Space>
          </>
        ) : (
          <RangePicker
            style={{ width: 162 }}
            value={
              selectedKeys?.length
                ? selectedKeys?.map((key: any) => moment(key))
                : null
            }
            onChange={(e) => {
              setSelectedKeys(
                e?.map((date: any) => date?.format('YYYY-MM-DD')),
              );
              handleSearch(selectedKeys as string[], confirm, item?.dataIndex);
            }}
          />
        )}
      </div>
    ),
    filterIcon: (filtered: boolean) => {
      return (
        <img
          src={customIcon(filtered, item?.filter?.condition?.type)}
          style={{ width: '12px' }}
        />
      );
    },
  });

  const customIcon = (filtered: boolean, type: string) => {
    if (type == 'search') {
      if (filtered) {
        return search_check;
      } else {
        return search_inco;
      }
    } else if (type == 'dateTime') {
      if (filtered) {
        return time_check;
      } else {
        return time_icon;
      }
    } else {
      if (filtered) {
        return muns_check;
      } else {
        return muns_icon;
      }
    }
  };
  const addView = async (value: any) => {
    try {
      const { status } = await getCreateAPI({
        ...value,
        isDef: checked,
        key: keyID,
        condition: tableFilter,
        columns: columnsList,
      });
      if (status) {
        setIsModalOpen(false);
        message.success('添加视图成功');
        getViewList();
      }
    } catch (error) {}
  };
  const getColumnDownProps = (item: any) => {
    return {
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
        close,
      }: any) => (
        <div
          className={styles.customSearch}
          onKeyDown={(e) => e.stopPropagation()}
        >
          <div className={styles.cousInput}>
            <div>
              <SearchOutlined />
              <Input
                placeholder="请输入搜索内容"
                onChange={(e) => hanDebounce(e, item?.filter?.condition)}
              />
            </div>
          </div>
          <div className={styles.cousCount}>
            {
              <Checkbox.Group
                value={selectedKeys}
                onChange={(e: any) => {
                  setSelectedKeys(e ?? []);
                }}
              >
                <Row>
                  {valueEnum[item?.filter?.condition?.type]?.map(
                    (item: any) => (
                      <Col
                        key={item.value}
                        span={24}
                        style={{ marginBottom: '5px' }}
                      >
                        <Checkbox value={item.value}>{item?.text}</Checkbox>
                      </Col>
                    ),
                  )}
                </Row>
              </Checkbox.Group>
            }
          </div>
          <div className={styles.cousFooter}>
            <a
              onClick={() => {
                if (clearFilters) {
                  handleReset(clearFilters);
                  setSelectedKeys([]);
                  asyncColumnsFilters(item?.filter?.condition).then((res) => {
                    setValueEnum((oldData: any) => ({
                      ...oldData,
                      [item?.filter?.condition?.type]: res,
                    }));
                  });
                }
              }}
            >
              重置
            </a>
            <Button
              type="primary"
              onClick={() => {
                handleSearch(
                  selectedKeys as string[],
                  confirm,
                  item?.dataIndex,
                );
                getStart(0);
              }}
            >
              确定
            </Button>
          </div>
        </div>
      ),
      onFilterDropdownVisibleChange: async (visible: boolean) => {
        if (visible && !valueEnum[item?.filter?.condition?.type]?.length) {
          asyncColumnsFilters(item?.filter?.condition).then((res) => {
            setValueEnum((oldData: any) => ({
              ...oldData,
              [item?.filter?.condition?.type]: oldData[
                item?.filter?.condition?.type
              ]?.length
                ? oldData[item?.filter?.condition?.type]
                : res,
            }));
          });
        }
      },
      filterIcon: (filtered: boolean) => {
        return (
          <img
            src={customIcon(filtered, item?.filter?.condition?.type)}
            style={{ width: '12px' }}
          />
        );
      },
    };
  };
  const handleSearch = (
    selectedKeys: string[],
    confirm: FilterDropdownProps['confirm'],
    dataIndex: any,
  ) => {
    confirm();
  };

  const handleReset = (clearFilters: () => void) => {
    clearFilters();
  };
  // 表格勾选逻辑
  const getRowSelection = {
    columnWidth: 40,
    fixed: true,
    checkStrictly: false,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      rowSelectionCablck!(selectedRows);
      setSelectedRows(selectedRows);
    },
    selectedRowKeys: selectedRows?.map((item: any) =>
      rowKey ? item[rowKey] : item?.id,
    ),
  };
  const sortByAge = (a: any, b: any) => {
    if (a === null) {
      return 1; // 将空值排序到数组的末尾
    } else if (b === null) {
      return -1; // 将空值排序到数组的末前
    } else if (typeof a === 'number') {
      return a - b;
    } else if (typeof a === 'string') {
      return a.localeCompare(b);
    }
  };
  const toolBarCard = (data: any): any => {
    if (!data) return;
    return data?.map((item: any) => {
      if (item?.type === 'btn') {
        return (
          <Button
            key={item?.key}
            ghost
            type={'primary'}
            onClick={() => toolBar?.onclick(item?.key)}
          >
            {item?.label}
          </Button>
        );
      } else if (item?.type === 'list') {
        return (
          <Dropdown
            key={item?.key}
            menu={{
              items: item?.list,
              onClick: ({ key }) => {
                toolBar?.onclick(key);
              },
            }}
            className="bg-#fafafa p-8px rd-4px"
            overlayStyle={{
              cursor: 'pointer',
            }}
          >
            <Space className="color-#333 ">
              {item?.label}
              <DownOutlined />
            </Space>
          </Dropdown>
        );
      } else {
        return '';
      }
    });
  };
  const FooterBox = () => {
    return (
      <Space>
        {rowSelectionCablck && (
          <>
            <div className="color-#707070 font-500 text-14px">
              已选
              <span> {selectedRows.length} </span>条
            </div>
            <Divider type="vertical" className="font-600" />
          </>
        )}

        {footerData && (
          <StatisticsOnThisPage
            itemList={[
              { label: '本页总计', key: '1' },
              { label: '全部总计', key: '2' },
              { label: '选中总计', key: '3' },
            ]}
          />
        )}
        {footerData?.map((item: any) => (
          <div
            className="font-500"
            style={{ marginLeft: '12px', fontWeight: 400 }}
          >
            {item?.desc}：{' '}
            <span
              className="color-#4071FF font-600"
              style={{ fontWeight: 700 }}
            >
              {' '}
              {item?.value}{' '}
            </span>{' '}
            {item?.unit}
          </div>
        ))}
      </Space>
    );
  };
  const StatisticsOnThisPage = ({ itemList }: any) => {
    // const [text, setText] = useState("本页总计");
    const lablekey: any = {
      1: '本页总计',
      2: '全部总计',
      3: '选中总计',
    };
    const handleMenuClick = (e: any) => {
      setText(lablekey[e.key]);
    };
    const menuProps = {
      items: itemList,
      onClick: handleMenuClick,
    };
    return (
      <Dropdown menu={menuProps}>
        <div>
          <Button size="small">
            <Space className="color-#333 ">
              {text}
              <img src={search_arrows} style={{ width: '10px' }} />
            </Space>
          </Button>
        </div>
      </Dropdown>
    );
  };
  // 导出
  const getExportData = () => {
    if (selectedRows?.length) {
      exportCallback!(selectedRows);
    } else {
      delete exportParams.current?.start;
      delete exportParams.current?.len;
      exportCallback!(exportParams.current);
    }
  };
  const flagStyleClass = () => {
    if (!filters) return;
    return Object.values(filters)?.some((item: any) => item?.type === 'search')
      ? flag
        ? styles['share_search_pack']
        : styles['share_search_unfold']
      : flag
      ? styles['share_keyword_unfold']
      : styles['share_search_unfold'];
  };

  // 重置页码
  const getStart = (start: number) => {
    setPagination({
      ...pagination,
      start,
    });
  };
  useEffect(() => {
    const dataFlag = Object.values(filters ?? {})?.some(
      (item: any) => item?.tabs,
    );
    if (flag) {
      const element = document.querySelector(
        Object.values(filters)?.find(
          (item: any) =>
            item?.type == 'keyword' || item?.reportType == 'statement',
        )
          ? '.share_keyword_unfold___Gn7K8'
          : '.share_search_pack___gGNsZ',
      ) as any;
      setScrollY(
        windowHeight -
          element?.offsetHeight -
          (sTableY
            ? sTableY?.pack
            : dataFlag
            ? 280
            : keyID == 'BOL/PreplanCabin/waybill'
            ? 354
            : 212),
      );
      //
    } else {
      if (dataFlag) {
        setScrollY(windowHeight - (titleChidren ? 384 : 344));
      } else if (!filters) {
        setScrollY(windowHeight - (modalTableY ? 460 : 520));
      } else {
        setScrollY(
          windowHeight -
            (sTableY
              ? sTableY?.unfold
              : keyID == 'BOL/PreplanCabin/waybill'
              ? 422
              : 280),
        );
      }
    }
    // }, [flag, windowHeight, Search])
  }, [flag, windowHeight, Object.keys(filters ?? {})?.length]);

  const { components, resizableColumns, tableWidth } = useAntdResizableHeader({
    columns: useMemo(() => columnsList, [columnsList]),
    minConstraints: 50,
    columnsState: {
      persistenceKey: `localKey${keyID}`,
      // persistenceType: 'sessionStorage',
      persistenceType: 'localStorage',
    },
  });
  const getDragCheckData = (data: any) => {
    rowSelectionCablck!(data, true);
    setSelectedRows(data);
  };
  function areColumnsDifferent(fixedColumns: any, variableColumns: any) {
    // 确保两个数组长度一致
    if (fixedColumns.length !== variableColumns.length) {
      return true;
    }
    // 遍历每个元素进行比较
    for (let i = 0; i < fixedColumns.length; i++) {
      const fixedCol = fixedColumns[i];
      const variableCol = variableColumns[i];
      // 检查dataIndex属性是否相同
      if (fixedCol.dataIndex !== variableCol.dataIndex) {
        return true;
      }
    }
    return false;
  }
  // 控制第一次不走请求
  const getFirst = () => (searchs.current = false);
  const CustomizeCondition = useCallback(
    () => (
      <CustomizeComponent
        getFirst={getFirst}
        refresh={refresh}
        keyID={keyID}
        getStart={getStart}
      />
    ),
    [keyID],
  );
  // 将表头筛选项变成受控制的整合
  const filterChecked = (stor: any, filter: any) => {
    columnsList.forEach((item: any) => {
      if (filter) {
        for (const key in filter) {
          if (item?.dataIndex == key) {
            item['filteredValue'] = filter[key];
          }
        }
      }
      if (stor) {
        for (const key in stor) {
          if (item?.dataIndex == key) {
            item['sortOrder'] = stor[key];
          }
        }
      }
    });
    setColumnsList([...columnsList]);
  };
  // 视图 condition
  const viewCondition = (cloudsCondition: any, condition: any) => {
    for (const keys in condition) {
      for (const key in cloudsCondition) {
        if (keys == key) {
          if (condition[keys]?.type == 'dateTimeRange') {
            condition[keys].startTime = cloudsCondition[key]?.startTime;
            condition[keys].endTime = cloudsCondition[key]?.endTime;
          } else {
            condition[keys]['value'] = cloudsCondition[key]?.value;
          }
        }
      }
    }
    return condition;
  };
  return (
    <div className={styles.tableWrapper} style={style}>
      {tableFilter && Object.values(tableFilter)?.length ? (
        <div>
          <div className={flagStyleClass()}>
            {tableFilter &&
              (Object.values(tableFilter)?.length > 3 ||
                Object.values(tableFilter)
                  ?.map((item: any) => item?.type)
                  .includes('search')) && (
                <>
                  {!flag && (
                    <div
                      className={styles.unfold}
                      onClick={() => {
                        setFlag(true);
                      }}
                    >
                      <a>展开</a> <DownOutlined style={{ color: '#1677ff' }} />
                    </div>
                  )}
                  {flag && (
                    <div
                      className={styles.pack}
                      onClick={() => {
                        setFlag(false);
                      }}
                    >
                      <a>收起</a> <UpOutlined style={{ color: '#1677ff' }} />
                    </div>
                  )}
                </>
              )}
            {tableFilter && <CustomizeCondition />}
          </div>
        </div>
      ) : (
        ''
      )}
      <div className={styles.customTable} ref={tableRef}>
        <ProTable
          columns={resizableColumns}
          actionRef={actionRef}
          className={styles.share_proTable}
          components={components}
          rowClassName={(record: any, index: number): any => {
            // if (keyID == 'waybillTable') {
            //     return `${styles.compact}`
            // }
            if (record[field]) {
              return `luofeng-${index} ${styles.become}`;
            } else {
              return 'luofeng-' + index;
            }
          }}
          // filters={{}}

          onRow={(record: any, index): any => {
            return {
              onContextMenu: (event: any) => {
                event.preventDefault();
                event.stopPropagation();
                toggleCopyPane(event, true);
              },
              // onKeydown: (e) => {
              //     console.log(e, 'eee');

              //     onKeydown()
              // },
              // onMouseDown: onRowMouseDown,
              // onScroll: paint
              onMouseDown: (e: any) => {
                onRowMouse(e, record, keyID, {
                  columns: dragColumns.current,
                  source: pagination?.dataSource,
                });
                // onRowMouse(e, record, keyID, { columns: resizableColumns.filter((a: any) => { return !a.hideInTable }), source: pagination?.dataSource })
              },
            };
          }}
          bordered
          rowSelection={rowSelectionCablck ? getRowSelection : false}
          tableAlertRender={false}
          request={(params: any, sort: any, filter: any) => {
            // console.log(sort, 'sort');
            // console.log(filter, 'filter');
            // filterChecked(sort, filter)
            if (searchs.current) return;
            const pageSizes = localStorage.getItem(`${keyID}`) as any;
            const filterData = extendData(columnsList, sort, filter);
            // const length = Object.values(filterData)?.map((item: any) => item?.value)?.filter(Boolean)?.length
            let requestData = {
              start: pagination?.start,
              len: pageSizes ? Number(pageSizes) : 100,
              columnsFilter: filterData,
              condition: tableFilter,
            };
            exportParams.current = requestData;
            return request(
              requestData,
              actionRef.current,
              clearSelectedRows,
            ).then((res: any) => {
              setPagination({
                ...pagination,
                total: res?.total,
                dataSource: res?.data,
              });
              return res;
            });
          }}
          columnsState={{
            persistenceKey: `persistenceKey${keyID}`,
            persistenceType: 'localStorage',
            // defaultValue: {
            //     option: { fixed: "right", disable: true },
            // },
          }}
          tableLayout="auto"
          rowKey={rowKey ?? 'id'}
          search={false}
          options={
            options ?? {
              fullScreen: true,
              density: false,
            }
          }
          onHeaderRow={(e: any, _: any): any => {
            if (onRowMouse && e?.length > 1) {
              const bool = areColumnsDifferent(
                e?.filter(
                  (item: any) =>
                    !item?.RC_TABLE_INTERNAL_COL_DEFINE &&
                    item?.key != 'option',
                ),
                columnsList?.filter((ele: any) => ele?.key != 'option'),
              );
              if (bool) {
                const drag = [...e].filter((a: any) => {
                  return !a.hideInTable && !a?.RC_TABLE_INTERNAL_COL_DEFINE;
                });
                dragColumns.current = drag;
                onRowMouse(_, _, _, {
                  columns: dragColumns.current,
                  source: pagination?.dataSource,
                });
              }
            }
          }}
          // scroll={{ x: columnsList.length * 200, y: windowHeight - 316 }}
          scroll={{
            // x: scrollX ?? columnsList.length * 140,
            x: tableWidth,
            y: scrollY,
            // y: windowHeight - 350,
          }}
          dataSource={dataSource}
          pagination={false}
          dateFormatter="string"
          virtual
          size={size}
          // size="small"
          headerTitle={headerTitle}
          toolBarRender={() => {
            if (
              [
                'dataManagement/address',
                'Waybilladministration/visit/salesman',
                'BringGroup/QingtiList',
              ].includes(keyID)
            ) {
              return toolBarRender;
            } else {
              return [
                <div className={styles.toolBar}>
                  {toolBarRender}
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {toolBarCard(toolBar?.list)}
                    {exportCallback && (
                      <Button
                        type="primary"
                        style={{ position: 'absolute', right: '-45px' }}
                        onClick={getExportData}
                        ghost
                      >
                        导出
                      </Button>
                    )}
                    {typeof options === 'undefined' && (
                      <div>
                        <Tooltip title="视图">
                          <DownSquareOutlined
                            className={styles.view_btn}
                            onClick={() => setViewFlag(!viewFlag)}
                            style={{
                              bottom: ['Booking/cabin/statistics'].includes(
                                keyID,
                              )
                                ? '16px'
                                : toolBarRender
                                ? '7px'
                                : '-8px',
                            }}
                          />
                        </Tooltip>
                      </div>
                    )}
                  </div>
                </div>,
              ];
            }
          }}
          toolbar={toolbar}
        />
        {pagination?.total ? (
          <div className={styles.paging}>
            <div className={styles.footer_page}>
              <div className="z-999">
                <FooterBox />
              </div>
              <Pagination
                size="small"
                total={pagination?.total || 20}
                showTotal={(total) => `共 ${total} 条`}
                defaultPageSize={
                  ['undefined', null].includes(
                    localStorage.getItem(`${keyID}`) as any,
                  )
                    ? 100
                    : (localStorage.getItem(`${keyID}`) as any)
                }
                defaultCurrent={pagination?.start}
                pageSizeOptions={[20, 100, 200, 500, 1000, 2000, 5000]}
                onChange={(e, page: any) => {
                  const pageSizes = localStorage.getItem(`${keyID}`) as any;
                  setPagination({
                    ...pagination,
                    start: (e - 1) * (pageSizes ? Number(pageSizes) : 100),
                  });
                  localStorage.setItem(`${keyID}`, page);
                  refresh();
                }}
                showSizeChanger
              />
            </div>
          </div>
        ) : (
          ''
        )}
        {viewFlag && (
          <div className={styles.table_view}>
            <div className={styles.addView}>
              <a onClick={() => setIsModalOpen(true)}>添加视图</a>
            </div>
            <div>
              {viewList?.map((item: any, index: number) => (
                <div
                  key={index}
                  className={styles.viewList}
                  onClick={() => {
                    getView(item);
                  }}
                >
                  {item?.name}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <CopyComponent
        columnsList={columnsList.filter((a: any) => {
          return !a.hideInTable;
        })}
        keyID={keyID}
        getDragCheckData={getDragCheckData}
        dataSource={pagination?.dataSource}
        rowSelectionCablck={rowSelectionCablck}
        modalTableY={modalTableY}
        modalCopy={modalCopy}
      />
      <Modal
        destroyOnClose
        title="添加视图"
        open={isModalOpen}
        afterClose={() => form?.resetFields()}
        onCancel={() => {
          setIsModalOpen(false);
          setViewFlag(false);
        }}
        onOk={() => form?.submit()}
      >
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 10 }}
          style={{ maxWidth: 600 }}
          autoComplete="off"
          onFinish={addView}
        >
          <Form.Item
            label="视图名称"
            name="name"
            rules={[{ required: true, message: '必填项不能为空!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="设为置顶">
            <Checkbox
              checked={checked}
              onChange={(e) => {
                setChecked(e?.target?.checked);
              }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default React.memo(MrTable);
