import { Select, Space, Tag } from 'antd';
import classNames from 'classnames';
import React, { useState } from 'react';
import styles from './styles.less';

const { CheckableTag } = Tag;

interface Props {
  options: any[];
  getKeywords: any;
  width?:any
  maxWidth?:any
}
const KeywordsSelect = ({ options, getKeywords,width,maxWidth }: Props) => {
  const [selectedTags, setSelectedTags] = useState<any>([]);
  const handleChange = (tag: any, checked: boolean) => {
    const nextSelectedTags: any = checked
      ? [...selectedTags, tag]
      : selectedTags.filter((t: any) => t.value !== tag.value);
    setSelectedTags(nextSelectedTags);
    getKeywords(nextSelectedTags);
  };

  const handleChange2 = (value: any, val: any) => {
    setSelectedTags([...val]);
    getKeywords(val);
  };

  return (
    <>
      <Select
        allowClear
        mode="tags"
        options={options}
        placeholder="关键词筛选"
        tokenSeparators={[',']}
        value={selectedTags}
        style={{ minWidth: width ?? '400px'
        // ,maxWidth:maxWidth ? maxWidth :  width ?? '400px'
      }}
        maxTagCount={3}
        onClear={() => {
          setSelectedTags([]);
        }}
        onChange={handleChange2}
        dropdownRender={() => {
          return (
            <div
              className={classNames(
                'pl12px pr12px pt5px pb5px',
                styles['warp'],
              )}
            >
              {options?.map((item: any, index: any) => {
                return (
                  <div key={index}>
                    <div className="color-#AEAEAE text-12px mb-10px">
                      {item.label}
                    </div>
                    <div className="mb-18px">
                      <Space size={[0, 8]} wrap>
                        {item?.options?.map((tag: any, index2: any) => (
                          <CheckableTag
                            key={index2}
                            checked={selectedTags?.some(
                              (val: any) => val.value === tag.value,
                            )}
                            onChange={(checked) => handleChange(tag, checked)}
                          >
                            {tag.label}
                          </CheckableTag>
                        ))}
                      </Space>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        }}
      />
    </>
  );
};

export default React.memo(KeywordsSelect);
