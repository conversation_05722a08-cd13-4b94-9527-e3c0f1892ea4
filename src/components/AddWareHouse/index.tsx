import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormSelect,
  // ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Form, InputNumber, message } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import { useEffect, useState } from 'react';
import {
  addressFuzzySearchAPI,
  addWareHouseAPI,
  editWareHouseAPI,
  getTagListApi,
} from '@/services/home/<USER>';
import { getBrokerList } from '@/services/customer/CustomerHome';
import { ProviderType } from '@/shared/ProviderTypeFn';
import styles from './index.less';
import { getCountryList } from '@/services/productAndPrice/api';
const AddWareHouse = (props: any) => {
  const { right } = props;
  /*是否是修改*/
  const isRevised = props?.Revised;
  /*外部掉用的时候添加这个参数*/
  const providerId = props?.providerId;
  const type = props?.type || 'primary';
  const activeKey = props?.activeKey || '无';
  console.log('activeKey', activeKey);
  const WareHouseType = props?.WareHouseType || 0;
  const { dataList } = props;
  const [form] = Form.useForm<any>();
  const [defaultValue, setDefaultValue] = useState<any>('');
  const [open, setOpen] = useState<any>(false);
  useEffect(() => {
    if (isRevised) {
      console.log('dataList?.tag', !!dataList?.tag);
      const param = {
        ...dataList,
        tag: dataList?.tag ? dataList?.tag?.split(',') : undefined,
        province2: defaultValue,
      };
      form.setFieldsValue(param);
      setDefaultValue(
        `${dataList?.country === 'CN' ? '中国' : dataList?.country} - ${
          dataList?.provinceShortName
        } / ${dataList?.province} - ${dataList?.city}`,
      );
    }
  }, [dataList, open]);
  /* 提交 */
  const onFinish = async (values: any) => {
    const { province2, ...other } = values;
    const params = {
      ...values,
      province: values.province2,
      service_id: 'Waybill',
    };
    if (providerId) {
      params.type = 2;
      params.providerId = providerId;
    }
    if (values?.tag) {
      params.tag = values?.tag?.join(',');
    }
    console.log('activeKey2222', activeKey);
    if (activeKey === '海外仓库') {
      console.log('海外仓库');
      params.type = 3;
    }
    const { status, data } = await addWareHouseAPI(params);
    if (status) {
      message.success('提交成功');
      props?.refreshTable(data);
      return true;
    } else {
      return false;
    }
  };
  /* 修改只能修改这四个
      contactName
      contactPhone
      companyName
      mnemonicCode
    */
  /*修改仓库*/
  const onRevised = async (values: any) => {
    const params: any = {
      contactName: values.contactName,
      contactPhone: values.contactPhone,
      companyName: values.companyName,
      weight: values.weight,
      name: values.name,
      email: values.email,
      fax: values.fax,
      id: dataList.id,
    };
    if (values?.tag) {
      params.tag = values?.tag?.join(',');
    }
    if (activeKey === '海外仓库') {
      params.type = 3;
    }
    const { status } = await editWareHouseAPI(params);
    if (status) {
      message.success('修改成功');
      props.refreshTable();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };

  return (
    <ModalForm
      title={`${isRevised ? '编辑' : '新建'}仓库`}
      trigger={
        isRevised ? (
          <a>修改</a>
        ) : (
          <Button type={type} style={{ marginRight: right ? right : 0 }}>
            <PlusOutlined rev={undefined} />
            新建仓库
          </Button>
        )
      }
      form={form}
      labelWrap={true}
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 15 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        className: styles.wrap,
      }}
      layout="horizontal"
      submitTimeout={2000}
      initialValues={{ weight: 0 }}
      onOpenChange={() => setOpen(!open)}
      onFinish={isRevised ? onRevised : onFinish}
    >
      <ProFormText
        name="name"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="仓库名称"
      />
      {!isRevised && (
        <ProFormSelect
          name="province"
          label="查询"
          style={{ minWidth: '196px' }}
          fieldProps={{
            labelInValue: true,

            filterOption: false,
            onChange: (e) => {
              console.log('JSON.parse(e?.value)', JSON.parse(e?.value));
              const { zipCode, city, country, province } = JSON.parse(e?.value);
              form.setFieldValue('zipCode', zipCode);
              form.setFieldValue('city', city);
              form.setFieldValue('country', country);
              form.setFieldValue('province2', province);
            },
          }}
          showSearch
          disabled={isRevised}
          placeholder="请选择，支持模糊搜索"
          //rules={[{ required: true, message: '必填不能为空' }]}
          debounceTime={300}
          request={async ({ keyWords }) => {
            const { status, data } = await addressFuzzySearchAPI({
              token: keyWords,
            });
            if (status) {
              //country - short_province_name / province - city
              return data.list.map((item: any) => {
                return {
                  label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                  value: JSON.stringify(item),
                };
              });
            } else {
              return [];
            }
          }}
        />
      )}

      <ProFormSelect
        name="country"
        style={{ minWidth: '196px' }}
        label="国家"
        rules={[{ required: true, message: '必填项不能为空' }]}
        disabled={isRevised}
        request={async ({ keyWords }) => {
          const { status, data } = await getCountryList({
            start: 0,
            len: 200,
            keyword: keyWords,
          });
          if (status) {
            return data.list.map((item: any) => {
              return {
                label: `${item.name}-${item.cnName}-${item.code}`,
                value: item.code,
              };
            });
          }
          return [];
        }}
      />
      <ProFormText
        name="province2"
        label="州/省"
        style={{ width: '90%' }}
        rules={[{ required: true, message: '必填项不能为空' }]}
        disabled={isRevised}
      />
      <ProFormText
        name="city"
        label="城市"
        style={{ width: '90%' }}
        rules={[{ required: true, message: '必填项不能为空' }]}
        disabled={isRevised}
      />
      <ProFormText
        name="street"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="街道"
        disabled={isRevised}
      />
      <ProFormText
        name="zipCode"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="邮编"
        disabled={isRevised}
      />
      <ProFormText
        name="contactName"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="联系人"
      />
      <ProFormText
        name="contactPhone"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="电话"
      />
      <ProFormText
        name="companyName"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="公司"
      />
      <Form.Item name="weight" label="权重">
        <InputNumber />
      </Form.Item>
      <ProFormSelect
        name="tag"
        label="标签"
        mode="multiple"
        request={async () => {
          const { status, data } = await getTagListApi({
            key:
              activeKey === '国内仓库'
                ? 'DomesticWarehouse'
                : 'AboardWarehouse',
          });
          if (status) {
            return data?.list?.map((item: any) => {
              return {
                label: item,
                value: item,
              };
            });
          }
          return [];
        }}
      />
      <ProFormText name="email" label="电子邮箱" />
      <ProFormText name="fax" label="传真" />
    </ModalForm>
  );
};
export default AddWareHouse;
