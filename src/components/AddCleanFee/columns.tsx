/*成本分摊字段*/
import { ProFormText } from '@ant-design/pro-components';
import React from 'react';
import { averageTypeMap, currency_map } from '@/utils/constant';

const SharingColumns: any = (props: any) => {
  const {
    SelectTotal,
    totalMoney = 0,
    type,
    costReconciliationType,
    myList,
    spaceFee,
    currency,
  } = props;
  console.log(' SelectTotal', SelectTotal);
  //console.log(props);
  return [
    {
      title: '主提单号',
      dataIndex: 'blno',
      key: 'blno',
    },
    {
      title: '运单号',
      dataIndex: ['waybillNo'],
      key: 'waybillId',
    },
    {
      title: '件数',
      dataIndex: ['fee', 'pcs'],
      key: 'address',
    },
    {
      title: '收货计费重',
      dataIndex: ['fee', 'chargeWeight'],
      key: 'address',
    },
    {
      title: '出货总体积',
      dataIndex: ['fee', 'volume'],
      key: 'address',
    },
    {
      title: '出货实重',
      dataIndex: ['fee', 'actualWeight'],
      key: 'address',
    },
    {
      title: `预计均摊金额/${currency_map[currency]}`,
      dataIndex: ['fee'],
      key: 'fee',
      render: (text: any, current: any) => {
        const types: number = averageTypeMap[type];
        const number: number = current?.fee?.[types];
        const isSelect = myList.includes(current?.id);
        console.log('isSelect', isSelect);
        if (props?.type === '6') {
          if ('subList' in current) {
            return '-';
          } else {
            return (
              <ProFormText
                name={['Sharing', current?.id]}
                width={100}
                noStyle
              />
            );
          }
        } else {
          if (costReconciliationType !== 2) {
            return myList.length > 0 ? totalMoney : 0;
          } else {
            if (isSelect) {
              return ((number / SelectTotal) * totalMoney).toFixed(2);
            } else {
              return '';
            }
          }
        }
      },
    },
  ];
};
/*运单加收字段*/
const WaybillSurchargeColumns: any = (props: any) => {
  const { SelectTotal, type, totalMoney, myAddList, spaceFee, currency } =
    props;
  return [
    {
      title: '运单号',
      dataIndex: ['waybillNo'],
      key: 'waybillNo',
    },
    {
      title: '销售产品',
      dataIndex: ['productName'],
      key: 'productName',
    },
    {
      title: '客户公司名',
      dataIndex: ['instanceName'],
      key: 'instanceName',
    },
    {
      title: '主提单号',
      dataIndex: 'blno',
      key: 'blno',
    },
    {
      title: '子提单号',
      dataIndex: ['subBlno'],
      key: 'age',
      width: '100px',
    },

    {
      title: '件数',
      dataIndex: ['pcs'],
      key: 'address',
    },
    {
      title: '收货计费重',
      dataIndex: ['chargeWeight'],
      key: 'address',
    },
    {
      title: '出货总体积',
      dataIndex: ['volume'],
      key: 'address',
    },
    {
      title: '出货实重',
      dataIndex: ['actualWeight'],
      key: 'address',
    },
    {
      title: `预计均摊金额/${currency_map[currency]}`,
      dataIndex: ['amount'],
      key: 'amount',
      render: (text: any, current: any) => {
        const types: number = averageTypeMap[type];
        /*单个数量*/
        const number: number = current?.[types];
        const isSelect = myAddList.includes(current?.waybillId);
        return props?.type === '6' ? (
          <ProFormText
            name={['Surcharge', current?.waybillId]}
            width={100}
            noStyle
          />
        ) : isSelect ? (
          ((number / SelectTotal) * totalMoney).toFixed(2)
        ) : (
          '0'
        );
      },
    },
  ];
};
export { SharingColumns, WaybillSurchargeColumns };
