/*成本分摊*/
import { TableRowSelection } from 'antd/es/table/interface';
import { Col, Form, Radio, Row, Select, Table } from 'antd';
import { SharingColumns } from './columns';
import React from 'react';
import { calculateSingSum } from '@/utils/utils';
import {
  averageType,
  averageTypeMap,
  convertObjectToArray,
} from '@/utils/constant';
import Decimal from 'decimal.js';

const CostSharing = (props: any) => {
  const {
    isDetail,
    fees,
    myList,
    costReconciliationType,
    costAverageType,
    totalMoney,
    spaceFee,
    currency,
  } = props;
  /*计算选中总价 */
  const countTotal = (type: any, array: any) => {
    let total: any = 0;
    array?.map((item: any) => {
      if (myList.includes(item.id)) {
        total += Number(item['fee'][type]);
      }
      return total;
      // 对象拥有该属性
    });
    return total;
  };
  /*选中的选项*/
  const rowSelection: TableRowSelection<any> = {
    checkStrictly: false,
    onChange: (selectedRowKeys) => {
      props?.setMyList(selectedRowKeys);
    },
    selectedRowKeys: myList,
  };
  /*选中的总额*/
  const SelectTotal = fees
    ? countTotal(averageTypeMap[costAverageType], fees[0]?.subList)
    : '';
  return (
    <div>
      <Row>
        <Form.Item
          name={['costReconciliationFee', 'costReconciliationType']}
          noStyle
        >
          <Radio.Group style={{ marginBottom: 8 }} buttonStyle="solid">
            <Radio.Button value={1}>不纳入成本汇算</Radio.Button>
            <Radio.Button value={2}>纳入运单成本汇算</Radio.Button>
            <Radio.Button value={3}>仅纳入提单成本汇算</Radio.Button>
          </Radio.Group>
        </Form.Item>
      </Row>
      <Row>
        <Col>
          <Form.Item name={['costReconciliationFee', 'averageType']} noStyle>
            <Select
              style={{ marginRight: '5px' }}
              options={convertObjectToArray(averageType)}
            />
          </Form.Item>
          {/* <Button type={'primary'}>计算</Button>*/}
        </Col>
      </Row>
      {props?.costReconciliationType !== 1 && (
        <Table
          columns={
            props?.costReconciliationType === 2
              ? SharingColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: totalMoney,
                  costReconciliationType: props?.costReconciliationType,
                  myList: myList,
                  spaceFee: spaceFee,
                  currency: currency,
                })
              : SharingColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: props?.totalMoney,
                  myList: myList,
                  costReconciliationType: props?.costReconciliationType,
                  spaceFee: spaceFee,
                  currency: currency,
                }).filter((i: any) => i.title !== '运单号')
          }
          // @ts-ignore
          rowSelection={
            isDetail
              ? false
              : {
                  ...rowSelection,
                }
          }
          dataSource={fees}
          rowKey="id"
          expandable={{
            childrenColumnName: costReconciliationType === 2 ? 'subList' : 'x',
            defaultExpandAllRows: true,
          }}
        />
      )}
    </div>
  );
};
export default CostSharing;
