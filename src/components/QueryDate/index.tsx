import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Drawer, Space } from 'antd';
import { getLogListAPI } from '@/services/productAndPrice/api';
import dayjs from 'dayjs';

interface Props {
  btnText?: string;
  btnType?:
    | 'link'
    | 'text'
    | 'default'
    | 'primary'
    | 'dashed'
    | undefined;
  extraId?: string;
}
const QueryDate = ({ btnText, btnType, extraId }: Props) => {
  const [dataList, setDataList] = useState<any[]>([]); //日志列表
  const [open, setOpen] = useState(false);
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };

  /* 查询日志 */
  const getLog = async () => {
    try {
      const { status, data } = await getLogListAPI({
        extraId_1: extraId,
      });
      if (status) {
        setDataList(data?.list);
      }
    } catch (err) {
      console.error('获取日志失败', err);
    }
  };

  useEffect(() => {
    if (open) {
      getLog();
    }
  }, [open]);

  return (
    <>
      <Button type={btnType} onClick={showDrawer}>
        {btnText}
      </Button>
      <Drawer title="操作日志" placement="right" onClose={onClose} open={open}>
        <div >
          {dataList.map((item: any, index: number) => {
            return (
              <div key={index} className='mb20px'>
                <div className="flex">
                  <div>
                    <Space>
                      <Avatar
                        src={`https://static.kmfba.com/${item?.operatorAvatar}`}
                      />
                      <span>{item?.operatorName}</span>
                      <div className='ml-40px'>
                        <div className='color-coolGray'>{dayjs(item?.time).format('YYYY-MM-DDTHH:mm:ss')}</div>
                        <div>{item?.content}</div>
                      </div>
                    </Space>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Drawer>
    </>
  );
};
export default QueryDate;
