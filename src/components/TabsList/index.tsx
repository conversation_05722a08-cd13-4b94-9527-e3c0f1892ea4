import { useLocation, useSelectedRoutes, history } from '@umijs/max';
import styles from './index.less';
import classNames from 'classnames';
import { useEffect } from 'react';
import global from '@/models/global';
import { CloseOutlined } from '@ant-design/icons';
const TabsList = () => {
  const { pathname } = useLocation();
  const alocation = useLocation();
  const myRoutes = useSelectedRoutes();
  const { useTabList ,useDelTabList} = global;
  const { tabList, setTabList } = useTabList();
  const {setDelValue} = useDelTabList();
  useEffect(() => {
    if (pathname === '/home' || pathname === '/') return;
    const tabLists: any = myRoutes.filter((item: any) => {
      return item.pathname === pathname;
    });
    const hasMatchedItem = tabList?.some((item) => item.pathname === pathname);

    if (!hasMatchedItem) {
      setTabList([
        ...tabList,
        {
          pathname: tabLists[0].route.path,
          name: tabLists[0].route.name,
          state: alocation?.state,
        },
      ]);
    }
  }, [myRoutes]);

  const handleTabClick = (item: any) => {
    history.push(item.pathname, item.state);
  };

  return (
    <>
      {tabList && pathname !== '/' && pathname !== '/home' && (
        <nav className={styles.tab}>
          {tabList.map((item: any) => {
            return (
              <div
                className={classNames(
                  styles['tab-item'],
                  pathname === item.pathname ? styles.active : '',
                )}
                key={item.pathname}
                onClick={() => handleTabClick(item)}
              >
                {item.name}{' '}
                {pathname !== item.pathname && (
                  <CloseOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      e.nativeEvent.stopImmediatePropagation();
                      setDelValue(item.pathname);
                      const newTabList = tabList.filter(
                        (tabItem: any) => tabItem.pathname !== item.pathname,
                      );
                      console.log('item', item);
                      setTabList(newTabList);
                    }}
                    rev={undefined}
                  />
                )}
              </div>
            );
          })}
        </nav>
      )}
    </>
  );
};
export default TabsList;
