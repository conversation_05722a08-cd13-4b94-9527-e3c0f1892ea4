import { Button, List, Modal, Radio } from 'antd';
import { useEffect, useState } from 'react';
import service from '@/services/home';
const {
  getPartitionTemplateListAPI,
  getWeightTemplateListAPI,
  getAdditionalFeeListAPI,
  getPartitionPriceListAPI,
  getLongAndHeavyRuleListAPI
} = service.UserHome;
interface Props {
  btnText?: string;
  title?: string;
  onChange?: any;
  radioId?: string | number; // 选中的radio的id
}
const TemplateFrame = ({ btnText, title, onChange, radioId }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState<any>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  // /* 保存分区模版 */
  // const savePartitionTemplate = async (data: any) => {
  //   try {
  //     const { status } = await savePartitionTemplateAPI({
  //       name: data.name,
  //       id: data.id,
  //       zoneDetailList: data.zoneDetailList,
  //     });
  //     if (status) {
  //       message.success('保存成功');
  //       onChange(selectedItem);
  //       setIsModalOpen(false);
  //     }
  //   } catch (err) {
  //     console.error('保存分区模版抛出异常: ', err);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    onChange(selectedItem);
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleItemClick = (item: any) => {
    setSelectedItem(item);
  };
  /* 获取分区模版列表 */
  const getPartitionTemplateList = async () => {
    setLoading(true);
    try {
      const { status, data } = await getPartitionTemplateListAPI({});
      if (status) {
        setListData([...data]);
        /* 设置默认选中 */
        const defaultItem = data.find((item: any) => item.id === radioId);
        setSelectedItem(defaultItem);
      }
    } catch (err) {
      console.error('获取分区模版列表抛出异常: ', err);
    } finally {
      setLoading(false);
    }
  };
  /* 获取重量模版 */
  const getWeightTemplateList = async () => {
    setLoading(true);
    try {
      const { status, data } = await getWeightTemplateListAPI({});
      if (status) {
        setListData([...data]);
        /* 设置默认选中 */
        const defaultItem = data.find((item: any) => item.id === radioId);
        setSelectedItem(defaultItem);
      }
    } catch (err) {
      console.error('获取重量模版列表抛出异常: ', err);
    } finally {
      setLoading(false);
    }
  };
  /* 获取附加费模版 */
  const getAdditionalFeeList = async () => {
    setLoading(true);
    try {
      const { status, data } = await getAdditionalFeeListAPI({});
      if (status) {
        setListData([...data]);
        /* 设置默认选中 */
        const defaultItem = data.find((item: any) => item.id === radioId);
        setSelectedItem(defaultItem);
      }
    } catch (err) {
      console.error('获取附加费模版列表抛出异常: ', err);
    } finally {
      setLoading(false);
    }
  };
  /* 分区价格表 */
  const getPartitionPriceList = async () => {
    setLoading(true);
    try {
      const { status, data } = await getPartitionPriceListAPI({});
      if (status) {
        setListData([...data]);
        /* 设置默认选中 */
        const defaultItem = data.find((item: any) => item.id === radioId);
        setSelectedItem(defaultItem);
      }
    } catch (err) {
      console.error('获取分区价格表抛出异常: ', err);
    } finally {
      setLoading(false);
    }
  };

  /* 超长超重规则 */
  const getLongAndHeavyRuleList = async () => {
    setLoading(true);
    try {
      const { status, data } = await getLongAndHeavyRuleListAPI({});
      if (status) {
        setListData([...data]);
        /* 设置默认选中 */
        const defaultItem = data.find((item: any) => item.id === radioId);
        setSelectedItem(defaultItem);
      }
    } catch (err) {
      console.error('获取超长超重规则抛出异常: ', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (title === '选择分区模版' && isModalOpen) {
      getPartitionTemplateList();
    }
    if (title === '选择重量模版' && isModalOpen) {
      getWeightTemplateList();
    }
    if (title === '选择附加费模版' && isModalOpen) {
      getAdditionalFeeList();
    }
    if (title === '选择分区价格表' && isModalOpen) {
      getPartitionPriceList();
    }
    if (title === '选择超长超重规则' && isModalOpen) {
      getLongAndHeavyRuleList();
    }
  }, [isModalOpen]);
  return (
    <>
      <Button type="link" onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={title}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose={true}
      >
        <div>
          <List
            loading={loading}
            dataSource={listData}
            renderItem={(item: any) => (
              <List.Item
                key={item.id}
                onClick={() => handleItemClick(item)}
                style={{ cursor: 'pointer' }}
              >
                <Radio checked={selectedItem?.id === item.id} />
                {item.name || item.ruleName}
                <span style={{ marginLeft: 10, color: '#AEAEAE' }}>
                  {title === '选择分区模版' && item.code}
                  {title === '选择重量模版' && item.weightName}
                  {title === '选择附加费模版' && item.zoneName}
                  {title === '选择超长超重规则' && item.ruleType}
                </span>
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </>
  );
};
export default TemplateFrame;
