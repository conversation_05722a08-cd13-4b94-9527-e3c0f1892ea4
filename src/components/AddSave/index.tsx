import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, DatePicker, Drawer, message, Tag } from 'antd';

import { waybillListAPIList } from '@/services/preplanCabin';
import { appendSave } from '@/services/ConfiguredCar';
import { ProgressStateType } from '@/utils/constant';
import SuperTables from '@/components/SuperTables';
import { formatTime } from '@/utils/format';
import { storageListAPI } from '@/services/overview';

const { RangePicker } = DatePicker;

const AddSave = (props: any) => {
  const {
    onSelectWaybill,
    btnTitle,
    DefaultSelected,
    details,
    handleAdd,
    pieceIds,
    refresh,
  } = props;
  console.log('details', details);
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的运单信息
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  // 被选中的运单信息
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [total, setTotal] = useState({
    number: 0,
    weight: 0,
    squares: 0,
  });
  const actionRef = useRef<any>();
  /*选中项*/
  const [Loading, setLoading] = useState<any>(false);

  useEffect(() => {
    getTotal();
  }, [selectedRows?.length]);
  useEffect(() => {
    /*设置回显选中项目*/
    if (isModalOpen && DefaultSelected) {
      setSelectedRowKeys(DefaultSelected);
    }
  }, [isModalOpen]);
  const getTotal = () => {
    const pieceNum = selectedRows?.reduce(
      (pre: any, cur: any) => pre + cur?.pieceNum,
      0,
    );
    const weight = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.weight),
      0,
    );
    const squares = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.squares),
      0,
    );
    setTotal({
      number: pieceNum,
      weight: weight,
      squares: squares,
    });
  };

  const columns: any = [
    {
      title: '单号',
      dataIndex: 'no',
      width: 135,
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 135,
      fieldFormat: (value: any) =>
        ProgressStateType?.find((item: any) => item?.value == value?.state)
          ?.label,
      render: (_: any, recode: any) => {
        return (
          <Tag
            color={
              ProgressStateType?.find(
                (item: any) => item?.value == recode?.state,
              )?.color
            }
          >
            {
              ProgressStateType?.find(
                (item: any) => item?.value == recode?.state,
              )?.label
            }
          </Tag>
        );
      },
    },

    {
      title: '客户',
      dataIndex: 'clientName',
      width: 135,
    },
    {
      title: '件数',
      dataIndex: 'waybillNum',
      width: 135,
    },
    {
      title: '托数',
      dataIndex: 'palletsNum',
      width: 135,
    },
    {
      title: '库位',
      dataIndex: 'slotId',
      width: 135,
    },

    {
      title: '标识',
      dataIndex: 'dest',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 135,
      fieldFormat: (value: any) =>
        value?.createTime ? formatTime(value?.createTime) : '',
    },
    {
      title: '结束时间',
      dataIndex: 'completeTime',
      width: 135,
      fieldFormat: (value: any) =>
        value?.completeTime ? formatTime(value?.completeTime) : '',
    },
    /*    {
      title: '操作',
      key: 'option',
      // align: 'left',
      width: 120,
      fixed: 'right',
      // hideInSearch: true,
      render: (text: any, record: any) => {
        return [
          <LogDashboard extraId_1={record?.id} btnType="link" btnText="日志" />,
          <Popconfirm
            key="delete"
            title="作废"
            description="确认作废吗?"
            onConfirm={() => {
              discardStorage({ id: record?.id });
              actionRef?.current?.reload();
            }}
            onCancel={() => {
              message.info('已取消操作');
            }}
            okText="确认"
            cancelText="再想想"
          >
            <Button type={'link'} danger>
              作废
            </Button>
          </Popconfirm>,
        ];
      },
    },*/
  ];

  const showModal = () => {
    if (pieceIds.length === 0) {
      message.error('仓储的单据不能为空');
      return;
    }
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    console.log('check', checkData);
    const ids = checkData
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id);
    if (checkData.length > 1) {
      message.error('只能选择一个仓储单');
      return;
    }
    setLoading(true);
    const res = await appendSave({
      id: ids[0],
      pieceIds: pieceIds.map((item) => item.id).join(','),
    });
    if (res?.status) {
      setLoading(false);
      refresh();
      setIsModalOpen(false);
    } else {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const getList = async () => {
    return await waybillListAPIList({
      service_id: 'Waybill',
    });
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    // signInTime
  };
  return (
    <>
      <div onClick={showModal}>
        {/* <IconItem name="#icon-dizhibao" style={{ marginRight: '6px' }} /> */}
        {!btnTitle ? (
          <div>
            <Button type={'link'}>追加</Button>
          </div>
        ) : (
          <div
            style={{
              display: 'inline-block',
              cursor: 'pointer',
              position: 'absolute',
              top: '374px',
              right: '50px',
              zIndex: 999,
              //       top: '319px',
              // right: '50px',
              // z-index: 999
            }}
          >
            <a style={{ display: 'inline-block' }}>{btnTitle}</a>
          </div>
        )}
      </div>
      <Drawer
        title="选择仓储单"
        style={{
          width: '100vw',
          position: 'fixed',
          top: '1px',
          bottom: '1px',
          // marginBottom: '-1em',
          right: '1px',
          left: '1px',
          zIndex: 999999,
        }}
        destroyOnClose
        open={isModalOpen}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <div style={{ height: 'calc(100% - 40px)' }}>
          {/* <div style={{ marginLeft: '25px' }}>
            <span>件数：<span style={{ color: 'rgb(100, 64, 255)' }}>{total?.number}</span></span>
            <span style={{ margin: '0 20px' }}>重量：<span style={{ color: 'rgb(0, 186, 217)' }}>{total?.weight}</span></span>
            <span>放数：<span style={{ color: 'rgb(255, 64, 165)' }}>{total?.squares}</span></span>
          </div> */}
          <>
            <SuperTables
              instanceId="addSave"
              columns={columns}
              rowSelection={(value: any) => {
                setCheckData(value);
                console.log('value', value);
              }}
              isHideToolBar={false}
              warpHeight={300}
              request={async (params: any) => {
                const msg = await storageListAPI({
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  columnsFilter: params.columnsFilter || {},
                  condition: params.condition || {},
                });
                return {
                  data: msg?.data?.list || [],
                  success: msg.status,
                  total: msg?.data?.total,
                };
              }}
              filters={{
                keyword: {
                  type: 'search',
                  value: '',
                  termQuery: false,
                },
                clientId: {
                  desc: '客户',
                  type: 'client',
                  value: '',
                },
                state: {
                  desc: '状态',
                  type: 'select',
                  range: [
                    { label: '已上架', value: 0 },
                    { label: '已出库', value: 1 },
                  ],
                },
              }}
              /* toolBarRender={<Button
                   key="primary"
                   type="primary"
                   onClick={() => setIsModalOpen(true)}
                 >
                   新建规则
                 </Button>}*/
            />
          </>
        </div>
        <div
          key="btn"
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '40px',
            zIndex: '10',
          }}
        >
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={Loading}
            onClick={handleOk}
            style={{ marginLeft: 10 }}
          >
            确认
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default React.memo(AddSave);
