import { ProFormDateTimePicker } from '@ant-design/pro-components';
import { Form } from 'antd';
import { useMemo, useState, useEffect } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import TimeZoneSelection, { timezoneOptions } from '@/components/TimeZoneSelection';

dayjs.extend(utc);
dayjs.extend(timezone);

export type ProFormDateTimePickerTimeZoneProps = {
  name: string;
  label?: string;
  width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
  rules?: any[];
  placeholder?: string;
  disabled?: boolean;
  fieldProps?: any;
  [key: string]: any;
};



const ProFormDateTimePickerTimeZone = ({
  name,
  label,
  width,
  rules,
  placeholder,
  disabled,
  fieldProps,
  ...restProps
}: ProFormDateTimePickerTimeZoneProps) => {
  const [currentDateTimeValue, setCurrentDateTimeValue] = useState<any>();
  const form = Form.useFormInstance();
  const [currentTimezone, setCurrentTimezone] = useState<string>(
    dayjs.tz.guess() || 'Asia/Shanghai',
  );

  const timezoneObjName = useMemo(() => `${name}_timezoneObj`, [name]);

  const itemzons = useMemo(() => {
    return timezoneOptions.find((item) => item.value === currentTimezone);
  }, [currentTimezone]);

  // 更新时区对象信息
  const updateTimezoneObj = (value: any) => {
    if (!value) return;
    
    const timestamp = value.valueOf();
    const formattedDate = value.format('YYYY-MM-DD HH:mm:ss');
    const beijingDate = dayjs(value).tz('Asia/Shanghai');
    const beijingTimestamp = beijingDate.valueOf();
    const beijingFormattedDate = beijingDate.format('YYYY-MM-DD HH:mm:ss');
    
    form?.setFieldsValue({
      [timezoneObjName]: {
        [name]: value,
        time: formattedDate,
        timestamp: timestamp,
        beijingTime: beijingFormattedDate,
        beijingTimestamp: beijingTimestamp,
        timezone: value?.$x?.$timezone || currentTimezone,
      },
    });
  };

  const handleDateChange = (value: any) => {
    if (!value) return;
    
    const tzDate = dayjs(value).tz(currentTimezone);
    
    form?.setFieldsValue({ [name]: tzDate });
    
    updateTimezoneObj(tzDate);
    
    setCurrentDateTimeValue(tzDate);
  };

  const handleTimezoneObject = (tzDateObj: any) => {
    if (!tzDateObj) return;
    
    const newTimezone = tzDateObj?.$x?.$timezone;
    if (newTimezone) {
      setCurrentTimezone(newTimezone);
    }
    
    form?.setFieldsValue({ [name]: tzDateObj });
    
    updateTimezoneObj(tzDateObj);
    
    setCurrentDateTimeValue(tzDateObj);
  };

  // 初始化和监听表单变化
  useEffect(() => {
    const currentValue = form.getFieldValue(name);
    if (currentValue && !currentDateTimeValue) {
      const tzDate = dayjs(currentValue).tz(currentTimezone);
      setCurrentDateTimeValue(tzDate);
      updateTimezoneObj(tzDate);
    }
  }, [form, name, currentTimezone, currentDateTimeValue]);

  return (
    <>
      <ProFormDateTimePicker
        name={name}
        label={label}
        width={width}
        rules={rules}
        placeholder={placeholder}
        disabled={disabled}
        fieldProps={{
          ...fieldProps,
          suffixIcon: (
            <div>
              {itemzons?.label || '逻辑错误'}
            </div>
          ),
          onChange: handleDateChange,
          renderExtraFooter: () => {
            return (
              <TimeZoneSelection
                onChange={(tzValue: any) => {
                  handleTimezoneObject(tzValue);
                }}
                value={currentDateTimeValue || form.getFieldValue(name)}
              />
            );
          },
        }}
        {...restProps}
      />
      <div style={{ display: 'none' }}>
        <ProFormDateTimePicker name={timezoneObjName} />
      </div>
    </>
  );
};

export default ProFormDateTimePickerTimeZone;
