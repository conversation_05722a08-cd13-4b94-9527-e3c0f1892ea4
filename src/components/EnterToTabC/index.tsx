import React, { useRef, useEffect } from 'react';

const EnhancedNavigation = ({ children }:any) => {
  const containerRef = useRef(null);

  useEffect(() => {
    const getFocusableElements = () => {
      const containers = containerRef.current;
      if (containers) {
        return Array.from(containers.querySelectorAll(
          'input, select, textarea, button, [tabindex]:not([tabindex="-1"])'
        ));
      }
      return [];
    };

    const focusElement = (element:any) => {
      if (element.tagName.toLowerCase() === 'button') {
        const innerButton = element.querySelector('.ant-btn-inner');
        if (innerButton) {
          innerButton?.focus?.();
          innerButton?.select?.();
        } else {
          element?.focus?.();
          element?.select?.();
        }
      } else {
        element?.focus?.();
        element?.select?.();
      }
    };

    const handleKeyDown = (e) => {
      const focusableElements = getFocusableElements();
      const currentElement = document.activeElement;
      const currentIndex = focusableElements.indexOf(currentElement);

      if (currentIndex === -1) return;

      let nextIndex;

      switch (e.key) {
        case 'Enter':
        case 'ArrowRight':
        case 'ArrowDown':
          e.preventDefault();
          nextIndex = (currentIndex + 1) % focusableElements.length;
          break;
        case 'ArrowLeft':
        case 'ArrowUp':
          e.preventDefault();
          nextIndex = (currentIndex - 1 + focusableElements.length) % focusableElements.length;
          break;
        default:
          return;
      }

      focusElement(focusableElements[nextIndex]);
    };

    const containers = containerRef.current;
    if (containers) {
      containers.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      if (containers) {
        containers.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, []);

  return <div ref={containerRef}>{children}</div>;
};

export default EnhancedNavigation;