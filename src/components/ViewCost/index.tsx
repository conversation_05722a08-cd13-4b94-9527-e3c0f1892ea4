import {
  ModalForm,
  // ProFormSelect,
} from '@ant-design/pro-components';
import { Button, Form } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import CostSharing from './CostSharing';

import { getCostDetailOfBill } from '@/services/financeApi';
import { costReconciliationType as costType } from '@/utils/constant';

const ViewCost = (props: any) => {
  const type = props?.type || 'primary';
  const title = props?.title || '添加费用';
  /*平摊类型*/
  const {
    costReconciliationType,
    counterpartyType,
    costReconciliationTypeDesc,
  } = props?.row;
  console.log('row', props?.row);
  const id = props?.id;
  /*标题名称*/
  const TopTitle = props?.TopTitle || title;

  /*是否是详情*/
  const isDetail = !!props.id;

  const [open, setOpen] = useState<any>(false);

  /*详情的值*/
  const [dataBase, setDataBase] = useState<any>();
  /*获取详情*/
  const getDetail = async () => {
    /*处理各种类型数据的层级*/
    const res = await getCostDetailOfBill({
      billId: id,
    });
    if (res?.status) {
      let data = [];
      console.log('counterpartyType', counterpartyType);
      /*报关的三层特殊处理*/
      if (counterpartyType === 'Declaration') {
        data = res?.data?.blnoCostList.map((i: any) => {
          if (costReconciliationType === 2) {
            return {
              ...i,
              subList: i.declarationCostList.map((item: any) => {
                return { ...item, subList: item?.waybillList };
              }),
            };
          } else {
            return i;
          }
        });
      } else if (
        counterpartyType === 'TailChannel' ||
        counterpartyType === 'TransChannel'
      ) {
        console.log('尾巴', res?.data);
        /*尾程和卖货是一层结构*/
        data = res?.data?.waybillCostList;
      } else {
        /*正常情况下*/
        if (costReconciliationType === 2) {
          data = res?.data?.blnoCostList.map((i: any) => {
            return { ...i, subList: res?.data?.waybillCostList };
          });
        } else {
          data = res?.data?.blnoCostList;
        }
      }
      console.log('data', data);
      setDataBase(data);
    }
  };
  useEffect(() => {
    if (open) {
      getDetail();
    }
  }, [open]);

  return (
    <ModalForm
      title={TopTitle}
      trigger={
        <Button type={type} ghost={props?.ghost}>
          {title}
        </Button>
      }
      width={1200}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 8 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      className={styles.wrap}
      layout="horizontal"
      submitTimeout={2000}
      onOpenChange={() => setOpen(!open)}
    >
      <div>
        <div>平摊类型：{costReconciliationTypeDesc}</div>
        <CostSharing
          fees={dataBase}
          isDetail={isDetail}
          counterpartyType={counterpartyType}
          billId={id}
          getDetail={getDetail}
          costReconciliationType={costReconciliationType}
        />
      </div>
    </ModalForm>
  );
};
export default ViewCost;
