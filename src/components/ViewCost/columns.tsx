/*成本分摊字段*/
import { ProFormText } from '@ant-design/pro-components';
import React from 'react';
import { averageTypeMap } from '@/utils/constant';

const SharingColumns: any = (props: any) => {
  console.log('币种', props);
  return [
    {
      title: '主提单号',
      dataIndex: 'blno',
      key: 'blno',
    },
    {
      title: '运单号',
      dataIndex: ['waybillNO'],
      key: 'waybillNO',
    },
    {
      title: '件数',
      dataIndex: ['pieceNum'],
      key: 'pieceNum',
    },
    {
      title: '收货计费重',
      dataIndex: ['signInChargeWeight'],
      key: 'signInChargeWeight',
    },
    {
      title: '出货总体积',
      dataIndex: ['outerVolume'],
      key: 'outerVolume',
    },
    {
      title: '出货实重',
      dataIndex: ['outerActualWeight'],
      key: 'outerActualWeight',
    },
    {
      title: '预计均摊金额',
      dataIndex: ['amount'],
      key: 'amount',
      /*     render: (_: any, record: any) => {
        return (record?.amount * record?.currentRate).toFixed(2);
      },*/
    },
    {
      title: '币种',
      dataIndex: ['currencyTypeDesc'],
      key: 'currencyTypeDesc',
      /*     render: (_: any, record: any) => {
        return (record?.amount * record?.currentRate).toFixed(2);
      },*/
    },
    {
      title: '汇率',
      dataIndex: ['currentRate'],
      key: 'currentRate',
      /*     render: (_: any, record: any) => {
        return (record?.amount * record?.currentRate).toFixed(2);
      },*/
    },
    {
      title: '本币金额',
      dataIndex: ['rmbAmount'],
      key: 'rmbAmount',
      /*     render: (_: any, record: any) => {
        return (record?.amount * record?.currentRate).toFixed(2);
      },*/
    },
  ];
};

/*尾程或者卖货的一层结构*/
const OtherColumns: any = (props: any) => {
  //console.log(props);
  return [
    {
      title: '运单号',
      dataIndex: ['waybillNo'],
      key: 'waybillId',
    },
    {
      title: '件数',
      dataIndex: ['pcs'],
      key: 'address',
    },
    {
      title: '收货计费重',
      dataIndex: ['chargeWeight'],
      key: 'address',
    },
    {
      title: '出货总体积',
      dataIndex: ['volume'],
      key: 'address',
    },
    {
      title: '出货实重',
      dataIndex: ['actualWeight'],
      key: 'address',
    },
    {
      title: '纳入成本金额/元',
      dataIndex: ['amount'],
      key: 'amount',
    },
  ];
};

export { SharingColumns, OtherColumns };
