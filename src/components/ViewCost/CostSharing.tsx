/*成本分摊*/
import { TableRowSelection } from 'antd/es/table/interface';
import { Button, Col, message, Modal, Row, Table } from 'antd';
import { SharingColumns } from './columns';
import React from 'react';
const { confirm } = Modal;

import { cancelCostDividedOfBill } from '@/services/financeApi';

const CostSharing = (props: any) => {
  const { isDetail, fees, myList, totalMoney, billId, getDetail } = props;
  /*选中的选项*/
  const rowSelection: TableRowSelection<any> = {
    checkStrictly: false,
    onChange: (selectedRowKeys) => {
      props?.setMyList(selectedRowKeys);
    },
    selectedRowKeys: myList,
  };
  console.log('fees', fees);
  /*撤销成本*/
  const handlerCancel = async () => {
    const res = await cancelCostDividedOfBill({ billId: billId });
    if (res?.status) {
      message.success('撤销成功');
      getDetail();
    }
  };
  return (
    <div>
      <Row style={{ marginBottom: 10 }}>
        <Col span={21}></Col>
        <Col span={3}>
          {fees?.length > 0 && (
            <Button
              type={'primary'}
              onClick={() => {
                confirm({
                  title: '是否撤销全部费用',
                  content: '撤销后默认变成不分摊费用',
                  onOk() {
                    handlerCancel();
                  },
                  onCancel() {
                    message.info('已取消');
                  },
                });
              }}
            >
              撤销全部成本
            </Button>
          )}
        </Col>
      </Row>
      {/*结构类型*/}
      {props?.costReconciliationType !== 1 && (
        <Table
          columns={
            props?.costReconciliationType === 2
              ? SharingColumns({
                  type: props?.costAverageType,
                  totalMoney: totalMoney,
                  costReconciliationType: props?.costReconciliationType,
                  myList: myList,
                })
              : SharingColumns({
                  type: props?.costAverageType,
                  totalMoney: props?.totalMoney,
                  myList: myList,
                  costReconciliationType: props?.costReconciliationType,
                }).filter((i: any) => i.title !== '运单号')
          }
          // @ts-ignore
          rowSelection={
            isDetail
              ? false
              : {
                  ...rowSelection,
                }
          }
          dataSource={fees}
          rowKey="id"
          expandable={{
            childrenColumnName: 'subList',
            defaultExpandAllRows: true,
          }}
        />
      )}
    </div>
  );
};
export default CostSharing;
