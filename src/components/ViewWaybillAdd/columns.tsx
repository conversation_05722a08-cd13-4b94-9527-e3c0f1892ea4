/*成本分摊字段*/
import { ProFormText } from '@ant-design/pro-components';
import React from 'react';
import { averageTypeMap } from '@/utils/constant';
import { Avatar, Space } from 'antd';
import { AVATA_URL } from '@/shared/Enumeration';
import { formatTime } from '@/utils/format';

/*运单加收字段*/
const WaybillSurchargeColumns: any = (props: any) => {
  const { SelectTotal, type, totalMoney, myAddList } = props;
  console.log('totalMoney', totalMoney);
  return [
    {
      title: '流水号',
      dataIndex: 'id',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '客户',
      dataIndex: 'counterpartyName',
      width: 120,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '账目',
      dataIndex: 'entityTypeDesc',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '账目类型',
      dataIndex: 'reason',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'writeOffStateDesc',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '应收费用/元',
      dataIndex: 'amount',
      width: 120,
      hideInSearch: true,
      render: (_: any, record: any) => {
        return (record?.amount * record?.currentRate).toFixed(2);
      },
    },
    {
      title: '已收款/元',
      dataIndex: 'writeOffAmount',
      width: 120,
      hideInSearch: true,
      render: (_: any, record: any) => {
        return (record?.writeOffAmount * record?.currentRate).toFixed(2);
      },
    },
    {
      title: '待核销/元',
      dataIndex: 'unpaidAmount',
      width: 120,
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          (Number(record.amount) - Number(record.writeOffAmount)) *
          record?.currentRate
        ).toFixed(2);
      },
    },
    {
      title: '关联ID-1',
      dataIndex: 'extraId_1',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '关联ID-2',
      dataIndex: 'extraId_2',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '关联ID-3',
      dataIndex: 'extraId_3',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'createUserInfo',
      width: 200,
      hideInSearch: true,
      render: (text: any, record: any) => {
        return (
          <Space>
            {record?.createUserInfo ? (
              <Avatar
                src={
                  record?.createUserInfo?.avatar?.includes('http')
                    ? record?.createUserInfo?.avatar
                    : `${AVATA_URL}${record?.createUserInfo?.avatar}`
                }
              />
            ) : (
              ''
            )}
            <span>{record?.createUserInfo?.name}</span>
          </Space>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'time',
      width: 200,
      hideInSearch: true,
      render: (text: any) => {
        return text ? formatTime(text) : '';
      },
    },
    {
      title: '结清时间',
      dataIndex: 'writeOffTime',
      width: 200,
      hideInSearch: true,
      render: (text: any) => {
        return text ? formatTime(text) : '';
      },
    },
  ];
};
export { WaybillSurchargeColumns };
