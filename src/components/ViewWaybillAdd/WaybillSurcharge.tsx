/*运单加收*/
import { Button, Col, Form, Input, Radio, Row, Select, Table } from 'antd';
import { WaybillSurchargeColumns } from './columns';
import React from 'react';
import { averageType, convertObjectToArray } from '@/utils/constant';

const WaybillSurcharge = (props: any) => {
  const { isDetail, totalMoney, myAddList, addAverageType, addfees, row } =
    props;
  console.log('addfees', addfees);
  console.log('row', row?.addedWaybillState);
  return (
    <div>
      <div style={{ marginBottom: '10px' }}>
        {row?.addedWaybillState === 0 && <div>类型：不加收</div>}
        {row?.addedWaybillState === 1 && <div>类型：加收运单</div>}
      </div>
      {row?.addedWaybillState === 1 && (
        <Table
          columns={WaybillSurchargeColumns({
            type: addAverageType,
            totalMoney: totalMoney,
            myAddList: myAddList,
          }).filter((i: any) => i.title !== '运单号')}
          // @ts-ignore
          rowSelection={
            isDetail
              ? false
              : {
                  onChange: (selectedRowKeys) => {
                    console.log(selectedRowKeys);
                    props?.setMyAddList(selectedRowKeys);
                  },
                  selectedRowKeys: props?.myAddList,
                }
          }
          rowKey={'waybillId'}
          dataSource={props?.addfees}
          scroll={{ x: 1200 }}
        />
      )}
    </div>
  );
};
export default WaybillSurcharge;
