import {
  ModalForm,
  // ProFormSelect,
} from '@ant-design/pro-components';
import { Button, Form, Tabs } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { showAddFeePreViews, showFeePreViews } from '@/services/bol';
import CostSharing from './CostSharing';

import {
  getAdditionBillToWaybillOfBill,
  getBillDetailById,
} from '@/services/financeApi';
import WaybillSurcharge from './WaybillSurcharge';

const ViewWaybillAdd = (props: any) => {
  const type = props?.type || 'primary';
  const title = props?.title || '添加费用';
  const id = props?.id;
  const row = props.row;
  /*标题名称*/
  const TopTitle = props?.TopTitle || title;
  /*费用类型*/
  const counterpartyType = props?.types;

  /*是否是详情*/
  const isDetail = !!props.id;

  const [open, setOpen] = useState<any>(false);
  /*成本分摊的值*/
  const [fees, setFees] = useState<any>();
  /*运单加收的值*/
  const [addfees, setAddFees] = useState<any>();
  /*加收原始值*/
  const [originalData, setOriginalData] = useState<any>();

  /*1成分分摊2运单加收*/
  const [waybillType, setWaybillType] = useState<string>('1');
  /*均摊选中项*/
  const [myList, setMyList] = useState<any>([]);
  const [form] = Form.useForm<any>();
  /*运单加收 运单是否加收*/
  const isAddedWaybillFee = Form.useWatch(
    ['addedWaybillFee', 'addedWaybillState'],
    form,
  );
  /*成本汇算 分摊类型*/
  const costAverageType = Form.useWatch(
    ['costReconciliationFee', 'averageType'],
    form,
  );
  /*运单加收 分摊类型*/
  const addAverageType = Form.useWatch(
    ['addedWaybillFee', 'averageType'],
    form,
  );
  /*成本汇算 成本汇算类型*/
  const costReconciliationType = Form.useWatch(
    ['costReconciliationFee', 'costReconciliationType'],
    form,
  );
  /*预算金额*/
  const totalMoney = Form.useWatch(['spaceFee', 'estimateFee'], form);

  /*详情的值*/
  const [dataBase, setDataBase] = useState<any>();
  /*获取详情*/
  const getDetail = async () => {
    const res = await getAdditionBillToWaybillOfBill({
      billId: id,
    });
    if (res?.status) {
      console.log('data', res?.data);
      setDataBase(res?.data?.list);
    }
  };
  useEffect(() => {
    if (open) {
      getDetail();
    }
  }, [open]);

  return (
    <ModalForm
      title={TopTitle}
      trigger={
        <Button type={type} ghost={props?.ghost}>
          {title}
        </Button>
      }
      form={form}
      initialValues={{
        costReconciliationFee: {
          costReconciliationType: 1,
          averageType: '1',
        },
        addedWaybillFee: { addedWaybillState: 0, averageType: '1' },
        spaceFee: { currency: '10', rate: '1' },
      }}
      width={1200}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 8 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      className={styles.wrap}
      layout="horizontal"
      submitTimeout={2000}
      onOpenChange={() => setOpen(!open)}
    >
      <div>
        <WaybillSurcharge
          isAddedWaybillFee={isAddedWaybillFee}
          addfees={dataBase}
          isDetail={isDetail}
          totalMoney={totalMoney}
          addAverageType={addAverageType}
          row={row}
        />
      </div>
    </ModalForm>
  );
};
export default ViewWaybillAdd;
