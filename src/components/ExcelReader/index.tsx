import { Button, message } from 'antd';
import { useRef } from 'react';
import * as XLSX from 'xlsx';

function ExcelReader({
  onChange,
  btnText,
  btnType,
  disabled,
  children,
  upType,
  Readertype,
  multiple = false,
  selectRow,
}: any) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // 验证文件类型（如果不是Readertype且不是all类型）
    if (!Readertype && upType !== 'all') {
      const invalidFiles = Array.from(files).filter(
        (file) =>
          file.type !==
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );

      if (invalidFiles.length > 0) {
        message.error('请上传excel文件');
        return;
      }
    }

    if (Readertype) {
      // 直接上传文件模式
      if (multiple) {
        onChange(null, files); // 多文件传递FileList
      } else {
        onChange(null, files[0]); // 单文件传递单个文件对象
      }
    } else {
      // 解析Excel模式
      if (multiple) {
        const promises = Array.from(files).map((file) => {
          return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              const binaryString = e.target?.result as string;
              const workbook = XLSX.read(binaryString, { type: 'binary' });
              const worksheet = workbook.Sheets[workbook.SheetNames[0]];
              const newData = XLSX.utils.sheet_to_json(worksheet);
              resolve({ data: newData, file });
            };
            reader.readAsBinaryString(file);
          });
        });

        Promise.all(promises).then((results) => {
          onChange(results, files);
        });
      } else {
        const file = files[0];
        const reader = new FileReader();
        reader.onload = (e) => {
          const binaryString = e.target?.result as string;
          const workbook = XLSX.read(binaryString, { type: 'binary' });
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const newData = XLSX.utils.sheet_to_json(worksheet);
          onChange(newData, file);
        };
        reader.readAsBinaryString(file);
      }
    }

    // 清空input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (selectRow && multiple) {
      if (!selectRow?.length) return message.error('请勾选');
    }
    fileInputRef.current?.click();
  };

  return (
    <div>
      {children ? (
        <div onClick={handleClick}>{children}</div>
      ) : (
        <Button type={btnType} disabled={disabled} onClick={handleClick}>
          {btnText}
        </Button>
      )}
      <input
        type="file"
        onChange={handleFile}
        ref={fileInputRef}
        style={{ display: 'none' }}
        multiple={multiple}
        accept={
          !Readertype && upType !== 'all'
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : undefined
        }
      />
    </div>
  );
}

export default ExcelReader;
