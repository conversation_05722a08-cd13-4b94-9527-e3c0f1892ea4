import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Modal,
  Pagination,
  Space,
  Tabs,
  message,
  Table,
  DatePicker,
  Select,
  Tag,
  Typography,
  Drawer,
} from 'antd';
import {
  ActionType,
  CheckCard,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import service from '@/services/home';
import styles from '../index.less';
import classNames from 'classnames';
import IconItem from '@/components/Icon';
import { getWaybillListAPI } from '@/services/carriers';
const { Paragraph, Text } = Typography;
import { generateTags } from '@/utils/assembly';
import {
  convertObjectToArray,
  interceptFlagMap,
  waybillStateMap,
  waybillStatusEnum,
} from '@/utils/constant';
import { SwapRightOutlined } from '@ant-design/icons';
import Qs from 'qs';
import {
  getWarehouseListAPI,
  waybillListAPI,
  waybillListAPIList,
} from '@/services/preplanCabin';
import MySearch from '@/components/MyProTable/MySearch';
import MySelect from '@/components/MyProTable/MySelect';
import { formatTime } from '@/utils/format';
import { getProductList } from '@/services/productAndPrice/api';
import MrTable, { textEllipsis } from '../MrTable';
const { RangePicker } = DatePicker;

const SelectWaybill = (props: any) => {
  const {
    onSelectWaybill,
    btnTitle,
    channelId,
    type,
    allocationType,
    DefaultSelected,
    isInlandTransfer,
    innerIntermodalFormId,
  } = props;
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的运单信息
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  // 被选中的运单信息
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [total, setTotal] = useState({
    number: 0,
    weight: 0,
    squares: 0,
  });
  const actionRef = useRef<any>();
  //内陆转运运单状态状态
  const InWaybillStateMap: any = {
    20: '已签入',
    40: '已确认',
    59: '部分配舱',
    60: '已配舱',
  };
  const inState = convertObjectToArray(InWaybillStateMap);
  const rowSelection: any = {
    type: type ? type : 'checkbox',
    selectedRowKeys,
    onChange: (keys: any, row: any) => {
      console.log('keys: ', keys);
      setSelectedRowKeys(keys);
      setSelectedRows(row);
    },
  };
  useEffect(() => {
    getTotal();
  }, [selectedRows?.length]);
  useEffect(() => {
    /*设置回显选中项目*/
    if (isModalOpen && DefaultSelected) {
      setSelectedRowKeys(DefaultSelected);
    }
  }, [isModalOpen]);
  const getTotal = () => {
    const pieceNum = selectedRows?.reduce(
      (pre: any, cur: any) => pre + cur?.pieceNum,
      0,
    );
    const weight = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.weight),
      0,
    );
    const squares = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.squares),
      0,
    );
    setTotal({
      number: pieceNum,
      weight: weight,
      squares: squares,
    });
  };
  /* 获取常用地址库地址 */
  const columns: any = [
    //序号
    {
      dataIndex: 'keyword',
      hideInTable: true,
      width: 700,
      renderFormItem: () => {
        return (
          <MySearch
            placeholder="请输入客户代码、运单号、收件地址等"
            allowClear
            enterButton="搜索"
            size="large"
            style={{ width: '380px', background: '#FBFBFB' }}
          />
        );
      },
    },
    {
      title: '签入时间',
      dataIndex: 'signInTime',
      hideInTable: true,
      renderFormItem: () => {
        return <RangePicker />;
      },
    },
    {
      title: '当前所在仓',
      dataIndex: 'currentWarehouse',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="warehouse"
            showSearch
            // rules={[{ required: true, message: '请输入站点' }]}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 12 }}
            request={async ({ keyWords }) => {
              const { status, data } = await getWarehouseListAPI({
                type: 0,
                len: 20,
                keyword: keyWords,
              });
              if (status) {
                return data.list.map((item: any) => {
                  return {
                    label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
                    value: item.id,
                  };
                });
              }
              return [];
            }}
            fieldProps={{
              onChange: (e, option) => {
                // setAddress(option?.label);
              },
              filterOption: false,
            }}
          ></ProFormSelect>
        );
      },
    },
    {
      title: '状态',
      hideInTable: true,
      dataIndex: 'states',
      renderFormItem: () => {
        return (
          <Select
            placeholder="请输入"
            allowClear
            mode="multiple"
            size="middle"
            options={
              isInlandTransfer ? inState : convertObjectToArray(waybillStateMap)
            }
            style={{ width: '336px', background: '#FBFBFB' }}
          />
        );
      },
    },
    {
      title: '销售产品',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="product"
            showSearch
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 12 }}
            request={async ({ keyWords }) => {
              const { status, data } = await getProductList({
                type: 0,
                len: 20,
                keyword: keyWords,
              });
              if (status) {
                return data.list.map((item: any) => {
                  return {
                    label: item?.name,
                    value: item?.id,
                  };
                });
              }
              return [];
            }}
            fieldProps={{
              filterOption: false,
            }}
          />
        );
      },
    },

    // {
    //   title: '基础信息',
    //   hideInSearch: true,
    //   children: [
    {
      title: '航程',
      dataIndex: 'tag',
      width: 250,
      render: (recode: any, row: any) => {
        return (
          <div>
            <span>
              {row?.recipientAddressDetail?.isFBA ? generateTags('FBA') : ''}
            </span>
            {row.senderAddressDetail?.city}
            <SwapRightOutlined
              style={{
                color: '#4071FF',
                margin: '0 10px',
                fontSize: '16px',
              }}
            />
            {row.recipientAddressDetail?.city}
          </div>
        );
      },
    },
    {
      title: '运单号',
      dataIndex: 'waybillNo',
      hideInSearch: true,
      width: 140,
      filter: {
        sort: 0,
      },
      render: (_: any, _text: any) => {
        return <Text copyable>{_text?.waybillNo}</Text>;
      },
    },
    //   ],
    // },
    // {
    //   title: '状态/阶段',
    //   hideInSearch: true,
    //   children: [
    {
      title: '运单状态',
      dataIndex: 'state',
      hideInSearch: true,
      width: 100,
      filter: {
        sort: 0,
      },
      render: (_: any, record: any) => {
        return (
          <Tag color={waybillStatusEnum[record.state].color}>
            {waybillStatusEnum[record.state].text}
          </Tag>
        );
      },
    },
    {
      title: '扣件',
      dataIndex: 'holdFlag',
      hideInSearch: true,
      width: 100,
      filter: {
        sort: 0,
      },
      render: (text: any, record: any) => {
        return (
          <>
            <div>{record?.holdFlag ? <Tag color="red">扣件</Tag> : '-'}</div>
            <div className="text-12px">{record?.holdReason}</div>
          </>
        );
      },
    },
    {
      title: '拦截',
      dataIndex: 'interceptFlag',
      hideInSearch: true,
      width: 60,
      filter: {
        sort: 0,
      },
      render: (_: any, _text: any) => {
        return _text?.interceptFlag
          ? interceptFlagMap[_text?.interceptFlag]
          : '--';
      },
    },
    //   ],
    // },

    // {
    //   title: '客户和产品',
    //   hideInSearch: true,
    //   children: [
    {
      title: '客户',
      dataIndex: 'clientName',
      hideInSearch: true,
      width: 200,
      filter: {
        sort: 0,
      },
      render: (_: any, _text: any) => {
        return (
          <div>
            {_text?.client?.name}-{_text?.client?.code}
          </div>
        );
      },
    },
    {
      title: '客户单号',
      dataIndex: 'outerOrderId',
      hideInSearch: true,
      width: 160,
      filter: {
        sort: 0,
      },
      render: (_: any, text: any) => textEllipsis(text?.outerOrderId),
    },
    {
      title: '运输方案',
      dataIndex: 'productName',
      hideInSearch: true,
      width: 200,
      filter: {
        sort: 0,
      },
      render: (_: any, text: any) => {
        return textEllipsis(text?.productName);
      },
    },
    //   ],
    // },
    // {
    //   title: '目的地',
    //   hideInSearch: true,
    //   children: [
    {
      title: '目的国',
      dataIndex: ['recipientAddressDetail', 'country'],
      hideInSearch: true,
      width: 100,
    },
    {
      title: '目的地',
      dataIndex: 'recipientAddressDetail',
      hideInSearch: true,
      width: 300,
      render: (_: any, _text: any) => {
        return (
          <div>
            {_text?.recipientAddressDetail?.city}-
            {_text?.recipientAddressDetail?.street}
          </div>
        );
      },
    },
    {
      title: '目的邮编',
      dataIndex: ['recipientAddressDetail', 'zipCode'],
      hideInSearch: true,
      width: 100,
    },
    {
      title: '仓库代码',
      dataIndex: 'recipientFbaCode',
      width: 150,
      filter: {
        condition: {
          type: 'search',
          value: '',
        },
        sort: 0, //0,1,-1
      },
      hideInSearch: true,
      render: (_: any, recode: any) => {
        if (recode?.recipientAddressDetail?.isFBA == 1) {
          return recode?.recipientAddressDetail?.fbaCode;
        }
      },
    },
    //   ],
    // },
    // {
    //   title: '仓库',
    //   hideInSearch: true,
    //   children: [
    {
      title: '当前所在仓',
      dataIndex: 'currentWarehouseDetail',
      width: 200,
      render: (_: any, _text: any) => {
        return (
          <Text
            ellipsis={{
              tooltip: `${_text?.currentWarehouseDetail?.province}-${_text?.currentWarehouseDetail?.city}-${_text?.currentWarehouseDetail?.street}`,
            }}
          >
            {_text?.currentWarehouseDetail?.name}
          </Text>
        );
      },
    },
    {
      title: '服务标签',
      dataIndex: 'tagStr',
      filter: {
        condition: {
          type: 'search',
          value: '',
        },
        sort: 0,
      },
      width: 150,
      hideInSearch: true,
      // render: (_: any, recode: any) => {
      //   return <Tag color={goodsTypeEnu[recode?.goodsType]?.color}>{goodsTypeEnu[recode?.goodsType]?.text}</Tag>
      // }
    },
    //   ],
    // },
    // {
    //   title: '轨迹',
    //   hideInSearch: true,
    //   children: [
    {
      title: '最新轨迹内容',
      dataIndex: ['lastestTrack', 'content'],
      hideInSearch: true,
      width: 200,
    },
    //   ],
    // },
    // {
    //   title: '负责人',
    //   hideInSearch: true,
    //   children: [
    {
      title: '业务员',
      dataIndex: 'salesManInfo',
      hideInSearch: true,
      width: 150,
      align: ' center',
      render: (_: any, text: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-evenly',
            }}
          >
            <img
              style={{ width: 30, height: 30, borderRadius: '50%' }}
              src={`https://static.kmfba.com/${text.salesManInfo?.avatar}`}
            />
            <span>{text?.salesManInfo?.name}</span>
          </div>
        );
      },
    },
    {
      title: '客服',
      dataIndex: 'customerServiceInfo',
      hideInSearch: true,
      width: 150,
      align: ' center',
      render: (_: any, text: any) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-evenly',
            }}
          >
            <img
              style={{ width: 30, height: 30, borderRadius: '50%' }}
              src={`https://static.kmfba.com/${text?.customerServiceInfo?.avatar}`}
            />
            <span>{text?.customerServiceInfo?.name}</span>
          </div>
        );
      },
    },
    //   ],
    // },
    // {
    //   title: '件毛体',
    //   hideInSearch: true,
    //   children: [
    {
      title: '件数',
      dataIndex: 'pieceNum',
      hideInSearch: true,
      width: 60,
    },
    {
      title: '收货计费重/kg',
      dataIndex: 'signChargeWeight',
      hideInSearch: true,
      width: 60,
    },
    {
      title: '收货实重/kg',
      dataIndex: 'signActualWeight',
      hideInSearch: true,
      width: 60,
    },
    {
      title: '收货立方/m³',
      dataIndex: 'signVolume',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '重货比',
      dataIndex: 'heavyCargoRatio',
      hideInSearch: true,
      width: 80,
    },
    {
      title: 'ups密度',
      dataIndex: 'upsDensity',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '出货实重/kg',
      dataIndex: 'outerActualWeight',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '出货立方/m³',
      dataIndex: 'outerVolume',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '打单重量/kg',
      dataIndex: 'orderActualWeight',
      hideInSearch: true,
      width: 80,
    },
    //   ],
    // },
    // {
    //   title: '其他',
    //   hideInSearch: true,
    //   elipsis: true,
    //   children: [
    {
      title: '备注',
      dataIndex: 'comment',
      hideInSearch: true,
      width: 200,
      render: (_: any, text: any) => {
        return (
          <Paragraph
            ellipsis={{
              tooltip: `${text?.comment?.content}`,
            }}
            style={{
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            {text?.comment?.content}
          </Paragraph>
        );
      },
    },
    {
      title: '下单时间',
      dataIndex: 'commitTime',
      hideInSearch: true,
      width: 300,
      render: (_: any, text: any) => {
        return <span>{formatTime(text?.commitTime)}</span>;
      },
    },
    //   ],
    // },
  ];

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    onSelectWaybill(selectedRows);
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const getList = async () => {
    return await waybillListAPIList({
      service_id: 'Waybill',
    });
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    // signInTime
    time: {
      desc: '签入时间',
      type: 'dateTimeRange',
      startTime: '',
      endTime: '',
    },
    warehouse: {
      desc: '所在仓库',
      type: 'warehouse',
      value: '',
      defaultKey: 'currentWarehouse',
      multi: true,
    },
    productIds: {
      desc: '销售产品',
      type: 'product',
      multi: true,
      value: '',
    },
    declarationType: {
      desc: '报关类型',
      type: 'select',
      multi: true,
      range: [
        {
          label: '非单独报关',
          value: '10',
        },
        {
          label: '单独报关',
          value: '20',
        },
        {
          label: '合并报关',
          value: '30',
        },
      ],
      value: isInlandTransfer ? '' : '30',
      defaultValue: isInlandTransfer ? '' : '30',
    },
    state: {
      desc: '状态',
      type: 'select',
      multi: true,
      range: [
        {
          label: '待签入',
          value: 10,
        },
        {
          label: '已签入',
          value: 20,
        },
        {
          label: '已确认',
          value: 40,
        },
        {
          label: '部分配舱',
          value: 59,
        },
        {
          label: '已配舱',
          value: 60,
        },
      ],
      defaultValue: isInlandTransfer ? '' : [10, 20, 40, 60],
    },
  };
  return (
    <>
      <div onClick={showModal}>
        {/* <IconItem name="#icon-dizhibao" style={{ marginRight: '6px' }} /> */}
        {!btnTitle ? (
          <div>
            主单： <a style={{ display: 'inline-block' }}>选择运单</a>
          </div>
        ) : (
          <div
            style={{
              display: 'inline-block',
              cursor: 'pointer',
              position: 'absolute',
              top: '374px',
              right: '50px',
              zIndex: 999,
              //       top: '319px',
              // right: '50px',
              // z-index: 999
            }}
          >
            <a style={{ display: 'inline-block' }}>{btnTitle}</a>
          </div>
        )}
      </div>
      <Drawer
        title="选择运单"
        style={{
          width: '100vw',
          position: 'fixed',
          top: '1px',
          bottom: '1px',
          // marginBottom: '-1em',
          right: '1px',
          left: '1px',
          zIndex: 999999,
        }}
        destroyOnClose
        open={isModalOpen}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <div style={{ height: 'calc(100% - 40px)' }}>
          {/* <div style={{ marginLeft: '25px' }}>
            <span>件数：<span style={{ color: 'rgb(100, 64, 255)' }}>{total?.number}</span></span>
            <span style={{ margin: '0 20px' }}>重量：<span style={{ color: 'rgb(0, 186, 217)' }}>{total?.weight}</span></span>
            <span>放数：<span style={{ color: 'rgb(255, 64, 165)' }}>{total?.squares}</span></span>
          </div> */}
          <MrTable
            columns={columns}
            keyID={'/inland/addWaybillss'}
            sTableY={{
              unfold: 380,
              pack: 310,
            }}
            filters={filters}
            // options={false}
            //rowSelectionCablck={rowSelectionCablck}
            request={async (params: any, action: any) => {
              actionRef.current = action;
              // const { current: start, pageSize: len } = params;
              // const param: any = {
              //   ...Params,
              //   start: (start - 1) * len,
              //   len,
              //   keyword,
              // };
              const msg = await getWaybillListAPI({
                ...params,
                // condition: {
                //   keyword: {
                //     value: keyword,
                //     termQuery,
                //   },
                //   state: {
                //     value: state,
                //   },
                //   time: {
                //     ...time,
                //   },
                //   volume: {
                //     value: volume,
                //   },
                //   productIds: {
                //     value: productId ? productId?.join(',') : '',
                //   },
                //   headTransType: {
                //     value: HeadTransType,
                //   },
                //   minRatio: {
                //     value: shipment?.minRatio,
                //   },
                //   maxRatio: {
                //     value: shipment?.maxRatio,
                //   },
                //   warehouseId: {
                //     value: warehouseId,
                //   },
                //   recipientZipCode: {
                //     value: recipientZipCode,
                //   },
                //   declarationType: {
                //     value: declarationTypeState,
                //   },
                // },
              });
              return {
                data: msg?.data?.list || [],
                success: msg.status,
                total: msg.data.total,
              };
            }}
            rowSelectionCablck={(value: any) => {
              setSelectedRows(value);
            }}
          />
        </div>
        <div
          key="btn"
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '40px',
            zIndex: '10',
          }}
        >
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={handleOk}
            style={{ marginLeft: 10 }}
          >
            确认
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default React.memo(SelectWaybill);
