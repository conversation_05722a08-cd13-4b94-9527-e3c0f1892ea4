/**
 * @选择客户分组列表
 */

import { getCustomGroupAPI } from '@/services/customer/CustomerHome';
import { Button, List, Modal, Radio, message, Pagination, Space } from 'antd';
import { useEffect, useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
// import { typePackageType } from '@/assets/types';
interface Props {
  type?:
    | 'link'
    | 'primary'
    | 'dashed'
    | 'text'
    | 'default'
    | undefined;
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any) => void;
}

const CustomerGroupingModal = (Props: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const {
    type = 'link',
    btnText,
    modalTitle = '运单标签模版',
    defaultChecked,
    onChange,
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const [total, setTotal] = useState<number>(0); // 总条数
  const [current, setCurrent] = useState(1);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return messageApi.error('当前没选择,请先选择或取消！');
    if (onChange) {
      onChange(selectItems);
    }
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectItems({});
  };

  /* 获取数据 */
  const getSurchargeListAPI = async (page: number, pageSize: number) => {
    try {
      const { status, data } = await getCustomGroupAPI({
        start: (page - 1) * pageSize,
        len: pageSize,
      });
      if (status) {
        setDataList([...data?.list]);
        setTotal(data?.total);
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };
  const paginationOnChange = (page: any, pageSize: any) => {
    setCurrent(page);
    getSurchargeListAPI(page, pageSize);
  };
  useEffect(() => {
    if (isModalOpen) {
      getSurchargeListAPI(1, 10);
    }
  }, [isModalOpen]);

  return (
    <>
      {contextHolder}
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
        footer={[
          <Space key="1">
            <Pagination
              current={current}
              defaultPageSize={10}
              total={total}
              onChange={paginationOnChange}
            />
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        <div>
          <List
            dataSource={dataList || []}
            renderItem={(item) => (
              <List.Item>
                <Radio
                  onClick={() => {
                    setSelectItems(item);
                  }}
                  checked={selectItems?.id === item?.id}
                  defaultChecked={defaultChecked}
                >
                  <div className="flex">
                    <div className="">{item?.name}</div>
                    <div className='color-coolGray ml-22px'>{item?.content}</div>
                  </div>
                </Radio>{' '}
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </>
  );
};

export default CustomerGroupingModal;
