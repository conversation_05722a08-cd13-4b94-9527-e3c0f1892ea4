/**
 * @超长超重扣件规则选择弹窗
 */

import { getOverLengthRule } from '@/services/productAndPrice/api';
import { ProTable } from '@ant-design/pro-components';
import { Button, Modal, message, DatePicker } from 'antd';
import { useRef, useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
// import { typePackageType } from '@/assets/types';
interface Props {
  type?: 'link' | 'primary' | 'dashed' | 'text' | 'default' | undefined;
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any, effectiveTime: any) => void;
}

const SuperLongOverweightModal = (Props: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [effectiveTime, setEffectiveTime] = useState<any>('');
  const [keyword, setKeyWord] = useState('');
  const actionRef = useRef<any>();
  const {
    type = 'link',
    btnText,
    modalTitle = '超长超重扣件规则',
    onChange,
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return messageApi.error('当前没选择,请先选择或取消！');
    if (!effectiveTime) return messageApi.warning('请选择生效时间');
    if (onChange) {
      onChange(selectItems, effectiveTime);
    }
    setIsModalOpen(false);
    setEffectiveTime('')
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectItems({});
  };

  const columns: any = [
    {
      title: '规则',
      dataIndex: 'ruleName',
      hideInSearch: true,
    },
  ];

  return (
    <>
      {contextHolder}
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
      >
        <div>
          <ProTable
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectItems(selectedRows[0]);
              },
              type: 'radio',
            }}
            ghost
            search={false}
            options={false}
            pagination={{
              defaultPageSize: 10,
            }}
            actionRef={actionRef}
            toolbar={{
              actions: [
                <DatePicker key="time" onChange={(e, se) => {
                  setEffectiveTime(se)
                }} showTime format='YYYY-MM-DD HH:mm:ss' placeholder='选择生效时间' className='w-100%' />,
              ],
              search: {
                onSearch: (value: string) => {
                  setKeyWord(value);
                  actionRef.current?.reload();
                },
              },
            }}
            rowKey="id"
            columns={columns}
            request={async (params: any) => {
              const { status, data } = await getOverLengthRule({
                start: (params.current - 1) * params.pageSize,
                len: params.pageSize,
                keyword: keyword
              });
              return {
                data: data?.list,
                success: status,
                total: data?.total,
              };
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default SuperLongOverweightModal;
