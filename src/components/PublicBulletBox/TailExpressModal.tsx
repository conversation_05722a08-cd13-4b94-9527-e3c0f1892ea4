/**
 * @尾程快递弹框
 */

import { getProductChannelList } from '@/services/productAndPrice/api';
import { ProTable } from '@ant-design/pro-components';
import { Button, List, Modal, Radio, message, Space, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
// import { typePackageType } from '@/assets/types';
interface Props {
  type?: "link" | "text" | "primary" | "default" | "dashed" | undefined
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any) => void;
  typeModal?: any; // 判定当前是什么弹框 默认尾巴程快递 type 20
  disabled?: boolean;
  typeValue?: any;//
}

const TailExpressModal = (Props: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [keyword,setKeyWord] = useState('');
  const actionRef = useRef<any>();
  const {
    type = 'link',
    btnText,
    modalTitle = '尾程快递选择',
    defaultChecked,
    onChange,
    typeModal = 20,
    disabled,
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const [total, setTotal] = useState<number>(0); // 总条数
  const [current, setCurrent] = useState(1);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectItems({});
    setCurrent(1)
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return messageApi.error('当前没选择,请先选择或取消！');
    if (onChange) {
      onChange(selectItems);
    }
    handleCancel();
  };
  

  /* 尾程列表接口 */
  const getSurchargeListAPI = async (page: number, pageSize: number) => {
    setDataList([]);
    try {
      const { status, data } = await getProductChannelList({
        start: (page - 1) * pageSize,
        len: pageSize,
        type: typeModal, //尾程快递
        // state:40
        enabled:1
      });
      if (status) {
        const result: any = [];
        data?.list?.forEach((item: any) => {
          const productChannelList = item.productChannelList;
          result.push(...productChannelList);
        });
        setDataList([...result]);
        setTotal(data?.total);
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };
  // const paginationOnChange = (page: any, pageSize: any) => {
  //   setCurrent(page);
  //   getSurchargeListAPI(page, pageSize);
  // };
  useEffect(() => {
    if (isModalOpen) {
      getSurchargeListAPI(1, 10);
    }
  }, [isModalOpen]);


  const columns: any = [
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      ellipsis: true,
    },
  ];

  return (
    <>
      {contextHolder}

      <Tooltip title={disabled?'左侧尾程运输勾选后可添加':''}>
        <Button type={type} onClick={showModal} disabled={disabled}>
          {btnText}
        </Button>
      </Tooltip>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
        footer={[
          <Space key="1">
            {/* <Pagination
              current={current}
              defaultPageSize={10}
              total={total}
              onChange={paginationOnChange}
            /> */}
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        <div>
          <ProTable
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectItems(selectedRows);
              },
              type: "checkbox",
            }}
            toolbar={{
              search: {
                placeholder: '请输入关键字',
                allowClear: true,
                onSearch: (value: string) => {
                  setKeyWord(value);
                  actionRef.current?.reloadAndRest();
                },
              },
            }}
            options={false}
            actionRef={actionRef}
            // dataSource={listData}
            search={false}
            rowKey="id"
            scroll={{y:320}}
            pagination={{
              defaultPageSize: 10,
            }}
            ghost
            columns={columns}
            request={async (params: any) => {
              const { status, data } = await getProductChannelList({
                start: (params.current - 1) * params.pageSize,
                len: params.pageSize,
                keyword:keyword,
                type: typeModal, //尾程快递
                // state:40
                enabled:1
              });
              const result: any = [];
              data?.list?.forEach((item: any) => {
                const productChannelList = item.productChannelList;
                result.push(...productChannelList);
              });
              return {
                data: result,
                success: status,
                total: data?.total || data?.length,
              };
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default TailExpressModal;
