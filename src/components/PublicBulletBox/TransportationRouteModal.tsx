/**
 * @附加费模版选择弹窗
 */

import { getAirLineList } from '@/services/productAndPrice/api';
import { ProTable } from '@ant-design/pro-components';
import { Button, List, Modal, Radio, message, Pagination, Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
// import { typePackageType } from '@/assets/types';
interface Props {
  type?:
    | 'link'
    | 'primary'
    | 'dashed'
    | 'text'
    | 'default'
    | undefined;
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any) => void;
  isType?:string //1 空 2海运
}

const CustomerListModal = (Props: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [keyword,setKeyWord] = useState('');
  const actionRef = useRef<any>();
  const {
    type = 'link',
    btnText,
    modalTitle = '航线选择',
    // defaultChecked,
    onChange,
    isType
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  // const [total, setTotal] = useState<number>(0); // 总条数
  // const [current, setCurrent] = useState(1);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    // setSelectItems({});
    // setCurrent(1)
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return messageApi.error('当前没选择,请先选择或取消！');
    if (onChange) {
      onChange(selectItems);
    }
    handleCancel();
  };
  

  /* 航线列表接口 */
  // const getSurchargeListAPI = async (page: number, pageSize: number) => {
  //   try {
  //     const { status, data } = await getAirLineList({
  //       start: (page - 1) * pageSize,
  //       len: pageSize,
  //       type:isType
  //     });
  //     if (status) {
  //       setDataList([...data?.list]);
  //       setTotal(data?.total);
  //     }
  //   } catch (err) {
  //     console.log('err: ', err);
  //   }
  // };
  // const paginationOnChange = (page: any, pageSize: any) => {
  //   setCurrent(page);
  //   getSurchargeListAPI(page, pageSize);
  // };
  // useEffect(() => {
  //   if (isModalOpen) {
  //     getSurchargeListAPI(1, 10);
  //   }
  // }, [isModalOpen]);

  const columns: any = [
    {
      title: '航线',
      dataIndex: 'name',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: 'code',
      dataIndex: ['vesselProperties','code'],
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '服务商',
      dataIndex: ['vesselProperties','providerName'],
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '出发港',
      dataIndex: ['vesselProperties','departsCity'],
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '到达港',
      dataIndex: ['vesselProperties','arrivesCity'],
      hideInSearch: true,
      ellipsis: true,
    },
  ];

  return (
    <>
      {contextHolder}
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
        // footer={[
        //   <Space key="1">
        //     <Pagination
        //       current={current}
        //       defaultPageSize={10}
        //       total={total}
        //       onChange={paginationOnChange}
        //     />
        //     <Button key="back" onClick={handleCancel}>
        //       取消
        //     </Button>
        //     <Button key="submit" type="primary" onClick={handleOk}>
        //       确定
        //     </Button>
        //   </Space>,
        // ]}
      >
        <div>
          {/* <List
            dataSource={dataList || []}
            renderItem={(item) => (
              <List.Item>
                <Radio
                  onClick={() => {
                    setSelectItems(item);
                  }}
                  checked={selectItems?.id === item?.id}
                  defaultChecked={defaultChecked}
                >
                  <div className="flex">
                    <div className="w-200px">{item?.name}</div>
                    <div className="w-100px color-coolgray">
                      {item?.vesselProperties?.code}
                    </div>
                    <div className="w-100px color-coolgray ml-6">
                      {item?.vesselProperties?.providerName}
                    </div>
                    <div className="ml-10">
                      <span className="color-coolGray">出发港：</span>
                      <span>{item?.vesselProperties?.departsCity}</span>
                    </div>
                    <div>
                      <span className="color-coolGray ml-10">到达港：</span>
                      <span>{item?.vesselProperties?.arrivesCity}</span>
                    </div>
                  </div>
                </Radio>{' '}
              </List.Item>
            )}
          /> */}
          <ProTable
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectItems(selectedRows);
              },
              type: "checkbox",
            }}
            toolbar={{
              search: {
                placeholder: '请输入关键字',
                allowClear: true,
                onSearch: (value: string) => {
                  setKeyWord(value);
                  actionRef.current?.reloadAndRest();
                },
              },
            }}
            options={false}
            actionRef={actionRef}
            // dataSource={listData}
            search={false}
            rowKey="id"
            scroll={{y:320}}
            pagination={{
              defaultPageSize: 1000,
            }}
            ghost
            columns={columns}
            request={async (params: any) => {
              const { status, data } = await getAirLineList({
                start: (params.current - 1) * params.pageSize,
                len: params.pageSize,
                keyword:keyword,
                type:isType
              });
              return {
                data: data?.list,
                success: status,
                total: data?.total || data?.length,
              };
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default CustomerListModal;
