/**
 * @超长超重扣件规则弹窗
 * ----------------------------
 * @btnText 按钮文字
 * @rowType 是否多选 'checkbox' | 'radio'
 * @onChange 选择后的回调
 */

import { ProList } from '@ant-design/pro-components';
import { Button, Modal } from 'antd';
import { useState } from 'react';
import { getLongAndHeavyRuleListAPI } from '@/services/home/<USER>';
interface Props {
  btnText?: string; // 按钮文字
  rowType?: string; // 是否多选 'checkbox' | 'radio'
  onChange?: (row: any, rowkey: any) => void; // 选择后的回调
}

const OverlengthAndOverweightRulesModal = ({
  btnText = '更换',
  rowType = 'radio',
  onChange,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (onChange) {
      onChange(selectedRows, selectedRowKeys);
    }
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const rowSelection: any = {
    type: rowType,
    selectedRowKeys,
    onChange: (keys: any, row: any) => {
      setSelectedRowKeys(keys);
      setSelectedRows(row);
    },
    getCheckboxProps: (record: any) => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name,
    }),
  };

  return (
    <>
      <Button type="link" onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="选择超长超重扣件规则"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确认
            </Button>
          </div>,
        ]}
      >
        <ProList
          split={false}
          style={{ marginTop: 40 }}
          metas={{
            title: {
              dataIndex: 'ruleName',
            },
            content: {
              dataIndex: 'ruleType',
            },
            // description: {
            //   // render: (text: any, record: any) => {
            //   //   const str = record?.chargeWeightRuleDetailList?.map(
            //   //     (item: any) => {
            //   //       return `${item?.volumeConst} ${item?.ruleDetail} ${item?.ruleResult}`;
            //   //     },
            //   //   );
            //   //   return <div>{str}</div>;
            //   // },
            //   render:(text:any,record:any)=>{
            //     return <div>{record?.ruleType}</div>
            //   }
            // },
          }}
          rowKey="title"
          rowSelection={rowSelection}
          pagination={{
            pageSize: 10,
          }}
          tableAlertRender={() => {
            return (
              <div>
                已选择{' '}
                <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a> 项
                ----
                {rowType === 'radio' ? selectedRows[0]?.ruleName : null}
              </div>
            );
          }}
          request={async (params: any) => {
            const msg: any = await getLongAndHeavyRuleListAPI({
              start: (params.current - 1) * params.pageSize,
              len: params.pageSize,
              // keyword: params.keyword,
              // type: 2,
            });
            return {
              data: msg?.data?.list,
              success: msg.status,
              total: msg.data?.total,
            };
          }}
        />
      </Modal>
    </>
  );
};

export default OverlengthAndOverweightRulesModal;
