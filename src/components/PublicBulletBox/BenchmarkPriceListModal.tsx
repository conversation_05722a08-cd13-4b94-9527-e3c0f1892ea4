/**
 * @基准价格表弹框
 */

import { Button, List, Modal, Radio, Space, message } from 'antd';
import { useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
import styles from './index.less';
import { useModel } from '@umijs/max';
import { typeState, typePackageType } from '@/assets/types';
import dayjs from 'dayjs';
interface Props {
  type?: 'link' | 'primary' | 'dashed' | 'text' | 'default' | undefined;
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any) => void;
}

const BenchmarkPriceListModal = (Props: Props) => {
  const {
    type = 'link',
    btnText,
    modalTitle = '基准价格表选择',
    defaultChecked,
    onChange,
  } = Props;
  const { basePriceList } = useModel('tableDataSharingStatus');
  // console.log('defaultChecked', defaultChecked);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return message.error('当前没选择价格表或价格表无改变,请重新选择或取消！');
    if (onChange) {
      onChange(selectItems);
    }
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
      >
        <div>
          <List
            // header={<div>Header</div>}
            // footer={<div>Footer</div>}
            // bordered
            dataSource={
              // basePriceList?.filter((item: any) => item?.state === 40) || []
              basePriceList
            }
            renderItem={(item) => (
              <List.Item>
                <Radio
                  onClick={() => {
                    setSelectItems(item);
                  }}
                  // checked={selectItems?.id === item?.id }
                  checked={
                    selectItems?.itemDetail?.refId
                      ? item?.itemDetail?.refId ===
                        selectItems?.itemDetail?.refId
                      : item?.itemDetail?.refId === defaultChecked
                  }
                  defaultChecked={item?.itemDetail?.refId === defaultChecked}
                >
                  <Space>
                    <div>{item?.itemDetail?.name} </div>
                    <div
                      className={styles['singe-line']}
                      style={{ width: 500, color: '#999' }}
                    >
                      {item?.itemDetail?.createTime ? (
                        <span>
                          【
                          {dayjs(item?.itemDetail?.createTime)?.format(
                            'MMDDHHmm',
                          )}
                          】
                        </span>
                      ) : null}
                      {typeState[item?.itemDetail?.state]?.text} -{' '}
                      {typePackageType[item?.itemDetail?.packageType]}
                    </div>
                  </Space>
                </Radio>{' '}
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </>
  );
};

export default BenchmarkPriceListModal;
