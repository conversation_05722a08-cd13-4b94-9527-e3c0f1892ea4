/**
 * @计重规则弹框
 */

import { ProCard, ProTable } from '@ant-design/pro-components';
import { Button, DatePicker, message, Modal, Space } from 'antd';
import { useRef, useState } from 'react';
import { getCustomRuleList } from '@/services/productAndPrice/api';
import { getCustomGroupAPI } from '@/services/customer/CustomerHome';
interface Props {
  btnText?: string; // 按钮文字
  rowType?: string; // 是否多选 'checkbox' | 'radio'
  onChange?: (row: any, rowkey: any, effectiveTime: any) => void;
  type?: any;
}

const WeighingRulesModal = ({
  btnText = '更换',
  rowType = 'radio',
  type = 'link',
  onChange,
}: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const actionRef = useRef<any>();
  const [keyword, setKeyWord] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  /* 计重固规则 */
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  /* 客户 */
  const [selectedRowKeys1, setSelectedRowKeys1] = useState<any>([]);
  const [selectedRows1, setSelectedRows1] = useState<any>([]);
  const [effectiveTime, setEffectiveTime] = useState<any>('');

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (selectedRows.length === 0) {
      return messageApi.warning('请选择计重规则');
    }
    if (!effectiveTime) {
      return messageApi.warning('请选择生效时间');
    }
    if (onChange) {
      onChange(selectedRows, selectedRows1, effectiveTime);
    }
    setIsModalOpen(false);
    setEffectiveTime('')
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const rowSelection: any = {
    type: rowType,
    selectedRowKeys,
    onChange: (keys: any, row: any) => {
      setSelectedRowKeys(keys);
      setSelectedRows(row);
    },
  };
  const rowSelection1: any = {
    type: rowType,
    selectedRowKeys1,
    onChange: (keys: any, row: any) => {
      setSelectedRowKeys1(keys);
      setSelectedRows1(row);
    },
  };
  const columns: any = [
    {
      title: '名称',
      dataIndex: 'ruleName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '内容',
      dataIndex: 'ruleResult',
      render: (text: any, record: any) => {
        const str = record?.chargeWeightRuleDetailList?.map((item: any) => {
          return `${item?.volumeConst} ${item?.ruleDetail} ${item?.ruleResult}`;
        });
        return <div>{str}</div>;
      },
    },
  ];

  const columns1: any = [
    {
      title: '客户',
      dataIndex: 'name',
    },
  ];

  return (
    <>
      {contextHolder}
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="计重规则"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={'80vw'}
        style={{ minWidth: '1200px' }}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Space>
              <Button key="back" onClick={handleCancel}>
                取消
              </Button>
              <Button key="submit" type="primary" onClick={handleOk}>
                确认
              </Button>
            </Space>
          </div>,
        ]}
        destroyOnClose
      >
        <ProCard split="vertical">
          <ProCard ghost>
            <ProTable
              rowKey="id"
              headerTitle="选择计重规则"
              rowSelection={rowSelection}
              pagination={{
                pageSize: 10,
                onChange: () => {
                  setSelectedRowKeys([]);
                  setSelectedRows([]);
                },
              }}
              actionRef={actionRef}
              toolbar={{
                search: {
                  onSearch: (value: string) => {
                    setKeyWord(value);
                    actionRef.current?.reload();
                  },
                },
              }}
              columns={columns}
              tableAlertRender={() => {
                return (
                  <div>
                    已选择{' '}
                    <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a>{' '}
                    项 ----
                    {rowType === 'radio' ? selectedRows[0]?.ruleName : null}
                  </div>
                );
              }}
              search={false}
              options={false}
              // ghost
              scroll={{ y: 400, x: 600 }}
              request={async (params: any) => {
                const msg: any = await getCustomRuleList({
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  // keyword: params.keyword,
                  type: 1,
                  keyword: keyword
                });
                return {
                  data: msg?.data?.list,
                  success: msg.status,
                  total: msg?.data?.total,
                };
              }}
            />
          </ProCard>
          <ProCard colSpan="384px" ghost>
            <ProTable
              rowKey="id"
              headerTitle={<>
                <span className='w-300px'>选择客户</span>
                <div><DatePicker onChange={(e, se) => {
                  setEffectiveTime(se)
                }} showTime format='YYYY-MM-DD HH:mm:ss' placeholder='选择生效时间' className='w-100%' /></div>
              </>}
              rowSelection={rowSelection1}
              pagination={{
                pageSize: 10,
                onChange: () => {
                  setSelectedRowKeys1([]);
                  setSelectedRows1([]);
                },
              }}
              columns={columns1}
              tableAlertRender={() => {
                return (
                  <div>
                    已选择{' '}
                    <a style={{ fontWeight: 600 }}>{selectedRowKeys1.length}</a>{' '}
                    项 ----
                    {rowType === 'radio' ? selectedRows1[0]?.name : null}
                  </div>
                );
              }}
              search={false}
              options={false}
              // ghost
              scroll={{ y: 400 }}
              request={async (params: any) => {
                const msg: any = await getCustomGroupAPI({
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  // keyword: params.keyword,
                });
                return {
                  data: msg?.data?.list,
                  success: msg.status,
                  total: msg?.data?.total || msg?.data?.list?.length,
                };
              }}
            />
          </ProCard>
        </ProCard>
      </Modal>
    </>
  );
};

export default WeighingRulesModal;
