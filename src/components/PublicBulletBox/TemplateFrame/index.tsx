import { Button, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import service from '@/services/home';
import { ProTable } from '@ant-design/pro-components';
const { getPartitionTemplateListAPI, getWeightTemplateListAPI } =
  service.UserHome;
interface Props {
  btnText?: string;
  title?: string;
  onChange?: any;
  radioId?: string | number; // 选中的radio的id
}
const TemplateFrame = ({ btnText, title, onChange }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [loading, setLoading] = useState(false);
  // const [listData, setListData] = useState<any>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [keyword, setKeyWord] = useState('');
  const actionRef = useRef<any>();

  // /* 保存分区模版 */
  // const savePartitionTemplate = async (data: any) => {
  //   try {
  //     const { status } = await savePartitionTemplateAPI({
  //       name: data.name,
  //       id: data.id,
  //       zoneDetailList: data.zoneDetailList,
  //     });
  //     if (status) {
  //       message.success('保存成功');
  //       onChange(selectedItem);
  //       setIsModalOpen(false);
  //     }
  //   } catch (err) {
  //     console.error('保存分区模版抛出异常: ', err);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    onChange(selectedItem);
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // const handleItemClick = (item: any) => {
  //   setSelectedItem(item);
  // };
  /* 获取分区模版列表 */
  // const getPartitionTemplateList = async () => {
  //   setLoading(true);
  //   try {
  //     const { status, data } = await getPartitionTemplateListAPI({
  //       keyword:keyword
  //     });
  //     if (status) {
  //       setListData([...data]);
  //       /* 设置默认选中 */
  //       const defaultItem = data.find((item: any) => item.id === radioId);
  //       setSelectedItem(defaultItem);
  //     }
  //   } catch (err) {
  //     console.error('获取分区模版列表抛出异常: ', err);
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  // /* 获取重量模版 */
  // const getWeightTemplateList = async () => {
  //   setLoading(true);
  //   try {
  //     const { status, data } = await getWeightTemplateListAPI({
  //       keyword:keyword
  //     });
  //     if (status) {
  //       setListData([...data]);
  //       /* 设置默认选中 */
  //       const defaultItem = data.find((item: any) => item.id === radioId);
  //       setSelectedItem(defaultItem);
  //     }
  //   } catch (err) {
  //     console.error('获取重量模版列表抛出异常: ', err);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // useEffect(() => {
  //   if (title === '选择分区模版' && isModalOpen) {
  //     getPartitionTemplateList();
  //   }
  //   if (title === '选择重量模版' && isModalOpen) {
  //     getWeightTemplateList();
  //   }
  // }, [isModalOpen]);

  const columns: any = [
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '内容',
      dataIndex: title === '选择分区模版' ? 'code' : 'weightName',
      hideInSearch: true,
      ellipsis: true,
    },
  ];
  return (
    <>
      <Button type="link" onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={title}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
      >
        <div>
          <ProTable
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectedItem(selectedRows[0]);
              },
              type: 'radio',
            }}
            toolbar={{
              search: {
                onSearch: (value: string) => {
                  setKeyWord(value);
                  actionRef.current?.reload();
                },
              },
            }}
            options={false}
            actionRef={actionRef}
            // dataSource={listData}
            search={false}
            rowKey="id"
            pagination={{
              defaultPageSize: 10,
            }}
            ghost
            columns={columns}
            request={async (params: any) => {
              if (title === '选择分区模版') {
                const { status, data } = await getPartitionTemplateListAPI({
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  keyword: keyword
                });
                return {
                  data: data?.list,
                  success: status,
                  total: data?.total || data?.length,
                };
              } else {
                const { status, data } = await getWeightTemplateListAPI({
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  keyword: keyword
                });
                return {
                  data: data?.list,
                  success: status,
                  total: data?.total || data?.length,
                };
              }
            }}
          />
        </div>
      </Modal>
    </>
  );
};
export default TemplateFrame;
