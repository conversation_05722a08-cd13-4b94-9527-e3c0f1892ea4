/**
 * @计重费进位规则选择弹窗
 */

import { ProTable } from '@ant-design/pro-components';
import { Button, DatePicker, message, Modal, Space } from 'antd';
import { useRef, useState } from 'react';
import { getCustomRuleList } from '@/services/productAndPrice/api';
interface Props {
  btnText?: string; // 按钮文字
  rowType?: string; // 是否多选 'checkbox' | 'radio'
  onChange?: (row: any, rowkey: any, effectiveTime: string) => void; // 选择后的回调
}

const SelectWeightFeeRoundingModal = ({
  btnText = '更换',
  rowType = 'radio',
  onChange,
}: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [effectiveTime, setEffectiveTime] = useState<any>('');
  const [keyword, setKeyWord] = useState('');
  const actionRef = useRef<any>();
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (!effectiveTime) return message.warning('请选择生效时间');
    if (onChange) {
      onChange(selectedRows, selectedRowKeys, effectiveTime);
    }
    setIsModalOpen(false);
    setEffectiveTime('')
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const rowSelection: any = {
    type: rowType,
    selectedRowKeys,
    onChange: (keys: any, row: any) => {
      setSelectedRowKeys(keys);
      setSelectedRows(row);
    },
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'ruleName',
    },
    {
      title: '内容',
      dataIndex: 'chargeWeightRuleDetailList',
      render: (text: any, record: any) => {
        const str = record?.chargeWeightRuleDetailList?.map((item: any) => {
          return ` ${item?.ruleDetail} ${item?.ruleResult}`;
        });
        return <div>{str}</div>;
      },
    },
  ];

  return (
    <>
      <Button type="link" onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={
          <Space>
            <div>选择计费重进位规则</div>
            <div></div>
          </Space>
        }
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Space>
              <Button key="back" onClick={handleCancel}>
                取消
              </Button>
              <Button key="submit" type="primary" onClick={handleOk}>
                确认
              </Button>
            </Space>
          </div>,
        ]}
      >
        <ProTable
          style={{ marginTop: 40 }}
          columns={columns}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={{
            defaultPageSize: 10,
          }}
          ghost
          options={false}
          search={false}
          scroll={{ y: 380 }}
          toolbar={{
            actions: [
              <DatePicker key="time" onChange={(e, se) => {
                setEffectiveTime(se)
              }} showTime format='YYYY-MM-DD HH:mm:ss' placeholder='选择生效时间' className='w-100%' />,
            ],
            search: {
              onSearch: (value: string) => {
                setKeyWord(value);
                actionRef.current?.reload();
              },
            },
          }}
          tableAlertRender={() => {
            return (
              <div>
                已选择{' '}
                <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a> 项
                ----
                {rowType === 'radio' ? selectedRows[0]?.ruleName : null}
              </div>
            );
          }}
          actionRef={actionRef}
          request={async (params: any) => {
            const msg: any = await getCustomRuleList({
              start: (params.current - 1) * params.pageSize,
              len: params.pageSize,
              // keyword: params.keyword,
              type: 2,
              keyword: keyword
            });
            return {
              data: msg?.data?.list,
              success: msg.status,
              total: msg.data?.total,
            };
          }}
        />
      </Modal>
    </>
  );
};

export default SelectWeightFeeRoundingModal;
