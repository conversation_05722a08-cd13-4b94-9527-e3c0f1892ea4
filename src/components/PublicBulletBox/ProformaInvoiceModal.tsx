import React, { useState } from 'react';
import { But<PERSON>, Modal } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import { getPrintLabelListAPI } from '@/services/financeApi';

interface Props {
  btnText?: string;
  btnType?: 'link' | 'text' | 'default' | 'primary' | 'dashed' | undefined;
  onChange?: (e: any) => void;
}
const ProformaInvoiceModal = ({ btnText, btnType, onChange }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRows, setSelectedRow] = useState<any[]>([]);

  const columns: any = [
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
    },
    {
      title: '尺寸（宽*高）',
      dataIndex: 'salesmanInfo',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
      render: (text: any, record: any) => {
        return (
          <span>
            {record?.mmWidth || '-'}mm*{record?.mmHeight || '-'}mm
          </span>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
      valueType: 'dateTime',
    },
  ];
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (selectedRows.length) {
      if (onChange) {
        onChange(selectedRows);
        setIsModalOpen(false);
      }
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title="选择模版"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
      >
          <ProTable
            rowSelection={{
              type: 'radio',
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRow(selectedRows);
              },
            }}
            search={false}
            columns={columns}
            pagination={{
              defaultPageSize: 10,
            }}
            ghost
            rowKey="id"
            options={false}
            request={async (params: any) => {
              const { data, status } = await getPrintLabelListAPI({
                type: 'Invoice',
                start: (params.current - 1) * params.pageSize,
                len: params.pageSize,
                keeword: params.keyword,
              });
              return {
                data: data?.list,
                success: status,
                total: data?.total,
              };
            }}
          />
      </Modal>
    </>
  );
};
export default ProformaInvoiceModal;
