/**
 * @分区模版选择弹框
 */

import { Button, Modal, message } from 'antd';
import { useRef, useState } from 'react';
import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
import { ProTable } from '@ant-design/pro-components';

interface Props {
  type?: "link" | "text" | "primary" | "default" | "dashed" | undefined
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any) => void;
}

const PartitionTemplateSelectionModal = (Props: Props) => {
  const [modal, contextHolder] = Modal.useModal();
  const [keyword, setKeyWord] = useState('');
  const actionRef = useRef<any>();

  const {
    type = 'link',
    btnText,
    modalTitle = '分区模版选择',
    defaultChecked,
    onChange,
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const showModal = () => {
    if (btnText !== '选择分区') {
      modal.confirm({
        title: '切换分区',
        content: '切换分区后，当前分区表将会被清空，是否确认切换？',
        onOk: () => {
          setIsModalOpen(true);
        },
      });
    } else {
      setIsModalOpen(true);
    }
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return message.error('当前没选择任何分区,请先选择分区或取消！');

    if (selectItems?.id === defaultChecked)
      return message.error('当前已是该基准价格表,如不更换无需重新选择！');
    if (onChange) {
      onChange(selectItems);
    }
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectItems({});
  };

  /* 获取分区模版 */
  // const getPartitionTemplateList = async () => {
  //   try {
  //     const { status, data } = await getPartitionTemplateListAPI({});
  //     if (status) {
  //       setDataList(data);
  //     }
  //   } catch (err) {
  //     console.error(err);
  //   }
  // };

  // useEffect(() => {
  //   if (isModalOpen) {
  //     getPartitionTemplateList();
  //   }
  // }, [isModalOpen]);

  const columns: any = [
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '内容',
      dataIndex: 'code',
      hideInSearch: true,
      ellipsis: true,
    },
  ];

  return (
    <>
      {contextHolder}
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
      >
        <div>
          <ProTable
            rowSelection={{
              onChange: (_, selectedRows) => {
                setSelectItems(selectedRows[0]);
              },
              type: 'radio',
            }}
            toolbar={{
              search: {
                onSearch: (value: string) => {
                  setKeyWord(value);
                  actionRef.current?.reload();
                },
              },
            }}
            options={false}
            actionRef={actionRef}
            // dataSource={listData}
            search={false}
            rowKey="id"
            pagination={{
              defaultPageSize: 10,
            }}
            ghost
            columns={columns}
            request={async (params: any) => {
              const { status, data } = await getPartitionTemplateListAPI({
                start: (params.current - 1) * params.pageSize,
                len: params.pageSize,
                keyword: keyword
              });
              return {
                data: data?.list,
                success: status,
                total: data?.total || data?.length,
              };
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default PartitionTemplateSelectionModal;
