/**
 * @附加费模版选择弹窗
 */

import { Button, List, Modal, Radio, Space, message, Pagination } from 'antd';
import { useEffect, useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
import styles from './index.less';
// import { typePackageType } from '@/assets/types';
import { getCustomGroupAPI } from '@/services/customer/CustomerHome';
interface Props {
  type?:
    | 'link'
    | 'primary'
    | 'dashed'
    | 'text'
    | 'default'
    | undefined;
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any) => void;
  basicInformationData?: any;
}

const CustomerListModal = (Props: Props) => {
  const {
    type = 'link',
    btnText,
    modalTitle = '客户列表',
    defaultChecked,
    onChange,
    basicInformationData,
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const [total, setTotal] = useState<number>(0); // 总条数
  const [current, setCurrent] = useState(1);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return message.error('当前没选择,请先选择或取消！');
    if (onChange) {
      onChange(selectItems);
    }
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /* 附加费模版列表 */
  const getSurchargeListAPI = async (page: number, pageSize: number) => {
    try {
      const { status, data } = await getCustomGroupAPI({
        start: (page - 1) * pageSize,
        len: pageSize,
      });
      if (status) {
        setDataList([
          {
            id: null,
            name: '全部',
          },
          ...data?.list,
        ]);
        setTotal(data?.total);
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  const paginationOnChange = (page: any, pageSize: any) => {
    setCurrent(page);
    getSurchargeListAPI(page, pageSize);
  };

  useEffect(() => {
    if (isModalOpen) {
      getSurchargeListAPI(1, 10);
    }
  }, [isModalOpen]);

  return (
    <>
      <span>
        {selectItems?.name
          ? selectItems?.name
          : basicInformationData?.clientGroupName}
      </span>
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        width={800}
        onCancel={handleCancel}
        footer={[
          <Space key="1">
            <Pagination
              current={current}
              defaultPageSize={10}
              total={total}
              onChange={paginationOnChange}
            />
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        <div>
          <List
            // header={<div>Header</div>}
            // footer={<div>Footer</div>}
            // bordered
            dataSource={dataList || []}
            renderItem={(item) => (
              <List.Item>
                <Radio
                  onClick={() => {
                    setSelectItems(item);
                  }}
                  checked={selectItems?.id === item?.id}
                  defaultChecked={defaultChecked}
                >
                  <Space>
                    <div>{item?.name} </div>
                    <div
                      className={styles['singe-line']}
                      style={{ width: 500, color: '#999' }}
                    >
                      {item?.clientList?.map((item: any) => item?.name + '、')}
                    </div>
                  </Space>
                </Radio>{' '}
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </>
  );
};

export default CustomerListModal;
