/**
 * @附加费模版选择弹窗
 */

import {
  Button,
  List,
  Modal,
  Radio,
  Space,
  message,
  DatePicker,
  Input,
} from 'antd';
import { useEffect, useState } from 'react';
// import { getPartitionTemplateListAPI } from '@/services/home/<USER>';
import styles from './index.less';
import { typePackageType } from '@/assets/types';
import { getSurchargeList } from '@/services/productAndPrice/api';
const { Search } = Input;
interface Props {
  type?: 'link' | 'primary' | 'dashed' | 'text' | 'default' | undefined;
  btnText: string;
  modalTitle?: string;
  defaultChecked?: any; // 默认选中的值
  onChange?: (e: any, effectiveTime: any) => void;
}

const SurchargeModal = (Props: Props) => {
  const {
    type = 'link',
    btnText,
    modalTitle = '附加费模版选择',
    defaultChecked,
    onChange,
  } = Props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]); // 分区模版列表
  const [selectItems, setSelectItems] = useState<any>({}); // 选中的分区模版列表
  const [effectiveTime, setEffectiveTime] = useState<any>('');
  // const [keyword,setKeyWord] = useState('');
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    if (Object.keys(selectItems).length === 0)
      return message.error('当前没选择,请先选择或取消！');
    if (!effectiveTime) return message.warning('请选择生效时间');
    if (onChange) {
      onChange(selectItems, effectiveTime);
    }
    setIsModalOpen(false);
    setEffectiveTime('');
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /* 附加费模版列表 */
  const getSurchargeListAPI = async () => {
    try {
      const { status, data } = await getSurchargeList({});
      if (status) {
        setDataList(data?.list);
      }
    } catch (err) {
      console.log('err: ', err);
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      getSurchargeListAPI();
    }
  }, [isModalOpen]);

  return (
    <>
      <Button type={type} onClick={showModal}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        destroyOnClose
      >
        <div>
          <Space>
            <Search
              placeholder="搜索名称"
              style={{
                width: 200,
              }}
              allowClear
              onSearch={async (value) => {
                try {
                  const { status, data } = await getSurchargeList({
                    keyword: value,
                  });
                  if (status) {
                    setDataList(data);
                  }
                } catch (err) {
                  console.log('err: ', err);
                }
              }}
            />
            <DatePicker
              key="time"
              onChange={(e, se) => {
                setEffectiveTime(se);
              }}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择生效时间"
            />
          </Space>
          <List
            // header={<div>Header</div>}
            // footer={<div>Footer</div>}
            // bordered
            pagination={{}}
            dataSource={dataList || []}
            renderItem={(item) => (
              <List.Item>
                <Radio
                  onClick={() => {
                    setSelectItems(item);
                  }}
                  checked={selectItems?.id === item?.id}
                  defaultChecked={defaultChecked}
                >
                  <Space>
                    <div>{item?.name} </div>
                    <div
                      className={styles['singe-line']}
                      style={{ width: 500, color: '#999' }}
                    >
                      {typePackageType[item?.packageType]}
                    </div>
                  </Space>
                </Radio>{' '}
              </List.Item>
            )}
          />
        </div>
      </Modal>
    </>
  );
};

export default SurchargeModal;
