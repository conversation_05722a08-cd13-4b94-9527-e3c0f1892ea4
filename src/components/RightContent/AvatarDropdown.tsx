import {
  ExclamationCircleFilled,
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Modal, Spin, message } from 'antd';
// import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback } from 'react';
import { history, useModel } from 'umi';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import service from '@/services/home'
const {logoutAPI} = service.UserHome
const { confirm } = Modal;
export type GlobalHeaderRightProps = {
  menu?: boolean;
};

// const outLogin = async () => {};
const {REACT_APP_ENV} = process.env;
const modifyUrl = (url: any) => {
  if (url.startsWith('https')) {
    return url;
  } else {
    return 'https://static.kmfba.com/' + url;
  }
};
const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ }) => {
  const { initialState, setInitialState } = useModel('@@initialState');

  /* 退出跳转逻辑 */
  const outLoginURL = async () => {
    const url = window.location.href;
    // 如果是本地 跳转到对应本地的 登录地址
    if(url.includes('localhost') || url.includes('9996')){
      location.replace('http://localhost:3000/')
    }
    //如果是dev环境 跳转到对应dev 的登录地址
    if(REACT_APP_ENV==='dev'){
      location.replace('https://dev-web-home.kmfba.com/login')
    }
    if(REACT_APP_ENV==='pre'){
      location.replace('https://staging-web-home.kmfba.com')
    }
    if(REACT_APP_ENV==='production'){
      const currentDomain = window.location.hostname;
      if(currentDomain.includes('mmdwl')){
        location.replace('https://home.mmdwl.com')
      }else if(currentDomain === 'mingrui.cloud' || currentDomain ==='www.mingrui.cloud'){
        location.replace('https://home.mingrui.cloud')
      }else{
        location.replace('https://home.kmfba.com')
      }
    }
    //清除本地缓存
    localStorage.clear();
  }

  const outLogin = async () => {
    try {
      const {status} = await logoutAPI({});
      if (status) {
        message.success('退出成功');
        outLoginURL()
      }
    }
    catch (error) {
      message.error('退出失败');
    }

  };
  /**
 * 退出登录，清除token
 */
const loginOut = async () => {
  confirm({
    title: '退出登录吗',
    icon: <ExclamationCircleFilled />,
    content: '请确认是否退出登录',
    onOk() {
      outLogin()
    },
    onCancel() {
      message.info('已取消')
    },
  });
};
  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;
      if (key === 'logout') {
        // setInitialState((s: any) => ({ ...s, currentUser: undefined }));
        loginOut();
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }
  // @ts-ignore
  const { currentUser } = initialState;

  if (!currentUser || !currentUser.user.name) {
    return loading;
  }
  const items = [
    {
      key: 'settings',
      label: (
        <div>
          <SettingOutlined />
          个人设置
        </div>
      ),
    },
    {
      key: 'logout',
      label: (
        <div>
          <LogoutOutlined />
          退出登录
        </div>
      ),
    },
  ];
  return (
    <HeaderDropdown
      menu={{
        items,
        selectedKeys: [],
        onClick: onMenuClick,
      }}
    >
      <span className={`${styles.action} ${styles.account}`}>
        <Avatar
          size="small"
          className={styles.avatar}
          src={
            modifyUrl(currentUser?.user?.avatar)
          }
          icon={<UserOutlined />}
          alt="avatar"
        />
        <span className={`${styles.name} anticon`}>
          {currentUser?.user?.name || 'xxx'}
        </span>
      </span>
    </HeaderDropdown>
  );
};

export default AvatarDropdown;
