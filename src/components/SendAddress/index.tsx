import { ProFormSelect } from '@ant-design/pro-components';
import { Form } from 'antd';
import { useEffect, useState } from 'react';
import service from '@/services/home';
const { addressFuzzySearchAPI } = service.UserHome;

interface Props {
  onChange?: (value: any) => void | undefined;
  style?: React.CSSProperties;
}
const SendAddress = ({onChange,style}:Props) => {
  const [form] = Form.useForm();
  const [selectedAreaObj, setSelectedAreaObj] = useState<any>(null);

  useEffect(() => {
    if(onChange){
      onChange(selectedAreaObj)
    }
  }, [selectedAreaObj]);

  const handleSelectChange = (e: any) => {
    setSelectedAreaObj(JSON.parse(e.value));
    form.setFieldsValue({
      province: e,
    });
  };

  const handleRequest = async ({ keyWords }: { keyWords: string }) => {
    const { status, data } = await addressFuzzySearchAPI({
      token: keyWords,
    });
    if (status) {
      return data.list.map((item: any) => ({
        label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
        value: JSON.stringify(item),
      }));
    }
    return [];
  };

  return (
    <>
      <Form name="basic" autoComplete="off" form={form} style={style}>
        <ProFormSelect
          name="province"
          label="区域"
          width={500}
          fieldProps={{
            labelInValue: true,
            filterOption: false,
            onChange: handleSelectChange,
          }}
          showSearch
          placeholder="请选择，支持模糊搜索"
          rules={[{ required: true, message: '必填不能为空' }]}
          debounceTime={300}
          request={handleRequest}
        />
      </Form>
    </>
  );
};

export default SendAddress;
