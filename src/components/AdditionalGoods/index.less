.selectWaybill {
  background: #722ED1;
}

/*自定义搜索表单*/
.searchWrap {
  background: white;
  margin-bottom: 20px;
  display: grid;
  padding: 10px;
  grid-template-columns: 1fr 1fr 1fr;

  .item1 {
    grid-area: header;

    .search_wrap {
      border: 1px solid #e8e8e8;
      height: 80px;
      display: flex;
      width: 442px;
      border-radius: 8px;

      :global(.ant-select-selector) {
        height: 100%;

        :global(.ant-select-selection-item) {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .item2 {
    grid-area: sidebar;
    background: red;
  }

  grid-template-areas: "header header2 header3"
  "header main sidebar"
  "menu footer2 footer3";
}

.search_wrap {
  border: 1px solid #e8e8e8;
  height: 91px;
  display: flex;
  width: 348px;
  margin-right: 16px;
  border-radius: 4px;
  float: left;
  margin-bottom: 16px;

  .text_area {
    // overflow-y: hidden;
    height: 100%;
    border: none;
    resize: none;
    border-radius: 0;
    width: 100%;
    padding-top: 35px;
    overflow: auto;
  }

  :global {
    .ant-select-selector {
      height: 100%;
      background-color: #fff;
      border-right: 1px solid #E5E5E5;
      border-radius: 0px;
      color: #707070;
    }

    .ant-select-selection-item {
      display: flex;
      align-items: center;
    }

  }

  // :global(.ant-select-selector) {
  //     height: 100%;

  //     :global(.ant-select-selection-item) {
  //         display: flex;
  //         align-items: center;
  //     }
  // }
}