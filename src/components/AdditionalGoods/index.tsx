import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, DatePicker, Drawer, Tag } from 'antd';

import { waybillListAPIList } from '@/services/preplanCabin';
import { appendPieces, getBoxList } from '@/services/ConfiguredCar';
import { DispatchMap } from '@/constants';
import { getObjectByValue, ProgressStateType2 } from '@/utils/constant';
import { formatTimes } from '@/utils/format';
import CargoDetail from '@/pages/CargoManagement/CargoDetail';
import HeadTotal from '@/components/HeadTotal';
import { getWarehouseListAPI } from '@/services/home/<USER>';
import SuperTables from '@/components/SuperTables';

const { RangePicker } = DatePicker;

const AdditionalGoods = (props: any) => {
  const { onSelectWaybill, btnTitle, DefaultSelected, details, Refresh } =
    props;
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的运单信息
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  // 被选中的运单信息
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [total, setTotal] = useState({
    number: 0,
    weight: 0,
    squares: 0,
  });
  const [Statistics, setStatistics] = useState<any>({});
  const actionRef = useRef<any>();
  /*选中项*/
  const [Loading, setLoading] = useState<any>(false);
  const refreshTable = () => {
    console.log('刷新');
    actionRef?.current?.reload();
  };
  useEffect(() => {
    getTotal();
  }, [selectedRows?.length]);
  useEffect(() => {
    /*设置回显选中项目*/
    /*  if (isModalOpen && DefaultSelected) {
      actionRef?.current?.reload();
      setSelectedRowKeys(DefaultSelected);
    }*/
    console.log('isModalOpen', isModalOpen);
    if (isModalOpen) {
      refreshTable();
    }
  }, [isModalOpen]);
  const getTotal = () => {
    const pieceNum = selectedRows?.reduce(
      (pre: any, cur: any) => pre + cur?.pieceNum,
      0,
    );
    const weight = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.weight),
      0,
    );
    const squares = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.squares),
      0,
    );
    setTotal({
      number: pieceNum,
      weight: weight,
      squares: squares,
    });
  };
  /* 刷新表格 */
  const columns = [
    {
      title: '客户箱唛',
      dataIndex: 'waybillNo',
      hideInSearch: true,
      width: 120,
      fieldFormat: (record: any) => {
        /*没有就返回子单号*/
        return record?.waybillNo || record?.subWaybillNo;
      },
    },
    {
      title: '状态',
      width: 60,
      dataIndex: 'state',
      hideInSearch: true,
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Tag
            style={{
              background: obj?.background,
              color: obj?.color,
              border: 'none',
            }}
          >
            {obj?.label}
          </Tag>
        );
      },
    },
    {
      title: '派送方式',
      width: 100,
      dataIndex: 'shipmentMethod',
      hideInSearch: true,
    },
    {
      title: 'FBA仓库',
      hideInSearch: true,
      width: 100,
      dataIndex: 'dest',
    },
    {
      title: '邮编',
      hideInSearch: true,
      width: 100,
      dataIndex: 'zipCode',
      fieldFormat: (record: any) => {
        return record?.recipient?.zipCode;
      },
    },
    {
      title: '总立方数/CBM',
      hideInSearch: true,
      dataIndex: 'volume',
      width: 100,
    },

    {
      title: '总重量/KG',
      hideInSearch: true,
      width: 100,
      dataIndex: 'weight',
    },
    {
      title: '总件数',
      hideInSearch: true,
      width: 100,
      dataIndex: 'pieceNum',
    },
    {
      title: '交货仓库',
      hideInSearch: true,
      dataIndex: 'recipient',
      width: 100,
      fieldFormat: (record: any) => {
        return record?.recipient?.name || record?.recipient?.fbaCode;
      },
    },
    {
      title: '提单号',
      hideInSearch: true,
      dataIndex: 'blno',
      width: 100,
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.blno;
      },
    },
    {
      title: '清提派单号',
      hideInSearch: true,
      width: 100,
      dataIndex: 'no',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.no;
      },
    },
    {
      title: '托盘标签号',
      width: 100,
      hideInSearch: true,
      dataIndex: 'palletsNO',
      fieldFormat: (record: any) => {
        return record?.palletsNO;
      },
    },
    {
      title: '客户',
      hideInSearch: true,
      width: 100,
      dataIndex: 'clientName',
    },
    {
      title: '柜型',
      hideInSearch: true,
      dataIndex: 'containerMode',
      width: 100,
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.containerMode;
      },
    },
    {
      title: '柜号',
      hideInSearch: true,
      width: 100,
      dataIndex: 'blnoOrder',
      fieldFormat: (record: any) => {
        return record?.blnoOrder?.containerNo;
      },
    },
    {
      title: '预计到港时间',
      hideInSearch: true,
      width: 100,
      dataIndex: 'estimateArriveTime',
      fieldFormat: (record: any) => {
        return formatTimes(record?.blnoOrder?.estimateArriveTime);
      },
    },
    {
      title: '预计提货时间',
      width: 100,
      hideInSearch: true,
      dataIndex: 'estimatePickupTime',
      valueType: 'dateTime',
      fieldFormat: (record: any) => {
        return formatTimes(record?.blnoOrder?.estimatePickupTime);
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        <CargoDetail record={record} btnText={'查看'} key={record?.id} />,
        /*      <Button
                key="edit"
                type="link"
                onClick={() => {
                  setValue(record);
                }}
              >
                编辑
              </Button>,
              <Button
                key="del"
                type="link"
                danger
                onClick={() => deleteIdRule(record.id)}
                style={{ visibility: record.removeable === 0 ? 'hidden' : 'visible' }}
                disabled={record.removeable === '0'}
              >
                删除
              </Button>,*/
      ],
    },
  ];

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    console.log('check', checkData);
    const ids = checkData
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id)
      .join(',');
    setLoading(true);
    const res = await appendPieces({ shipmentId: details?.id, pieceIds: ids });
    if (res.status) {
      setLoading(false);
      Refresh();
      setIsModalOpen(false);
    } else {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const getList = async () => {
    return await waybillListAPIList({
      service_id: 'Waybill',
    });
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    // signInTime
  };
  return (
    <>
      <div onClick={showModal}>
        {/* <IconItem name="#icon-dizhibao" style={{ marginRight: '6px' }} /> */}
        {!btnTitle ? (
          <div>
            <Button type={'primary'}>追加</Button>
          </div>
        ) : (
          <div
            style={{
              display: 'inline-block',
              cursor: 'pointer',
              position: 'absolute',
              top: '374px',
              right: '50px',
              zIndex: 999,
              //       top: '319px',
              // right: '50px',
              // z-index: 999
            }}
          >
            <a style={{ display: 'inline-block' }}>{btnTitle}</a>
          </div>
        )}
      </div>
      <Drawer
        title="追加货物"
        style={{
          width: '100vw',
          position: 'fixed',
          top: '1px',
          bottom: '1px',
          // marginBottom: '-1em',
          right: '1px',
          left: '1px',
        }}
        zIndex={1001}
        destroyOnClose
        open={isModalOpen}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <div style={{ height: 'calc(100% - 40px)' }}>
          <HeadTotal checkData={checkData} statistic={Statistics} />
          {/* <div style={{ marginLeft: '25px' }}>
            <span>件数：<span style={{ color: 'rgb(100, 64, 255)' }}>{total?.number}</span></span>
            <span style={{ margin: '0 20px' }}>重量：<span style={{ color: 'rgb(0, 186, 217)' }}>{total?.weight}</span></span>
            <span>放数：<span style={{ color: 'rgb(255, 64, 165)' }}>{total?.squares}</span></span>
          </div> */}
          <>
            <SuperTables
              ref={actionRef}
              instanceId="AdditionalGoods"
              columns={columns}
              isTree={true}
              isHideToolBar={false}
              warpHeight={340}
              /*    expandable={{
                      childrenColumnName:  'pieceList',
                      expandRowByClick:true
                      //defaultExpandAllRows: true,
                    }}*/
              rowSelection={(value: any) => {
                setCheckData(value);
                console.log('value', value);
              }}
              request={async (params: any, action: any) => {
                console.log('追加货物');
                // console.log('tableactiveKey',activeKey);
                let msg = await getBoxList({
                  condition: params?.condition || {},
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  type: 1,
                });
                setStatistics(msg?.data?.statistics);
                return {
                  data:
                    msg?.data?.list?.map((item: any) => {
                      return { ...item, children: item?.pieceList };
                    }) || [],
                  success: msg?.status,
                  total: msg?.data?.amount || msg?.data?.total,
                };
              }}
              filters={{
                keyword: {
                  type: 'search',
                  value: '',
                  termQuery: false,
                },
                shipmentMethod: {
                  desc: '派送方式',
                  type: 'select',
                  range: DispatchMap,
                  value: '',
                },
                warehouseId: {
                  desc: '交货仓库',
                  type: 'overseas',
                  value: '',
                },
                fbaCode: {
                  desc: 'FBA仓库',
                  type: 'custom',
                  value: '',
                  generate: async (v: any) => {
                    const res = await getWarehouseListAPI({
                      keyword: v,
                      type: 1,
                    });
                    if (res?.status) {
                      return res?.data?.list.map((item: any) => {
                        return {
                          label: item?.name || item?.fbaCode,
                          value: item?.name || item?.fbaCode,
                        };
                      });
                    }
                  },
                },
                estimateArriveTime: {
                  desc: '到港时间',
                  type: 'dateTimeRange',
                  value: '',
                },
                /*   clientId: {
                  desc: '客户',
                  type: 'client',
                  value: '',
                },*/
                zipCode: {
                  desc: '邮编',
                  type: 'text',
                  value: '',
                },
                estimatePickupTime: {
                  desc: '提货时间',
                  type: 'dateTimeRange',
                  value: '',
                },
              }}
            />
          </>
        </div>
        <div
          key="btn"
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '40px',
            zIndex: '10',
          }}
        >
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={Loading}
            onClick={handleOk}
            style={{ marginLeft: 10 }}
          >
            确认
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default AdditionalGoods;
