import { message, Select, Space, Tag } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import styles from './styles.less';

const { CheckableTag } = Tag;

const DateSelect = ({ type = 1, handleEdit, value, isDetail = true,disabled }: any) => {
  //type 1月结 2半月结
  // 创建一个空数组来存储对象
  const options: any = [];
  const frantMonth: any = [];
  for (let i = 1; i <= 15; i++) {
    frantMonth.push(i);
  }
  // 使用循环生成1到28的值和标签，并将它们添加到对象中
  for (let i = 1; i <= 28; i++) {
    const obj = {
      value: i,
      label: i, // 你可以根据需要自定义标签
    };
    // 将对象添加到数组中
    options.push(obj);
  }
  options.push({ value: '月底', label: '月底' });
  /*选中*/
  const [selectedTags, setSelectedTags] = useState<any>([]);
  useEffect(() => {
    if (value && value?.length > 0) {
      /*只有数字能比较大小，这个处理一下*/
      setSelectedTags(
        value
          ?.split(',')
          .map((item: any) => (item !== '月底' ? Number(item) : item)),
      );
    }
  }, [value]);
  const handleChange = (tag: any, checked: boolean) => {
    /*选中就加上没选中去去掉*/
    const nextSelectedTags = checked
      ? [...selectedTags, tag]
      : selectedTags.filter((t: any) => t !== tag);
    if (nextSelectedTags.length > (type === 1 ? 1 : 2)) {
      message.error(`最多只能选${type}个日期！`);
      return;
    }
    if (type === 1) {
      //月底
      if (isDetail) {
        handleEdit({ settlementDate: nextSelectedTags[0] });
      }
    } else {
      /*半月结选第二的日期必须在*/
      console.log('nextSelectedTags', nextSelectedTags);
      if (nextSelectedTags.length > 1) {
        if (
          frantMonth.includes(nextSelectedTags[0]) &&
          frantMonth.includes(tag)
        ) {
          message.error(`1-15最多只能选1个日期！`);
          return;
        }
        if (
          !frantMonth.includes(nextSelectedTags[0]) &&
          !frantMonth.includes(tag)
        ) {
          message.error(`16-月底最多只能选1个日期！`);
          return;
        }
        const sortData = nextSelectedTags
          .sort(function (a, b) {
            if (a === '月底') {
              return 1;
            } else if (b === '月底') {
              return -1;
            }
            return a - b; // 根据数字大小进行排序
          })
          .join(',');
        if (isDetail) {
          handleEdit({ settlementDate: sortData });
        }
      }
    }

    setSelectedTags(
      /*排序*/
      nextSelectedTags.sort(function (a, b) {
        if (a === '月底') {
          return 1;
        } else if (b === '月底') {
          return -1;
        }
        return a - b; // 根据数字大小进行排序
      }),
    );
  };

  /*删除标签*/
  const tagChange = (value: any) => {
    setSelectedTags([...value]);
  };
  return (
    <>
      <Select
        options={options}
        placeholder="日期选择"
        disabled={disabled}
        mode={'tags'}
        tokenSeparators={[',']}
        value={selectedTags}
        style={{ minWidth: '200px' }}
        maxTagCount={2}
        onClear={() => {
          setSelectedTags([]);
        }}
        onChange={tagChange}
        dropdownRender={() => {
          return (
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {options?.map((item: any, index: any) => {
                return (
                  <div
                    key={index}
                    style={{
                      width: '14.28%',
                      marginBottom: '10px',
                      boxSizing: 'border-box',
                    }}
                  >
                    <CheckableTag
                      style={{
                        width: '36px',
                        textAlign: 'center',
                      }}
                      key={item.value}
                      checked={
                        selectedTags?.length > 0
                          ? selectedTags?.includes(item.value)
                          : false
                      }
                      onChange={(checked) => handleChange(item.value, checked)}
                    >
                      {item.value}
                    </CheckableTag>
                  </div>
                );
              })}
            </div>
          );
        }}
      />
    </>
  );
};

export default React.memo(DateSelect);
