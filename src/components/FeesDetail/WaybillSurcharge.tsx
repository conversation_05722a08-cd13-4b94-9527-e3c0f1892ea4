/*运单加收*/
import { Button, Col, Form, Input, Radio, Row, Select, Table } from 'antd';
import { WaybillSurchargeColumns } from './columns';
import React from 'react';
import {
  averageType,
  averageTypeMap,
  convertObjectToArray,
} from '@/utils/constant';
import { calculateSingSum } from '@/utils/utils';

const WaybillSurcharge = (props: any) => {
  const { isDetail, totalMoney, myAddList, addAverageType, addfees } = props;
  console.log('addfee', addfees);
  console.log('myAddList', myAddList);
  /*计算选中总价 */
  const countTotal = (type: any, array: any) => {
    let total: number = 0;
    array
      ?.filter((i: any) => myAddList.includes(i.waybillId))
      .map((item: any) => {
        const addData = Number(item[type]);
        total += addData;
        return total;
      });
    return total;
  };
  /*选中的总额*/
  const SelectTotal = countTotal(averageTypeMap[addAverageType], addfees);
  console.log('SelectTotal', SelectTotal);
  return (
    <div>
      <Form.Item name={['addedWaybillFee', 'addedWaybillState']}>
        <Radio.Group
          style={{ marginBottom: 8 }}
          buttonStyle="solid"
          defaultValue={0}
        >
          <Radio.Button value={0}>不加收</Radio.Button>
          <Radio.Button value={1}>加收运单</Radio.Button>
        </Radio.Group>
      </Form.Item>
      {props.isAddedWaybillFee !== 0 && (
        <Row>
          <Col>
            <Form.Item
              label={'费用描述（面向客户）'}
              labelCol={{ span: 12 }}
              name={['addedWaybillFee', 'feeName']}
            >
              <Input placeholder={'请输入费用备注'}></Input>
            </Form.Item>
          </Col>
          <Col>
            <Form.Item
              label={'费用备注'}
              name={['addedWaybillFee', 'averageType']}
              noStyle
            >
              <Select
                defaultValue={1}
                style={{ marginRight: '5px', width: '200px' }}
                options={convertObjectToArray(averageType)}
              />
            </Form.Item>
            <Button type={'primary'}>计算</Button>
          </Col>
        </Row>
      )}
      {props?.isAddedWaybillFee !== 0 && (
        <Table
          columns={
            props?.isAddedWaybillFee === 1
              ? WaybillSurchargeColumns({
                  SelectTotal: SelectTotal,
                  type: addAverageType,
                  totalMoney: totalMoney,
                  myAddList: myAddList,
                })
              : WaybillSurchargeColumns({
                  SelectTotal: SelectTotal,
                  type: addAverageType,
                  totalMoney: totalMoney,
                  myAddList: myAddList,
                }).filter((i: any) => i.title !== '运单号')
          }
          // @ts-ignore
          rowSelection={
            isDetail
              ? false
              : {
                  onChange: (selectedRowKeys) => {
                    console.log(selectedRowKeys);
                    props?.setMyAddList(selectedRowKeys);
                  },
                  selectedRowKeys: props?.myAddList,
                }
          }
          rowKey={'waybillId'}
          dataSource={props?.addfees}
        />
      )}
    </div>
  );
};
export default WaybillSurcharge;
