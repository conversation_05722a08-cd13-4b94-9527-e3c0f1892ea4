import {
  ModalForm,
  ProDescriptions,
  // ProFormSelect,
} from '@ant-design/pro-components';
import { Button, Form, Image, Row } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { getAvataUrl } from '@/shared/Enumeration';
import { getBillDetailById } from '@/services/financeApi';

const FeesDetail = (props: any) => {
  const type = props?.type || 'primary';
  const title = props?.title || '添加费用';

  const [open, setOpen] = useState<any>(false);
  /*详情的值*/
  const [dataBase, setDataBase] = useState<any>();

  const [form] = Form.useForm<any>();

  /*获取详情*/
  const getDetail = async () => {
    const res = await getBillDetailById({
      billId: props?.id,
    });
    if (res?.status) {
      setDataBase(res?.data?.info);
    }
  };
  useEffect(() => {
    if (open) {
      getDetail();
    }
  }, [open]);

  return (
    <ModalForm
      title={title}
      trigger={<Button type={type}>{title}</Button>}
      form={form}
      initialValues={{
        costReconciliationFee: {
          costReconciliationType: 1,
          averageType: '1',
        },
        addedWaybillFee: { addedWaybillState: 0, averageType: '1' },
        spaceFee: { currency: '10', rate: '1' },
      }}
      width={1200}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 8 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      className={styles.wrap}
      layout="horizontal"
      submitTimeout={2000}
      onOpenChange={() => setOpen(!open)}
    >
      <Row>
        <ProDescriptions column={3}>
          <ProDescriptions.Item
            dataIndex="reason"
            label="编号"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.id}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="reason"
            label="服务商"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.counterpartyName}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="reason"
            label="费用名称"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.reason}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="reason"
            label="费用"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.amount}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="currency"
            label="币种"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.currencyTypeDesc}
          </ProDescriptions.Item>

          <ProDescriptions.Item
            dataIndex="currency"
            label="附件"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.data?.attachmentFiles
              ?.split(',')
              .map((item: any, index: any) => {
                return (
                  <span>
                    <a
                      href={'https://static.kmfba.com/' + item}
                      target="_blank"
                      rel="noreferrer"
                    >
                      {item}
                    </a>
                  </span>
                );
              })}
          </ProDescriptions.Item>
          <ProDescriptions.Item
            dataIndex="currentRate"
            label="汇率"
            editable={false}
            valueType="text"
            className={styles.require}
          >
            {dataBase?.currentRate}
          </ProDescriptions.Item>
        </ProDescriptions>
      </Row>
    </ModalForm>
  );
};
export default FeesDetail;
