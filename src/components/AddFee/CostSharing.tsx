/*成本分摊*/
import { TableRowSelection } from 'antd/es/table/interface';
import { Col, Form, Radio, Row, Select, Table } from 'antd';
import { SharingColumns } from './columns';
import React from 'react';
import { calculateSingSum } from '@/utils/utils';
import {
  averageType,
  averageTypeMap,
  convertObjectToArray,
} from '@/utils/constant';

const CostSharing = (props: any) => {
  const {
    isDetail,
    fees,
    myList,
    costReconciliationType,
    costAverageType,
    totalMoney,
    spaceFee,
    currency,
  } = props;

  /*计算选中总价 */
  const countTotal = (type: any, array: any) => {
    let total: number = 0;
    array?.map((item: any) => {
      if ('subList' in item) {
        const addData = calculateSingSum(item?.subList, type);
        if (myList.includes(item.id)) {
          total += Number(addData);
        } else {
          item?.subList?.map((a: any) => {
            if (myList.includes(a.id)) {
              total += Number(a['fee'][type]);
            }
            return total;
          });
        }
        return total;
        // 对象拥有该属性
      } else {
        // 对象不拥有该属性
        const addData = Number(item['fee'][type]);
        total += addData;
        return total;
      }
    });
    return total;
  };
  /*选中的选项*/
  const rowSelection: TableRowSelection<any> = {
    checkStrictly: false,
    onChange: (selectedRowKeys) => {
      props?.setMyList(selectedRowKeys);
    },
    selectedRowKeys: myList,
  };
  console.log(fees);
  /*选中的总额*/
  const SelectTotal = countTotal(
    averageTypeMap[costAverageType],
    fees[0]?.subList,
  );
  console.log('countTotal', SelectTotal);
  return (
    <div>
      <Row>
        <Form.Item
          name={['costReconciliationFee', 'costReconciliationType']}
          noStyle
        >
          <Radio.Group style={{ marginBottom: 8 }} buttonStyle="solid">
            <Radio.Button value={1}>不纳入成本汇算</Radio.Button>
            <Radio.Button value={2}>纳入运单成本汇算</Radio.Button>
            <Radio.Button value={3}>仅纳入提单成本汇算</Radio.Button>
          </Radio.Group>
        </Form.Item>
      </Row>
      <Row>
        <Col>
          <Form.Item name={['costReconciliationFee', 'averageType']} noStyle>
            <Select
              style={{ marginRight: '5px' }}
              options={convertObjectToArray(averageType)}
            />
          </Form.Item>
          {/* <Button type={'primary'}>计算</Button>*/}
        </Col>
      </Row>
      {props?.costReconciliationType !== 1 && (
        <Table
          columns={
            props?.costReconciliationType === 2
              ? SharingColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: totalMoney,
                  myList: myList,
                  spaceFee: spaceFee,
                  currency: currency,
                })
              : SharingColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: props?.totalMoney,
                  myList: myList,
                  spaceFee: spaceFee,
                  currency: currency,
                }).filter((i: any) => i.title !== '运单号')
          }
          // @ts-ignore
          rowSelection={
            isDetail
              ? false
              : {
                  ...rowSelection,
                }
          }
          dataSource={fees}
          rowKey="id"
          expandable={{
            childrenColumnName: 'subList',
            defaultExpandAllRows: true,
          }}
        />
      )}
    </div>
  );
};
export default CostSharing;
