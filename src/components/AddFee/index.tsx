import {
  ModalForm,
  ProFormSelect,
  // ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Col, Form, message, Row, Tabs, Upload } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { convertObjectToArray, currency_map } from '@/utils/constant';
import {
  GetCustomFee,
  GetCustomRate,
  getPreviewBill,
  showAddFeePreViews,
  showFeePreViews,
  UploadFeeFile,
} from '@/services/bol';
import WaybillSurcharge from './WaybillSurcharge';
import CostSharing from '@/components/AddFee/CostSharing';
import { UploadOutlined } from '@ant-design/icons';

import { addFeeManually } from '@/services/financeApi';

const AddFee = (props: any) => {
  const type = props?.type || 'primary';
  const title = props?.title || '添加费用';
  /*是否是详情*/
  const isDetail = !!props.id;
  const { details } = props;
  console.log(details);
  const [open, setOpen] = useState<any>(false);
  /*成本分摊的值*/
  const [fees, setFees] = useState<any>();
  /*运单加收的值*/
  const [addfees, setAddFees] = useState<any>();
  /*加收原始值*/
  const [originalData, setOriginalData] = useState<any>();

  /*1成分分摊2运单加收*/
  const [waybillType, setWaybillType] = useState<string>('1');
  /*加收选中项*/
  const [myAddList, setMyAddList] = useState<any>([]);
  /*均摊选中项*/
  const [myList, setMyList] = useState<any>([]);
  const [form] = Form.useForm<any>();
  const [fileList, setFileList] = useState<any>([]);
  /*运单加收 运单是否加收*/
  const isAddedWaybillFee = Form.useWatch(
    ['addedWaybillFee', 'addedWaybillState'],
    form,
  );
  /*成本汇算 分摊类型*/
  const costAverageType = Form.useWatch(
    ['costReconciliationFee', 'averageType'],
    form,
  );
  /*运单加收 分摊类型*/
  const addAverageType = Form.useWatch(
    ['addedWaybillFee', 'averageType'],
    form,
  );
  /*成本汇算 成本汇算类型*/
  const costReconciliationType = Form.useWatch(
    ['costReconciliationFee', 'costReconciliationType'],
    form,
  );
  /*预算金额*/
  const totalMoney = Form.useWatch(['spaceFee', 'estimateFee'], form);
  /*汇率*/
  const spaceFee = Form.useWatch(['spaceFee', 'rate'], form);
  /*币种*/
  const currency = Form.useWatch(['spaceFee', 'currency'], form);
  /*获取展示费用*/
  const getFees = async () => {
    try {
      //成本分摊
      if (waybillType === '1') {
        const { status, data } = await showFeePreViews({
          keyword: details?.id,
          costReconciliationType: costReconciliationType,
          counterpartyType: 'Declaration',
        });
        if (status) {
          const newData = data?.formList?.map((Fitem: any, Findex: any) => {
            if (Fitem?.subList?.length > 0) {
              /*过滤后端返回的空数组。。。*/
              return {
                ...Fitem,
                subBlno: Fitem?.subBlno,
                id: `${Findex}`,
                subList: Fitem?.subList?.map((item: any, index: any) => {
                  return {
                    ...item,
                    id: `${Findex}-${index}`,
                  };
                }),
              };
            } else {
              return {
                fee: Fitem?.fee,
                subBlno: Fitem?.subBlno,
                id: `${Findex}`,
              };
            }
          });
          setFees([{ ...data, subList: newData, id: '10000000' }]);
        }
      } else {
        //运单加收
        const { status, data } = await showAddFeePreViews({
          keyword: details?.id,
          type: waybillType,
          costReconciliationType: costReconciliationType,
          addedWaybillState: isAddedWaybillFee,
          counterpartyType: 'Declaration',
        });
        if (status) {
          //运单加收
          setOriginalData(data);
          setAddFees(data);
        }
      }
    } catch (err) {
      console.log('获取信息抛出异常: ', err);
    }
  };

  /*获取费用列表*/
  useEffect(() => {
    getFees();
  }, [isAddedWaybillFee, costReconciliationType, waybillType]);
  /*上传文件*/
  const uploadFile = async (res: any) => {
    //setLoading(true)
    const formData = new FormData();
    if (fileList.length === 0) {
      return;
    }
    fileList.forEach((item: any) => {
      formData.append('multipartFiles', item);
    });

    formData.append('id', res?.billId);
    try {
      const { status } = await UploadFeeFile(formData);
      if (status) {
        message.success('操作成功');
        //setLoading(false);
      }
    } catch (error) {
      //setLoading(false);
    }
  };
  /*提交添加费用*/

  const onFinish = async (values: any) => {
    console.log('提交', values);

    const params = {
      service_id: 'Waybill',
      /*报关单号*/
      keyword: details.id,
      /*费用类型*/
      counterpartyType: 'Declaration',
      /*基本信息对象*/
      spaceFee: values.spaceFee,
      costReconciliationFee: {
        ...values.costReconciliationFee,
        blno: fees[0].blno,
        fee: fees[0].fee,
        select: true,
        costReconciliationType,
        averageType: costAverageType,
        formList: fees[0]?.subList?.map((item: any) => {
          /*去掉id*/
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...newItem } = item;
          return {
            ...newItem,
            subList: item?.subList?.map((i: any) => {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              const { id, ...newI } = i;
              return {
                ...newI,
                select: myList.includes(i?.id),
              };
            }),
            select: myList.includes(item?.id),
          };
        }),
      },
      addedWaybillFee: {
        blno: details?.blno,
        addedWaybillState: isAddedWaybillFee || 0,
        averageType: addAverageType,
        fee: originalData?.fee,
        select: true,
        waybillFees: originalData?.map((item: any) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...newItem } = item;
          return {
            ...newItem,
            select: true,
          };
        }),
      },
    };
    /*自主填写分摊逻辑*/
    if (values?.Sharing) {
      //获取自主填写的数据
      const keys = Object.keys(values?.Sharing);
      params.costReconciliationFee.formList = fees[0]?.subList?.map(
        (item: any) => {
          /*去掉id*/
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...newItem } = item;
          return {
            ...newItem,
            subList: item?.subList?.map((i: any) => {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              const { id, ...newI } = i;
              return {
                ...newI,
                fee: {
                  ...newI.fee,
                  amount: keys.includes(id) ? values?.Sharing[id] : '0',
                },
                select: myList.includes(i?.id),
              };
            }),
            select: myList.includes(item?.id),
          };
        },
      );
    }
    /*自主填写附件费逻辑*/
    if (values?.Surcharge) {
      //获取自主填写的数据
      const keys = Object.keys(values?.Surcharge);
      console.log('keys', keys);
      params.addedWaybillFee.waybillFees = originalData?.map((item: any) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { waybillId, ...newItem } = item;
        return {
          ...newItem,
          amount: keys.includes(waybillId) ? values?.Surcharge[waybillId] : '0',
          select: true,
        };
      });
    }
    const { status, data } = await addFeeManually(params);
    if (status) {
      await uploadFile(data);
      message.success('提交成功');
      props?.getDetail();
      return true;
    } else {
      return false;
    }
  };
  const tabChange = (key: any) => {
    setWaybillType(key);
    console.log(key);
  };

  const items: any = [
    {
      key: '1',
      label: `成本分摊`,
      children: (
        <CostSharing
          setMyList={setMyList}
          myList={myList}
          costReconciliationType={costReconciliationType}
          costAverageType={costAverageType}
          fees={fees}
          isDetail={isDetail}
          totalMoney={totalMoney}
          spaceFee={spaceFee}
          currency={currency}
        />
      ),
    },
    {
      key: '2',
      label: `运单加收`,
      children: (
        <WaybillSurcharge
          isAddedWaybillFee={isAddedWaybillFee}
          myAddList={myAddList}
          addfees={addfees}
          setMyAddList={setMyAddList}
          isDetail={isDetail}
          totalMoney={totalMoney}
          addAverageType={addAverageType}
          spaceFee={spaceFee}
          currency={currency}
        />
      ),
    },
  ];

  /*费用名称改变*/
  const feeNameChange = async (v: any) => {
    /*调用接口获取预计值*/
    const { status, data } = await GetCustomFee({
      feeType: v,
      keyword: details?.id,
      counterpartyType: 'Declaration',
    });
    if (status) {
      if (data === 'null') {
        form.setFieldValue(['spaceFee', 'estimateFee'], '');
      } else {
        form.setFieldValue(['spaceFee', 'estimateFee'], Object.keys(data)[0]);
      }
    }
  };
  /*币种改变*/
  const currencyChange = async (v: any) => {
    /*调用接口获取预计值*/
    const { status, data } = await GetCustomRate({
      fromCode: v,
      toCode: 10,
    });
    if (status) {
      if (!data) {
        form.setFieldValue(['spaceFee', 'rate'], '');
      } else {
        form.setFieldValue(['spaceFee', 'rate'], data?.exchangeRate);
      }
    }
  };
  return (
    <ModalForm
      title={title}
      trigger={
        <Button type={type} disabled={!details?.blno && !details?.spaceId}>
          {title}
        </Button>
      }
      form={form}
      initialValues={{
        costReconciliationFee: {
          costReconciliationType: 1,
          averageType: '1',
        },
        addedWaybillFee: { addedWaybillState: 0, averageType: '1' },
        spaceFee: { currency: '10', rate: '1' },
      }}
      width={1200}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 8 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      className={styles.wrap}
      layout="horizontal"
      submitTimeout={2000}
      onOpenChange={() => setOpen(!open)}
      onFinish={onFinish}
    >
      <Row>
        <Col span={8}>
          <ProFormSelect
            name={['spaceFee', 'feeType']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="费用名称"
            disabled={isDetail}
            request={async () => {
              const res = await getPreviewBill({
                keyword: details?.id,
                counterpartyType: 'Declaration',
              });
              const { data, status } = res;
              if (status) {
                const transformedData = data?.feeType?.map((item: any) => {
                  const key = Object.keys(item)[0];
                  const value = item[key];
                  return { label: value, value: key };
                });
                return transformedData;
              }
              return [];
            }}
            fieldProps={{
              onChange: (value) => {
                feeNameChange(value);
              },
              filterOption: false,
            }}
          />
        </Col>
        <Col span={8}>
          <ProFormSelect
            name={['spaceFee', 'currency']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="币种"
            disabled={isDetail}
            options={convertObjectToArray(currency_map)}
            fieldProps={{
              onChange: (value) => {
                currencyChange(value);
              },
              filterOption: false,
            }}
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name={['spaceFee', 'estimateFee']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="预计费用"
            disabled={isDetail}
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name={['spaceFee', 'rate']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="汇率"
            disabled={isDetail}
          />
        </Col>
        <Col span={8}>
          <Form.Item label={'附件'}>
            <Upload
              name="certificateFiles"
              multiple
              fileList={fileList}
              onRemove={(e) => {
                setFileList(
                  fileList?.filter((item: any) => item?.uid !== e.uid),
                );
              }}
              customRequest={({ file }) => {
                fileList.push(file);
                setFileList([...fileList]);
              }}
            >
              {!isDetail && <Button icon={<UploadOutlined />}>上传文件</Button>}
            </Upload>
          </Form.Item>
        </Col>
      </Row>

      <div
        style={{
          width: '1200px',
          background: '#f4f4f4',
          position: 'relative',
          right: '24px',
          height: '10px',
        }}
      ></div>
      <div>
        <Tabs defaultActiveKey="1" items={items} onChange={tabChange} />
      </div>
    </ModalForm>
  );
};
export default AddFee;
