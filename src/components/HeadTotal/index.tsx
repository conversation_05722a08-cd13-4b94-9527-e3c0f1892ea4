import styles from '@/pages/BOL/PreplanCabin/Waybill2/index.less';
import { Col, Divider, Row, Tooltip } from 'antd';
import React from 'react';
import { calculateSum } from '@/utils/utils';

const HeadTotal = (props: any) => {
  const { statistic, checkData } = props;
  console.log('statistic', statistic);
  console.log('checkData', checkData);
  //子单数据
  const pieceData = checkData.filter((item: any) => !item?.children);
  console.log('pieceData', pieceData);
  const totalVolume = calculateSum(pieceData, 'volume', 5);
  const totalWeight = calculateSum(pieceData, 'weight', 5);
  const Dests = pieceData.map((item: any) => item.dest);
  const uniqueDests = [...new Set(Dests)];
  console.log('dest', uniqueDests);
  const selectData = {
    num: pieceData.length,
    totalVolume,
    totalWeight,
    Dests: uniqueDests.join(' '),
  };
  console.log('pieceData', pieceData);
  const flexParam = '1 0 0';
  return (
    <div className={styles.total}>
      <Row wrap={false}>
        <Col style={{ width: '20em' }}>
          <span style={{ color: '#000', fontWeight: 600, marginRight: '1em' }}>
            总计
          </span>
          <span style={{ display: 'inline-block', width: '7em' }}></span>
          <span style={{ display: 'inline-block' }}>
            <span style={{ marginRight: '2px', color: '#BBB' }}>件：</span>
            <span style={{ color: '#6440FF' }}>{statistic?.countPiecesId}</span>
          </span>
        </Col>
        <Col style={{ whiteSpace: 'nowrap', width: '300px' }}>
          <span className={styles.label}>立方：</span>{' '}
          <span style={{ color: '#00BAD9' }}>
            {statistic?.sumVolume?.toFixed(6)}
          </span>
        </Col>
        <Col style={{ whiteSpace: 'nowrap', width: '300px' }}>
          <span className={styles.label}>实重：</span>
          <span style={{ color: '#333', fontWeight: 500 }}>
            {statistic?.sumWeight}
          </span>
        </Col>
        <Col style={{ whiteSpace: 'nowrap' }}>
          <span className={styles.label}>配车标识：</span>
          <span style={{ color: '#333', fontWeight: 500 }}></span>
        </Col>

        {/*   <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
                <span className={styles.label}>密度比例：</span>{' '}
                <span style={{ color: '#333', fontWeight: 500 }}>
              {statistic?.sumOuterActualWeight
                  ? (
                      statistic?.sumOuterActualWeight / statistic?.sumOuterVolume
                  ).toFixed(2)
                  : '-'}{' '}
            </span>
            </Col>
            <Col flex={flexParam} className="text-ellipsis">
                <span className={styles.label}>出货体积重：</span>{' '}
                <span style={{ color: '#333', fontWeight: 500 }}>
              <Tooltip title={statistic?.sumOuterVolumeWeight?.toFixed(2)}>
                {' '}
                  {statistic?.sumOuterVolumeWeight?.toFixed(2)}{' '}
              </Tooltip>
            </span>
            </Col>
            <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
                <span className={styles.label}>收货计费重：</span>{' '}
                <span style={{ color: '#FF40A5' }}>
              {statistic?.sumChargeWeight?.toFixed(2)}{' '}
            </span>
            </Col>
            <Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>
                <span className={styles.label}>出货计费重：</span>{' '}
                <span style={{ color: '#FF40A5' }}>
              {statistic?.sumOuterChargeWeight?.toFixed(2)}{' '}
            </span>
            </Col>*/}
      </Row>
      <div style={{ width: '60vw' }}>
        {' '}
        <Divider style={{ margin: '0.3em 0' }} />
      </div>
      <Row wrap={false}>
        <Col style={{ width: '20em' }}>
          <span style={{ color: '#000', fontWeight: 600, marginRight: '1em' }}>
            已选
          </span>
          <span style={{ display: 'inline-block', width: '7em' }}></span>
          <span style={{ display: 'inline-block' }}>
            <span
              style={{
                marginRight: '2px',
                visibility: 'hidden',
                color: '#BBB',
              }}
            >
              件：
            </span>
            <span style={{ color: '#6440FF' }}>{selectData?.num}</span>
          </span>
        </Col>
        <Col style={{ whiteSpace: 'nowrap', width: '300px' }}>
          <span style={{ visibility: 'hidden' }} className={styles.label}>
            立方：
          </span>
          <span style={{ color: '#00BAD9' }}>
            {' '}
            {Number(selectData?.totalVolume).toFixed(2)}
          </span>
        </Col>
        <Col style={{ whiteSpace: 'nowrap', width: '300px' }}>
          <span className={styles.label} style={{ visibility: 'hidden' }}>
            实重：
          </span>
          <span style={{ color: '#333', fontWeight: 500 }}>
            {Number(selectData?.totalWeight).toFixed(2)}
          </span>
        </Col>
        <Col style={{ whiteSpace: 'nowrap' }}>
          <span style={{ color: '#333', fontWeight: 500 }}>
            {selectData?.Dests}
          </span>
        </Col>
        {/*<Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>*/}
        {/*<span style={{ visibility: 'hidden' }} className={styles.label}>*/}
        {/*  密度比例：*/}
        {/*</span>*/}
        {/*    <span style={{ color: '#333', fontWeight: 500 }}>*/}
        {/*  {' '}*/}
        {/*        {isNaN(totalState?.outerActualWeight / totalState?.OuterVolume)*/}
        {/*            ? '0.00'*/}
        {/*            : (*/}
        {/*                totalState?.outerActualWeight / totalState?.OuterVolume*/}
        {/*            ).toFixed(2)}*/}
        {/*</span>*/}
        {/*</Col>*/}
        {/*<Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>*/}
        {/*<span className={styles.label} style={{ visibility: 'hidden' }}>*/}
        {/*  出货体积重：*/}
        {/*</span>{' '}*/}
        {/*    <span style={{ color: '#333', fontWeight: 500 }}>*/}
        {/*  {Number(totalState?.outerVolumeWeight).toFixed(2)}{' '}*/}
        {/*</span>*/}
        {/*</Col>*/}
        {/*<Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>*/}
        {/*<span style={{ visibility: 'hidden' }} className={styles.label}>*/}
        {/*  收货计费重：*/}
        {/*</span>*/}
        {/*       {statistic?.sumChargeWeight}*/}
        {/*</Col>*/}

        {/*<Col flex={flexParam} style={{ whiteSpace: 'nowrap' }}>*/}
        {/*<span style={{ visibility: 'hidden' }}>*/}
        {/*  {' '}*/}
        {/*    className={styles.label}出货计费重:*/}
        {/*</span>*/}
        {/*      {statistic?.sumOuterChargeWeight}*/}
        {/*      {statistic?.sumOuterChargeWeight}*/}
        {/*</Col>*/}
      </Row>
    </div>
  );
};

export default HeadTotal;
