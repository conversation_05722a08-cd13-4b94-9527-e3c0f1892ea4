import {
    formatTime,
    getLastLastLastWeekRange,
    getLastLastWeekRange,
    getLastWeekRange,
    getNextWeekRange,
    getTheWeekRange,
    getTodayWeekRange
} from "@/utils/format";
import { Select } from "antd";
import { FC, memo, useEffect, useState } from "react";
interface Props {
    targetSelect: (data: any) => void
    allowClear: boolean
    defaultValue?:string
}
const WeekDate: FC<Props> = memo((props) => {
    const { targetSelect, allowClear,defaultValue } = props
    const [previous, setPrevious] = useState<any>()
    const [value,setValue] = useState<any>(null)
    useEffect(() => {
        if(defaultValue) {
            setValue(defaultValue)
        }
    },[defaultValue])
    useEffect(() => {
        setPrevious([
            { label: `${getLastLastLastWeekRange().startWeek}`, value: `${getLastLastLastWeekRange().startWeek}` },
            { label: `${getLastLastWeekRange().startWeek}`, value: `${getLastLastWeekRange().startWeek}` },
            { label: '-1周', value: '-1周' },
            { label: '本周', value: '本周' },
            { label: `${getNextWeekRange().startWeek}`, value: `${getNextWeekRange().startWeek}` },
            { label: `${getTheWeekRange().startWeek}`, value: `${getTheWeekRange().startWeek}` }
        ])
    }, [])
    const handleChange = (e: any) => {
        if (e == '-1周') {
            targetSelect({
                startDate: formatTime(getLastWeekRange().startDate),
                endDate: formatTime(getLastWeekRange().endDate)
            })
            setValue('-1周')
        } else if (e == getLastLastWeekRange().startWeek) {
            targetSelect({
                startDate: formatTime(getLastLastWeekRange().startDate),
                endDate: formatTime(getLastLastWeekRange().endDate)
            })
            setValue(getLastLastWeekRange().startWeek)
        } else if (e == getLastLastLastWeekRange().startWeek) {
            targetSelect({
                startDate: formatTime(getLastLastLastWeekRange().startDate),
                endDate: formatTime(getLastLastLastWeekRange().endDate)
            })
            setValue(getLastLastLastWeekRange().startWeek)
        } else if (e == '本周') {
            targetSelect({
                startDate: formatTime(getTodayWeekRange().startDate),
                endDate: formatTime(getTodayWeekRange().endDate)
            })
            setValue('本周')
        } else if (e == getNextWeekRange().startWeek) {
            targetSelect({
                startDate: formatTime(getNextWeekRange().startDate),
                endDate: formatTime(getNextWeekRange().endDate)
            })
            setValue(getNextWeekRange().startWeek)
        } else if (e == getTheWeekRange().startWeek) {
            targetSelect({
                startDate: formatTime(getTheWeekRange().startDate),
                endDate: formatTime(getTheWeekRange().endDate)
            })
            setValue(getTheWeekRange().startWeek)
        } else {
            targetSelect({})
        }
    }
    return <>
        <Select
            placeholder="请选择"
            allowClear={allowClear}
            value={value ?? '本周'}
            style={{ width: 140 }}
            onChange={handleChange}
            options={previous}
        />
    </>
})
export default WeekDate