import React from 'react';
import { CSSTransition } from 'react-transition-group';
import styles from './style.less'

interface Props {
  isActive: boolean;
  children: React.ReactNode;

}

const TransitionCard = ({ children, isActive, ...props }:Props) => (
  <CSSTransition
    in={isActive}
    timeout={500}
    classNames={styles['fade']}
    {...props}
  >
    {children}
  </CSSTransition>
);
export default React.memo(TransitionCard);