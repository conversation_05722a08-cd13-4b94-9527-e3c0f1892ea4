import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Input, Modal, Row } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
const { TextArea } = Input;
import classNames from 'classnames';
import service from '@/services/home';
const { checkRuleAPI } = service.UserHome;

const EditPopUpBox = ({ onOk, text }: any) => {
  const [value, setValue] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [passedOrNot, setPassedOrNot] = useState<boolean>(false);
  console.log('passedOrNot: ', passedOrNot);
  useEffect(() => {
    if (text) {
      setValue(text);
    }
  }, [text]);

  /* 计算按钮 */
  const calculationList = [
    { id: 1, name: '+', value: '+' },
    { id: 2, name: '-', value: '-' },
    { id: 3, name: 'x', value: '×' },
    { id: 4, name: '÷', value: '÷' },
    { id: 5, name: '不等于', value: '不等于' },
    { id: 6, name: '>', value: '>' },
    { id: 7, name: '≥', value: '≥' },
    { id: 8, name: '=', value: '=' },
    { id: 9, name: '<', value: '<' },
    { id: 10, name: '≤', value: '≤' },
    { id: 11, name: '（', value: '（' },
    { id: 12, name: '）', value: '）' },
    { id: 13, name: '或者', value: '或者' },
    { id: 14, name: '并且', value: '并且' },
    { id: 15, name: '空格', value: '  ' },
    { id: 16, name: '向上进位', value: '向上进位' },
    { id: 17, name: '四舍五入进位', value: '四舍五入进位' },
    { id: 18, name: '取最大值', value: '取最大值' },
    { id: 19, name: '占位', value: '占位' },
    { id: 20, name: '占位', value: '占位' },
  ];
  /* 业务熟悉按钮 */
  const businessList = [
    { id: 1, name: '[燃油费率]', value: '[燃油费率]' },
    { id: 2, name: '[基准价格]', value: '[基准价格]' },
    { id: 5, name: '[偏远地区]', value: '[偏远地区]' },
    { id: 6, name: '[区间偏远]', value: '[区间偏远]' },
    { id: 7, name: '[非FBA地址]', value: '[非FBA地址]' },
    { id: 8, name: '[超长超重]', value: '[超长超重]' },
    { id: 9, name: '[私人地址费]', value: '[私人地址费]' },
    { id: 13, name: '[旺季附加费]', value: '[旺季附加费]' },
    { id: 14, name: '[异性费]', value: '[异性费]' },
    // { id: 13, name: '磁检服务费', value: '磁检服务费' },
    // { id: 14, name: '报关服务费', value: '报关服务费' },
    // { id: 15, name: '仓库操作服务费', value: '仓库操作服务费' },
    { id: 91, name: '占位', value: '占位' },
  ];
  /* 语法检测 */
  const [checkResult, setCheckResult] = useState<any>({});
  const checkRule = async () => {
    try {
      const { status, data } = await checkRuleAPI({ ruleExp: value });
      if (status) {
        setCheckResult(data);
        if(data.valid){
          setPassedOrNot(true)
        }
      }
    } catch (error) {
      console.error('语法检测接口报错', error);
    }
  };
  const reset = () => {
    setValue('');
    setCheckResult({});
  };
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    onOk(value);
    setIsModalOpen(false);
    reset();
  };
  const handleCancel = () => {
    reset();
    setIsModalOpen(false);
  };
  
  useEffect(() => {
    setPassedOrNot(false)
  }, [value])


  return (
    <>
      <Button type='link' onClick={showModal}>编辑</Button>
      <Modal
        title="条件公式设置"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        // maskClosable={false}
        footer={[
          <div key={1} style={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              type="primary"
              // disabled={!checkResult?.valid || !passedOrNot} // 暂时不写语法检测
              onClick={handleOk}
            >
              确定
            </Button>
          </div>,
        ]}
      >
        <div className={styles.warp}>
          <div className={styles.title}>计算按钮</div>
          <div style={{ background: '#FBFBFB', padding: 8 }}>
            <Row gutter={[0, 16]} style={{ marginBottom: 2 }}>
              {calculationList.map((item) => (
                <Col flex={1} key={item.id}>
                  <div
                    onClick={() => {
                      setValue(value + item.value);
                    }}
                    className={classNames(
                      item.name === '占位' ? styles['no-btn'] : styles.btn,
                    )}
                  >
                    {item.name === '占位' ? null : item.name}
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </div>
        <div className={styles.warp}>
          <div className={styles.title}>业务属性按钮</div>
          <div style={{ background: '#FBFBFB', padding: 8 }}>
            <Row gutter={[0, 16]} style={{ marginBottom: 2 }}>
              {businessList.map((item) => (
                <Col flex={1} key={item.id}>
                  <div
                    onClick={() => {
                      setValue(value + item.value);
                    }}
                    className={classNames(
                      item.name === '占位' ? styles['no-btn'] : styles.btn,
                    )}
                  >
                    {item.name === '占位' ? null : item.name}
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </div>

        <div className={styles.warp}>
          <div className={classNames(styles.title, styles.titbox)}>
            <div>预览</div>
            <div>
              <Button type="link" disabled={!value} onClick={reset}>
                重置
              </Button>
              <Button type="link" disabled={!value} onClick={checkRule}>
                语法检测
              </Button>
            </div>
          </div>
          <div style={{ padding: 8 }}>
            <TextArea
              value={value}
              onChange={(e: any) => {
                setValue(e.target.value);
              }}
            />
            {!checkResult?.valid && checkResult?.msg && value ? (
              <div style={{ marginTop: 12 }}>
                <Alert message={checkResult?.msg} type="warning" />
              </div>
            ) : null}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default EditPopUpBox;
