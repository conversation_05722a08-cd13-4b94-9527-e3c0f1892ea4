import { Form, Input, Select } from 'antd';
import { useImperativeHandle, forwardRef, useCallback } from 'react';
import styles from './index.less';
import EditPopUpBox from '../EditPopUpBox';
import usePermissionFiltering from '@/hooks/usePermissionFiltering'
const { TextArea } = Input;

interface Item {
  label: string;
  isRequired: boolean;
  type: string;
  placeholder: string;
  name: string;
  options?: any[];
  pattern?: any;
  isPattern?: boolean;
  setIsServiceCharge?: any;
  
}

interface Props {
  itemList: Item[];
  selectOnChange?: (value: any, s: any) => void;
  setIsServiceCharge?: any;
  formName?:string
}

const TemplateForm = forwardRef((props: Props, ref: any) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [toList,IsAccessible] = usePermissionFiltering()
  const { itemList, selectOnChange,setIsServiceCharge ,formName} = props;
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    onFinish: form,
  }));

  const FormItem = ({ item }: any) => {
    if (item.type === 'input') {
      return (
        <Form.Item
          key={item.label}
          label={<div className={styles.labelC}>{item.label}</div>}
          name={item.name}
          rules={[
            {
              required: item.isRequired,
              message: `必填项不能为空`,
            },
            {
              pattern: item?.isPattern ? item?.pattern : '',
              message: `请输入1以下的小数,保留4位小数`,
            },
          ]}
        >
          <Input placeholder={item.placeholder} />
        </Form.Item>
      );
    }
    if (item.type === 'inputNumber') {
      return (
        <Form.Item
          key={item.label}
          label={<div className={styles.labelC}>{item.label}</div>}
          name={item.name}
          rules={[
            {
              required: item.isRequired,
              message: `必填项不能为空`,
            },
            {
              pattern: new RegExp(/^[0-9]+([.]{1}[0-9]{1,3})?$/),
              message: `请填写数值`,
            },
          ]}
        >
          <Input placeholder={item.placeholder} />
        </Form.Item>
      );
    }

    if (item.type === 'select') {
      return (
        <Form.Item
          key={item.label}
          label={<div className={styles.labelC}>{item.label}</div>}
          name={item.name}
          rules={[
            {
              required: item.isRequired,
              message: `必填项不能为空`,
            },
          ]}
        >
          <Select
            placeholder={item.placeholder}
            options={item.options}
            onChange={(e, s) => {
              console.log('s: ', item);
              if (selectOnChange && item.label === '计重模式') {
                selectOnChange(e, s);
              }
              if(setIsServiceCharge && item.label === '通用服务费'){
                setIsServiceCharge(e)
              }
            }}
          />
        </Form.Item>
      );
    }

    if (item.type === 'textArea') {
      return (
        <>
          <Form.Item
            key={item.label}
            label={<div className={styles.labelC}>{item.label}</div>}
            name={item.name}
            rules={[
              {
                required: item.isRequired,
                message: `必填项不能为空`,
              },
            ]}
          >
            <TextArea placeholder={item.placeholder} />
          </Form.Item>
          <div
            style={{
              display: 'inline-block',
              position: 'relative',
              top: '-22px',
              left: '40px',
            }}
          >
            {/* <Button type="link" >更改</Button> */}
            <EditPopUpBox
              onOk={(e: any) => {
                form.setFieldsValue({ ruleDetail: e });
              }}
            />
          </div>
        </>
      );
    }
    return <></>;
  };
  const isDisabled:any = useCallback(()=>{
    if(formName==='燃油费模版'){
      return !IsAccessible([':Product:FuelCostAccess'])
    }
    if(formName==='服务费模版'){
      return !IsAccessible([':Product:FeeServiceAccess'])
    }
    if(formName==='附加费模版'){
      return !IsAccessible([':Product:FeeSurchargeAccess'])
    }
    // console.log('asdasd',12312312);
    return false
  },[formName])
  return (
    <div className={styles.warp}>
      <Form
        style={{
          maxWidth: 420,
        }}
        name="TemplateForm1"
        form={form}
        autoComplete="off"
        disabled={isDisabled()}
      >
        {itemList.map((item: Item) => {
          return <FormItem key={item.label} item={item} />;
        })}
      </Form>
    </div>
  );
});

export default TemplateForm;
