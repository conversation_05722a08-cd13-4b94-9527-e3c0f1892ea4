.warp{
  display: flex;
  cursor: pointer;
  width: 160px;
  height: 32px;
  background: #F4F4F4;
  border-radius: 4px;

  .box1{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 32px;
    color: #707070;
    border-radius: 4px 0 0 4px;
  }

  .box2{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #707070;
    width: 80px;
    height: 32px;
    border-radius: 0 4px 4px 0;
  }

  .choice{
    color: #fff;
    background: linear-gradient(180deg, #F7BA38 0%, #EE8E20 18%, #EC841A 100%);
    transition: all 0.9s;
  }
}