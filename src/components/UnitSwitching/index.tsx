import { Space } from 'antd';
import classNames from 'classnames';
import s from './index.less';
import { useCallback, useEffect, useState } from 'react';

interface Props {
  onChange?: (activeKey: string) => void;
  weightUnit: any;
}

const types: any = {
  0: { text: 'KG' },
  1: { text: 'LB' },
  2: { text: '盎司' },
};
const UnitSwitching = ({ onChange = () => {}, weightUnit }: Props) => {
  const [activeKey, setActiveKey] = useState('KG');

  useEffect(() => {
    onChange(activeKey);
  }, [onChange, activeKey]);
  useEffect(() => {
    setActiveKey(types[weightUnit]?.text);
  }, [weightUnit]);
  const handleSetActiveKey = useCallback((key: string) => {
    setActiveKey(key);
  }, []);

  return (
    <Space>
      <div style={{ color: '#707070', marginLeft: '18px' }}>单位切换</div>
      <div className={s.warp}>
        <div
          className={classNames(s.box1, activeKey === 'KG' && s.choice)}
          onClick={() => handleSetActiveKey('KG')}
        >
          KG/cm
        </div>
        <div
          className={classNames(s.box2, activeKey === 'LB' && s.choice)}
          onClick={() => handleSetActiveKey('LB')}
        >
          LB/in
        </div>
      </div>
    </Space>
  );
};

export default UnitSwitching;
