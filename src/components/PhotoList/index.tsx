import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { getPhotoList } from '@/services/Waybilladministration';
import { Image } from 'antd';
import { formatTime } from '@/utils/format';
import { ProList } from '@ant-design/pro-components';

const PhotoList = (props: any) => {
  const { id } = props;
  const [DataList, setDataList] = useState([]);
  const getList = async () => {
    const res = await getPhotoList({
      outId: id,
    });
    setDataList(res?.data?.list || []);
  };
  useEffect(() => {
    getList();
  }, []);
  const uploadingColumns: any = [
    {
      title: '图片',
      dataIndex: 'name',
      selectable: 'none',
      render: (text: any, record: any) => {
        return (
          <Image.PreviewGroup
            preview={{
              onChange: (current, prev) =>
                console.log(`current index: ${current}, prev index: ${prev}`),
            }}
          >
            {record?.urls.map((item: any) => {
              return <Image width={50} height={50} src={item} />;
            })}
          </Image.PreviewGroup>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'type',
      fieldFormat: (record: any) => {
        return record?.remark;
      },
    },
    {
      title: '上传人',
      dataIndex: 'userName',
      width: '50px',
      fieldFormat: (record: any) => {
        return record?.user?.name;
      },
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      width: '50px',
      fieldFormat: (record: any) => {
        return dayjs(record?.createTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ];
  return (
    <>
      {/*    <SuperTables
        columns={uploadingColumns}
        // options={false}
        instanceId={'PhotoList'}
        request={async (params: any, action: any) => {
          const res = await getPhotoList({
            outId: id,
          });
          return {
            data: res.data.list || [],
            success: res.status,
            total: res.data?.total,
          };
        }}
        height={300}
      />*/}
      <ProList
        itemLayout="horizontal"
        dataSource={DataList}
        ghost={true}
        metas={{
          title: {
            dataIndex: ['user', 'name'],
          },
          subTitle: {
            render: (text: any, record: any) => {
              return formatTime(record?.createTime);
            },
          },
          description: {
            dataIndex: 'remark',
          },
          extra: {
            render: (text: any, record: any) => (
              <Image.PreviewGroup
                preview={{
                  onChange: (current, prev) =>
                    console.log(
                      `current index: ${current}, prev index: ${prev}`,
                    ),
                }}
              >
                {record?.urls?.map((item: any) => {
                  return <Image width={80} height={80} src={item} />;
                })}
              </Image.PreviewGroup>
            ),
          },
        }}
      />
      {/*        {DataList.map((item) => {
          return (
            <div
              key={item?.id}
              style={{
                marginLeft: 50,
                marginBottom: 20,
                borderTop: '1px solid #d3d3d3',
              }}
            >
              <div style={{ display: 'flex' }}>
                <div>{item?.user?.name}</div>
                <div style={{ color: '#d3d3d3', marginLeft: 10 }}>
                  {formatTime(item.createTime)}
                </div>
              </div>
              <div style={{ fontWeight: 700 }}>{item?.remark}</div>
              <div>
                <Image.PreviewGroup
                  preview={{
                    onChange: (current, prev) =>
                      console.log(
                        `current index: ${current}, prev index: ${prev}`,
                      ),
                  }}
                >
                  {item?.urls?.map((item: any) => {
                    return <Image width={100} height={100} src={item} />;
                  })}
                </Image.PreviewGroup>
              </div>
            </div>
          );
        })}*/}
    </>
  );
};
export default PhotoList;
