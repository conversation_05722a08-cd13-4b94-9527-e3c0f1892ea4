import React, { useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Divider,
  Input,
  message,
  Modal,
  Select,
  Space,
} from 'antd';
import {
  addRemarkAPI,
  getInvisiblePostAPI,
  getRemarkListAPI,
  remarkTopAPI,
} from '@/services/productAndPrice/api';
import { ProList } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { VerticalAlignTopOutlined } from '@ant-design/icons';
import { deleteRemarkAPI } from '@/services/financeApi';

interface Props {
  btnText?: string;
  btnType?: 'link' | 'text' | 'default' | 'primary' | 'dashed' | undefined;
  ghost?: boolean;
  selectedRows?: any[];
  refresh?: any;
  onCleanSelected?: any;
  record?: any;
  batch?: any;
  style?: any;
}
const { TextArea } = Input;
const RemarksModal = ({
  btnText,
  btnType,
  record,
  refresh,
  batch,
  ghost,
  style,
}: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [isModalOpen, setIsModalOpen] = useState(false);
  //不可见客户数据列表
  const [invisibleCustomerList, setInvisibleCustomerList] = useState<any>([]);
  const [excludeDutyIds, setExcludeDutyIds] = useState<any>(''); //不可见客户id
  const [excludeClient, setExcludeClient] = useState<any>(false); //客户可见
  const [content, setContent] = useState<any>(''); //备注
  const [notesList, setNotesList] = useState<any>([]); //备注列表
  /* 查询不可见岗位 */
  const getInvisiblePosition = async () => {
    try {
      const { data, status } = await getInvisiblePostAPI({});
      if (status) {
        setInvisibleCustomerList(data?.list || []);
      }
    } catch (err) {
      console.error('获取不可见岗位失败', err);
    }
  };

  /* 获取备注列表 */
  const getRemarksList = async () => {
    if (batch) {
      return;
    }
    try {
      const { data, status } = await getRemarkListAPI({
        start: 0,
        len: 1000,
        outId: record?.id,
      });
      if (status) {
        data?.list?.forEach((item: any) => {
          if (item.avatar) {
            item.avatar = `https://static.kmfba.com/${item.avatar}`;
          }
        });
        console.log(data, 'data?.listdata?.list');

        setNotesList(data?.list || []);
      }
    } catch (err) {
      console.error('获取备注失败', err);
    }
  };
  /* 添加备注 */
  const addRemarks = async () => {
    try {
      const { status } = await addRemarkAPI({
        outId: record?.id,
        excludeClient: excludeClient ? 1 : 0,
        content,
        excludeDutyIds: excludeDutyIds,
      });
      if (status) {
        refresh();
        messageApi.success('添加备注成功');
        getRemarksList();
        setContent('');
      }
    } catch (err) {
      console.error('获取备注失败', err);
    }
  };
  /* 备注置顶 */
  const setTop = async (id: any) => {
    try {
      const { status } = await remarkTopAPI({
        commentId: id,
        type: 1,
      });
      if (status) {
        messageApi.success('置顶成功');
        getRemarksList();
      }
    } catch (err) {
      console.error('置顶操作失败', err);
    }
  };
  /* 取消置顶 */
  const cancelTop = async (id: any) => {
    try {
      const { status } = await remarkTopAPI({
        commentId: id,
        type: 0,
      });
      if (status) {
        messageApi.success('取消置顶成功');
        getRemarksList();
      }
    } catch (err) {
      console.error('取消置顶操作失败', err);
    }
  };

  /* 删除备注 */
  const deleteComment = async (id: any) => {
    try {
      const { status } = await deleteRemarkAPI({
        id: id,
      });
      if (status) {
        messageApi.success('删除备注成功');
        getRemarksList();
      }
    } catch (err) {
      console.error('删除备注失败：', err);
    }
  };

  const showModal = () => {
    if (!record?.id) {
      message.error('请选择需要备注的内容');
      setIsModalOpen(false);
    } else {
      setIsModalOpen(true);
    }
  };
  const handleOk = () => {
    addRemarks();
    refresh();
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    refresh();
  };

  useEffect(() => {
    if (isModalOpen) {
      getInvisiblePosition();
      getRemarksList();
    }
  }, [isModalOpen]);
  return (
    <>
      {contextHolder}
      <Button
        type={btnType}
        onClick={showModal}
        style={style ? style : { height: '100%', padding: 0, fontSize: 12 }}
        ghost={ghost}
      >
        {btnText}
      </Button>
      <Modal
        title="备注"
        width={800}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={[
          <div
            key={1}
            className="flex items-center absolute left-28px bottom-20px"
          >
            <Checkbox
              key={1}
              checked={excludeClient}
              onChange={(e) => setExcludeClient(e)}
            >
              客户可见
            </Checkbox>
            <div className="flex items-center w-300px justify-between">
              <div className="ml-65px">不可见岗位：</div>
              <Select
                style={{ width: '50%' }}
                placeholder="请选择不可见岗位"
                options={invisibleCustomerList}
                fieldNames={{ label: 'desc', value: 'id' }}
                onChange={(value) => {
                  setExcludeDutyIds(value);
                }}
              />
            </div>
          </div>,
          <Button key="back" onClick={handleCancel}>
            关闭
          </Button>,
          <Button key="publish" onClick={handleOk} type="primary">
            发布
          </Button>,
        ]}
      >
        {!batch && (
          <>
            <div className="min-h-300px max-h-320px overflow-auto">
              <ProList<any>
                // onRow={(record: any) => {
                //   return {
                //     // onMouseEnter: () => {
                //     //   console.log(record);
                //     // },
                //     onClick: () => {
                //       console.log(record);
                //     },
                //   };
                // }}
                rowKey="id"
                // headerTitle="基础列表"
                // tooltip="基础列表的配置"
                dataSource={notesList}
                showActions="hover"
                showExtra="hover"
                metas={{
                  title: {
                    dataIndex: 'userName',
                  },
                  avatar: {
                    dataIndex: 'avatar',
                  },
                  description: {
                    dataIndex: 'content',
                  },
                  subTitle: {
                    render: (text: any, record: any) => {
                      return (
                        <Space size={0}>
                          <div className="text-12px">
                            {dayjs(record?.createTime).format(
                              'YYYY-MM-DD HH:mm:ss',
                            )}
                          </div>
                          <div>
                            <Button
                              type="link"
                              onClick={() => {
                                setTop(record?.id);
                              }}
                              size="small"
                              icon={<VerticalAlignTopOutlined />}
                            >
                              置顶
                            </Button>
                          </div>
                          {record?.top === 1 && (
                            <Button
                              type="link"
                              size="small"
                              className="color-coolGray"
                              onClick={() => cancelTop(record?.id)}
                            >
                              取消置顶
                            </Button>
                          )}
                        </Space>
                      );
                    },
                  },
                  actions: {
                    render: (text, row) => {
                      const currentUser = localStorage.getItem('user');
                      const userId = currentUser ? JSON.parse(currentUser)?.id : null;
                      console.log('当前用户ID:', userId);
                      console.log('备注创建者ID:', row.uid);
                      
                      if (row.uid === userId) {
                        return (
                          <Button
                            type="link"
                            onClick={() => {
                              deleteComment(row.id);
                            }}
                            key="del"
                            danger
                          >
                            删除
                          </Button>
                        );
                      }
                      return null;
                    },
                  },
                }}
              />
            </div>
            <Divider />
          </>
        )}
        <div>
          <TextArea
            rows={4}
            placeholder="请输入备注内容"
            value={content}
            onChange={(e) => {
              setContent(e.target.value);
            }}
          />
        </div>
      </Modal>
    </>
  );
};
export default RemarksModal;
