.search-card-warp {
  :global {
    // .ant-input {
    //   box-shadow: none;
    // }

    // .ant-input-affix-wrapper{
    //   box-shadow: none;
    // }

    // .ant-input:hover {
    //   border: 1px solid #fff;
    // }

    // .ant-input:focus {
    //   border: 1px solid #fff;
    // }

    // .ant-input-group-addon{
    //   .ant-input-search-button{
    //     // background: #fafafa;
    //     color: #4071ff;
    //   }
    // }

    // .ant-input-affix-wrapper >input.ant-input{
    //   border: 1px solid #fafafa !important;
    // }

    // .ant-form-item-no-colon{
    //   color: #707070 !important;
    // }

    .ant-pro-query-filter {
      padding: 20px 24px 12px;
    }

    :where(.css-dev-only-do-not-override-1yqwnbs).ant-pro-query-filter-row {
      row-gap: 12px;
    }
  }


  .btnWarp {
    position: absolute;
    width: 52px;
    right: 0;
    top: 0;
    height: 100%;
    // background:#fafafa;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 5px 5px 0;
    color: #4071ff;
    font-size: 14px;
    cursor: pointer;

    .btn {
      text-align: center;
    }

    .icon-delete {
      position: absolute;
      left: 10px;
    }

  }



  // .btnWarp:hover{
  //   background: #6c8cf7;
  //   color: #fff;
  //   transition: all 0.8s;
  // }
}


.search-card-warp:first-child {
  :global {
    .ant-btn {
      background: #fff;
      color: #4071ff;
      border: 1px solid #d9d9d9;
    }


    .ant-form-item-label {
      border: 1px solid #d9d9d9;
      padding-left: 10px;
      border-radius: 4px 0 0 4px;
      border-right: none;
    }

    .ant-form-item-no-colon {
      color: #707070;
    }

    .ant-form-item-control {
      border: 1px solid #d9d9d9;
      border-radius: 0 4px 4px 0;
    }

    .ant-select-selector {
      border: none;
    }

    .ant-picker-outlined {
      border: none;
    }

    .ant-input-outlined {
      border: none;
    }

  }

  .search_wrap-search {
    display: flex;
    position: relative;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    height: 35px;
  }
}