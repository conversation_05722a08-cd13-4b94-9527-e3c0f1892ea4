.tableWarp {
  :global {
    .ant-input-affix-wrapper {
      border-color: #fafafa;
      background: #fafafa;
    }

    .ant-input-affix-wrapper:hover {
      border-color: #fafafa;
      background: #fafafa;
    }

    .ant-input {
      background: #fafafa;
      border: 1px solid #fafafa;
    }

    .ant-select-selector {
      background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-picker-range {
      background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-input-number-input {
      background: #fafafa !important;
      border: 1px solid #fafafa !important;
    }

    .ant-input-number {
      border: none;
    }

    .ant-picker {
      background: #fafafa !important;
      border: none;
    }

    .ant-input-number-group-addon {
      // background: #fafafa !important;
      border: none;
    }
  }
}

.body {
  // background: #f6f6f8;
  background: #f6f6f8;
}