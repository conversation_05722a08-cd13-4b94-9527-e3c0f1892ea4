.tableWarp :global .ant-input-affix-wrapper {
  border-color: #fafafa;
  background: #fafafa;
}
.tableWarp :global .ant-input-affix-wrapper:hover {
  border-color: #fafafa;
  background: #fafafa;
}
.tableWarp :global .ant-input {
  background: #fafafa;
  border: 1px solid #fafafa;
}
.tableWarp :global .ant-select-selector {
  background: #fafafa !important;
  border: 1px solid #fafafa !important;
}
.tableWarp :global .ant-picker-range {
  background: #fafafa !important;
  border: 1px solid #fafafa !important;
}
.tableWarp :global .ant-input-number-input {
  background: #fafafa !important;
  border: 1px solid #fafafa !important;
}
.tableWarp :global .ant-input-number {
  border: none;
}
.tableWarp :global .ant-picker {
  background: #fafafa !important;
  border: none;
}
.tableWarp :global .ant-input-number-group-addon {
  border: none;
}
.body {
  background: #f6f6f8;
}
