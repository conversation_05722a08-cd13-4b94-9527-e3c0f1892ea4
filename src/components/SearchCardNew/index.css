.search-card-warp :global .ant-pro-query-filter {
  padding: 20px 24px 12px;
}
.search-card-warp :global :where(.css-dev-only-do-not-override-1yqwnbs).ant-pro-query-filter-row {
  row-gap: 12px;
}
.search-card-warp .btnWarp {
  position: absolute;
  width: 52px;
  right: 0;
  top: 0;
  height: 100%;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0 5px 5px 0;
  color: #4071ff;
  font-size: 14px;
  cursor: pointer;
}
.search-card-warp .btnWarp .btn {
  text-align: center;
}
.search-card-warp .btnWarp .icon-delete {
  position: absolute;
  left: 10px;
}
.search-card-warp:first-child :global .ant-btn {
  background: #fff;
  color: #4071ff;
  border: 1px solid #d9d9d9;
}
.search-card-warp:first-child :global .ant-form-item-label {
  border: 1px solid #d9d9d9;
  padding-left: 10px;
  border-radius: 4px 0 0 4px;
  border-right: none;
}
.search-card-warp:first-child :global .ant-form-item-no-colon {
  color: #707070;
}
.search-card-warp:first-child :global .ant-form-item-control {
  border: 1px solid #d9d9d9;
  border-radius: 0 4px 4px 0;
}
.search-card-warp:first-child :global .ant-select-selector {
  border: none;
}
.search-card-warp:first-child :global .ant-picker-outlined {
  border: none;
}
.search-card-warp:first-child :global .ant-input-outlined {
  border: none;
}
.search-card-warp:first-child .search_wrap-search {
  display: flex;
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  height: 35px;
}
