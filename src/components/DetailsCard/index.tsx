import { ProCard } from '@ant-design/pro-components';
import { Descriptions } from 'antd';

/* 定义组件 Props 类型 */
interface Props {
  title: string;
  list: { title: string; content: string }[] /* 定义 list 数组每个元素的结构 */;
  loading?: boolean;
  children?:any
}

const DetailsCard = ({ title, list, loading,children}: Props) => {
  return (
    <ProCard title={title} style={{ marginBottom: '22px' }} loading={loading}>
      {children ? (
        children
      ) : (
        <Descriptions>
          {list?.map((item) => (
            <Descriptions.Item
              key={item.title} /*  key，这里使用了 title */
              labelStyle={{ color: '#AEAEAE' }}
              label={item.title}
            >
              {item.content}
            </Descriptions.Item>
          ))}
        </Descriptions>
      )}
    </ProCard>
  );
};

export default DetailsCard;
