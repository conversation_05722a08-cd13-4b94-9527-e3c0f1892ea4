import { Tabs } from 'antd';

import styles from './index.less';
interface Props {
  onChange?: (key: string) => void;
  items: any[];
  defaultActiveKey?: any;
  style?: React.CSSProperties;
}
const TabsType = ({ items, onChange, defaultActiveKey, style }: Props) => {
  return (
    <div className={styles.warp} style={style} >
      <Tabs
        animated={{ inkBar: false, tabPane: false }}
        activeKey={defaultActiveKey}
        items={[...items]}
        onChange={(key) => {
          if (onChange) {
            onChange(key);
          }
        }}
      />
    </div>
  );
};
export default TabsType;
