import React, { useEffect, useState } from 'react';
import {  Button, Drawer, List, Space, Tag } from 'antd';
import { useRequest } from 'ahooks';
import { deliveryLogAPI } from '@/services/financeApi';
// import { getAvataUrl } from '@/shared/Enumeration';
import dayjs from 'dayjs';

interface LogDashboardProps {
  btnText: string;
  btnType: "link" | "text" | "primary" | "default" | "dashed" | undefined
  extraId_1?: string;
  extraId_2?: string;
  holdFlag?: boolean
  waybillNo?: string
}


const LogDashboard: React.FC<LogDashboardProps> = ({ btnText, btnType, extraId_1, extraId_2, holdFlag,waybillNo }: LogDashboardProps) => {
  const [open, setOpen] = useState(false);

  const { runAsync, data, loading } = useRequest(deliveryLogAPI, {
    manual: true

  })

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (open) {
      runAsync({
        extraId_1: extraId_1,
        extraId_2: extraId_2,
        waybillNo:waybillNo
      })
    }
  }, [open]);





  return (
    <>
      <Button style={{ color: holdFlag ? '#FFEFC3' : '#1677ff' }} type={btnType} onClick={showDrawer}>
        {btnText}
      </Button>
      <Drawer width={'46vw'} title="速递日志记录" onClose={onClose} open={open}>
        <List
          pagination={{
            position: 'bottom',
          }}
          size="small"
          dataSource={data?.data?.list}
          loading={loading}
          rowKey="id"
          renderItem={(item: any) => (
            <List.Item>
              <List.Item.Meta
                // avatar={<Avatar src={getAvataUrl(item?.operatorAvatar)} />}
                title={<Space>
                  <div className='color-#b1b1b1'>{item?.operatorName}</div>
                  {item?.spSignInWarehouse && <Tag bordered={false} color="orange" className='text-12px'>{item?.spSignInWarehouse} </Tag>}
                  {item?.spOperator && <Tag bordered={false} color="orange" className='text-12px'>{item?.spOperator} </Tag>}
                  {/* {item?.serviceName && <Tag bordered={false} color="geekblue" className='text-12px'>{item?.serviceName}</Tag>} */}

                  <div className='text-12px color-#b1b1b1'>{dayjs(item?.spTime).format('YYYY-MM-DD HH:mm:ss')}</div>
                  <div className='text-12px color-#b1b1b1'>运单号:{item?.spWaybillNo}</div>

                </Space>}
                description={<>
                  <div className='color-#414141 m-y-0.5em whitespace-normal break-words overflow-auto'>
                    <div>{item?.spLogContent}</div>
                  </div>
                  {/* <Space className='mt-16px'>
                  {item?.extra_1 && <div className='color-#2f3542 text-12px'>extra_1：{item?.extra_1}</div>} 
                  {item?.extra_2 && <div className='color-#2f3542 text-12px'>extra_2：{item?.extra_2}</div>}
                </Space> */}
                </>}
              />
            </List.Item>
          )}
        />
      </Drawer>
    </>
  );
};

export default LogDashboard;