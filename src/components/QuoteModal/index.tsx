import React, { useEffect, useState } from 'react';
import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Space,
  Tag,
  Upload,
} from 'antd';

import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { addQuotationAPI, getQuotationAPI } from '@/services/financeApi';
import { waybillStatusEnum } from '@/utils/constant';
import { REQUESTADDRESS_W } from '@/globalData';

/**
 * @description 报价弹框
 * @param btnText 按钮文本
 * @param btnType 按钮类型
 * @param modalTitle 弹窗标题
 * @param record 传入的数据
 */
const layout = {
  labelCol: {
    span: 4,
  },
  // wrapperCol: {
  //   span: 20,
  // },
};
interface ModalProps {
  btnText: string;
  btnType?: 'link' | 'text' | 'default' | 'primary' | 'dashed' | undefined;
  modalTitle?: string;
  record?: any; // 传入的数据
  refresh?: () => void; // 刷新列表
  selectedData?: any; // 选中的数据
  refreshTable?: () => void; // 刷新列表
  disabled?: boolean; // 是否禁用
}

const QuoteModal = ({
  btnText,
  btnType,
  modalTitle,
  record,
  disabled,
  refresh,
}: ModalProps) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [chargeWeight, setChargeWeight] = useState<any>(0);
  const getBase64 = (file: any) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [fileList, setFileList] = useState<any>([]); //上传文件列表
  const beforeUpload = (file: any) => {
    const isLt1G = file.size / 1024 / 1024 / 1024 < 1;
    if (!isLt1G) {
      messageApi.error('上传文件大小不能超过1G!');
    }
    return false;
    // const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    // if (!isJpgOrPng) {
    //   messageApi.error('You can only upload JPG/PNG file!');
    // }
    // const isLt2M = file.size / 1024 / 1024 < 2;
    // if (!isLt2M) {
    //   messageApi.error('Image must smaller than 2MB!');
    // }
    // return isJpgOrPng && isLt2M;
  };
  const handleCancel2 = () => setPreviewOpen(false);
  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
    setPreviewTitle(
      file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
    );
  };
  const handleChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
    // if (file.status === 'done') {
    //   if (file.response.status) {
    //     messageApi.success(`${file.name} 上传成功`);
    //     form?.setFieldValue('payCredential', file.response.data);
    //   }
    // }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div
        style={{
          marginTop: 8,
        }}
      >
        上传文件
      </div>
    </div>
  );

  /* 监听上传文件列表 */
  useEffect(() => {
    if (!isModalOpen) return;
    // console.log('fileList: ', fileList);
    form?.setFieldValue('attachments', fileList);
  }, [fileList, isModalOpen]);

  const showModal = () => {
    setIsModalOpen(true);
  };
  const { run }: any = useRequest(addQuotationAPI, {
    manual: true,
    onSuccess: (res) => {
      if (res?.status) {
        messageApi.success('操作成功');
        setIsModalOpen(false);
        form.resetFields();
        setFileList([]);
        setChargeWeight(0);
        if (refresh) {
          refresh();
        }
      }
    },
    onError: (err) => {
      console.log('err: ', err);
    },
  });

  const handleOk = () => {
    if (btnText === '查看报价单') return setIsModalOpen(false);
    form.validateFields().then((values) => {
      const formData = new FormData();
      values.attachments.forEach((item: any) => {
        formData.append('attachments', item.originFileObj);
      });
      formData.append('quotationPrice', values.quotationPrice);
      formData.append('actualPrice', values.actualPrice);
      formData.append('incidentalsAmount', values.incidentalsAmount || '');
      formData.append('volume', values.volume || '');
      formData.append('remark', values.remark || '');
      formData.append('waybillId', record?.id);
      run(formData);
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /* 查看报价单 */
  const { data, run: getQuotation }: any = useRequest(getQuotationAPI, {
    manual: true,
    onSuccess: (res) => {
      // console.log('res: ', res);
      if (res?.status) {
        form.setFieldsValue({
          quotationPrice: res.data.quotationPrice,
          actualPrice: res.data.actualPrice,
          incidentalsAmount: res.data.incidentalsAmount,
          volume: res.data.volume,
          remark: res.data.remark,
          oaFormId: res.data.oaFormId,
        });
        // setFileList(res.data.attachments)
        setChargeWeight(
          res.data.actualPrice * Number(record?.signChargeWeight),
        );
      }
    },
  });

  useEffect(() => {
    if (isModalOpen && btnText === '查看报价单') {
      getQuotation({
        waybillId: record?.id,
      });
    }
  }, [isModalOpen]);

  return (
    <>
      {contextHolder}
      <Button type={btnType} onClick={showModal} disabled={disabled}>
        {btnText}
      </Button>
      <Modal
        title={modalTitle}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={620}
        destroyOnClose
      >
        <div className="mb-20px mt-20px ml-12px">
          <div className="text-16px font-500">
            <Space>
              <div>运单号：{record?.waybillNo}</div>
              <Tag color={waybillStatusEnum[record?.state].color}>
                {waybillStatusEnum[record?.state].text}
              </Tag>
            </Space>
          </div>
        </div>
        <Form
          name="basicQuoteModal"
          autoComplete="off"
          form={form}
          {...layout}
          layout="horizontal"
          style={{
            maxWidth: 600,
          }}
          onValuesChange={(changedValues) => {
            if (changedValues.actualPrice) {
              setChargeWeight(
                changedValues.actualPrice * Number(record?.signChargeWeight),
              );
            }
          }}
          disabled={btnText === '查看报价单'}
        >
          <Row>
            <Col span={24}>
              <Form.Item
                label="报价员单价"
                name="quotationPrice"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <InputNumber
                  style={{ width: '90%' }}
                  placeholder="请输入内容"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="实际单价"
                name="actualPrice"
                rules={[
                  {
                    required: true,
                    message: '必填项不能为空',
                  },
                ]}
              >
                <InputNumber
                  style={{ width: '90%' }}
                  placeholder="请输入内容"
                />
              </Form.Item>
            </Col>
            <Col span={24} className="ml-40px mb-20px">
              计费重：{record?.signChargeWeight}
            </Col>
            <Col span={24} className="ml-27px mb-20px">
              {/* 运费总价=计费重*实际单价 */}
              {`运费总价：${chargeWeight || '-'}`}
            </Col>
            <Col span={24}>
              <Form.Item label="杂费" name="incidentalsAmount">
                <InputNumber
                  placeholder="请输入内容"
                  style={{ width: '90%' }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="方数" name="volume">
                <InputNumber
                  placeholder="请输入内容"
                  style={{ width: '90%' }}
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="附件" name="attachments">
                <>
                  {btnText === '填写报价' && (
                    <Upload
                      name="file"
                      // accept="image/*"
                      accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                      listType="picture-card"
                      className="avatar-uploader"
                      onPreview={handlePreview}
                      onChange={handleChange}
                      beforeUpload={beforeUpload}
                      maxCount={9}
                      fileList={fileList}
                      headers={{
                        Token: `${localStorage.getItem('token')}`,
                      }}
                      withCredentials={true}
                      data={{
                        visible: true,
                        service_id: 'TMS',
                      }}
                    >
                      {fileList.length >= 10 ? null : uploadButton}
                    </Upload>
                  )}
                  {btnText === '查看报价单' && (
                    <Space>
                      {data?.data?.attachmentList?.map(
                        (item: any, index: any) => {
                          return (
                            <a
                              key={index}
                              href={`${REQUESTADDRESS_W}/file/preview?accessToken=${encodeURIComponent(
                                item[Object?.keys(item)?.[0]],
                              )}`}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {Object.keys(item)}
                            </a>
                          );
                        },
                      )}
                    </Space>
                  )}
                </>
              </Form.Item>
              <Modal
                open={previewOpen}
                title={previewTitle}
                footer={null}
                onCancel={handleCancel2}
              >
                <img
                  alt="example"
                  style={{
                    width: '100%',
                  }}
                  src={previewImage}
                />
              </Modal>
            </Col>
            <Col span={24}>
              <Form.Item label="申请事由" name="remark">
                <Input.TextArea
                  placeholder="请输入备注"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            {btnText === '查看报价单' && (
              <Col span={24}>
                <Form.Item label="OA单号" name="oaFormId">
                  <Input style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            )}
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(QuoteModal);
