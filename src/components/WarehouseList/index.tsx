import AccessCard from "@/AccessCard"
import { getWarehouseListByProviderIdAPI, getRemoveWarehouse } from "@/services/booking"
import { formatTime } from "@/utils/format"
import { ProTable } from "@ant-design/pro-components"
import { message, Popconfirm, Space } from "antd"
import React, { useEffect, useRef } from "react"
import Add<PERSON>areHouse from "../AddWareHouse"
import { ProviderType } from '@/shared/ProviderTypeFn';
import styles from './index.less'
interface Props {
    providerId: string
    type:number
}
const WarehouseList: React.FC<Props> = React.memo((props: any) => {
    const { providerId,type } = props
    const actionRef = useRef<any>(null)
    const flagRef = useRef(false)
    const columns: any = [
        {
            title: '名称',
            dataIndex: 'name',
            hideInSearch: true,
            fixed: 'left',
            width: 120,
            align: 'center',
        },
        {
            title: '国家',
            dataIndex: 'country',
            width: 90,
            align: 'center',
            hideInSearch: true,
        },
        {
            title: '州/省',
            dataIndex: 'provinceShortName',
            align: 'center',
            hideInSearch: true,
            width: 120,
            render: (_text: any, row: any) => {
                return `${row.provinceShortName}-${row.province}`;
            },
        },
        {
            title: '城市',
            dataIndex: 'city',
            align: 'center',
            width: 120,
            hideInSearch: true,
            render: (_text: any, row: any) => {
                if (row.county) {
                    return `${_text}-${row.county}`;
                } else {
                    return _text;
                }
            },
        },
        {
            title: '邮编',
            align: 'center',
            dataIndex: 'zipCode',
            width: 120,
            hideInSearch: true,
        },
        {
            title: '街道门牌',
            dataIndex: 'street',
            align: 'center',
            width: 200,
            hideInSearch: true,
        },
        {
            title: '公司',
            dataIndex: 'companyName',
            width: 200,
            align: 'center',
            hideInSearch: true,
        },
        {
            title: '联系人',
            dataIndex: 'contactName',
            align: 'center',
            width: 150,
            hideInSearch: true,
        },
        {
            title: '电话',
            width: 120,
            align: 'center',
            dataIndex: 'contactPhone',
            hideInSearch: true,
        },
        {
            title: '时间',
            align: 'center',
            dataIndex: 'updateTime',
            width: 160,
            hideInSearch: true,
            render: (text: any) => {
                return <span>{formatTime(text)}</span>;
            },
        },
        {
            title: '电子邮箱',
            align: 'center',
            dataIndex: 'email',
            valueType: 'email',
            width: 160,
            hideInSearch: true,
        },
        {
            title: '传真',
            dataIndex: 'fax',
            align: 'center',
            valueType: 'fax',
            width: 160,
            hideInSearch: true,
        },
        {
            title: '操作',
            key: 'option',
            width: 120,
            align: 'center',
            valueType: 'option',
            fixed: 'right',
            hideInSearch: true,
            render: (_: any, recode: any) => {
                return <Space>
                   {/* <a key='modification'>修改</a> */}
                  { ProviderType.AirTransport.getCode() === type && <AddWareHouse
                        refreshTable={refreshTable}
                        key={'warehouses'}
                        providerId={providerId}
                        Revised
                        dataList={recode}
                    />}
                    <AccessCard accessible={[':Waybill:TransferProviderFullAccess']}>
                        <Popconfirm
                            key="delete"
                            title="删除仓库"
                            description="想好了吗，删除仓库?"
                            onConfirm={() => {
                                getremoveWarehouse(recode?.id)
                            }}
                            onCancel={() => {
                                message.info('已取消操作');
                            }}
                            okText="确认"
                            cancelText="再想想"
                        >
                            <a key='delete' style={{ color: 'red' }}>删除</a>
                        </Popconfirm>
                    </AccessCard>
                 

                </Space>

            }


        },
    ]
    // 删除仓库
    const getremoveWarehouse = async (id: string) => {
        try {
            const { status } = await getRemoveWarehouse({ id })
            if (status) {
                message.success('删除成功')
                refreshTable()
            }
        } catch (error) {

        }
    }
    // 控制第一次不走请求，后续 providerId 改变 走接口渲染最新数据
    useEffect(() => {
        if (flagRef.current && providerId) {
            refreshTable()
        }
        flagRef.current = true
    }, [providerId])
    const refreshTable = () => {
        actionRef?.current?.reload();
    }
    return <>
        <ProTable<any>
            columns={columns}
            actionRef={actionRef}
            className={styles.warehouse_table}
            rowKey='id'
            bordered
            headerTitle="仓库"
            scroll={{ y: 400 }}
            request={async (params: any) => {
                const { current: start, pageSize: len } = params;
                const res = await getWarehouseListByProviderIdAPI({
                    start: (start - 1) * len,
                    len,
                    providerId
                })
                return {
                    data: res?.data?.list || [],
                    success: res?.status,
                    total: res?.data?.total,
                };
            }}
            editable={{
                type: 'multiple',

            }}
            search={false}
            dateFormatter="string"
            style={{ marginBottom: 20 }}
            options={false}
            toolBarRender={() => [
                <AccessCard key={1} accessible={[':Provider:UpdateAccess']}>
                    <AddWareHouse
                        refreshTable={refreshTable}
                        key={'warehouse'}
                        providerId={providerId}
                    />
                </AccessCard>
            ]} />
    </>
})
export default WarehouseList