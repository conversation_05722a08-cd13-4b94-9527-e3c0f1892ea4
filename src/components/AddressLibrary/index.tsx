import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Modal, Pagination, Space, Tabs, message } from 'antd';
import { CheckCard } from '@ant-design/pro-components';
import service from '@/services/home';
import styles from '../index.less';
import classNames from 'classnames';
import Search from 'antd/es/input/Search';
import { getWarehouseListAPIJSON } from '@/services/home/<USER>';
// import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
const { getAddressInformationAPI, getWarehouseListAPI } = service.UserHome;
/* 
@params onChangeAddressLibrary 选中地址回调
@params btnTitle 按钮标题
@params channelId 渠道id
@params isFBA 是否是FBA
@params isTabVal 是否显示tab 【】 1 常用地址 2 FBA仓库 3 仓库
*/
const AddressLibrary = (props: any) => {
  const {
    onChangeAddressLibrary,
    btnTitle,
    channelId,
    isFBA,
    isTabVal = ['1', '2', '3', '4'],
  } = props;
  const [page, setPage] = useState(1);
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的地址信息
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  //总条数
  const [total, setTotal] = useState<any>(0);
  // 地址列表
  const [addressList, setAddressList] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [activeKey, setKey] = useState();
  const [keywords, setKeyword] = useState<any>();

  /* 获取常用地址库地址 */
  const getAddressInformation = async (
    page: any,
    pageSize: any,
    keyword?: any,
  ) => {
    setLoading(true);
    try {
      if (activeKey === '1') {
        const { status, data } = await getAddressInformationAPI({
          start: (page - 1) * pageSize,
          len: pageSize,
          keyword: keyword,
        });
        if (status) {
          setTotal(data?.amount);
          setAddressList([...data?.list]);
          setLoading(false);
        }
      } else if (activeKey === '2') {
        const { status, data } = await getWarehouseListAPI({
          start: (page - 1) * pageSize,
          len: pageSize,
          type: 1,
          keyword: keyword,
        });
        if (status) {
          setTotal(data?.amount);
          setAddressList([...data?.list]);
          setLoading(false);
        }
      } else if (activeKey === '4') {
        const { status, data } = await getWarehouseListAPIJSON({
          start: (page - 1) * pageSize,
          len: pageSize,
          type: 3,
          keyword: keyword,
        });
        if (status) {
          setTotal(data?.amount);
          setAddressList([...data?.list]);
          setLoading(false);
        }
      } else if (activeKey === '3') {
        const { status, data } = await getWarehouseListAPI({
          start: (page - 1) * pageSize,
          len: pageSize,
          type: 0,
          keyword: keyword,
        });
        if (status) {
          setTotal(data?.amount);
          setAddressList([...data?.list]);
          setLoading(false);
        }
      }
      setLoading(false);
    } catch (err) {
      console.log('地址库选择抛出异常: ', err);
      setLoading(false);
    }
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    //判断selectedAddress是否为空
    if (!selectedAddress) {
      return message.error('请选择地址');
    }
    onChangeAddressLibrary({
      ...selectedAddress,
      checked: true,
      channelIdADD: channelId,
    });
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  useEffect(() => {
    if (isModalOpen) {
      setKey(isTabVal[0]);
      //打开默认首页
      //getAddressInformation(1, 10);
    }
  }, [isModalOpen]);
  /*当选择项改变的时候重新请求*/
  useEffect(() => {
    if (isModalOpen) {
      setPage(1);
      getAddressInformation(page, 10);
    }
  }, [activeKey]);
  const items = [
    {
      key: '1',
      label: `常用地址`,
      disabled: isFBA || !isTabVal.includes('1'),
      // children: `Content of Tab Pane 1`,
    },
    {
      key: '2',
      label: `FBA仓库`,
      disabled: !isTabVal.includes('2'),
    },
    {
      key: '3',
      label: `仓库`,
      disabled: !isTabVal.includes('3'),
    },
    {
      key: '4',
      label: `海外仓`,
      disabled: !isTabVal.includes('4'),
    },
  ];
  const onChange = (key: any) => {
    setKey(key);
  };
  useEffect(() => {
    if (isFBA) {
      setKey('2');
    }
  }, [isFBA]);

  /* 取当前那个tbs key  */
  useEffect(() => {
    if (isTabVal.length && isModalOpen) {
      //兼容之前的旧逻辑 isFBA
      if (isFBA) {
        // setKey(isTabVal[1]);
        return;
      }
      //setKey(isTabVal[0]);
    }
  }, [isModalOpen]);
  return (
    <>
      <div
        onClick={showModal}
        style={{ display: 'inline-block', cursor: 'pointer' }}
      >
        {!btnTitle ? '从地址库选择' : <a>{btnTitle}</a>}
      </div>
      <Modal
        width="740px"
        title="选择地址"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        footer={[
          <Space key="btn">
            <Pagination
              key="paginat"
              total={total}
              showSizeChanger
              // showQuickJumper
              current={page}
              // defaultCurrent={1}
              // defaultPageSize={2}
              // pageSizeOptions={[2, 4, 6, 8, 10]}
              showTotal={(total) => `共 ${total} 条`}
              onChange={(page, pageSize) => {
                setPage(page);
                getAddressInformation(page, pageSize, keywords);
              }}
            />
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        <Tabs activeKey={activeKey} items={items} onChange={onChange} />
        <div style={{ maxHeight: '400px', overflow: 'auto' }}>
          <div style={{ marginBottom: '10px' }}>
            <Search
              placeholder="请输入"
              allowClear
              enterButton="搜索"
              size="middle"
              style={{ width: '300px' }}
              onSearch={(value: any) => {
                setKeyword(value);
                getAddressInformation(1, 10, value);
              }}
            />
          </div>
          {/* 地址选项列表 */}
          <CheckCard.Group
            value={selectedAddress}
            loading={loading}
            // 选项变化回调
            onChange={(value) => {
              setSelectedAddress(value);
            }}
            className={styles.isNoCheckCard}
          >
            {/* 渲染地址选项 */}
            {addressList.map((item: any) => (
              <CheckCard
                className="h-130px overflow-auto"
                key={item.id}
                value={item}
                // 地址卡片标题
                title={
                  activeKey === '1' ? (
                    <div
                      className={classNames(
                        selectedAddress?.id === item.id ? styles.choice : null,
                      )}
                    >
                      <Space>
                        <div>
                          <span>{item.contactName || item.province}</span>
                          {item.isFBA === 1 ? (
                            <span className={styles.isFBA}>FBA</span>
                          ) : null}
                        </div>
                        <div
                          className={classNames(
                            selectedAddress?.id === item.id
                              ? styles['lable-name']
                              : styles['lable-name-a'],
                          )}
                        >
                          {item.contactPhone}
                        </div>
                        <div
                          className={classNames(
                            selectedAddress?.id === item.id
                              ? styles['lable-name']
                              : styles['lable-name-a'],
                          )}
                        >
                          {item.companyName}
                        </div>
                      </Space>
                    </div>
                  ) : (
                    <div
                      className={classNames(
                        selectedAddress?.id === item.id ? styles.choice : null,
                      )}
                    >
                      {item.name}
                    </div>
                  )
                }
                // 地址卡片描述
                description={
                  <div style={{ color: '#707070' }}>
                    {activeKey !== '1' ? (
                      <div>{`
                        ${item.contactName || item.province} 
                        ${item.contactPhone} ${item.companyName}
                      `}</div>
                    ) : (
                      ''
                    )}
                    {item.street},{item.city}
                    {`${item.county ? `-${item.county}` : ''}`},
                    {item.provinceShortName},
                    {activeKey === '3' && item.country === 'CN'
                      ? '中国'
                      : item.country}
                    ,{item.zipCode}
                  </div>
                }
                // 地址卡片右侧操作
                // extra={
                //   <Space key="operation">
                //     <EditOutlined style={{ color: '#979797' }} />
                //     <DeleteOutlined style={{ color: '#979797' }} />
                //   </Space>
                // }
              />
            ))}
            {/* </Row> */}
          </CheckCard.Group>
        </div>
      </Modal>
    </>
  );
};

export default React.memo(AddressLibrary);
