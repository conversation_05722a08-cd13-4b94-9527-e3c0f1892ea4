import {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { Avatar, Checkbox, Col, Modal, Radio, Row, Tooltip } from 'antd';
import styles from './index.less';
import MySearch from '../MyProTable/MySearch';
import { getUserListAPI } from '@/services/booking';
interface Props {
  // 控制单选还是多选
  control: 'radio' | 'checkbox';
  // 获取 ref 实列
  ref: any;
  // 选择用户后的回调
  selectUserCallback: (value: any) => void;
  // 传递给接口的参数 传递什么岗位，展示什么岗位人员 不传展示全部
  dutyId?: string;
}
const UserSelector: FC<Props> = forwardRef((props, ref) => {
  const { control, selectUserCallback, dutyId } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [userList, setUserList] = useState<any[]>();
  const [selected, setSelected] = useState<any>([]);
  const [keyword, setKeyword] = useState('');
  const [checkedKey, setCheckedKey] = useState<any>([]);
  useImperativeHandle(ref, () => ({
    control() {
      setIsModalOpen(true);
    },
  }));
  const handleOk = () => {
    selectUserCallback?.(selected);
    setIsModalOpen(false);
    setSelected([]);
  };
  const userData = async () => {
    try {
      const { status, data } = await getUserListAPI({
        start: 0,
        len: 20,
        dutyId,
        keyword,
      });
      if (status) {
        setUserList(data.list);
      }
    } catch (error) {}
  };
  useEffect(() => {
    if (isModalOpen) {
      userData();
    }
  }, [dutyId, isModalOpen, keyword]);

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelected([]);
    setKeyword('');
  };
  return (
    <div>
      <Modal
        style={{ minWidth: 950 }}
        destroyOnClose
        title="选择用户"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className={styles.ListSearch}>
          <div className={styles.search}>
            <MySearch
              placeholder="请输入用户名字"
              allowClear
              enterButton="搜索"
              size="large"
              style={{ width: '448px', background: '#FBFBFB' }}
              onSearch={(e: string) => {
                setKeyword(e);
              }}
              // 后期加上搜索
            />
            {control === 'checkbox' ? (
              <div>
                {' '}
                <Checkbox
                  style={{ marginTop: '15px' }}
                  onChange={() => {
                    if (checkedKey?.length) {
                      setCheckedKey([]);
                      setSelected([]);
                    } else {
                      setCheckedKey(userList?.map((ele: any) => ele?.id));
                      setSelected(userList);
                    }
                    // setSelected(userList?.map((ele: any) => ele && JSON.stringify(ele)))
                  }}
                >
                  全选/反选
                </Checkbox>
                <Checkbox.Group
                  value={checkedKey}
                  onChange={(e: any) => {
                    const newData = userList?.filter((item: any) =>
                      e.includes(item?.id),
                    );
                    setSelected(newData);
                    setCheckedKey(e);
                  }}
                >
                  <Row>
                    {userList?.map((item: any) => (
                      <Col key={item.id} span={24}>
                        <Checkbox
                          value={item?.id}
                          className={styles.checkboxMultiple}
                        >
                          <div className={styles.checkboxList}>
                            <img
                              src={
                                item?.user?.avatar.startsWith('https')
                                  ? item?.user?.avatar
                                  : `https://static.kmfba.com/${item?.user?.avatar}` ||
                                    ''
                              }
                            />
                            <span
                              style={{ position: 'absolute', marginLeft: 15 }}
                            >
                              {item?.user?.name}
                            </span>
                          </div>
                          <Tooltip title={item?.dutyDesc}>
                            <div className={styles.textHide}>
                              <span style={{ color: '#AEAEAE' }}>
                                {item?.dutyDesc}
                              </span>
                            </div>
                          </Tooltip>
                        </Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </div>
            ) : (
              <Radio.Group
                onChange={(e: any) => {
                  const newData =
                    e?.target?.value && JSON.parse(e?.target?.value);
                  setSelected([newData]);
                }}
              >
                <Row>
                  {userList?.map((item: any) => (
                    <Col key={item.id} span={24}>
                      <Radio
                        value={JSON.stringify(item)}
                        className={styles.checkbox}
                      >
                        <div className={styles.radioList}>
                          <div style={{ width: '50%' }}>
                            <Avatar
                              src={
                                item?.user?.avatar.startsWith('https')
                                  ? item?.user?.avatar
                                  : `https://static.kmfba.com/${item?.user?.avatar}` ||
                                    ''
                              }
                            />
                            <span style={{ marginLeft: 10 }}>
                              {item?.user?.name}
                            </span>
                          </div>
                          <Tooltip title={item?.dutyDesc}>
                            <div className={styles.textHide}>
                              <span style={{ color: '#AEAEAE' }}>
                                {item?.dutyDesc}
                              </span>
                            </div>
                          </Tooltip>
                        </div>
                      </Radio>
                    </Col>
                  ))}
                </Row>
              </Radio.Group>
            )}
          </div>
          <div className={styles.checkRight}>
            <div className={styles.selectedTitle}>
              {control === 'checkbox' ? (
                <>
                  {' '}
                  已选（{selected?.length}/{userList?.length}）
                </>
              ) : (
                '已选'
              )}
            </div>
            <div className={styles.userCheck}>
              {selected?.map((item: any) => {
                return (
                  <div className={styles.userMessage} key={item.id}>
                    <img
                      src={
                        item?.user?.avatar.startsWith('https')
                          ? item?.user?.avatar
                          : `https://static.kmfba.com/${item?.user?.avatar}` ||
                            ''
                      }
                    />
                    <div> {item?.user?.name}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
});
export default UserSelector;
