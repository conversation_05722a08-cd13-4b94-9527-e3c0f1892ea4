.ListSearch {
    display: flex;
    width: 100%;
    height: 500px;
    margin-top: 20px;

    .search {
        width: 50%;
        height: 100%;
        // background-color: pink;
        background-color: #fff;
        box-sizing: border-box;
        overflow-y: scroll;
        overflow-x: hidden;

        :global(.ant-radio-group) {
            width: 100%;
        }

        :global {
            .ant-input {
                border: none;
            }
        }
    }

    .checkRight {
        width: 50%;
        height: 100%;
        // background-color: aqua;
        box-sizing: border-box;
        padding: 0 20px;

        .userCheck {
            display: flex;
            flex-wrap: wrap;
            height: calc(100% - 40px);
            overflow: auto;
        }

        .selectedTitle {
            width: 100%;
            height: 40px;
            line-height: 40px;

        }

        .userMessage {
            display: flex;
            // width: 96px;
            height: 36px;
            background: #F4F4F4;
            border-radius: 4px;
            line-height: 36px;
            justify-content: space-around;
            margin-right: 12px;
            margin-bottom: 12px;
            // padding-right: 5px;
            box-sizing: border-box;
            // padding: 0 5px;
            padding-left: 8px;
            padding-right: 5px;

            >img {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                margin-right: 10px;
                margin-top: 3px;
                margin-bottom: 3px;
            }
        }
    }
}

.checkbox {
    width: 100%;
    height: 48px;
    // background-color: aqua;
    line-height: 45px;
    box-sizing: border-box;

    >span:nth-child(2) {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
        box-sizing: border-box;
    }
}

.checkboxMultiple {
    width: 100%;
    height: 48px;
    // background-color: aqua;
    line-height: 45px;
    position: relative;
    box-sizing: border-box;

    >span:nth-child(1) {
        position: absolute;
        top: 16px;
    }

    >span:nth-child(2) {
        width: 464px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        padding-right: 25px;
        box-sizing: border-box;
    }

}

.checkboxList {
    position: relative;
    width: 50%;
    line-height: 48px;
    margin-left: 22px;

    >img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-top: 7px;
    }
}

.textHide {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.radioList {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    >img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-top: 7px;
    }
}