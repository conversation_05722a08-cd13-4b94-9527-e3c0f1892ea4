import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProFormSelect,
  // ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import { useEffect, useState } from 'react';
import {
  addFbaAPI,
  addressFuzzySearchAPI,
  addWareHouseAPI,
} from '@/services/home/<USER>';
import { getCountryList } from '@/services/productAndPrice/api';
const AddFba = (props: any) => {
  const { right } = props;
  /*是否是修改*/
  const isRevised = props?.Revised;
  /*外部掉用的时候添加这个参数*/
  const providerId = props?.providerId;
  const type = props?.type || 'primary';
  const { dataList } = props;
  const [form] = Form.useForm<any>();
  const [defaultValue, setDefaultValue] = useState<any>('');
  const [open, setOpen] = useState<any>(false);
  useEffect(() => {
    if (isRevised) {
      form.setFieldsValue({ ...dataList, province: defaultValue });
      setDefaultValue(
        `${dataList.country === 'CN' ? '中国' : dataList.country} - ${
          dataList.provinceShortName
        } / ${dataList.province} - ${dataList.city}`,
      );
    }
  }, [dataList, open]);
  /* 提交 */
  const onFinish = async (values: any) => {
    const { province2, ...other } = values;
    delete values.province;
    const params = {
      ...other,
      service_id: 'Waybill',
      province: values.province2,
      //country: province.country,
      //province: province.province,
      //county: province.county,
    };
    /*  if (providerId) {
      params.type = 2;
      params.providerId = providerId;
    }*/
    console.log(params, 'params');

    const { status, data } = await addFbaAPI(params);
    if (status) {
      message.success('提交成功');
      props?.refreshTable(data);
      return true;
    } else {
      return false;
    }
  };
  /* 修改只能修改这四个
      contactName
      contactPhone
      companyName
      mnemonicCode
    */
  /*修改仓库*/
  /*  const onRevised = async (values: any) => {
    const { status } = await editWareHouseAPI({
      contactName: values.contactName,
      contactPhone: values.contactPhone,
      companyName: values.companyName,
      name: values.name,
      email: values.email,
      fax: values.fax,
      id: dataList.id,
    });
    if (status) {
      message.success('修改成功');
      props.refreshTable();
      return true;
    } else {
      // message.error('提交失败');
      return false;
    }
  };*/

  return (
    <ModalForm
      title={`${isRevised ? '编辑' : '新建'}仓库`}
      trigger={
        isRevised ? (
          <a>修改</a>
        ) : (
          <Button type={type} style={{ marginRight: right ? right : 0 }}>
            <PlusOutlined rev={undefined} />
            新建仓库
          </Button>
        )
      }
      form={form}
      labelWrap={true}
      labelCol={{ span: 3 }}
      wrapperCol={{ span: 15 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      layout="horizontal"
      submitTimeout={2000}
      onOpenChange={() => setOpen(!open)}
      onFinish={onFinish}
    >
      <ProFormText
        name="name"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="FBA代码"
      />
      <ProFormSelect
        name="province"
        label="查询"
        style={{ minWidth: '196px' }}
        fieldProps={{
          labelInValue: true,

          filterOption: false,
          onChange: (e) => {
            console.log('JSON.parse(e?.value)', JSON.parse(e?.value));
            const { zipCode, city, country, province } = JSON.parse(e?.value);
            form.setFieldValue('zipCode', zipCode);
            form.setFieldValue('city', city);
            form.setFieldValue('country', country);
            form.setFieldValue('province2', province);
          },
        }}
        showSearch
        disabled={isRevised}
        placeholder="请选择，支持模糊搜索"
        //rules={[{ required: true, message: '必填不能为空' }]}
        debounceTime={300}
        request={async ({ keyWords }) => {
          const { status, data } = await addressFuzzySearchAPI({
            token: keyWords,
          });
          if (status) {
            //country - short_province_name / province - city
            return data.list.map((item: any) => {
              return {
                label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                value: JSON.stringify(item),
              };
            });
          } else {
            return [];
          }
        }}
      />
      <ProFormSelect
        name="country"
        style={{ minWidth: '196px' }}
        label="国家"
        rules={[{ required: true, message: '必填项不能为空' }]}
        disabled={isRevised}
        request={async ({ keyWords }) => {
          const { status, data } = await getCountryList({
            start: 0,
            len: 200,
            keyword: keyWords,
          });
          if (status) {
            return data.list.map((item: any) => {
              return {
                label: `${item.name}-${item.cnName}-${item.code}`,
                value: item.code,
              };
            });
          }
          return [];
        }}
      />
      <ProFormText
        name="province2"
        label="州/省"
        style={{ width: '90%' }}
        rules={[{ required: true, message: '必填项不能为空' }]}
        disabled={isRevised}
      />
      <ProFormText
        name="city"
        label="城市"
        style={{ width: '90%' }}
        rules={[{ required: true, message: '必填项不能为空' }]}
        disabled={isRevised}
      />
      <ProFormText
        name="street"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="街道门牌"
        disabled={isRevised}
      />
      <ProFormText
        name="zipCode"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="邮编"
        disabled={isRevised}
      />
      <ProFormText
        name="contactName"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="联系人"
      />
      <ProFormText
        name="contactPhone"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="电话"
      />
      <ProFormText
        name="companyName"
        rules={[{ required: true, message: '必填项不能为空' }]}
        label="公司"
      />
      <ProFormText name="email" label="电子邮箱" />
      <ProFormText name="fax" label="传真" />
    </ModalForm>
  );
};
export default AddFba;
