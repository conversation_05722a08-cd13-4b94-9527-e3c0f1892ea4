import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { Select } from 'antd';
import { useState, useEffect } from 'react';
import React from 'react';
dayjs.extend(utc);
dayjs.extend(timezone);

const timezoneOptions = [
  { label: '上海 (UTC+8)', value: 'Asia/Shanghai' },
  { label: '纽约 (UTC-5)', value: 'America/New_York' },
  { label: '洛杉矶 (UTC-8)', value: 'America/Los_Angeles' },
  { label: '芝加哥 (UTC-6)', value: 'America/Chicago' },
];

interface TimeZoneSelectionProps {
  onChange: (value: any) => void;
  value: any | (() => any); // 当前时区，可以是值或函数
  forms?: any;
}

const TimeZoneSelection = ({ value, onChange }: TimeZoneSelectionProps) => {
  const initialTimezone = value?.$x?.$timezone || dayjs.tz.guess() || 'Asia/Shanghai';
  const [selectedTimezone, setSelectedTimezone] = useState(initialTimezone);
  
  // 当value变化时更新selectedTimezone
  useEffect(() => {
    if (value && value.$x && value.$x.$timezone) {
      setSelectedTimezone(value.$x.$timezone);
    }
  }, [value]);

  const handleTimezoneChange = (tz: string) => {
    setSelectedTimezone(tz);
    
    const currentValue = value || dayjs();
    
    const tzDate = dayjs(currentValue).tz(tz);
    
    onChange(tzDate);
  };

  // const handleTimezoneChange = (tz: string) => {
  //   console.log('tz', tz);
  //   setSelectedTimezone(tz);
  //   console.log('value', value);
  //   console.log('forms', forms.getFieldsValue?.(value)?.[value]);
  //   const currentValue = forms.getFieldValue?.(value)?.[value];
  //   console.log('currentValue', currentValue);
  //   if (currentValue) {
  //     const tzDate = dayjs(currentValue).tz(tz);
  //     console.log('tzDate', tzDate);
  //     forms.setFieldsValue({ [value]: tzDate });
  //   }
  // };

  return (
    <div
      style={{
        // padding: '8px 0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <span>选择时区：</span>
      <Select
        style={{ width: 200 }}
        value={selectedTimezone}
        onChange={handleTimezoneChange}
        options={timezoneOptions}
        placeholder="选择时区"
      />
    </div>
  );
};
export { timezoneOptions };
export default React.memo(TimeZoneSelection);
