import { ProCard, ProFormSelect } from '@ant-design/pro-components';
import { Form } from 'antd';
import service from '@/services/home';
// import {  useState } from 'react';
import AddressLibrary from '@/components/AddressLibrary';
import { SearchOutlined } from '@ant-design/icons';
const { addressFuzzySearchAPI } = service.UserHome;
const SenderAddress = ({setSelectedArea}:any) => {
  const [form] = Form.useForm();
  // const [channelCount, setChannelCount] = useState<any>(null);
  /* 发件地址已选数据 */
  // const [selectedAreaObj, setSelectedAreaObj] = useState<any>(null);

  /* 从地址库选择的回调 */
  const changeAddressLibrary = (value: any) => {
    // console.log('value: ', value);
    // setSelectedAreaObj(value);
    setSelectedArea({
      key: JSON.stringify(value),
      value: JSON.stringify(value),
      label:JSON.stringify(value),
    });
    form.setFieldsValue({
      province: {
        key: JSON.stringify(value),
        label: `${value.country} - ${value.provinceShortName} / ${value.province} - ${value.city} / ${value.zipCode}`,
        value: JSON.stringify(value),
      },
    });
  };
  /* 获取客户渠道条数 */
  // const getChannelCount = async () => {
  //   try {
  //     const { status, data } = await getChannelCountAPI({
  //       Address: selectedAreaObj,
  //     });
  //     if (status) {
  //       setChannelCount(data.count);
  //     }
  //   } catch (e) {
  //     console.error('获取客户渠道条数接口出错',e);
  //   }
  // };
  // useEffect(() => {
  //   getChannelCount();
  // }, [selectedAreaObj]);

  return (
    <>
      <ProCard title="" style={{ marginBottom: '22px' }}
        extra={[
          <a key={1}>
            <SearchOutlined /> <AddressLibrary onChangeAddressLibrary={changeAddressLibrary} />
          </a>
        ]}
      >
        <Form
          name="basic"
          autoComplete="off"
          form={form}
          labelCol={{
            span: 2,
          }}
        >
          <ProFormSelect
            name="province"
            label="区域"
            width={500}
            fieldProps={{
              labelInValue: true,
              style: {
                minWidth: 140,
              },
              filterOption: false,
              onChange:(e)=>{
                // setSelectedAreaObj(JSON.parse(e.value));
                setSelectedArea(e);
                form.setFieldsValue({
                  province:e,
                });
                
              }
            }}
            showSearch
            placeholder="请选择，支持模糊搜索"
            rules={[{ required: true, message: '必填不能为空' }]}
            debounceTime={300}
            request={async ({ keyWords }) => {
              const { status, data } = await addressFuzzySearchAPI({
                token: keyWords,
              });
              if (status) {
                //country - short_province_name / province - city
                return data.list.map((item: any) => {
                  return {
                    label: `${item.country} - ${item.provinceShortName} / ${item.province} - ${item.city} / ${item.zipCode}`,
                    value: JSON.stringify(item),
                  };
                });
              } 
            }}
            
            // addonAfter={[<div key={1}>
            //   {
            //     channelCount?
            //     <span style={{color:'#1890ff',cursor:'pointer'}}>{channelCount} 个可用渠道</span>
            //     :
            //     <span style={{color:'#AEAEAE',cursor:'pointer'}}>暂无可用渠道</span>
            //   }
            // </div>]}
          />
        </Form>
      </ProCard>
    </>
  );
};
export default SenderAddress;
