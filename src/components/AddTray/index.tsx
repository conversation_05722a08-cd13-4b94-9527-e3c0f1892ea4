import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, DatePicker, Drawer, Tag } from 'antd';

import { waybillListAPIList } from '@/services/preplanCabin';
import { appendPallets, getPallets } from '@/services/ConfiguredCar';
import { DispatchMap } from '@/constants';
import { getObjectByValue, ProgressStateType2 } from '@/utils/constant';
import TrayDetail from '@/pages/CargoManagement/TrayDetail';
import SuperTables from '@/components/SuperTables';
import { formatTime } from '@/utils/format';

const { RangePicker } = DatePicker;

const AddTray = (props: any) => {
  const { onSelectWaybill, btnTitle, DefaultSelected, details, handleAdd } =
    props;
  console.log('details', details);
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的运单信息
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  // 被选中的运单信息
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [total, setTotal] = useState({
    number: 0,
    weight: 0,
    squares: 0,
  });
  const actionRef = useRef<any>();
  /*选中项*/
  const [Loading, setLoading] = useState<any>(false);

  useEffect(() => {
    getTotal();
  }, [selectedRows?.length]);
  useEffect(() => {
    /*设置回显选中项目*/
    if (isModalOpen && DefaultSelected) {
      setSelectedRowKeys(DefaultSelected);
    }
  }, [isModalOpen]);
  const getTotal = () => {
    const pieceNum = selectedRows?.reduce(
      (pre: any, cur: any) => pre + cur?.pieceNum,
      0,
    );
    const weight = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.weight),
      0,
    );
    const squares = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.squares),
      0,
    );
    setTotal({
      number: pieceNum,
      weight: weight,
      squares: squares,
    });
  };
  const columns: any = [
    {
      title: '托盘标签号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 150,
    },
    {
      title: '所在仓库',
      dataIndex: 'warehouse',
      hideInSearch: true,
      width: 150,
      fieldFormat: (record: any) => {
        return record?.warehouse?.name;
      },
    },
    {
      title: '所在货位',
      width: 100,
      hideInSearch: true,
      dataIndex: 'slotId',
    },
    {
      title: '状态',
      hideInSearch: true,
      dataIndex: 'state',
      width: 100,
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Tag
            style={{
              background: obj?.background,
              color: obj?.color,
              border: 'none',
            }}
          >
            {obj?.label}
          </Tag>
        );
      },
    },
    {
      title: '配车单号',
      width: 150,
      hideInSearch: true,
      dataIndex: 'shipmentNo',
    },

    {
      title: '派送方式',
      hideInSearch: true,
      width: 150,
      dataIndex: 'shipmentMethods',
    },
    /*   {
      title: 'FBA仓库',
      hideInSearch: true,
      dataIndex: 'createTime',
    },*/
    {
      title: '总件数',
      hideInSearch: true,
      width: 100,
      dataIndex: 'pieceNumber',
    },
    {
      title: '总立方数/CBM',
      hideInSearch: true,
      width: 100,
      dataIndex: 'volume',
    },
    {
      title: '总重量/Kg',
      hideInSearch: true,
      width: 100,
      dataIndex: 'weight',
    },
    {
      title: '上架时间',
      hideInSearch: true,
      width: 150,
      dataIndex: 'createTime',
      fieldFormat: (record: any) => {
        return record?.createTime ? formatTime(record?.createTime) : '';
      },
    },
    {
      title: '出货时间',
      hideInSearch: true,
      width: 150,
      dataIndex: 'signOutTime',
      fieldFormat: (record: any) => {
        return record?.signOutTime ? formatTime(record?.signOutTime) : '';
      },
    },
    {
      title: '送达时间',
      hideInSearch: true,
      width: 150,
      dataIndex: 'deliveriedTime',
      fieldFormat: (record: any) => {
        return record?.deliveriedTime ? formatTime(record?.deliveriedTime) : '';
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 160,
      fixed: 'right',
      valueType: 'option',
      // hideInTable: access.RAMReadOnly(),
      render: (text: any, record: any) => [
        <TrayDetail record={record} btnText={'查看'} key={record.id} />,
        /*     <Button
                       key="edit"
                       type="link"
                       onClick={() => {
                         setValue(record);
                         setIsModalOpen(true);
                       }}
                     >
                       编辑
                     </Button>,
                     <Button
                       key="del"
                       type="link"
                       danger
                       onClick={() => deleteIdRule(record.id)}
                       style={{ visibility: record.removeable === 0 ? 'hidden' : 'visible' }}
                       disabled={record.removeable === '0'}
                     >
                       删除
                     </Button>,*/
      ],
    },
  ];

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    console.log('check', checkData);
    const ids = checkData
      ?.filter((item: any) => !item.children)
      .map((item: any) => item.id)
      .join(',');
    setLoading(true);
    const res = await appendPallets({
      shipmentId: details?.id,
      palletsIds: ids,
    });
    if (res?.status) {
      setLoading(false);
      handleAdd();
      setIsModalOpen(false);
    } else {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const getList = async () => {
    return await waybillListAPIList({
      service_id: 'Waybill',
    });
  };
  const filters = {
    keyword: {
      type: 'search',
      value: '',
      termQuery: false,
    },
    // signInTime
  };
  return (
    <>
      <div onClick={showModal}>
        {/* <IconItem name="#icon-dizhibao" style={{ marginRight: '6px' }} /> */}
        {!btnTitle ? (
          <div>
            <Button type={'primary'}>追加</Button>
          </div>
        ) : (
          <div
            style={{
              display: 'inline-block',
              cursor: 'pointer',
              position: 'absolute',
              top: '374px',
              right: '50px',
              zIndex: 999,
              //       top: '319px',
              // right: '50px',
              // z-index: 999
            }}
          >
            <a style={{ display: 'inline-block' }}>{btnTitle}</a>
          </div>
        )}
      </div>
      <Drawer
        title="追加托盘"
        style={{
          width: '100vw',
          position: 'fixed',
          top: '1px',
          bottom: '1px',
          // marginBottom: '-1em',
          right: '1px',
          left: '1px',
          zIndex: 999999,
        }}
        destroyOnClose
        open={isModalOpen}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <div style={{ height: 'calc(100% - 40px)' }}>
          {/* <div style={{ marginLeft: '25px' }}>
            <span>件数：<span style={{ color: 'rgb(100, 64, 255)' }}>{total?.number}</span></span>
            <span style={{ margin: '0 20px' }}>重量：<span style={{ color: 'rgb(0, 186, 217)' }}>{total?.weight}</span></span>
            <span>放数：<span style={{ color: 'rgb(255, 64, 165)' }}>{total?.squares}</span></span>
          </div> */}
          <>
            <SuperTables
              instanceId="addtraylist222"
              columns={columns}
              rowSelection={(value: any) => {
                setCheckData(value);
                console.log('value', value);
              }}
              isHideToolBar={false}
              warpHeight={300}
              request={async (params: any, action: any) => {
                actionRef.current = action;
                // console.log('tableactiveKey',activeKey);
                let msg = await getPallets({
                  condition: params?.condition || {},
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                });
                return {
                  data: msg?.data?.list || [],
                  success: msg?.status,
                  total: msg?.data?.amount || msg?.data?.total,
                };
              }}
              filters={{
                keyword: {
                  type: 'search',
                  value: '',
                  termQuery: false,
                },
                warehouseId: {
                  desc: '所在仓库',
                  type: 'overseas',
                  value: '',
                },
                /* fbaCode: {
                  desc: 'FBA仓库',
                  type: 'FBA',
                  value: '',
                },*/
                slotId: {
                  desc: '所在货位',
                  type: 'slotId',
                  value: '',
                },
                shipmentMethod: {
                  desc: '派送方式',
                  type: 'select',
                  range: DispatchMap,
                  value: '',
                },
                /*      clientId: {
                  desc: '客户',
                  type: 'client',
                  value: '',
                },*/
                zipCode: {
                  desc: '邮编',
                  type: 'text',
                  value: '',
                },
                estimatePickupTime: {
                  desc: '提货时间',
                  type: 'dateTimeRange',
                  value: '',
                },
              }}
              /* toolBarRender={<Button
                   key="primary"
                   type="primary"
                   onClick={() => setIsModalOpen(true)}
                 >
                   新建规则
                 </Button>}*/
            />
          </>
        </div>
        <div
          key="btn"
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '40px',
            zIndex: '10',
          }}
        >
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            loading={Loading}
            onClick={handleOk}
            style={{ marginLeft: 10 }}
          >
            确认
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default React.memo(AddTray);
