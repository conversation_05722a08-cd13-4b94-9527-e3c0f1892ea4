import { AgGridReact } from 'ag-grid-react';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { AG_GRID_LOCALE_CN } from '@ag-grid-community/locale';
import myTheme from './themes';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import { usePagination, useSize } from 'ahooks';
import { useSnapshot } from 'umi';
import {
  paramsStore,
  screenStore,
  settingsStore,
} from '../SuperTables/store/screenStore';
import SearchFilters from '../SuperTables/components/SearchFilters';
import { ProCard } from '@ant-design/pro-components';
import Loading from '../SuperTables/components/Loding';
import { ConfigProvider, Pagination, Space, Tooltip } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import './index.less';
import TotalNumberOfPages from '../SuperTables/TotalNumberOfPages';
// import ScreenModal from './components/ScreenModal';
interface StandardTableProps {
  columns: any[]; // 列配置
  rowSelection?: any; // 行选择配置
  instanceId: string; // 实例id
  request: any; // 请求函数
  isApiSorter?: boolean; // 是否启用API排序
  isPageCacheEnabled?: boolean; // 是否启用分页缓存
  filters?: any;//搜索条件 参数同之前版本
  autoQueryEnabled?: boolean; // 是否启用自动查询
  warpHeight?: number;//表格高度
  isWarpTabs?: boolean;//是否开启标签页
  height?: string | number | undefined;//表格高度
  ghost?: boolean;//是否开启幽灵模式
  isHideToolBar?: boolean;//是否隐藏工具栏
  toolBarRender?: () => JSX.Element;//工具栏渲染
  toolBarRenderRight?: () => JSX.Element;//工具栏右侧渲染
  isHidePagination?: boolean; // 是否隐藏分页
  TotalConfiguration?: any; // 总计配置
  dataSource?: any[];//数据源
}

const SuperTableMax = forwardRef((props: StandardTableProps, ref) => {
  const {
    columns,
    request,
    instanceId,
    filters,
    autoQueryEnabled,
    warpHeight = 0,
    isWarpTabs,
    height,
    ghost = false,
    isHideToolBar,
    toolBarRender,
    toolBarRenderRight,
    isHidePagination = false,
    TotalConfiguration,
    dataSource,
    rowSelection,
    isApiSorter = false,
  } = props;
  const RefTable = useRef<any>(null);

  const [toList] = usePermissionFiltering();
  const snapParams = useSnapshot(paramsStore);
  const snapScreen = useSnapshot(screenStore);
  const snapSettings = useSnapshot(settingsStore);

  const tableSearchRef = useRef<any>(null);
  const size = useSize(tableSearchRef);
  const HEADER_HEIGHT = warpHeight ? warpHeight : isWarpTabs ? 190 + 50 : 190;
  const [tableHeight, setTableHeight] = useState<string>(
    `calc(100vh - ${HEADER_HEIGHT}px`,
  );

  useEffect(() => {
    if (size?.height) {
      const newHeight = `calc(100vh - ${HEADER_HEIGHT}px - ${size.height}px)`;
      setTableHeight(newHeight);
    }
  }, [size?.height]);

  // console.log('props', props);

  const [params, setParams] = useState<any>({});
  const [condition, setCondition] = useState<any>({});
  const [rowDatas, setRowDatas] = useState<any>([]);
  const [selectedData, setSelectedData] = useState<any[]>([]);

  /* 搜索兼容 增加userSoleId */
  const tableFilter = useMemo(() => {
    if (!filters) return {};
    const newFilters = Object.fromEntries(
      Object.entries(filters).map(([key, value]) => [
        key,
        {
          ...(typeof value === 'object' ? value : { value }),
          userSoleId: key,
        },
      ]),
    );
    setCondition(newFilters);
    return newFilters;
  }, [JSON.stringify(filters), Object?.keys(filters)?.length]);

  const resetScreenStore = useCallback(
    (tableInstanceId?: string | Array<string> | undefined) => {
      if (tableInstanceId && typeof tableInstanceId === 'string') {
        screenStore[tableInstanceId] = {};
      }
      if (tableInstanceId && Array.isArray(tableInstanceId)) {
        tableInstanceId.forEach((item: string) => {
          screenStore[item] = {};
        });
      }

      if (!tableInstanceId) {
        screenStore[instanceId] = {};
      }
      setParams({
        ...params,
        columnsFilter: snapScreen[instanceId],
      });
    },
    [instanceId],
  );

  useEffect(() => {
    if (
      snapScreen[instanceId] &&
      Object.keys(snapScreen[instanceId]).length > 0
    ) {
      setParams({
        columnsFilter: snapScreen[instanceId],
        // condition: { ...condition },
      });
    }
  }, [snapScreen[instanceId], instanceId]);

  const columnDefs = useMemo(() => {
    return toList([
      // {
      //   field: 'checkbox',
      //   headerName: '',
      //   checkboxSelection: true,
      //   pinned: 'left',
      //   headerCheckboxSelection: true,
      //   width: 50,
      //   suppressHeaderMenuButton: true,
      //   suppressHeaderContextMenu: true,
      //   suppressSorting: true, // 禁用排序
      //   suppressMovable: true, // 禁用移动
      //   resizable: false, // 禁用调整大小
      //   filter: false,
      //   sortable: false,
      //   // cellStyle: { 'user-select': 'none' },
      // },
      ...columns.map((item: any) => {
        return {
          // ...item,
          enableRowGroup: item?.enableRowGroup,
          enableValue: item?.enableValue,
          headerName: item.title,
          // enableRowGroup: true,
          field: item.dataIndex,
          width: item.width,
          pinned: item?.pinned,
          ...(item?.fieldFormat
            ? {
                tooltipValueGetter: (params: any) =>
                  item.fieldFormat(params?.data),
                valueFormatter: (params: any) => item.fieldFormat(params?.data),
                valueGetter: (params: any) => item.fieldFormat(params?.data),
                keyCreator: (params: any) => item.fieldFormat(params?.data),
              }
            : { tooltipField: item.dataIndex }),
          ...(item?.render && {
            cellRenderer: (params: any) =>
              item.render(params?.value, params?.data), //TODO: 会影响数据穿透 
            // cellRenderer: (params: any) =>
            //   item.render(params),
          }),
          ...(item?.style && {
            cellStyle: (params: any) => item.style(params?.value, params?.data),
          }),
          // ...(isApiSorter ? { comparator: () => 0 } : {}),//TODO: 1
          ...(item?.filters &&
            {
              // headerComponent: ScreenModal,
              // headerComponentParams: {
              //   innerHeaderComponent: ScreenModal,
              //   innerHeaderComponentParams: {
              //     filters: item.filters,
              //     instanceId: instanceId,
              //   },
              // },
              // filter: ScreenModal,
              // filterParams: {
              //   filters: item.filters,
              //   instanceId: instanceId,
              // },
            }),
          filter: true,
        };
      }),
    ]);
  }, [columns]);

  const onGridReady = useCallback((params: any) => {
    console.log('onGridReady', params);
  }, []);

  const { data, loading, pagination, refresh }: any = usePagination(
    async (paginationParams) => {
      if (dataSource) {
        setRowDatas(dataSource);
      }
      if (!request) return Promise.reject('No request function');

      //TODO: 分页缓存
      // if (isPageCacheEnabled || snapSettings[instanceId]?.cachePagination) {
      //   paramsStore[instanceId] = paginationParams;
      // }

      return request({
        condition: { ...condition },
        ...(paginationParams || {
          current: snapParams[instanceId]?.current || pagination.current,
          pageSize: snapParams[instanceId]?.pageSize || pagination.pageSize,
        }),
        // ...paginationParams,
        ...params,
      });
    },
    {
      debounceWait: 600,
      defaultCurrent: snapParams[instanceId]?.current || 1,
      defaultPageSize: snapParams[instanceId]?.pageSize || 100,
      refreshDeps: [params, condition],
      onSuccess: (res: any) => {
        if (res?.success) {
          setRowDatas(res?.data || []);
        }
      },
      // defaultParams: [
      //   {
      //     current: 1,
      //     pageSize: 100,
      //   },
      // ],
    },
  );

  useImperativeHandle(
    ref,
    () => ({
      refresh,
      reload: refresh,
      resetScreenStore,
      tableInstance: { ...RefTable.current },
    }),
    [refresh],
  );

  const rowClassRules = useMemo(() => {
    return {
      'cell-red': (params: any) => params?.data?.holdFlag,
    };
  }, []);

  const onSelectionChanged = useCallback((e: any) => {
    const selectedRows = e.api.getSelectedRows();
    if (rowSelection) {
      rowSelection(selectedRows);
      setSelectedData(selectedRows);
    }
  }, []);

  const getContextMenuItems: any = useCallback((params: any) => {
    const defaultItems = [
      'copy',
      'copyWithHeaders',
      // 'paste',
      'separator',
      'export',
    ];
    const customItems = [
      // {
      //   name: '复制整列数据',
      //   action: () => {
      //     const columnField = params.column.getColId();
      //     const columnData: any[] = [];
      //     params.api.forEachNode((node: any) => {
      //       columnData.push(node.data[columnField]);
      //     });
      //     const columnText = columnData.join('\n');
      //     navigator.clipboard
      //       .writeText(columnText)
      //       .then(() => {
      //         console.log('整列数据已复制到剪贴板');
      //       })
      //       .catch((err) => {
      //         console.error('复制失败:', err);
      //       });
      //   },
      //   icon: '<i class="fa fa-copy"></i>',
      // },
      {
        name: '选中',
        action: () => {
          const cellRanges = params.api.getCellRanges();
          if (!cellRanges || cellRanges.length === 0) {
            alert('未选择任何范围！');
            return;
          }
          params.api.forEachNode((node: any) => {
            const rowIndex = node.rowIndex;
            const inRange = cellRanges.some((range: any) => {
              const startRow = range.startRow.rowIndex;
              const endRow = range.endRow.rowIndex;
              return rowIndex >= startRow && rowIndex <= endRow;
            });

            if (inRange) {
              node.setSelected(true);
            }
          });
        },
        icon: '<i class="ag-icon ag-icon-tick"/>',
      },
      {
        name: '取消选中范围',
        action: () => {
          const cellRanges = params.api.getCellRanges();
          if (!cellRanges || cellRanges.length === 0) {
            alert('未选择任何范围！');
            return;
          }
          params.api.forEachNode((node: any) => {
            const rowIndex = node.rowIndex;
            const inRange = cellRanges.some((range: any) => {
              const startRow = range.startRow.rowIndex;
              const endRow = range.endRow.rowIndex;
              return rowIndex >= startRow && rowIndex <= endRow;
            });

            if (inRange) {
              node.setSelected(false);
            }
          });
        },
        icon: '<i class="ag-icon ag-icon-cross"></i>',
      },
    ];
    return [...defaultItems, 'separator', ...customItems];
  }, []);

  const onSortChanged = useCallback((e: any) => {
    return; //TODO: 2
    if (!isApiSorter) return;
    const colId =
      e?.columns?.length === 1 ? e?.columns[0]?.colId : e?.columns[1]?.colId;
    const allColumns = e?.api
      ?.getColumnState()
      .filter((col: any) => col.sort)
      .map((col: any) => ({ colId: col.colId, sort: col.sort }));
    const { sort = 'normal' } = allColumns?.[0] || {};
    // 清空其他列的排序状态
    Object.keys(screenStore[instanceId] || {}).forEach((key) => {
      if (screenStore[instanceId][key].sort) {
        screenStore[instanceId][key].sort = '';
      }
    });

    const sortValueMap = {
      normal: '',
      asc: 1,
      desc: -1,
    };

    settingsStore[instanceId] = {
      ...settingsStore[instanceId],
      sortState: (sort as keyof typeof sortValueMap) ?? '',
    };

    screenStore[instanceId] = {
      ...screenStore[instanceId],
      [colId]: {
        ...screenStore?.[instanceId]?.[colId],
        sort: sortValueMap[sort as keyof typeof sortValueMap] ?? '',
      },
    };
  }, []);

  const defaultColDef = useMemo(() => {
    return {
      // filter: true,
    };
  }, []);

  const rowSelections: any = useMemo(() => {
    return {
      mode: 'multiRow',
      enableClickSelection: 'enableDeselection',
      checkboxes: true,
    };
  }, []);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div ref={tableSearchRef}>
        {Object?.keys(tableFilter)?.length > 0 && (
          <SearchFilters
            tableFilter={tableFilter}
            callBack={(e: any) => {
              let newE = e;
              if (Object?.keys(e)?.length === 0) {
                newE = tableFilter;
              }
              setCondition({ ...newE });
            }}
            autoQueryEnabled={
              autoQueryEnabled
                ? autoQueryEnabled
                : snapSettings[instanceId]?.manualSearch
            }
          />
        )}
      </div>
      <Loading loading={loading} text="加载中">
        <ProCard ghost={ghost}>
          <div className="flex justify-between mb-10px h-26px">
            <ConfigProvider componentSize="small">
              {toolBarRender ? toolBarRender() : <Space> </Space>}
            </ConfigProvider>
            {!isHideToolBar ? (
              <Space className="mr-6px" size={10}>
                {toolBarRenderRight ? toolBarRenderRight() : <Space> </Space>}
                {/* <RoamingGuidance
                      refs={RefTable}
                      refs2={refreshRef}
                      refs3={settingRef}
                    /> */}
                {/* <FilterModal columns={columns} /> */}
                {/* <RowHeightSetting instanceId={instanceId} /> */}
                <div>
                  <Tooltip title="刷新" placement="bottom">
                    <ReloadOutlined
                      className="hover:text-#1677ff"
                      style={{ fontSize: 16, fontWeight: 500 }}
                      spin={loading}
                      onClick={refresh}
                    />
                  </Tooltip>
                </div>
                <div>
                  {/* <SetUpModal
                        instanceId={instanceId}
                        isPageCacheEnabled={isPageCacheEnabled}
                        columns={columns}
                      /> */}
                </div>
                {/* <SaveView instanceId={instanceId} /> */}
              </Space>
            ) : (
              <Space> </Space>
            )}
          </div>
          <div
            style={{
              transition: 'height 0.3s ease',
              height: height || tableHeight,
            }}
          >
            <AgGridReact
              ref={RefTable} // 表格实例
              theme={myTheme} // 主题
              columnDefs={columnDefs} // 列配置
              rowData={rowDatas} // 数据源
              localeText={AG_GRID_LOCALE_CN} // 语言
              cellSelection={true} // 单元格选择
              rowSelection={rowSelections}
              selectionColumnDef={{
                pinned: 'left',
                suppressHeaderMenuButton: true,
                suppressHeaderContextMenu: true,
              }}
              // rowSelection="multiple"
              onGridReady={onGridReady} // 表格初始化
              animateRows={true} // 动画
              rowClassRules={rowClassRules} // 行样式
              rowNumbers={true} // 行号
              // suppressRowClickSelection={true} // 禁用行点击选择 弃用了
              rowHeight={40}
              headerHeight={40}
              // rowBuffer={20} //优化
              onSelectionChanged={onSelectionChanged}
              onSortChanged={onSortChanged}
              // debug
              getContextMenuItems={getContextMenuItems}
              suppressMultiSort={true} // 禁用多列排序
              defaultExcelExportParams={{
                sheetName: '明锐云数据表',
              }}
              // loading={loading}
              // onColumnResized={onColumnResized}
              // groupSelectsChildren={true}
              sideBar={true}
              // sideBar={{
              //   toolPanels: [
              //     {
              //       id: 'columns',
              //       labelDefault: 'Columns',
              //       labelKey: 'columns',
              //       iconKey: 'columns',
              //       toolPanel: 'agColumnsToolPanel', // 默认列工具面板
              //     },
              //     {
              //       id: 'filters',
              //       labelDefault: 'Filters',
              //       labelKey: 'filters',
              //       iconKey: 'filter',
              //       toolPanel: 'agFiltersToolPanel', // 默认过滤工具面板
              //     },
              //   ],
              //   // defaultToolPanel: 'collapsed', // 设置侧边栏默认收起
              // }}
              // groupAllowUnbalanced={true} // 允许不平衡分组
              // autoGroupColumnDef={{
              //   headerName: '分组信息', // 自定义分组列的标题
              //   cellRendererParams: {
              //     innerRenderer: (params: any) => {
              //       console.log('分组', params);
              //       return params.value !== null && params.value !== undefined
              //         ? params.value
              //         : '';
              //     },
              //   },
              // }}
              defaultColDef={defaultColDef}
            />
          </div>
          {!isHidePagination && (
            <div className="flex justify-between mt-5px border-1 border-solid rd-3px border-#E1E4E8 p-8px bg-#fbfbfd hover:bg-#F3F8FF select-none">
              <div className="flex-grow overflow-hidden mr-4">
                <TotalNumberOfPages
                  rowSelection={rowSelection}
                  selectedData={selectedData}
                  TotalConfiguration={TotalConfiguration}
                  dataSource={dataSource || data?.data || []}
                />
              </div>
              <div className="flex-shrink-0">
                <Pagination
                  current={dataSource?.length ? 1 : pagination.current}
                  pageSize={dataSource?.length ? 10000 : pagination.pageSize}
                  total={dataSource?.length || data?.total}
                  onChange={pagination.onChange}
                  showSizeChanger
                  size="small"
                  pageSizeOptions={[100, 500, 1000, 2000, 3000, 5000, 10000]}
                  showTotal={(total) => `共 ${total} 条`}
                />
              </div>
            </div>
          )}
        </ProCard>
      </Loading>
    </div>
  );
});

export default React.memo(SuperTableMax);
