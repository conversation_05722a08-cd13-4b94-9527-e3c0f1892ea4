import { AgGridReact } from 'ag-grid-react';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { AG_GRID_LOCALE_CN } from '@ag-grid-community/locale';
import myTheme from './themes';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import { useMount, usePagination, useSize, useDebounceFn } from 'ahooks';
import { useSnapshot } from 'umi';
import {
  paramsStore,
  rowHeightStore,
  screenStore,
  settingsStore,
} from '../SuperTables/store/screenStore';
import SearchFilters from '../SuperTables/components/SearchFilters';
import { ProCard } from '@ant-design/pro-components';
import Loading from '../SuperTables/components/Loding';
import { ConfigProvider, Pagination, Space, Tooltip, Alert } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import styles from './index.module.less';
import TotalNumberOfPages from '../SuperTables/TotalNumberOfPages';
import RowHeightSetting from '../SuperTables/components/RowHeightSetting';
import { tableColumnWidthStore } from './store';
import ScreenModal from './components/ScreenModal';
import SetUpModal from './components/SetUpModal';
import SaveView from './components/SaveView';

// 性能优化：添加深度比较工具函数，避免使用 JSON.stringify 进行依赖比较
const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;
  if (obj1 === null || obj2 === null) return false;
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

// 性能优化：创建稳定的过滤器键值，避免每次渲染都重新计算
const createFilterKey = (filters: any): string => {
  if (!filters || typeof filters !== 'object') return '';
  return Object.keys(filters).sort().join('|');
};
// 性能优化：添加更严格的类型定义，提高代码质量和开发体验
interface ColumnDefinition {
  title: string;
  dataIndex: string;
  width?: number;
  flex?: number;
  render?: (value: any, record: any, params: any) => React.ReactNode;
  style?: (value: any, record: any) => React.CSSProperties;
  fieldFormat?: (data: any) => string;
  filters?: any[];
  rowGroup?: boolean;
  enableRowGroup?: boolean;
  enableValue?: boolean;
  aggFunc?: string;
  pinned?: 'left' | 'right';
  hide?: boolean;
  editable?: boolean;
  cellEditor?: string;
  cellEditorParams?: any;
  onCellValueChanged?: (params: any) => void;
  wrapText?: boolean;
  autoHeight?: boolean;
  suppressExcelExport?: boolean;
  rowSpan?: boolean;
  refData?: any;
}

interface StandardTableProps {
  columns: ColumnDefinition[]; // 列配置
  rowSelection?: ((selectedRows: any[]) => void) | any; // 行选择配置 - 保持向后兼容
  instanceId: string; // 实例id
  request?: (params: any) => Promise<any>; // 请求函数
  isApiSorter?: boolean; // 是否启用API排序
  isPageCacheEnabled?: boolean; // 是否启用分页缓存
  filters?: Record<string, any>; //搜索条件 参数同之前版本
  autoQueryEnabled?: boolean; // 是否启用自动查询
  warpHeight?: number; //表格高度
  isWarpTabs?: boolean; //是否开启标签页
  height?: string | number | undefined; //表格高度
  ghost?: boolean; //是否开启幽灵模式
  isHideToolBar?: boolean; //是否隐藏工具栏
  toolBarRender?: () => JSX.Element; //工具栏渲染
  toolBarRenderRight?: () => JSX.Element; //工具栏右侧渲染
  isHidePagination?: boolean; // 是否隐藏分页
  TotalConfiguration?: any; // 总计配置
  dataSource?: any[]; //数据源
  treeData?: boolean; //是否开启树形结构
  getDataPath?: (data: any) => string; // 获取数据路径
  enableRowPinning?: boolean; //是否开启行固定
  isAutoFetch?: boolean; //是否开启自动查询
  enableCellSpan?: boolean; //是否开启单元格合并
  getRowIdFn?: (params: any) => string; //设置唯一 id 兼容后端数据无唯一 id
  rowSelectionMode?: 'multiRow' | 'singleRow'; // 行选择模式
  groupDefaultExpanded?: number; // 默认展开组
}

const SuperTableMax = forwardRef((props: StandardTableProps, ref) => {
  const {
    columns,
    request,
    instanceId,
    filters = {},
    autoQueryEnabled,
    warpHeight = 0,
    isWarpTabs,
    height,
    ghost = false,
    isHideToolBar,
    toolBarRender,
    toolBarRenderRight,
    isHidePagination = false,
    TotalConfiguration,
    dataSource,
    rowSelection,
    isApiSorter = false,
    enableRowPinning = false,
    isPageCacheEnabled = false,
    isAutoFetch,
    enableCellSpan = false,
    getRowIdFn,
    rowSelectionMode = 'multiRow',
    groupDefaultExpanded = 1,
  } = props;
  const RefTable = useRef<any>(null);

  const [toList] = usePermissionFiltering();
  const snapParams = useSnapshot(paramsStore);
  const snapScreen = useSnapshot(screenStore);
  const snapSettings = useSnapshot(settingsStore);
  const snapRowHeightStore = useSnapshot(rowHeightStore);

  const [isItTheFirstTime, setIsItTheFirstTime] = useState(false);

  const tableSearchRef = useRef<any>(null);
  const size = useSize(tableSearchRef);
  const HEADER_HEIGHT = warpHeight ? warpHeight : isWarpTabs ? 190 + 50 : 190;
  const [tableHeight, setTableHeight] = useState<string>(
    `calc(100vh - ${HEADER_HEIGHT}px`,
  );

  useEffect(() => {
    if (size?.height) {
      const newHeight = `calc(100vh - ${HEADER_HEIGHT}px - ${size.height}px)`;
      setTableHeight(newHeight);
    }
  }, [size?.height]);

  // console.log('props', props);

  const [params, setParams] = useState<any>({});
  const [condition, setCondition] = useState<any>({});
  const [rowDatas, setRowDatas] = useState<any>([]);
  const [selectedData, setSelectedData] = useState<any[]>([]);

  // 性能优化：使用 ref 存储上一次的数据，避免不必要的重新渲染
  const prevRowDataRef = useRef<any[]>([]);

  // 性能优化：缓存列定义的计算结果，避免重复计算
  const prevColumnsRef = useRef<ColumnDefinition[]>([]);
  const prevColumnDefsRef = useRef<any[]>([]);

  // 功能增强：添加错误状态管理
  const [error, setError] = useState<string | null>(null);
  const [dataValidationErrors, setDataValidationErrors] = useState<any[]>([]);

  useMount(() => {
    setIsItTheFirstTime(true);
  });

  // 性能优化：组件卸载时清理缓存，防止内存泄漏
  useEffect(() => {
    return () => {
      // 清理当前实例的列状态缓存
      if (tableColumnWidthStore[instanceId]) {
        delete tableColumnWidthStore[instanceId];
      }
    };
  }, [instanceId]);



  /* 搜索兼容 增加userSoleId */
  // 性能优化：使用更高效的依赖比较，避免 JSON.stringify
  const filterKey = useMemo(() => createFilterKey(filters), [filters]);
  const tableFilter = useMemo(() => {
    if (!filters) return {};
    const newFilters = Object.fromEntries(
      Object.entries(filters).map(([key, value]) => [
        key,
        {
          ...(typeof value === 'object' ? value : { value }),
          userSoleId: key,
        },
      ]),
    );
    setCondition(newFilters);
    return newFilters;
  }, [filterKey, Object?.keys(filters)?.length]);

  const resetScreenStore = useCallback(
    (tableInstanceId?: string | Array<string> | undefined) => {
      if (tableInstanceId && typeof tableInstanceId === 'string') {
        screenStore[tableInstanceId] = {};
      }
      if (tableInstanceId && Array.isArray(tableInstanceId)) {
        tableInstanceId.forEach((item: string) => {
          screenStore[item] = {};
        });
      }

      if (!tableInstanceId) {
        screenStore[instanceId] = {};
      }
      setParams({
        ...params,
        columnsFilter: snapScreen[instanceId],
      });
    },
    [instanceId],
  );

  useEffect(() => {
    if (
      snapScreen[instanceId] &&
      Object.keys(snapScreen[instanceId]).length > 0
    ) {
      setParams({
        columnsFilter: snapScreen[instanceId],
        // condition: { ...condition },
      });
    }
  }, [snapScreen[instanceId], instanceId]);

  // 验证缓存状态是否与当前列定义匹配
  const validateColumnState = useCallback(
    (cachedState: any[], columnDefs: any[]) => {
      const currentFields = new Set(columnDefs.map((col) => col.field));
      return cachedState.filter(
        (col) => col && col.colId && currentFields.has(col.colId),
      );
    },
    [],
  );

  // 性能优化：将复杂的列状态同步逻辑拆分为独立函数
  const syncColumnProperties = useCallback((cachedCol: any, matchingColDef: any): boolean => {
    let hasChanges = false;

    // 同步聚合函数
    if (matchingColDef.aggFunc && cachedCol.aggFunc !== matchingColDef.aggFunc) {
      cachedCol.aggFunc = matchingColDef.aggFunc;
      hasChanges = true;
    }

    // 同步固定状态
    if (matchingColDef.pinned && cachedCol.pinned !== matchingColDef.pinned) {
      cachedCol.pinned = matchingColDef.pinned;
      hasChanges = true;
    }

    return hasChanges;
  }, []);

  // 性能优化：创建新列状态的工厂函数
  const createNewColumnState = useCallback((colDef: any) => ({
    colId: colDef.field,
    width: colDef.width || 100,
    hide: false,
    pinned: colDef.pinned || null,
    sort: null,
    sortIndex: null,
    aggFunc: colDef.aggFunc || null,
  }), []);

  // 应用列缓存状态的统一函数
  const applyColumnStateToGrid = useCallback(
    (api: any, columnDefs: any[]) => {
      if (
        !tableColumnWidthStore[instanceId] ||
        !tableColumnWidthStore[instanceId].length
      ) {
        return;
      }

      try {
        const cachedState = JSON.parse(
          JSON.stringify(tableColumnWidthStore[instanceId]),
        );

        // 验证缓存状态的有效性
        const validatedState = validateColumnState(cachedState, columnDefs);
        let hasChanges = validatedState.length !== cachedState.length;

        // 找出新增的列（在columnDefs中存在但在缓存中不存在）
        const cachedFields = new Set(validatedState.map((col) => col.colId));
        const newColumns = columnDefs.filter(
          (col) => col.field && !cachedFields.has(col.field),
        );

        // 为新增列创建默认状态并按原始顺序插入
        let finalState = [...validatedState];

        if (newColumns.length > 0) {
          // 保持用户调整的列顺序，智能插入新增列
          finalState = [...validatedState];

          // 性能优化：使用提取的函数同步现有列的属性
          finalState.forEach((cachedCol: any) => {
            const matchingColDef = columnDefs.find(
              (col: any) => col.field === cachedCol.colId,
            );

            if (matchingColDef) {
              const columnHasChanges = syncColumnProperties(cachedCol, matchingColDef);
              if (columnHasChanges) {
                hasChanges = true;
              }
            }
          });

          // 为每个新增列找到合适的插入位置
          newColumns.forEach((newColDef: any) => {
            // 找到新增列在原始 columnDefs 中的位置
            const newColIndex = columnDefs.findIndex(
              (col) => col.field === newColDef.field,
            );

            if (newColIndex === -1) return;

            // 性能优化：使用工厂函数创建新列状态
            const newColState = createNewColumnState(newColDef);

            // 找到合适的插入位置
            let insertIndex = finalState.length; // 默认插入到最后

            // 查找前一个和后一个存在于缓存中的列
            let prevColInCache = -1;
            let nextColInCache = -1;

            // 向前搜索，找到最近的已存在于缓存中的列
            for (let i = newColIndex - 1; i >= 0; i--) {
              const colField = columnDefs[i]?.field;
              if (colField) {
                const cacheIndex = finalState.findIndex(
                  (cached) => cached.colId === colField,
                );
                if (cacheIndex !== -1) {
                  prevColInCache = cacheIndex;
                  break;
                }
              }
            }

            // 向后搜索，找到最近的已存在于缓存中的列
            for (let i = newColIndex + 1; i < columnDefs.length; i++) {
              const colField = columnDefs[i]?.field;
              if (colField) {
                const cacheIndex = finalState.findIndex(
                  (cached) => cached.colId === colField,
                );
                if (cacheIndex !== -1) {
                  nextColInCache = cacheIndex;
                  break;
                }
              }
            }

            // 根据前后列的位置确定插入位置
            if (prevColInCache !== -1 && nextColInCache !== -1) {
              // 如果前后都有列，插入到前一列的后面
              insertIndex = prevColInCache + 1;
            } else if (prevColInCache !== -1) {
              // 只有前面有列，插入到前一列的后面
              insertIndex = prevColInCache + 1;
            } else if (nextColInCache !== -1) {
              // 只有后面有列，插入到后一列的前面
              insertIndex = nextColInCache;
            }
            // 如果前后都没有列，使用默认值（最后）

            // 插入新列
            finalState.splice(insertIndex, 0, newColState);
            hasChanges = true;
          });
        } else {
          // 性能优化：没有新增列，只需要同步现有列的属性
          validatedState.forEach((cachedCol: any) => {
            const matchingColDef = columnDefs.find(
              (col: any) => col.field === cachedCol.colId,
            );

            if (matchingColDef) {
              const columnHasChanges = syncColumnProperties(cachedCol, matchingColDef);
              if (columnHasChanges) {
                hasChanges = true;
              }
            }
          });
          finalState = validatedState;
        }

        // 如果有变化，更新缓存
        if (hasChanges) {
          tableColumnWidthStore[instanceId] = finalState;
        }

        // 应用列状态
        if (finalState.length > 0) {
          api.applyColumnState({
            state: finalState,
            applyOrder: true,
          });
        }
      } catch (error) {
        console.error('Error applying column state:', error);
        // 发生错误时清除无效缓存
        tableColumnWidthStore[instanceId] = [];
      }
    },
    [instanceId, validateColumnState],
  );

  // 性能优化：改进列定义的依赖项，避免不必要的重新计算
  const columnDefs = useMemo(() => {
    console.log('columns执行次数');

    // 性能优化：检查列定义是否真正发生变化
    if (deepEqual(prevColumnsRef.current, columns) &&
        prevColumnDefsRef.current.length > 0) {
      return prevColumnDefsRef.current;
    }

    const result = toList([
      ...columns.map((item: any) => {
        return {
          // ...item,
          rowGroup: item?.rowGroup,
          wrapText: item?.wrapText,
          autoHeight: item?.autoHeight,
          suppressExcelExport: item?.suppressExcelExport,
          spanRows: item?.rowSpan,
          editable: item?.editable,
          cellEditor: item?.cellEditor,
          cellEditorParams: item?.cellEditorParams,
          refData: item?.refData,
          onCellValueChanged: item?.onCellValueChanged,
          enableRowGroup: item?.enableRowGroup,
          enableValue: item?.enableValue,
          headerName: item.title,
          headerTooltip: item?.title,
          field: item.dataIndex,
          width: item.width,
          flex: item?.flex,
          aggFunc: item.aggFunc,
          pinned: item?.pinned
            ? item?.pinned
            : item?.title === '操作'
            ? 'right'
            : undefined,
          ...(item?.title === '操作'
            ? { valueGetter: (params: any) => params.data }
            : {}),
          ...(item?.fieldFormat
            ? {
                tooltipValueGetter: (params: any) =>
                  item.fieldFormat(params?.data),
                valueFormatter: (params: any) => item.fieldFormat(params?.data),
                valueGetter: (params: any) => item.fieldFormat(params?.data),
                // keyCreator: (params: any) => item.fieldFormat(params?.data),
              }
            : { tooltipField: item.dataIndex }),
          ...(item?.render && {
            cellRenderer: (params: any) => {
              // console.log('params', params);
              if (params.node.rowPinned) {
                return undefined; // 返回undefined使用默认渲染方式
              }
              return item.render(params?.value, params?.data, params);
            },
          }),

          ...(item?.style && {
            cellStyle: (params: any) => item.style(params?.value, params?.data),
          }),
          ...(isApiSorter && snapSettings[instanceId]?.sortMode === 'api'
            ? { comparator: () => 0 }
            : {}),
          ...(item?.filters &&
            snapSettings[instanceId]?.filterMode === 'api' && {
              // headerComponent: ScreenModal,
              // headerComponentParams: {
              //   innerHeaderComponent: ScreenModal,
              //   innerHeaderComponentParams: {
              //     filters: item.filters,
              //     instanceId: instanceId,
              //   },
              // },
              filter: ScreenModal,
              filterParams: {
                filters: item.filters,
                instanceId: instanceId,
              },
            }),

          // filter: 'agTextColumnFilter',
          // filter: true,
          // filter: 'agMultiColumnFilter',
          // sortable: false,
          hide: item?.hide,
        };
      }),
    ]);

    // 性能优化：缓存计算结果
    prevColumnsRef.current = columns;
    prevColumnDefsRef.current = result;

    return result;
  }, [
    columns?.length,
    instanceId,
    isApiSorter,
    snapSettings[instanceId]?.sortMode,
    snapSettings[instanceId]?.filterMode,
    // 性能优化：添加更精确的依赖项，确保在相关设置变化时重新计算
  ]);

  // 当列定义变化时，重新应用列缓存状态
  useEffect(() => {
    if (RefTable.current && RefTable.current.api && columnDefs) {
      const api = RefTable.current.api;
      // 应用列缓存状态
      applyColumnStateToGrid(api, columnDefs);
    }
  }, [columnDefs]);

  const { data, loading, pagination, refresh }: any = usePagination(
    async (paginationParams) => {
      // 性能优化：修复数据源处理逻辑，避免无法访问的代码
      if (dataSource) {
        setRowDatas(dataSource);
        return false;
      }
      if (!request) return false;

      if (
        (snapSettings[instanceId]?.autoFetch === false ||
          isAutoFetch === false) &&
        isItTheFirstTime
      ) {
        setIsItTheFirstTime(false);
        return false;
      }

      //TODO: 分页缓存
      // if (isPageCacheEnabled || snapSettings[instanceId]?.cachePagination) {
      //   paramsStore[instanceId] = paginationParams;
      // }

      return request({
        condition: { ...condition },
        ...(paginationParams || {
          current: snapParams[instanceId]?.current || pagination.current,
          pageSize: snapParams[instanceId]?.pageSize || pagination.pageSize,
        }),
        // ...paginationParams,
        ...params,
      });
    },
    {
      debounceWait: 600,
      defaultCurrent: snapParams[instanceId]?.current || 1,
      defaultPageSize: snapParams[instanceId]?.pageSize || 500,
      refreshDeps: [params, condition],
      onSuccess: (res: any) => {
        if (res?.success) {
          const newData = res?.data || [];

          // 功能增强：数据验证和错误处理
          try {
            // 清除之前的错误
            setError(null);
            setDataValidationErrors([]);

            // 验证数据结构
            if (Array.isArray(newData)) {
              const validationErrors: any[] = [];

              // 检查每行数据的完整性
              newData.forEach((row, index) => {
                if (!row || typeof row !== 'object') {
                  validationErrors.push({
                    row: index,
                    message: `第 ${index + 1} 行数据格式无效`,
                    type: 'structure'
                  });
                }
              });

              if (validationErrors.length > 0) {
                setDataValidationErrors(validationErrors);
                console.warn('数据验证警告:', validationErrors);
              }
            }

            // 性能优化：只在数据真正发生变化时才更新状态
            if (!deepEqual(prevRowDataRef.current, newData)) {
              prevRowDataRef.current = newData;
              setRowDatas(newData);
            }
          } catch (validationError) {
            console.error('数据验证失败:', validationError);
            setError('数据验证失败，请检查数据格式');
          }
        } else {
          // 功能增强：处理 API 错误响应
          const errorMessage = res?.message || '数据加载失败';
          setError(errorMessage);
          console.error('API 响应错误:', res);
        }
      },
      onError: (err: any) => {
        // 功能增强：统一错误处理
        const errorMessage = err?.message || '网络请求失败';
        setError(errorMessage);
        console.error('请求失败:', err);
      },
      // defaultParams: [
      //   {
      //     current: 1,
      //     pageSize: 100,
      //   },
      // ],
    },
  );

  useImperativeHandle(
    ref,
    () => ({
      refresh,
      reload: refresh,
      resetScreenStore,
      tableInstance: RefTable,
      updateHeader: (colId: string, headerText: string) => {
        const gridApi = RefTable.current?.api;
        if (
          !headerText ||
          !gridApi ||
          typeof gridApi.getColumns !== 'function'
        ) {
          return;
        }

        const column = gridApi
          .getColumns()
          ?.find((c: any) => c.getColId() === colId);

        if (column) {
          const colDef = column.getColDef();
          if (colDef.headerName !== headerText) {
            colDef.headerName = headerText;
            gridApi.refreshHeader();
          }
        }
      },
    }),
    [refresh, RefTable],
  );

  const rowClassRules = useMemo(() => {
    return {
      [styles['cell-red']]: (params: any) => params?.data?.holdFlag,
    };
  }, []);

  const onSelectionChanged = useCallback((e: any) => {
    const selectedRows = e.api.getSelectedRows();
    if (rowSelection) {
      rowSelection(selectedRows);
      setSelectedData(selectedRows);
    }
  }, []);

  // 功能增强：智能导出功能，支持更多选项和格式
  const handleSmartExport = useCallback((params: any, format: 'excel' | 'csv') => {
    const columns = params?.api?.getColumns();
    const exportColumns = columns
      .filter((col: any) => !col.colDef.suppressExcelExport)
      .map((col: any) => col.colId);

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const fileName = `${instanceId}_export_${timestamp}.${format === 'excel' ? 'xlsx' : 'csv'}`;

    const exportParams = {
      columnKeys: exportColumns,
      fileName,
      // 功能增强：添加更多导出选项
      skipColumnHeaders: false,
      skipColumnGroupHeaders: false,
      skipPinnedTop: false,
      skipPinnedBottom: false,
      allColumns: false,
      onlySelected: false,
      suppressQuotes: false,
      columnSeparator: ',',
    };

    if (format === 'excel') {
      params.api.exportDataAsExcel({
        ...exportParams,
        // Excel 特有选项
        sheetName: `${instanceId}_数据表`,
        author: '明锐云数据表',
      });
    } else {
      params.api.exportDataAsCsv(exportParams);
    }
  }, [instanceId]);

  const getContextMenuItems: any = useCallback((params: any) => {
    const defaultItems = [
      'copy',
      'copyWithHeaders',
      // 'paste',
      'separator',
      // 'export',
      {
        name: '导出为 Excel',
        action: () => handleSmartExport(params, 'excel'),
        icon: '<i class="ag-icon ag-icon-excel"></i>',
      },
      {
        name: '导出为 CSV',
        action: () => handleSmartExport(params, 'csv'),
        icon: '<i class="ag-icon ag-icon-csv"></i>',
      },
      // 功能增强：添加仅导出选中行的选项
      {
        name: '导出选中行 (Excel)',
        action: () => {
          const selectedRows = params.api.getSelectedRows();
          if (selectedRows.length === 0) {
            alert('请先选择要导出的行');
            return;
          }
          const columns = params?.api?.getColumns();
          const exportColumns = columns
            .filter((col: any) => !col.colDef.suppressExcelExport)
            .map((col: any) => col.colId);

          const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
          params.api.exportDataAsExcel({
            columnKeys: exportColumns,
            fileName: `${instanceId}_selected_${timestamp}.xlsx`,
            onlySelected: true,
            sheetName: `${instanceId}_选中数据`,
          });
        },
        icon: '<i class="ag-icon ag-icon-excel"></i>',
      },
      // 'chartRange',
    ];
    const customItems = [
      // {
      //   name: '复制整列数据',
      //   action: () => {
      //     const columnField = params.column.getColId();
      //     const columnData: any[] = [];
      //     params.api.forEachNode((node: any) => {
      //       columnData.push(node.data[columnField]);
      //     });
      //     const columnText = columnData.join('\n');
      //     navigator.clipboard
      //       .writeText(columnText)
      //       .then(() => {
      //         console.log('整列数据已复制到剪贴板');
      //       })
      //       .catch((err) => {
      //         console.error('复制失败:', err);
      //       });
      //   },
      //   icon: '<i class="fa fa-copy"></i>',
      // },
      {
        name: '选中',
        action: () => {
          const cellRanges = params.api.getCellRanges();
          if (!cellRanges || cellRanges.length === 0) {
            alert('未选择任何范围！');
            return;
          }
          params.api.forEachNode((node: any) => {
            const rowIndex = node.rowIndex;
            const inRange = cellRanges.some((range: any) => {
              const startRow = range.startRow.rowIndex;
              const endRow = range.endRow.rowIndex;
              return rowIndex >= startRow && rowIndex <= endRow;
            });

            if (inRange) {
              node.setSelected(true);
            }
          });
        },
        icon: '<i class="ag-icon ag-icon-tick"/>',
      },
      {
        name: '取消选中范围',
        action: () => {
          const cellRanges = params.api.getCellRanges();
          if (!cellRanges || cellRanges.length === 0) {
            alert('未选择任何范围！');
            return;
          }
          params.api.forEachNode((node: any) => {
            const rowIndex = node.rowIndex;
            const inRange = cellRanges.some((range: any) => {
              const startRow = range.startRow.rowIndex;
              const endRow = range.endRow.rowIndex;
              return rowIndex >= startRow && rowIndex <= endRow;
            });

            if (inRange) {
              node.setSelected(false);
            }
          });
        },
        icon: '<i class="ag-icon ag-icon-cross"></i>',
      },
    ];
    return [...defaultItems, 'separator', ...customItems];
  }, []);

  const onSortChanged = useCallback((e: any) => {
    if (snapSettings[instanceId]?.sortMode === 'local' || !isApiSorter) return;
    const colId =
      e?.columns?.length === 1 ? e?.columns[0]?.colId : e?.columns[1]?.colId;
    const allColumns = e?.api
      ?.getColumnState()
      .filter((col: any) => col.sort)
      .map((col: any) => ({ colId: col.colId, sort: col.sort }));
    const { sort = 'normal' } = allColumns?.[0] || {};
    // 清空其他列的排序状态
    Object.keys(screenStore[instanceId] || {}).forEach((key) => {
      if (screenStore[instanceId][key].sort) {
        screenStore[instanceId][key].sort = '';
      }
    });

    const sortValueMap = {
      normal: '',
      asc: 1,
      desc: -1,
    };

    settingsStore[instanceId] = {
      ...settingsStore[instanceId],
      sortState: (sort as keyof typeof sortValueMap) ?? '',
    };

    screenStore[instanceId] = {
      ...screenStore[instanceId],
      [colId]: {
        ...screenStore?.[instanceId]?.[colId],
        sort: sortValueMap[sort as keyof typeof sortValueMap] ?? '',
      },
    };
  }, []);

  const defaultColDef: any = useMemo(() => {
    return {
      // filter: true,
      filter: 'agMultiColumnFilter',
      menuTabs: ['filterMenuTab', 'generalMenuTab', 'columnsMenuTab'],
      // floatingFilter: true,
    };
  }, []);

  // const statusBar = useMemo(() => {
  //   return {
  //     statusPanels: [
  //       { statusPanel: 'agTotalAndFilteredRowCountComponent',align: 'left' },// 总和过滤行计数组件
  //       { statusPanel: 'agTotalRowCountComponent' },// 总行计数组件
  //       { statusPanel: 'agFilteredRowCountComponent' },// 过滤行计数组件
  //       { statusPanel: 'agSelectedRowCountComponent' },// 选中行计数组件
  //       { statusPanel: 'agAggregationComponent' }// 聚合组件
  //       // {
  //       //   statusPanel: 'customFilteredRowCountComponent', // 自定义组件
  //       //   align: 'right',
  //       //   key: 'filteredCount',
  //       // },
  //     ],
  //   };
  // }, []);

  const rowHeight = useMemo(() => {
    return snapRowHeightStore[instanceId] || 40;
  }, [snapRowHeightStore[instanceId]]);

  const headerHeight = useMemo(() => {
    return snapRowHeightStore[instanceId] || 40;
  }, [snapRowHeightStore[instanceId]]);
  // console.log('重新渲染', instanceId);

  // 性能优化：更新列状态缓存的统一函数，添加错误处理和性能监控
  const updateColumnStateCache = useCallback(
    (api: any) => {
      try {
        const columnState = api
          .getColumnState()
          .filter(
            (col: any) =>
              col.colId !== 'ag-Grid-SelectionColumn' &&
              col.colId !== 'ag-Grid-AutoColumn',
          );

        // 性能优化：只在状态真正发生变化时才更新缓存
        const currentCache = tableColumnWidthStore[instanceId];
        if (!deepEqual(currentCache, columnState)) {
          tableColumnWidthStore[instanceId] = columnState;
        }
      } catch (error) {
        console.error('更新列状态缓存失败:', error);
        // 发生错误时不影响用户操作，只记录错误
      }
    },
    [instanceId],
  );

  // 性能优化：使用防抖优化列状态更新，避免频繁的缓存操作
  const { run: debouncedUpdateColumnStateCache } = useDebounceFn(
    updateColumnStateCache,
    { wait: 300 }
  );

  //性能优化：列宽变化 - 使用防抖优化
  const onColumnResized = useCallback(
    (e: any) => {
      if (e.finished && e.source === 'uiColumnResized') {
        debouncedUpdateColumnStateCache(e.api);
      }
    },
    [debouncedUpdateColumnStateCache],
  );

  //性能优化：列移动 - 使用防抖优化
  const onColumnMoved = useCallback(
    (e: any) => {
      if (
        e.finished &&
        (e.source === 'uiColumnMoved' || e.source === 'toolPanelUi')
      ) {
        debouncedUpdateColumnStateCache(e.api);
      }
    },
    [debouncedUpdateColumnStateCache],
  );

  //性能优化：列显示 - 使用防抖优化
  const onColumnVisible = useCallback(
    (e: any) => {
      if (e.type === 'columnVisible' && e.source === 'toolPanelUi') {
        debouncedUpdateColumnStateCache(e.api);
      }
    },
    [debouncedUpdateColumnStateCache],
  );

  const getRowId = useMemo(() => {
    return () => {
      return `row-${Math.random().toString(36).substring(2, 10)}`;
    };
  }, []);

  // 功能增强：添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + R: 刷新数据
      if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        refresh();
        return;
      }

      // Ctrl/Cmd + E: 导出 Excel
      if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
        event.preventDefault();
        if (RefTable.current?.api) {
          handleSmartExport({ api: RefTable.current.api }, 'excel');
        }
        return;
      }

      // Ctrl/Cmd + A: 全选
      if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
        event.preventDefault();
        if (RefTable.current?.api) {
          RefTable.current.api.selectAll();
        }
        return;
      }

      // Escape: 取消选择
      if (event.key === 'Escape') {
        if (RefTable.current?.api) {
          RefTable.current.api.deselectAll();
        }
        return;
      }
    };

    // 只在组件聚焦时监听键盘事件
    const tableElement = RefTable.current?.eGridDiv;
    if (tableElement) {
      tableElement.addEventListener('keydown', handleKeyDown);
      return () => {
        tableElement.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [refresh, handleSmartExport]);

  const onGridReady = useCallback(
    (params: any) => {
      const api = params.api;
      // 网格初始化完成后应用列缓存状态
      if (columnDefs) {
        applyColumnStateToGrid(api, columnDefs);
      }
    },
    [applyColumnStateToGrid, columnDefs],
  );

  // 检查是否是总计行或页脚行，禁止选择
  const isRowSelectable = useCallback((params: any) => {
    // console.log('params', params);
    return !params?.node?.footer && !params?.node?.totalRow;
  }, []);

  const rowSelections: any = useMemo(() => {
    if (!rowSelection) return false;
    return {
      mode: rowSelectionMode,
      enableClickSelection: 'enableDeselection',
      checkboxes: true,
      isRowSelectable: isRowSelectable,
    };
  }, []);

  // 功能增强：扩展聚合函数，添加更多统计功能
  const aggFuncs = useMemo(() => {
    return {
      sum: (params: any) => {
        const { values } = params;
        const result = values
          .filter(
            (val: any) =>
              val !== null && val !== undefined && !Number.isNaN(Number(val)),
          )
          .reduce((acc: number, curr: any) => acc + Number(curr), 0);

        return Number(result).toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        });
      },
      // 功能增强：添加平均值计算
      avg: (params: any) => {
        const { values } = params;
        const numericValues = values.filter(
          (val: any) =>
            val !== null && val !== undefined && !Number.isNaN(Number(val)),
        );
        if (numericValues.length === 0) return '0';

        const sum = numericValues.reduce((acc: number, curr: any) => acc + Number(curr), 0);
        const avg = sum / numericValues.length;

        return Number(avg).toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        });
      },
      // 功能增强：添加最大值计算
      max: (params: any) => {
        const { values } = params;
        const numericValues = values.filter(
          (val: any) =>
            val !== null && val !== undefined && !Number.isNaN(Number(val)),
        );
        if (numericValues.length === 0) return '0';

        const max = Math.max(...numericValues.map(Number));
        return Number(max).toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        });
      },
      // 功能增强：添加最小值计算
      min: (params: any) => {
        const { values } = params;
        const numericValues = values.filter(
          (val: any) =>
            val !== null && val !== undefined && !Number.isNaN(Number(val)),
        );
        if (numericValues.length === 0) return '0';

        const min = Math.min(...numericValues.map(Number));
        return Number(min).toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        });
      },
      // 功能增强：添加计数功能
      count: (params: any) => {
        const { values } = params;
        return values.filter((val: any) => val !== null && val !== undefined).length;
      },
    };
  }, []);

  return (
    <div
      style={{ width: '100%', height: '100%' }}
      className={styles['wrap-table-super-table-max']}
    >
      <div ref={tableSearchRef}>
        {Object?.keys(tableFilter)?.length > 0 && (
          <SearchFilters
            tableFilter={tableFilter}
            callBack={(e: any) => {
              let newE = e;
              if (Object?.keys(e)?.length === 0) {
                newE = tableFilter;
              }
              setCondition({ ...newE });
            }}
            autoQueryEnabled={
              autoQueryEnabled
                ? autoQueryEnabled
                : snapSettings[instanceId]?.manualSearch
            }
          />
        )}
      </div>
      <Loading loading={loading} text="加载中">
        {/* 功能增强：错误信息显示 */}
        {error && (
          <Alert
            message="数据加载错误"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
            style={{ marginBottom: 16 }}
          />
        )}
        {/* 功能增强：数据验证警告显示 */}
        {dataValidationErrors.length > 0 && (
          <Alert
            message={`发现 ${dataValidationErrors.length} 个数据验证问题`}
            description="部分数据可能存在格式问题，请检查数据完整性"
            type="warning"
            showIcon
            closable
            onClose={() => setDataValidationErrors([])}
            style={{ marginBottom: 16 }}
          />
        )}
        <ProCard ghost={ghost}>
          <div className="flex justify-between mb-10px h-26px">
            <ConfigProvider componentSize="small">
              {toolBarRender ? toolBarRender() : <Space> </Space>}
            </ConfigProvider>

            <Space className="mr-6px" size={10}>
              {toolBarRenderRight ? toolBarRenderRight() : <Space> </Space>}
              {!isHideToolBar && (
                <>
                  {/* <RoamingGuidance
                      refs={RefTable}
                      refs2={refreshRef}
                      refs3={settingRef}
                    /> */}
                  {/* <FilterModal columns={columns} /> */}
                  <RowHeightSetting instanceId={instanceId} />
                  <div>
                    <Tooltip
                      title="刷新,此刷新只更新数据不会刷新整个页面"
                      placement="bottom"
                    >
                      <ReloadOutlined
                        className="hover:text-#1677ff"
                        style={{ fontSize: 16, fontWeight: 500 }}
                        spin={loading}
                        onClick={refresh}
                      />
                    </Tooltip>
                  </div>
                  <div>
                    <SetUpModal
                      instanceId={instanceId}
                      isPageCacheEnabled={isPageCacheEnabled}
                      columns={columns}
                      isApiSorter={isApiSorter}
                    />
                  </div>
                  <SaveView instanceId={instanceId} />
                </>
              )}
            </Space>
          </div>
          <div
            style={{
              transition: 'height 0.3s ease',
              height: height || tableHeight,
            }}
          >
            <AgGridReact
              ref={RefTable} // 表格实例
              theme={myTheme} // 主题
              columnDefs={columnDefs} // 列配置
              rowData={dataSource || rowDatas} // 数据源
              localeText={AG_GRID_LOCALE_CN} // 语言
              cellSelection={enableCellSpan ? false : true} // 单元格选择
              rowSelection={rowSelections}
              suppressMenuHide={false}
              columnMenu="legacy"
              enableCellSpan={enableCellSpan}
              selectionColumnDef={{
                pinned: 'left',
                suppressHeaderMenuButton: true,
                suppressHeaderContextMenu: true,
              }}
              // rowSelection="multiple"
              onGridReady={onGridReady} // 表格初始化
              animateRows={true} // 动画
              rowClassRules={rowClassRules} // 行样式
              rowNumbers={true} // 行号
              rowHeight={rowHeight}
              headerHeight={headerHeight}
              // 性能优化：启用虚拟化和缓冲区优化，提升大数据集渲染性能
              rowBuffer={20} // 缓冲区行数，提升滚动性能
              suppressRowVirtualisation={false} // 启用行虚拟化
              suppressColumnVirtualisation={false} // 启用列虚拟化
              onSelectionChanged={onSelectionChanged}
              onSortChanged={onSortChanged}
              // debug
              getContextMenuItems={getContextMenuItems}
              suppressMultiSort={true} // 禁用多列排序
              defaultExcelExportParams={{
                sheetName: '明锐云数据表',
              }}
              suppressRowHoverHighlight={true}
              // loading={loading}
              onColumnResized={onColumnResized}
              onColumnMoved={onColumnMoved}
              onColumnVisible={onColumnVisible}
              getRowId={getRowIdFn || getRowId}
              // onCellValueChanged={(e) => {
              //   console.log('e', e);
              // }}
              // groupSelectsChildren={true}
              // sideBar={true}
              // statusBar={statusBar}
              sideBar={{
                // toolPanels: [
                //   // {
                //   //   id: 'columns',
                //   //   labelDefault: 'Columns',
                //   //   labelKey: 'columns',
                //   //   iconKey: 'columns',
                //   //   toolPanel: 'agColumnsToolPanel', // 默认列工具面板
                //   // },
                //   // {
                //   //   id: 'filters',
                //   //   labelDefault: 'Filters',
                //   //   labelKey: 'filters',
                //   //   iconKey: 'filter',
                //   //   toolPanel: 'agFiltersToolPanel', // 默认过滤工具面板
                //   // },
                // ],
                // defaultToolPanel: 'collapsed', // 设置侧边栏默认收起
                toolPanels: ['columns', 'filters'],
              }}
              // groupAllowUnbalanced={true} // 允许不平衡分组
              // autoGroupColumnDef={{
              //   headerName: '分组信息', // 自定义分组列的标题
              //   cellRendererParams: {
              //     innerRenderer: (params: any) => {
              //       console.log('分组', params);
              //       return params.value !== null && params.value !== undefined
              //         ? params.value
              //         : '';
              //     },
              //   },
              // }}
              defaultColDef={defaultColDef}
              // enableAdvancedFilter={true} // 启用高级过滤 互斥
              // enableCharts={true}
              //-- --
              grandTotalRow="pinnedBottom"
              enableRowPinning={enableRowPinning}
              // isRowSelectable={isRowSelectable} //弃用
              aggFuncs={aggFuncs}
              groupDefaultExpanded={groupDefaultExpanded}
            />
          </div>
          {!isHidePagination && (
            <div className="flex justify-between mt-5px border-1 border-solid rd-3px border-#E1E4E8 p-8px bg-#fbfbfd hover:bg-#F3F8FF select-none">
              <div className="flex-grow overflow-hidden mr-4">
                <TotalNumberOfPages
                  rowSelection={rowSelection}
                  selectedData={selectedData}
                  TotalConfiguration={TotalConfiguration}
                  dataSource={dataSource || data?.data || []}
                />
              </div>
              <div className="flex-shrink-0">
                <Pagination
                  current={dataSource?.length ? 1 : pagination.current}
                  pageSize={dataSource?.length ? 10000 : pagination.pageSize}
                  total={dataSource?.length || data?.total}
                  onChange={pagination.onChange}
                  showSizeChanger
                  size="small"
                  pageSizeOptions={[100, 500, 1000, 2000, 3000, 5000, 10000]}
                  showTotal={(total) => `共 ${total} 条`}
                />
              </div>
            </div>
          )}
        </ProCard>
      </Loading>
    </div>
  );
});

export default React.memo(SuperTableMax);
