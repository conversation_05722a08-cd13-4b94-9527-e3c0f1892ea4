import React, { useEffect, useMemo, useState } from 'react';
import { DesktopOutlined } from '@ant-design/icons';
import {
  Button,
  Dropdown,
  theme,
  Tooltip,
  Modal,
  Input,
  message,
  Checkbox,
} from 'antd';
import { useRequest } from 'ahooks';
import {
  deleteViewAPI,
  getCreateAPI,
  getListAPI,
  setDefaultViewAPI,  
} from '@/services/view';
import Loading from '../Loding';
import { tableColumnWidthStore } from '../../store';

interface SaveViewProps {
  instanceId: string;
}

const SaveView = ({ instanceId }: SaveViewProps) => {
  const { token } = theme.useToken();
  const contentStyle = useMemo(
    () => ({
      backgroundColor: token.colorBgElevated,
      borderRadius: token.borderRadiusLG,
      boxShadow: token.boxShadowSecondary,
    }),
    [token],
  );

  const [isOpen, setIsOpen] = useState(false);
  const [viewList, setViewList] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewName, setViewName] = useState('');
  const [isDefault, setIsDefault] = useState(true);

  /* 获取视图列表 */
  const { runAsync: getViewList, loading: getViewListLoading } = useRequest(
    getListAPI,
    {
      manual: true,
    },
  );

  /* 创建视图 */
  const { runAsync: createView } = useRequest(getCreateAPI, {
    manual: true,
  });

  /* 删除视图 */
  const { runAsync: deleteView } = useRequest(deleteViewAPI, {
    manual: true,
  });

  /* 设置默认视图 */
  const { runAsync: setDefaultView } = useRequest(setDefaultViewAPI, {
    manual: true,
  });
  useEffect(() => {
    if (isOpen) {
      getViewList({ key: instanceId }).then((res) => {
        if (res.status) {
          setViewList(res.data?.list || []);
        }
      });
    }
  }, [isOpen]);

  const handleCreateView = async () => {
    if (!viewName.trim()) {
      message.error('请输入视图名称');
      return;
    }

    createView({
      name: viewName,
      key: instanceId,
      columns: tableColumnWidthStore[instanceId] || [],
      condition: {},
      isDef: isDefault,
    }).then((res) => {
      if (res.status) {
        message.success('创建成功');
        setIsModalOpen(false);
        setViewName('');
        setIsDefault(false);
        // 刷新视图列表
        getViewList({ key: instanceId }).then((res) => {
          if (res.status) {
            setViewList(res.data?.list || []);
          }
        });
      }
    });
  };

  /* 删除视图确认 */
  const handleDeleteView = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该视图吗？',
      onOk: () => {
        deleteView({ id, key: instanceId }).then((res) => {
          if (res.status) {
            message.success('删除成功');
            getViewList({ key: instanceId }).then((res) => {
              if (res.status) {
                setViewList(res.data?.list || []);
              }
            });
          }
        });
      },
    });
  };

  /* 设置默认视图确认 */
  const handleSetDefault = (id: string) => {
    Modal.confirm({
      title: '设置默认视图',
      content: '确定要将该视图设为默认吗？',
      onOk: () => {
        setDefaultView({ id, key: instanceId }).then((res) => {
          if (res.status) {
            message.success('设置成功');
            getViewList({ key: instanceId }).then((res) => {
              if (res.status) {
                tableColumnWidthStore[instanceId] = res.data?.list[0]?.columns || [];
                setViewList(res.data?.list || []);
                window.location.reload();
              }
            });
          }
        });
      },
    });
  };

  return (
    <>
      <Dropdown
        dropdownRender={() => {
          return (
            <div style={contentStyle} className="min-w-280px min-h-320px p-4 transition-all">
              <Loading loading={getViewListLoading}>
                <Button
                  type="primary"
                  onClick={() => setIsModalOpen(true)}
                  className="mb-4 w-full bg-blue-500 hover:bg-blue-600 transition-colors"
                >
                  + 新增视图
                </Button>
                <div className="flex flex-col">
                  <div className="space-y-2">
                    {viewList.length === 0 ? (
                      <div className="text-center text-gray-400 py-8">
                        暂无数据
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {viewList.map((item: any) => (
                          <div
                            key={item.id}
                            className="group flex items-center justify-between p-3 hover:bg-gray-50 rounded-md transition-colors duration-200"
                          >
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{item.name}</span>
                              {!!item.isDef && (
                                <span className="text-blue-500 text-xs px-2 py-0.5 bg-blue-50 rounded-full">
                                  默认
                                </span>
                              )}
                            </div>
                            <div className="hidden group-hover:flex items-center space-x-3">
                              {!item.isDef ? (
                                <div
                                  className="text-gray-500 hover:text-blue-500 text-sm transition-colors cursor-pointer select-none"
                                  onClick={() => handleSetDefault(item.id)}
                                >
                                  设为默认
                                </div>
                              ) : (
                                <div
                                  className="text-gray-500 hover:text-blue-500 text-sm transition-colors cursor-pointer select-none"
                                  onClick={() => handleSetDefault(item.id)}
                                >
                                  恢复
                                </div>
                              )}
                              <div
                                className="text-gray-500 hover:text-red-500 text-sm transition-colors cursor-pointer select-none"
                                onClick={() => handleDeleteView(item.id)}
                              >
                                删除
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </Loading>
            </div>
          );
        }}
        trigger={['click']}
        arrow
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <Tooltip title="视图" placement="top">
          <DesktopOutlined
            onClick={(e) => e.preventDefault()}
            className="text-gray-600 hover:text-blue-500 transition-colors duration-300 cursor-pointer text-lg"
          />
        </Tooltip>
      </Dropdown>

      <Modal
        title="新增视图"
        open={isModalOpen}
        onOk={handleCreateView}
        onCancel={() => {
          setIsModalOpen(false);
          setViewName('');
          setIsDefault(false);
        }}
      >
        <div className="space-y-4">
          <Input
            placeholder="请输入视图名称"
            value={viewName}
            onChange={(e) => setViewName(e.target.value)}
          />
          <Checkbox
            checked={isDefault}
            onChange={(e) => setIsDefault(e.target.checked)}
          >
            设置为默认视图
          </Checkbox>
        </div>
      </Modal>
    </>
  );
};
export default SaveView;
