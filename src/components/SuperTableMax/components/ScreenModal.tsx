import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Divider,
  Input,
  Spin,
  theme,
  DatePicker,
  ConfigProvider,
} from 'antd';
import { useRequest } from 'ahooks';
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import {
  screenStore,
  screenValue,
} from '@/components/SuperTables/store/screenStore';
import { useGridFilter } from 'ag-grid-react';
const { TextArea } = Input;
const { RangePicker } = DatePicker;

// interface FilterOption {
//   value: string;
//   text: string;
// }

// interface Filters {
//   options: FilterOption[];
//   key: string;
//   type?: string;
//   fieldNames?: {
//     label: string | string[];
//     value: string;
//   };
//   api?: any;
//   params?: any;
// }

const rangePresets: any = [
  {
    label: '最近7天',
    value: [dayjs().add(-7, 'd'), dayjs()],
  },
  {
    label: '最近14天',
    value: [dayjs().add(-14, 'd'), dayjs()],
  },
  {
    label: '最近15天',
    value: [dayjs().add(-15, 'd'), dayjs()],
  },
  {
    label: '最近28天',
    value: [dayjs().add(-28, 'd'), dayjs()],
  },
  {
    label: '最近30天',
    value: [dayjs().add(-30, 'd'), dayjs()],
  },
  {
    label: '最近31天',
    value: [dayjs().add(-31, 'd'), dayjs()],
  },
  {
    label: '最近60天',
    value: [dayjs().add(-60, 'd'), dayjs()],
  },
  {
    label: '最近90天',
    value: [dayjs().add(-90, 'd'), dayjs()],
  },
];

const ScreenModal = (dataProps: any) => {
  const { token } = theme.useToken();
  const [searchValue, setSearchValue] = useState('');
  const [textValue, setTextValue] = useState('');
  const [filterValue, setFilterValue] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<any>(null);

  const doesFilterPass = useCallback(
    ({ }: any) => {
      // console.log('node',node);
      return true
    },
    [dataProps?.model],
  );

  const afterGuiAttached = useCallback((params?: any) => {
    if (!params || !params.suppressFocus) {
      const inputElement = document.querySelector('.ant-input');
      if (inputElement) {
        (inputElement as HTMLElement).focus();
      }
    }
  }, []);

  // register filter callbacks with the grid
  useGridFilter({ doesFilterPass, afterGuiAttached });

  const contentStyle = useMemo(
    () => ({
      backgroundColor: token.colorBgElevated,
      borderRadius: token.borderRadiusLG,
      boxShadow: token.boxShadowSecondary,
    }),
    [token],
  );

  useEffect(() => {
    if (dataProps.filters?.key) {
      const savedValue =
        screenStore[dataProps.instanceId]?.[dataProps.filters.key];

      if (savedValue) {
        const actualValue = savedValue.value || savedValue;

        if (dataProps.filters.api) {
          setFilterValue(
            typeof actualValue === 'string' ? actualValue.split(',') : [],
          );
        }
        if (dataProps.filters.options?.length) {
          const filterValue =
            typeof actualValue === 'string'
              ? actualValue.split(',').map((item: any) => {
                  return isNaN(item) ? item : Number(item);
                })
              : [];
          setFilterValue(filterValue);
        } else {
          setTextValue(typeof actualValue === 'string' ? actualValue : '');
        }
      }
    }
  }, [dataProps.filters]);

  const handleFiltersChange = useCallback((checkedValues: string[]) => {
    setFilterValue(checkedValues);
  }, []);

  const handleReset = useCallback(() => {
    if (dataProps.filters?.key) {
      screenStore[dataProps.instanceId] = {
        ...screenStore[dataProps.instanceId],
        [dataProps.filters.key]: '',
      };
      if (dataProps?.filters?.api) {
        screenValue[dataProps.instanceId] = {
          ...screenValue[dataProps.instanceId],
          [dataProps.filters.key]: '',
        };
      }
      setSearchValue('');
      setTextValue('');
      setFilterValue([]);
      setDateRange(null);
      dataProps.onModelChange(null);
    }
  }, [dataProps.filters, dataProps.instanceId]);

  const handleConfirm = useCallback(() => {
    if (dataProps.filters?.key) {
      const newValue =
        dataProps.filters.options?.length || dataProps.filters.api
          ? filterValue.join(',')
          : textValue;
      screenStore[dataProps.instanceId] = {
        ...screenStore[dataProps.instanceId],
        [dataProps.filters.key]: {
          value: newValue,
        },
      };
      if (dataProps?.filters?.api) {
        screenValue[dataProps.instanceId] = {
          ...screenValue[dataProps.instanceId],
          [dataProps.filters.key]: {
            value: searchValue,
          },
        };
      }
      if (dataProps.filters.type === 'time' && dateRange) {
        screenStore[dataProps.instanceId] = {
          ...screenStore[dataProps.instanceId],
          [dataProps.filters.key]: {
            value: dateRange
              .map((date: any) => date.format('YYYY-MM-DD'))
              .join(','),
          },
        };
      }
      // 通知 ag-grid 筛选已更改
      if (dataProps.api && typeof dataProps.api.onFilterChanged === 'function') {
        dataProps.onModelChange('key');
      }
    }
  }, [
    dataProps.filters,
    dataProps.instanceId,
    filterValue,
    textValue,
    searchValue,
    dateRange,
  ]);

  const fuzzyMatch = (text: string, search: string): boolean => {
    let searchIndex = 0;
    for (let i = 0; i < text.length; i++) {
      if (searchIndex >= search.length) return true;
      if (text[i] === search[searchIndex]) {
        searchIndex++;
      }
    }
    return searchIndex === search.length;
  };

  const getNestedValue = useCallback((obj: any, path: string | string[]) => {
    if (typeof path === 'string') return obj[path];
    return path.reduce(
      (current, key) => (current ? current[key] : undefined),
      obj,
    );
  }, []);

  const filteredOptions = useMemo(() => {
    if (!dataProps.filters?.options) return [];
    return dataProps.filters?.options.filter((option: any) =>
      fuzzyMatch(option.text.toLowerCase(), searchValue.toLowerCase()),
    );
  }, [searchValue, dataProps.filters?.options]);

  const {
    data: apiData,
    loading: apiLoading,
    runAsync: apiRun,
  }: any = useRequest(dataProps?.filters?.api, {
    debounceWait: 500,
    manual: true,
  });

  const apiOption = useMemo(() => {
    if (apiData?.status) {
      return (
        apiData.data?.list?.map((item: any) => ({
          text: getNestedValue(
            item,
            dataProps?.filters?.fieldNames?.label || 'name',
          ),
          value: getNestedValue(
            item,
            dataProps?.filters?.fieldNames?.value || 'id',
          ),
        })) || []
      );
    }
    return [];
  }, [apiData, dataProps?.filters?.fieldNames, getNestedValue]);

  useEffect(() => {
    if (dataProps?.filters?.api && apiOption.length === 0) {
      const value = screenValue[dataProps.instanceId]?.[dataProps.filters.key];
      if (dataProps?.filters?.params) {
        setSearchValue(value);
        apiRun({
          ...dataProps?.filters?.params,
          keyword: value,
          start: 0,
          len: 20,
        });
      } else {
        setSearchValue(value?.value);
        apiRun({ keyword: value?.value, start: 0, len: 20 });
      }
    }
  }, []);

  const dropdownContent = (
    <div style={contentStyle}>
      {dataProps?.filters?.api ? (
        <Spin spinning={apiLoading}>
          <div className="p-2 ">
            <Input
              placeholder="请输入筛选条件"
              prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
              onChange={(e) => {
                setSearchValue(e.target.value);
                if (dataProps?.filters?.params) {
                  apiRun({
                    ...dataProps?.filters?.params,
                    keyword: e.target.value,
                    start: 0,
                    len: 20,
                  });
                } else {
                  apiRun({ keyword: e.target.value, start: 0, len: 20 });
                }
              }}
              value={searchValue}
            />
          </div>
          <Divider style={{ margin: 0 }} />
          <div className="p-2 max-w-md overflow-y-auto max-h-300px">
            <Checkbox.Group
              style={{ width: '100%' }}
              onChange={handleFiltersChange}
              value={filterValue}
            >
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {apiOption.map((item: any) => (
                  <Checkbox
                    key={item.value}
                    value={item.value}
                    style={{ width: 'calc(50% - 8px)' }}
                  >
                    <div
                      style={{
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                        width: '100%',
                      }}
                    >
                      {item.text}
                    </div>
                  </Checkbox>
                ))}
              </div>
            </Checkbox.Group>
          </div>
        </Spin>
      ) : (
        <>
          <div className="p-2 ">
            {dataProps?.filters && dataProps?.filters?.options?.length > 0 ? (
              <Input
                placeholder="请输入筛选条件"
                prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                onChange={(e) => setSearchValue(e.target.value)}
                value={searchValue}
              />
            ) : dataProps.filters?.type === 'time' ? (
              <ConfigProvider locale={locale}>
                <RangePicker
                  style={{ width: '100%' }}
                  onChange={(dates) =>
                    setDateRange(dates as [moment.Moment, moment.Moment])
                  }
                  value={dateRange}
                  placeholder={['开始时间', '结束时间']}
                  presets={rangePresets}
                />
              </ConfigProvider>
            ) : (
              <TextArea
                value={textValue}
                onChange={(e) => setTextValue(e.target.value)}
                placeholder="请输入查询条件"
                // autoSize={{
                //   minRows: 5,
                //   maxRows: 8,
                // }}
              />
            )}
          </div>
          {dataProps?.filters && dataProps?.filters?.options?.length > 0 && (
            <>
              <Divider style={{ margin: 0 }} />
              <div className="p-2 max-w-md overflow-y-auto max-h-300px">
                <Checkbox.Group
                  style={{ width: '100%' }}
                  onChange={handleFiltersChange}
                  value={filterValue}
                >
                  <div
                    style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}
                  >
                    {filteredOptions.map((item: any) => (
                      <Checkbox
                        key={item.value}
                        value={item.value}
                        style={{ width: 'calc(50% - 8px)' }}
                      >
                        <div
                          style={{
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            textOverflow: 'ellipsis',
                            width: '100%',
                          }}
                        >
                          {item.text}
                        </div>
                      </Checkbox>
                    ))}
                  </div>
                </Checkbox.Group>
              </div>
            </>
          )}
        </>
      )}
      <Divider style={{ margin: 0 }} />
      <div className="flex justify-between p-2 ">
        <div className="flex gap-2">
          <Button
            type="link"
            size="small"
            onClick={handleReset}
            disabled={!textValue && filterValue.length === 0 && !dateRange}
          >
            重置
          </Button>
          {((dataProps?.filters?.options &&
            dataProps.filters.options.length > 0) ||
            dataProps?.filters?.api) && (
            <Button
              type="link"
              size="small"
              onClick={() => {
                const options = dataProps?.filters?.api
                  ? apiOption
                  : filteredOptions;
                const newValues =
                  filterValue.length === options.length
                    ? []
                    : options.map((item: any) => item.value);
                setFilterValue(newValues);
              }}
            >
              {filterValue.length ===
              (dataProps?.filters?.api ? apiOption : filteredOptions).length
                ? '取消全选'
                : '全选'}
            </Button>
          )}
        </div>
        <Button
          type="primary"
          size="small"
          onClick={handleConfirm}
          // disabled={!textValue && filterValue.length === 0}
        >
          确定
        </Button>
      </div>
    </div>
  );

  // 直接返回筛选面板内容，不再使用Dropdown包装
  return dropdownContent;
};

export default React.memo(ScreenModal);
