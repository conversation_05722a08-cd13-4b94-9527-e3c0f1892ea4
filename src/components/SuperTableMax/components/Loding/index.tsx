import { FC, ReactNode } from 'react'
import Loding from './icon'

interface LoadingProps {
  loading?: boolean
  children?: ReactNode
  text?: string
}

const Loading: FC<LoadingProps> = ({ 
  loading = false, 
  children, 
  text = '加载中' 
}) => {
  return (
    <div className="relative">
      {loading && (
        <div className="absolute inset-0 bg-white/80 z-10">
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center">
            {/* <div 
              style={{
                width: '32px',
                height: '32px',
                border: '4px solid #e5e7eb',
                borderTopColor: '#3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}
            />
            <style>
              {`
                @keyframes spin {
                  from {
                    transform: rotate(0deg);
                  }
                  to {
                    transform: rotate(360deg);
                  }
                }
              `}
            </style> */}
            <Loding />
            {text && (
              <span 
                className="mt-20px text-#1677ff text-sm flex items-center justify-center gap-2"
                style={{
                  animation: 'sailing 2s linear infinite'
                }}
              >
                {text}
                <style>
                  {`
                    @keyframes sailing {
                      0% {
                        opacity: 0;
                        transform: translateX(-20px);
                      }
                      20% {
                        opacity: 1;
                        transform: translateX(0);
                      }
                      80% {
                        opacity: 1;
                        transform: translateX(0);
                      }
                      100% {
                        opacity: 0;
                        transform: translateX(20px);
                      }
                    }
                  `}
                </style>
              </span>
            )}
          </div>
        </div>
      )}
      <div className={loading ? 'pointer-events-none select-none' : ''}>
        {children}
      </div>
    </div>
  )
}

export default Loading