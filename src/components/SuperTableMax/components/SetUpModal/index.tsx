import React, { useEffect, useState } from 'react';
import { Modal, Tooltip, Checkbox, message, Alert, Radio, Space } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

import { useSnapshot } from 'umi';
import { settingsStore } from '@/components/SuperTables/store/screenStore';
interface Props {
  btnText?: string;
  btnType?: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  instanceId: string;
  isPageCacheEnabled?: boolean;
  columns: any;
  onChange?: (newColumns: any) => void;
  isApiSorter?: boolean;
}

const SetUpModal = ({
  instanceId,
  isPageCacheEnabled,
  isApiSorter,
  columns,
}: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const snapSettings = useSnapshot(settingsStore);

  const hasFilters = columns.some((item: any) => item.filters)


  const [settings, setSettings] = useState(
    snapSettings[instanceId] || {
      cachePagination: isPageCacheEnabled || false,
      cacheSort: false,
      autoFetch: true,
      cacheColumnWidth: true,
      manualSearch: false,
      sortMode: isApiSorter ? 'api' : 'local', 
      filterMode: hasFilters ? 'api' : 'local', 
    },
  );

  useEffect(() => {
    if (instanceId) {
      if (!settingsStore[instanceId]) {
        settingsStore[instanceId] = {
          cachePagination: isPageCacheEnabled || false,
          cacheSort: false,
          autoFetch: true,
          cacheColumnWidth: true,
          manualSearch: false,
          sortMode: isApiSorter ? 'api' : 'local', 
          filterMode: hasFilters ? 'api' : 'local', 
        };
      }
    }
  }, [instanceId]);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    messageApi.success('设置保存成功');
    settingsStore[instanceId] = settings;
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleSettingChange = (key: string) => (e: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [key]: e.target.checked,
    }));
  };

  const handleModeChange = (key: string) => (e: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [key]: e.target.value,
    }));
  };

  return (
    <>
      {contextHolder}
      <Tooltip title="设置" placement="bottom">
        <SettingOutlined
          onClick={showModal}
          className=" hover:text-blue-500 transition-colors duration-300 cursor-pointer"
          style={{ fontSize: 16 }}
        />
      </Tooltip>
      <Modal
        // title={<div className="text-lg font-medium pb-2 border-b">设置</div>}
        title="高级设置"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="保存设置"
        width={900}
        destroyOnClose={true}
      >
        <div className="">
          <div className="flex items-center gap-4 mb-4 px-4">
            <Tooltip title="启用后，表格将在初始化时自动请求数据，关闭则第一次不请求数据搜索条件改变后触发搜索">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.autoFetch}
                  onChange={handleSettingChange('autoFetch')}
                  className="mr-2"
                  // disabled={true}
                >
                  <span className="text-gray-700">默认请求数据</span>
                </Checkbox>
              </div>
            </Tooltip>
            <Tooltip title="启用后，需要手动点击搜索按钮来触发搜索">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.manualSearch}
                  onChange={handleSettingChange('manualSearch')}
                  className="mr-2"
                  // disabled={true}
                >
                  <span className="text-gray-700">是否手动搜索</span>
                </Checkbox>
              </div>
            </Tooltip>
            {/* <Tooltip title="启用后，表格的分页状态会保持上次的分页设置">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.cachePagination}
                  onChange={handleSettingChange('cachePagination')}
                  className="mr-2"
                >
                  <span className="text-gray-700">缓存分页</span>
                </Checkbox>
              </div>
            </Tooltip> */}
          </div>
          <div className="h-[1px] bg-gray-200 my-4 pl-4"></div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-lg mb-4">数据处理模式</h3>

            <div className="mb-5">
              <div className="font-medium mb-2">表头排序：</div>
              <Radio.Group
                value={settings.sortMode}
                onChange={handleModeChange('sortMode')}
                className="ml-4"
              >
                <Space direction="vertical">
                  <Radio value="local">
                    <span className="font-medium">本地排序</span>
                    <span className="text-gray-500 ml-2">
                      - 响应更快，仅支持当前页数据
                    </span>
                  </Radio>
                  <Radio value="api">
                    <span className="font-medium">接口排序</span>
                    <span className="text-gray-500 ml-2">
                      - 支持所有数据，需等待接口响应
                    </span>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>

            <div className="mb-3">
              <div className="font-medium mb-2">表头筛选：</div>
              <Radio.Group
                value={settings.filterMode}
                onChange={handleModeChange('filterMode')}
                className="ml-4"
              >
                <Space direction="vertical">
                  <Radio value="local">
                    <span className="font-medium">本地筛选</span>
                    <span className="text-gray-500 ml-2">
                      - 响应更快，仅支持当前页数据
                    </span>
                  </Radio>
                  <Radio value="api">
                    <span className="font-medium">接口筛选</span>
                    <span className="text-gray-500 ml-2">
                      - 支持所有数据，需等待接口响应
                    </span>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>

            <Alert
              message="注意"
              description="选择本地模式处理速度更快，但只能处理当前页数据（可自行调整分页大小）；选择接口模式可处理全部数据，但需等待服务器响应。"
              type="info"
              showIcon
              className="mt-4 bg-blue-50 border-blue-100"
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SetUpModal;
