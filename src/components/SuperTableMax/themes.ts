import { themeQuartz } from 'ag-grid-community';

// to use myTheme in an application, pass it to the theme grid option
const myTheme = themeQuartz
	.withParams({
        accentColor: "#416EFF",
        backgroundColor: "#FFFFFF",
        borderColor: "#0808083D",
        // borderColor: "#E1E4E8",
        browserColorScheme: "light",
        cellTextColor: "#333333",
        columnBorder: true,
        foregroundColor: "#181D1F",
        headerBackgroundColor: "#fafafd",
        headerFontSize: 14,
        headerFontWeight: 400,
        headerTextColor: "#556C7E",
        oddRowBackgroundColor: "#FFFFFF",
        wrapperBorderRadius: 4,
    });
    
export default myTheme;