import { AgGridReact } from 'ag-grid-react';
import { AG_GRID_LOCALE_CN } from '@ag-grid-community/locale';
import { useCallback, useMemo, useRef } from 'react';
import './index.less';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import React from 'react';
import myTheme from './themes';

interface SuperTableMaxProps {
  dataSource?: any[];
  columns: any[];
  request: (params: any) => Promise<any>;
}

const SuperTableMax = (props: SuperTableMaxProps) => {
  const { dataSource, columns, request } = props;
  const RefTable = useRef<any>(null);

  const [toList] = usePermissionFiltering();

  /* 状态栏 */
  const statusBar = useMemo(() => {
    return {
      statusPanels: [
        // { statusPanel: 'agTotalAndFilteredRowCountComponent',align: 'left' },// 总和过滤行计数组件
        // { statusPanel: 'agTotalRowCountComponent' },// 总行计数组件
        // { statusPanel: 'agFilteredRowCountComponent' },// 过滤行计数组件
        // { statusPanel: 'agSelectedRowCountComponent' },// 选中行计数组件
        // { statusPanel: 'agAggregationComponent' }// 聚合组件
        // {
        //   statusPanel: 'customFilteredRowCountComponent', // 自定义组件
        //   align: 'right',
        //   key: 'filteredCount',
        // },
      ],
    };
  }, []);

  const onGridReady = useCallback((params: any) => {
    console.log('onGridReady', params);
  }, []);

  const rowClassRules = useMemo(() => {
    return {
      'cell-red': (params: any) => params?.data?.holdFlag,
    };
  }, []);

  const columnDefs = useMemo(() => {
    console.log('columns渲染次数');
    return toList(
      columns.map((item: any) => {
        return {
          ...item,
          // filter: true, // 是否显示过滤器
          // floatingFilter: true, // 是否显示过滤器
          // editable: true,// 是否可编辑
        };
      }),
    );
  }, [columns]);

  console.log('RefTable', RefTable);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      {/* <div onClick={()=>{
        console.log('执行了',RefTable.current.api);
        RefTable.current.api.refreshServerSide({ purge: true });
      }}>刷新</div> */}
      <AgGridReact
        ref={RefTable}
        theme={myTheme}
        localeText={AG_GRID_LOCALE_CN}
        cellSelection={true} // 单元格选择
        // rowSelection={{
        //   mode: 'singleRow', // 多选
        //   enableClickSelection: true, // 点击选择
        //   checkboxes: true, // 显示复选框
        // }} // 行选择
        rowSelection="multiple" // 行选择
        defaultColDef={{
          // filter: true, // 是否显示过滤器
          // floatingFilter: true, // 是否显示过滤器
          // editable: true,// 是否可编辑
          filterParams: {
            // 关键配置：使过滤器在本地工作，不发送到服务器
            serverSideFilter: false
          }
        }}
        rowData={dataSource} // 行数据
        columnDefs={columnDefs} // 列定义
        rowNumbers={true} // 行号
        pagination={true} // 启用分页
        paginationPageSize={1000} // 每页条数
        cacheBlockSize={1000} // 每次请求的行数
        // paginationAutoPageSize={true}// 自动适应分页
        paginationPageSizeSelector={[100, 200, 500, 1000, 2000, 5000, 10000]}
        rowModelType="serverSide" // 使用服务器端行模型
        animateRows={true} // 启用动画
        serverSideDatasource={{
          getRows: (params: any) => {
            const { sortModel } = params.request;
            params.request.columnsFilter = {
              [sortModel?.[0]?.colId]: {
                sort:
                  sortModel?.[0]?.sort === 'asc'
                    ? 1
                    : sortModel?.[0]?.sort === 'desc'
                    ? -1
                    : '',
              },
            };
            return request(params);
          },
        }}
        serverSideEnableClientSideSort={true} // 启用客户端排序
        rowClassRules={rowClassRules}
        statusBar={statusBar}
        onGridReady={onGridReady} // 网格准备就绪
        // enableCharts={true} // 启用图表
        sideBar={{
          // toolPanels: ['columns', 'filters'],
          toolPanels: [
            {
              id: 'columns',
              labelDefault: 'Columns',
              labelKey: 'columns',
              iconKey: 'columns',
              toolPanel: 'agColumnsToolPanel',
              toolPanelParams: {
                // 列面板的本地配置
                suppressSyncLayoutWithGrid: false
              }
            },
            {
              id: 'filters',
              labelDefault: 'Filters',
              labelKey: 'filters',
              iconKey: 'filter',
              toolPanel: 'agFiltersToolPanel',
            },
          ],
          defaultToolPanel: 'collapsed', // 设置侧边栏默认收起
          position: 'right',
        }} // 启用侧边栏
        suppressRowClickSelection={true} // 点击行时不选中
        rowHeight={40}
        headerHeight={40}
        // enableAdvancedFilter={true}  // 启用高级过滤
        // debug
        suppressServerSideFullWidthLoadingRow={true} // 禁用服务器端全宽加载行
      />
    </div>
  );
};

export default React.memo(SuperTableMax);
