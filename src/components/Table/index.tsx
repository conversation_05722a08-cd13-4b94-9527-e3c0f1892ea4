import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import ProTable from '@ant-design/pro-table';

const CopyableTable = ({ dataSource, columns, rowKey = 'key', ...props }) => {
  const [selectedCells, setSelectedCells] = useState(new Set());
  const [isSelecting, setIsSelecting] = useState(false);
  const [startCell, setStartCell] = useState(null);
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const [shiftPressed, setShiftPressed] = useState(false);
  const tableRef = useRef(null);

  // 处理复制操作
  const handleCopy = () => {
    if (selectedCells.size === 0) {
      message.warning('请先选择要复制的内容');
      return;
    }

    // 将选中的单元格按行和列排序
    const sortedCells = Array.from(selectedCells)
      .map((cell) => {
        const [rowKeyValue, dataIndex] = cell.split('|');
        const record = dataSource.find((item) => item[rowKey] == rowKeyValue);
        const rowIndex = dataSource.findIndex(
          (item) => item[rowKey] == rowKeyValue,
        );
        const colIndex = columns.findIndex(
          (col) => col.dataIndex === dataIndex,
        );

        return {
          rowIndex,
          colIndex,
          rowKeyValue,
          dataIndex,
          value: record ? record[dataIndex] : '',
        };
      })
      .sort((a, b) => {
        // 先按行排序，再按列排序
        if (a.rowIndex !== b.rowIndex) return a.rowIndex - b.rowIndex;
        return a.colIndex - b.colIndex;
      });

    // 分组为行
    const rows = [];
    let currentRow = [];
    let lastRowIndex = null;

    sortedCells.forEach(({ rowIndex, value }) => {
      if (rowIndex !== lastRowIndex && currentRow.length > 0) {
        rows.push(currentRow.join('\t'));
        currentRow = [];
      }
      currentRow.push(value !== undefined ? String(value) : '');
      lastRowIndex = rowIndex;
    });

    // 添加最后一行
    if (currentRow.length > 0) {
      rows.push(currentRow.join('\t'));
    }

    const textToCopy = rows.join('\n');

    // 复制到剪贴板
    const copyToClipboard = (text) => {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();

      try {
        const successful = document.execCommand('copy');
        if (successful) {
          message.success('复制成功');
        } else {
          message.error('复制失败，请重试');
        }
      } catch (err) {
        message.error('复制失败: ' + err);
      }

      document.body.removeChild(textarea);
    };

    // 尝试使用现代API，失败时回退到传统方法
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(textToCopy)
        .then(() => message.success('复制成功'))
        .catch(() => copyToClipboard(textToCopy)); // 回退
    } else {
      copyToClipboard(textToCopy); // 直接使用传统方法
    }
  };

  // 处理鼠标按下事件
  const handleMouseDown = (record, dataIndex) => {
    setIsSelecting(true);

    const cellKey = `${record[rowKey]}|${dataIndex}`;

    if (shiftPressed && selectedCells.size > 0) {
      // Shift 多选模式
      const newSelectedCells = new Set(selectedCells);
      const firstCell = Array.from(selectedCells)[0];
      const [firstRowKey, firstDataIndex] = firstCell.split('|');

      const firstRowIndex = dataSource.findIndex(
        (item) => item[rowKey] == firstRowKey,
      );
      const firstColIndex = columns.findIndex(
        (col) => col.dataIndex === firstDataIndex,
      );

      const currentRowIndex = dataSource.findIndex(
        (item) => item[rowKey] == record[rowKey],
      );
      const currentColIndex = columns.findIndex(
        (col) => col.dataIndex === dataIndex,
      );

      const minRow = Math.min(firstRowIndex, currentRowIndex);
      const maxRow = Math.max(firstRowIndex, currentRowIndex);
      const minCol = Math.min(firstColIndex, currentColIndex);
      const maxCol = Math.max(firstColIndex, currentColIndex);

      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const rowKeyValue = dataSource[row][rowKey];
          const colDataIndex = columns[col].dataIndex;
          newSelectedCells.add(`${rowKeyValue}|${colDataIndex}`);
        }
      }

      setSelectedCells(newSelectedCells);
    } else {
      // 普通选择模式
      setStartCell({ record, dataIndex });
      setSelectedCells(new Set([cellKey]));
    }
  };

  // 处理鼠标移动事件
  const handleMouseMove = (record, dataIndex) => {
    if (isSelecting && startCell && !shiftPressed) {
      const newSelectedCells = new Set();
      const startRowKey = startCell.record[rowKey];
      const startDataIndex = startCell.dataIndex;

      const startRowIndex = dataSource.findIndex(
        (item) => item[rowKey] == startRowKey,
      );
      const startColIndex = columns.findIndex(
        (col) => col.dataIndex === startDataIndex,
      );

      const currentRowKey = record[rowKey];
      const currentDataIndex = dataIndex;

      const currentRowIndex = dataSource.findIndex(
        (item) => item[rowKey] == currentRowKey,
      );
      const currentColIndex = columns.findIndex(
        (col) => col.dataIndex === currentDataIndex,
      );

      const minRow = Math.min(startRowIndex, currentRowIndex);
      const maxRow = Math.max(startRowIndex, currentRowIndex);
      const minCol = Math.min(startColIndex, currentColIndex);
      const maxCol = Math.max(startColIndex, currentColIndex);

      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          const rowKeyValue = dataSource[row][rowKey];
          const colDataIndex = columns[col].dataIndex;
          newSelectedCells.add(`${rowKeyValue}|${colDataIndex}`);
        }
      }

      setSelectedCells(newSelectedCells);
    }
  };

  // 处理鼠标松开事件
  const handleMouseUp = () => {
    setIsSelecting(false);
    setStartCell(null);
  };

  // 处理右键点击事件
  const handleContextMenu = (e, record, dataIndex) => {
    e.preventDefault();
    e.stopPropagation();

    const cellKey = `${record[rowKey]}|${dataIndex}`;

    // 如果右键点击的单元格不在选中区域，则选中该单元格
    if (!selectedCells.has(cellKey)) {
      setSelectedCells(new Set([cellKey]));
    }

    setMenuVisible(true);
    setMenuPosition({ x: e.clientX, y: e.clientY });
  };

  // 处理选择整行
  const handleSelectRow = () => {
    if (selectedCells.size === 0) return;

    const firstCell = Array.from(selectedCells)[0];
    const [rowKeyValue] = firstCell.split('|');

    const newSelectedCells = new Set();
    columns.forEach((col) => {
      if (col.dataIndex) {
        newSelectedCells.add(`${rowKeyValue}|${col.dataIndex}`);
      }
    });

    setSelectedCells(newSelectedCells);
    setMenuVisible(false);
  };

  // 监听键盘事件
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Shift') {
        setShiftPressed(true);
      }

      if (e.ctrlKey && e.key === 'c') {
        e.preventDefault();
        handleCopy();
      }
    };

    const handleKeyUp = (e) => {
      if (e.key === 'Shift') {
        setShiftPressed(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [selectedCells]);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = () => {
      setMenuVisible(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 渲染单元格
  const renderCell = (text, record, dataIndex) => {
    const cellKey = `${record[rowKey]}|${dataIndex}`;
    const isSelected = selectedCells.has(cellKey);

    return (
      <div
        style={{
          backgroundColor: isSelected ? '#bae7ff' : 'transparent',
          //padding: '8px',
          cursor: 'cell',
          userSelect: 'none',
          height: '100%',
        }}
        onMouseDown={(e) => {
          if (e.button === 0) {
            // 左键
            handleMouseDown(record, dataIndex);
          }
        }}
        onMouseMove={() => handleMouseMove(record, dataIndex)}
        onMouseUp={handleMouseUp}
        onContextMenu={(e) => handleContextMenu(e, record, dataIndex)}
      >
        {text}
      </div>
    );
  };

  // 处理列定义
  const processedColumns = columns.map((col) => ({
    ...col,
    render: (text, record) => renderCell(text, record, col.dataIndex),
  }));

  return (
    <div ref={tableRef} style={{ position: 'relative', userSelect: 'none' }}>
      <ProTable
        dataSource={dataSource}
        columns={processedColumns}
        rowKey={rowKey}
        pagination={false}
        search={false}
        options={false}
        {...props}
      />

      {menuVisible && (
        <div
          style={{
            position: 'fixed',
            left: menuPosition.x,
            top: menuPosition.y,
            backgroundColor: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div
            style={{
              padding: '5px 12px',
              cursor: 'pointer',
              whiteSpace: 'nowrap',
              ':hover': { backgroundColor: '#f5f5f5' },
            }}
            onClick={() => {
              handleCopy();
              setMenuVisible(false);
            }}
          >
            复制
          </div>
          <div
            style={{
              padding: '5px 12px',
              cursor: 'pointer',
              whiteSpace: 'nowrap',
              ':hover': { backgroundColor: '#f5f5f5' },
            }}
            onClick={handleSelectRow}
          >
            选择整行
          </div>
        </div>
      )}
    </div>
  );
};

export default CopyableTable;
