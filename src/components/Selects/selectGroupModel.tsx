import { ModalForm, ProForm, ProFormRadio } from '@ant-design/pro-components';
import { Form, Pagination } from 'antd';
import { getCustomGroupAPI } from '@/services/customer/CustomerHome';
import { useState } from 'react';
import { findNameById } from '@/utils/utils';

const SelectGroupModel = (props: any) => {
  const { trigger,onChange } = props;
  const [form] = Form.useForm<{ name: string; company: string }>();
  const [params, setParams] = useState({ current: 1, pageSize: 10 });
  const [total, setTotal] = useState();
  const [data, setData] = useState([]);
  /*已选*/
  const selected = Form.useWatch('selected', form);
  const handlePaination = (page: any) => {
    setParams({ current: page, pageSize: 10 });
  };
  /*    const handleFinish = async (values: any) => {
    };*/
  const request = async (params: any) => {
    const msg = await getCustomGroupAPI({
      start: (params.current - 1) * params.pageSize,
      len: params.pageSize,
    });
    setTotal(msg.data.total);
    setData(msg.data.list);
    return msg.data.list.map((i: any) => {
      return {
        label: i.name + '(' + i.clientList.map((i: any) => i.name) + ')',
        value: i,
      };
    });
  };
  const handleFinish:any = ()=>{
    if(onChange){
      onChange(selected)
    }
    return true
  }
  return (
    <ModalForm<{
      name: string;
      company: string;
    }>
      title={`选择客户分组`}
      trigger={trigger}
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      submitTimeout={2000}
      onFinish={handleFinish}
    >
      <ProForm.Group>
        <ProFormRadio.Group
          name="selected"
          label={`已选 ${findNameById(selected, data)}`}
          layout="vertical"
          params={params}
          request={request}
        />
      </ProForm.Group>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div></div>
        <Pagination
          total={total}
          showQuickJumper
          onChange={(page: number) => {
            handlePaination(page);
          }}
          showTotal={(total) => `共 ${total} 页`}
        />
      </div>
    </ModalForm>
  );
};

export default SelectGroupModel;
