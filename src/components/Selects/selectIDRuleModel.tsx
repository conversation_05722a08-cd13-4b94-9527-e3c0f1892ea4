import { ModalForm, ProForm, ProFormRadio } from '@ant-design/pro-components';
import { Form, Pagination } from 'antd';
import { useState } from 'react';
import { getIdRuleList } from '@/services/system/IdRule';

const SelectIDRuleModel = (props: any) => {
  const { getIDRule, trigger } = props;
  const [form] = Form.useForm<{ name: string; company: string }>();
  const [params, setParams] = useState({ current: 1, pageSize: 10 });
  const [total, setTotal] = useState();
  const selected = Form.useWatch('selected', form);
  const handlePaination = (page: any) => {
    setParams({ current: page, pageSize: 10 });
  };
  /*    const handleFinish = async (values: any) => {
    };*/
  const request = async (params: any) => {
    const msg = await getIdRuleList({
      start: (params.current - 1) * params.pageSize,
      len: params.pageSize,
    });
    setTotal(msg.data.total);
    return msg.data.list.map((i: any) => {
      return {
        label: (
          <span>
            <span>{i.name}</span>
            <span>{i.content}</span>
          </span>
        ),
        value: i,
      };
    });
  };
  /*这个返回的结果*/
  const handleFinish = async () => {
    getIDRule(selected);
    return true;
  };
  /*
   父级获取对象方法
   const getIDRule = (IDRule: any) => {
    console.log(IDRule);
  };
  调用方法
  <SelectIDRuleModel getIDRule={getIDRule}  trigger={trigger} />
  */
  return (
    <ModalForm<{
      name: string;
      company: string;
    }>
      title={`选择ID编码规则`}
      trigger={trigger}
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      onFinish={handleFinish}
    >
      <ProForm.Group>
        <ProFormRadio.Group
          name="selected"
          label={`已选 ${selected?.name}`}
          layout="vertical"
          params={params}
          request={request}
        />
      </ProForm.Group>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div></div>
        <Pagination
          total={total}
          showQuickJumper
          onChange={(page: number) => {
            handlePaination(page);
          }}
          showTotal={(total) => `共 ${total} 页`}
        />
      </div>
    </ModalForm>
  );
};

export default SelectIDRuleModel;
