import {
  ModalForm,
  ProForm,
  ProFormCheckbox,
} from '@ant-design/pro-components';
import { Form, message, Tag } from 'antd';
import {
  getLabelListAPI,
  setCustomLabelAPI,
} from '@/services/customer/CustomerHome';
import React, { useState } from 'react';
import { GenerateTab } from '@/utils/format';

const SelectLabelModel = (props: any) => {
  const { trigger, EditId, name, fresh, value, onChange,isAccessibleFlag } = props;
  const [form] = Form.useForm<{ name: string; company: string }>();
  const [list, setList] = useState([]);
  const selected = Form.useWatch('selected', form);
  /*请求列表*/
  const requestData = async () => {
    const msg = await getLabelListAPI({});
    setList(msg.data);
    return msg.data.map((i: any) => {
      return {
        label: (
          <Tag
            color={i.tag && GenerateTab(i.tag)}
            key={i.id}
            style={{ marginTop: '10px' }}
          >
            {i.tag}
          </Tag>
        ),
        value: i.tag,
      };
    });
  };
  /*已选列表*/
  const findNameById = () => {
    // @ts-ignore
    const data = list.filter((obj: any) => {
      return selected?.includes(obj.tag) || '';
    });
    return data.map((i: any) => (
      <Tag
        key={i}
        style={{ marginRight: '5px' }}
        color={i.tag && GenerateTab(i.tag)}
      >
        {i.tag}
      </Tag>
    ));
  };
  /*提交数据*/
  const handleFinish = async () => {
    if (!!EditId) {
      //详情走接口
      const res = await setCustomLabelAPI({
        id: EditId,
        tags: selected.join(','),
      });
      if (res.status) {
        message.success('设置成功');
        fresh();
        return true;
      }
    } else {
      //新增返回值给页面
      console.log('提交', selected.join(','));
      onChange(selected.join(','));
      return true;
    }
  };
  /*打开弹窗设置默认值*/
  const handelRender = async (open: boolean) => {
    if (open) {
      form.setFieldValue('selected', value?.split(','));
    }
  };
  return (
    <ModalForm<{
      name: string;
      company: string;
    }>
      title={
        <div>
          设置标签
          <span
            style={{ color: '#AEAEAE', marginLeft: '10px', fontSize: '85%' }}
          >
            {name}
          </span>
        </div>
      }
      trigger={trigger}
      form={form}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={handleFinish}
      onOpenChange={handelRender}
      disabled={!isAccessibleFlag}
    >
      <div>已选 {findNameById()}</div>
      <ProForm.Group>
        <div>
          <ProFormCheckbox.Group
            name="selected"
            layout="vertical"
            request={requestData}
          />
        </div>
      </ProForm.Group>
    </ModalForm>
  );
};

export default SelectLabelModel;
