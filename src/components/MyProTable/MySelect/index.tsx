import React from 'react';
import styles from './index.less';
import { Select } from 'antd';

// 脚手架示例组件
const MySearch = (props: any) => {
  const { style, placeholder, enterButton, size, onSearch } = props;
  return (
    <div className={styles.Wrap}>
      <Select
        {...props}
        placeholder={placeholder}
        allowClear
        enterButton={enterButton}
        size={size}
        style={style}
        onSearch={onSearch}
      />
    </div>
  );
};

export default MySearch;
