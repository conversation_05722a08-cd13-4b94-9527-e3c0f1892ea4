
import React from 'react';
import styles from './index.less';

interface Props {
  name: string;
  style?:any;
}

// 脚手架示例组件
const IconItem: React.FC<Props> = (props) => {
  const { name,style} = props;
  return (
      <svg className={styles.icon} aria-hidden="true" style={style}>
       {/* <use xlinkHref="#icon-xxx"></use>*/}
        <use xlinkHref={name} ></use>
      </svg>
  );
};

export default IconItem;
