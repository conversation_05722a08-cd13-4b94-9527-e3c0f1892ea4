.search-card-warp{
  :global{
    .ant-input {
      box-shadow: none;
    }

    .ant-input-affix-wrapper{
      box-shadow: none;
    }

    .ant-input:hover {
      border: 1px solid #fff;
    }

    .ant-input:focus {
      border: 1px solid #fff;
    }

    .ant-input-group-addon{
      .ant-input-search-button{
        background: #fafafa;
        color: #4071ff;
      }
      
      
    }

    .ant-input-affix-wrapper >input.ant-input{
      border: 1px solid #fafafa !important;
    }

    .ant-form-item-no-colon{
      color: #707070 !important;
    }

    .ant-pro-query-filter{
      padding: 20px 24px 12px;
    }
  }


  .btnWarp{
    position: absolute;
    width: 70px;
    right: 0;
    top: 0;
    height: 100%;
    background:#fafafa;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 5px 5px 0;
    color: #4071ff;
    font-size: 14px;
    cursor: pointer;

    .btn{
      text-align: center;
    }
  }

  .btnWarp:hover{
    background: #6c8cf7;
    color: #fff;
    transition: all 0.8s;
  }
}