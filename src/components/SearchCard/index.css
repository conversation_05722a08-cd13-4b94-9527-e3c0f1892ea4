.search-card-warp :global .ant-input {
  box-shadow: none;
}
.search-card-warp :global .ant-input-affix-wrapper {
  box-shadow: none;
}
.search-card-warp :global .ant-input:hover {
  border: 1px solid #fff;
}
.search-card-warp :global .ant-input:focus {
  border: 1px solid #fff;
}
.search-card-warp :global .ant-input-group-addon .ant-input-search-button {
  background: #fafafa;
  color: #4071ff;
}
.search-card-warp :global .ant-input-affix-wrapper > input.ant-input {
  border: 1px solid #fafafa !important;
}
.search-card-warp :global .ant-form-item-no-colon {
  color: #707070 !important;
}
.search-card-warp :global .ant-pro-query-filter {
  padding: 20px 24px 12px;
}
.search-card-warp .btnWarp {
  position: absolute;
  width: 70px;
  right: 0;
  top: 0;
  height: 100%;
  background: #fafafa;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0 5px 5px 0;
  color: #4071ff;
  font-size: 14px;
  cursor: pointer;
}
.search-card-warp .btnWarp .btn {
  text-align: center;
}
.search-card-warp .btnWarp:hover {
  background: #6c8cf7;
  color: #fff;
  transition: all 0.8s;
}
