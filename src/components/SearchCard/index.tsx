import { ProCard, QueryFilter } from '@ant-design/pro-components';
import { Input } from 'antd';
import React, { useState } from 'react';
import MyIcon from '../MyIcon';
import styles from './index.less';
interface Props {
  mbHeight?: number;
  onCardValuesChange?: (val: any, vals: any) => void;
  children?: React.ReactNode;
  onSearch?: (val: string) => void;
  initialValues?: any;
  isBatch?: boolean;
}

const SearchCard = ({
  mbHeight = 20,
  onCardValuesChange,
  children,
  onSearch,
  initialValues,
  isBatch,
}: Props) => {
const { TextArea } = Input;
  
  const [textAreaVal, setTextAreaVal] = useState<string>('');
  return (
    <div className={styles['search-card-warp']}>
      <ProCard className={`mb-${mbHeight}px`} bodyStyle={{ padding: 0 }}>
        <QueryFilter
          // size="middle"
          submitter={false}
          labelWidth="auto"
          onValuesChange={(val, vals) => {
            if (onCardValuesChange) {
              onCardValuesChange(val, vals);
            }
          }}
          colon={false}
          initialValues={initialValues}
        >
          {isBatch ? (
            <div className="flex relative">
              <TextArea
                allowClear={{
                  clearIcon: [
                    <div
                    key={1}
                    onClick={() => {
                      if (onSearch) {
                        onSearch('');
                      }
                    }}
                  >
                    <MyIcon type="icon-biaoqianshanchuanniu" />
                  </div>
                  ],
                }}
                autoSize={{ minRows: 1 }}
                placeholder="请输入要查询的内容(回车可换行)"
                style={{ width: '84%' }}
                onChange={(e) => {
                  setTextAreaVal(e.target.value);
                }}
              />
              <div
                className={styles.btnWarp}
                onClick={() => {
                  if (onSearch) {
                    onSearch(textAreaVal);
                  }
                }}
              >
                <div className={styles.btn}>搜索</div>
              </div>
            </div>
          ) : (
            <Input.Search
              placeholder="请输入要查询的内容"
              enterButton="搜索"
              style={{ maxWidth: 522, width: '100%' }}
              // size="large"
              name="keyword"
              allowClear
              onSearch={(val) => {
                if (onSearch) {
                  onSearch(val);
                }
              }}
            />
          )}

          {children}
        </QueryFilter>
      </ProCard>
    </div>
  );
};

export default React.memo(SearchCard);
