import { EditOutlined } from '@ant-design/icons';
import { Input, Space } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';

interface Props {
  lable: string;
  width?: any;
  defaultValue: string;
  onChange?: any;
}
const InputSave = ({ lable, width, defaultValue, onChange }: Props) => {
  const [flage, setFlage] = useState<boolean>(false);
  const [inpVal, setInpVal] = useState<string>('');
  const [sValue, setSValue] = useState<any>('');
  const confirm = () => {
    onChange(inpVal);
    setSValue(inpVal);
    setFlage(false);
  };
  useEffect(() => {
    setInpVal(defaultValue);
    onChange(defaultValue);
  }, [defaultValue]);
  return (
    <div>
      <span className={styles['text-lable']}>{lable}：</span>
      <span>
        {flage ? (
          <Input
            value={inpVal}
            onChange={(e) => {
              setInpVal(e.target.value);
            }}
            style={{ width: width }}
          />
        ) : (
          <span style={{ color: '#333333' }}>{inpVal}</span>
        )}
      </span>
      {flage ? (
        <Space style={{ marginLeft: 10 }}>
          <a type="link" onClick={confirm}>
            保存
          </a>
          <a
            type="link"
            onClick={() => {
              setInpVal(defaultValue || sValue);
              setFlage(false);
            }}
          >
            取消
          </a>
        </Space>
      ) : (
        <span
          style={{ color: '#AEAEAE', marginLeft: 10 }}
          onClick={() => setFlage(true)}
        >
          <EditOutlined />
        </span>
      )}
    </div>
  );
};

export default InputSave;
