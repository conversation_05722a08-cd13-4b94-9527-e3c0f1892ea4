/* stylelint-disable property-no-vendor-prefix */
/* stylelint-disable value-no-vendor-prefix */

.warp{
  display: flex;
  // background: pink;
  align-items: center;
  padding:3px 5px;

  .title{
    font-size: 24px;
    font-weight: 600;
    color: #333;
    line-height: 37px;
    margin-left: 16px;
  }
}

.acssHead{
  top: 0;
  position: -webkit-sticky;
  position: sticky;
  // background-color: transparent;
  background: #F6F6F8;
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  z-index: 1000;
  // border-bottom: 1px solid rgba(5, 5, 5, 0.06);
  grid-area: head;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}