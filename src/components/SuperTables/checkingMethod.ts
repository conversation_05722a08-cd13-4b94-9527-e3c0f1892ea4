// 将树结构拍平为一维数组，但保留父节点的children字段
const flattenTree = (nodes: any) => {
  const result: any[] = [];

  const processNode = (node: any) => {
    // 创建节点的副本，保留children字段
    const nodeCopy = { ...node };
    result.push(nodeCopy);

    // 如果有子节点，递归处理
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        processNode(child);
      });
    }
  };

  nodes.forEach((node: any) => {
    processNode(node);
  });

  return result;
};

// 获取节点及其所有子节点的UUID集合
const getNodeAndChildrenUUIDs = (node: any): Set<string> => {
  const uuids = new Set<string>();

  const collectUUIDs = (currentNode: any) => {
    if (currentNode.tableUUID) {
      uuids.add(currentNode.tableUUID);
    }

    if (currentNode.children && currentNode.children.length > 0) {
      currentNode.children.forEach((child: any) => {
        collectUUIDs(child);
      });
    }
  };

  collectUUIDs(node);
  return uuids;
};

export const manageCheckedData = (
  list: any[],
  e: any,
  currentSelectedList: any = [],
) => {
  // 确保currentSelectedList是数组
  const selectedList = Array.isArray(currentSelectedList)
    ? [...currentSelectedList]
    : [];

  // 全选
  if (e.cellLocation === 'columnHeader' && e.checked) {
    return flattenTree(list);
  }

  // 取消全选
  if (e.cellLocation === 'columnHeader' && !e.checked) {
    return [];
  }

  // 取消选中单个节点
  if (e.cellLocation === 'body' && !e.checked) {
    // 获取要移除的节点及其子节点的UUID集合
    const uuidsToRemove = getNodeAndChildrenUUIDs(e.originData);

    // 过滤掉需要移除的节点，保持原有顺序
    return selectedList.filter(
      (item: any) => !uuidsToRemove.has(item.tableUUID),
    );
  }

  // 选中单个节点
  if (e.cellLocation === 'body' && e.checked) {
    const selectedMap = new Map();
    selectedList.forEach((item: any) => {
      selectedMap.set(item.tableUUID, item);
    });

    // 获取节点及其所有子节点
    const nodeAndChildren = flattenTree([e.originData]);

    // 添加新节点到已选列表中，避免重复
    nodeAndChildren.forEach((item: any) => {
      if (!selectedMap.has(item.tableUUID)) {
        selectedMap.set(item.tableUUID, item);
        selectedList.push(item);
      }
    });
  }

  return selectedList;
};
