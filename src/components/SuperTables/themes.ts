/*eslint no-bitwise:0*/

function getBackgroundColor(args: any): string {
  const { row, table, col } = args;
  const record = table.getRecordByCell(col, row);
  const fieId = table.getHeaderField(col, row);
  // if (row < table.frozenRowCount) {
  //   return "#FFF";
  // }
  if (record?.holdFlag && fieId !== 'operation') {
    return '#E34343';
  }

  if (record) {
    const keys = Object.keys(record);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const value = record[key];
      if (typeof value === 'string' && value.includes('style')) {
        try {
          if (
            value.includes('{"backgroundColor"') ||
            value.includes('{"color"')
          ) {
            const cleanedStr = value.replace(/style:/i, '');
            const styleObj = JSON.parse(cleanedStr);
            if (styleObj.backgroundColor) {
              return styleObj.backgroundColor;
            }
          }
        } catch (error) {
          const bgMatch = value.match(/backgroundColor['":\s]+([^'"}\s]+)/i);
          if (bgMatch && bgMatch[1]) {
            return bgMatch[1];
          }
        }
      }
    }
  }
  // const index = row - table.frozenRowCount;
  // if (!(index & 1)) {
  //   return '#FAF9FB';
  // }
  // return '#FDFDFD';
  return '#fff';
}

// function getBorderColor(args: any): string {
//   const { row, table, col } = args;
//   const record = table.getRecordByCell(col, row);
//   const fieId = table.getHeaderField(col, row);
//   if (record?.holdReason && fieId !== 'operation') {
//     return '#eb2f06';
//   }
//   return '#F3F8FF';
// }

function getFontColor(args: any): string {
  const { row, table, col } = args;
  const record = table.getRecordByCell(col, row);
  const fieId = table.getHeaderField(col, row);
  if (record?.holdFlag && fieId !== 'operation') {
    return '#f1f2f6';
  }

  if (record) {
    const keys = Object.keys(record);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const value = record[key];
      if (typeof value === 'string' && value.includes('style')) {
        try {
          if (
            value.includes('{"backgroundColor"') ||
            value.includes('{"color"')
          ) {
            const cleanedStr = value.replace(/style:/i, '');
            const styleObj = JSON.parse(cleanedStr);
            if (styleObj.color) {
              return styleObj.color;
            }
          }
        } catch (error) {
          const colorMatch = value.match(/color['":\s]+([^'"}\s]+)/i);
          if (colorMatch && colorMatch[1]) {
            return colorMatch[1];
          }
        }
      }
    }
  }
  return '#333';
}
export default {
  underlayBackgroundColor: '#FFF',
  // selectionBgColor: '#CCE0FF',
  defaultStyle: {
    borderColor: '#E1E4E8',
    // borderColor: '#E9E9E9',
    color: '#333',
    // fontFamily: 'Arial,sans-serif',
    fontFamily: 'PingFang SC, Arial, sans-serif',
    bgColor: '#ECF1F5',
  },
  headerStyle: {
    fontSize: 14,
    fontWeight: '400',
    // fontFamily: 'Arial,sans-serif',
    fontFamily: 'PingFang SC, Arial, sans-serif',
    // bgColor: '#ECF1F5',
    // color: '#8098AB',
    bgColor: '#fbfbfd',
    color: '#556c7e',
    hover: {
      //   cellBorderColor: "#003fff",
      cellBgColor: '#CCE0FF',
      inlineRowBgColor: '#F3F8FF',
      inlineColumnBgColor: '#F3F8FF',
    },
    // click: {
    //   cellBgColor: '#82b2f5',
    //   // inlineColumnBgColor: "#82b2f5",
    //   cellBorderColor: '#0000ff',
    //   cellBorderLineWidth: 2, // [0, 1, 3, 1],
    // },
  },
  rowHeaderStyle: {
    fontSize: 14,
    // fontWeight: 'bold',
    bgColor: '#ECF1F5',
    // fontFamily: 'Arial,sans-serif',
    fontFamily: 'PingFang SC, Arial, sans-serif',
    // click: {
    //   cellBgColor: '#82b2f5',
    //   // inlineColumnBgColor: "#82b2f5",
    //   cellBorderColor: '#0000ff',
    //   cellBorderLineWidth: 2, // [0, 1, 3, 1],
    // },
    hover: {
      //   cellBorderColor: "#003fff",
      cellBgColor: '#CCE0FF',
      inlineRowBgColor: '#F3F8FF',
      inlineColumnBgColor: '#F3F8FF',
    },
  },
  cornerHeaderStyle: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  bodyStyle: {
    fontSize: 14,
    bgColor: getBackgroundColor,
    // color: '#333',
    color: getFontColor,
    fontFamily: 'PingFang SC, Arial, sans-serif',
    // hover: {
    //   // cellBorderColor: "#003fff",
    //   cellBgColor: '#CCE0FF',
    //   inlineRowBgColor: getBorderColor,
    //   inlineColumnBgColor: '#F3F8FF',
    //   // cellBorderLineWidth:2
    // },
    // click: {
    //   cellBgColor: 'rgba(0, 0, 255,0.1)',
    //   cellBorderLineWidth: 2,
    //   inlineColumnBgColor: '#CCE0FF',
    //   inlineRowBgColor: '#CCE0FF',
    //   cellBorderColor: '#0000ff',
    // },
  },
  frameStyle: {
    borderColor: '#E1E4E8',
    borderLineWidth: 1,
    // borderLineDash: [],
    cornerRadius: 2,
    // shadowBlur: 0,
    // shadowOffsetX: 0,
    // shadowOffsetY: 1,
    // shadowColor: '#E1E4E8',
  },
  cellInnerBorder: false,
  columnResize: {
    lineWidth: 1,
    lineColor: '#416EFF',
    bgColor: '#D9E2FF',
    width: 3,
  },
  frozenColumnLine: {
    shadow: {
      width: 3,
      startColor: 'rgba(225, 228, 232, 0.6)',
      endColor: 'rgba(225, 228, 232, 0.2)',
    },
  },
  // menuStyle: {
  //   color: '#000',
  //   highlightColor: '#2E68CF',
  //   fontSize: 12,
  //   fontFamily: 'Arial,sans-serif',
  //   highlightFontSize: 12,
  //   highlightFontFamily: 'Arial,sans-serif',
  //   hoverBgColor: '#EEE'
  // },
  selectionStyle: {
    cellBgColor: 'rgba(0, 0, 255,0.1)',
    cellBorderLineWidth: 1,
    cellBorderColor: '#0000ff',
  },
  tooltipStyle: {
    bgColor: '#000',
    color: '#fff',
    fontSize: 12,
    // fontFamily: 'Arial,sans-serif'
    fontFamily: 'PingFang SC, Arial, sans-serif',
  },
  scrollStyle: {
    visible: 'always',
    hoverOn: false,
    scrollSliderColor: 'rgba(0,0,0,0.2)',
    // scrollRailColor: 'rgba(0,0,0,0.1)',
    width: 18,
    horizontalPadding: [7, 0, 0, 0],
    verticalPadding: [0, 0, 0, 7],
    cursor: 'pointer',
    // barToSide: true,
  },
  bottomFrozenStyle: {
    bgColor: '#fbfbfd',
    color: '#556c7e',
    // borderLineWidth: [2, 0, 1, 0],
  },
};
