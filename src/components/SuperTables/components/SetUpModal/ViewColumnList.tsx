import { getListAPI } from '@/services/view';
import { useRequest } from 'ahooks';
import { useState, useEffect } from 'react';
import Loading from '../Loding';
import { Space, Tag } from 'antd';

interface ViewItem {
  id: string;
  name: string;
  isDef?: boolean;
}

interface ViewColumnListProps {
  instanceId: string;
  onSelect?: (viewId: string) => void;
}

const ViewColumnList = ({ instanceId, onSelect }: ViewColumnListProps) => {
  const [viewList, setViewList] = useState<ViewItem[]>([]);
  const [selectedViewId, setSelectedViewId] = useState<string | null>(null);
  /* 获取视图列表 */
  const { runAsync: getViewList, loading: getViewListLoading } = useRequest(
    getListAPI,
    {
      manual: true,
    },
  );

  useEffect(() => {
    getViewList({ key: instanceId }).then((res) => {
      if (res.status) {
        const list = res.data?.list || [];
        setViewList(list);
        // 设置默认视图为选中状态
        const defaultView = list.find((view: { isDef: any }) => view.isDef);
        if (defaultView) {
          setSelectedViewId(defaultView.id);
          onSelect?.(defaultView.id);
        }
      }
    });
  }, [instanceId]);

  return (
    <div className="px-4 pb-4">
      <Space className="mb-2">
        <div className="text-sm font-medium">视图列表</div>
        <div className="text-xs text-gray-500">
          保存到选中的视图内 可调整视图快速切换
        </div>
      </Space>
      <Loading loading={getViewListLoading}>
        <div className="flex flex-wrap gap-2">
          {viewList.map((view) => (
            <Tag
              key={view.id}
              className={`cursor-pointer px-3 py-1 ${
                selectedViewId === view.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-50'
              }`}
              onClick={() => {
                setSelectedViewId(view.id);
                onSelect?.(view.id);
              }}
            >
              {view.name}
              {!!view.isDef && (
                <span
                  className={
                    selectedViewId === view.id
                      ? 'text-blue-50'
                      : 'text-gray-400'
                  }
                >
                  (默认)
                </span>
              )}
            </Tag>
          ))}
        </div>
      </Loading>
    </div>
  );
};

export default ViewColumnList;
