import React, { useEffect, useState } from 'react';
import { Modal, Tooltip, Checkbox, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import {
  settingsStore,
  columnStore,
  paramsStore,
} from '../../store/screenStore';
import { useSnapshot } from 'umi';
import ColumnDisplay from './ColumnDisplay';
import ViewColumnList from './ViewColumnList';
import { useRequest } from 'ahooks';
import { updateViewAPI } from '@/services/view';
interface Props {
  btnText?: string;
  btnType?: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  instanceId: string;
  isPageCacheEnabled?: boolean;
  columns: any;
  onChange?: (newColumns: any) => void;
}

const SetUpModal = ({
  instanceId,
  isPageCacheEnabled,
  columns,
  onChange,
}: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const snapSettings = useSnapshot(settingsStore);
  const snapColumnStore = useSnapshot(columnStore);
  const [settings, setSettings] = useState(
    snapSettings[instanceId] || {
      cachePagination: isPageCacheEnabled || false,
      cacheSort: false,
      autoFetch: true,
      cacheColumnWidth: true,
      manualSearch: false,
    },
  );
  const [columnsList, setColumnsList] = useState(
    snapColumnStore[instanceId] || [],
  );
  const [selectedViewId, setSelectedViewId] = useState('');
  const syncColumns = (sourceColumns: any[], storedColumns: any[]) => {
    // 如果缓存为空，直接返回源columns
    if (!storedColumns?.length) {
      return sourceColumns.map((col) => ({ ...col }));
    }

    // 创建源列的映射
    const sourceColumnsMap = new Map(
      sourceColumns.map((col, index) => [
        col.dataIndex,
        { ...col, sourceIndex: index },
      ]),
    );

    // 首先保留所有有效的缓存列（过滤掉已不存在的列）
    let result = storedColumns
      .filter((col) => sourceColumnsMap.has(col.dataIndex))
      .map((stored) => {
        const sourceCol = sourceColumnsMap.get(stored.dataIndex)!;
        return {
          ...sourceCol,
          hide: stored.hide !== undefined ? stored.hide : false,
          width: stored.width || sourceCol.width,
          fixed: stored.fixed || sourceCol.fixed,
        };
      });

    // 找出新增的列
    const newColumns = sourceColumns.filter(
      (col) =>
        !storedColumns.some((stored) => stored.dataIndex === col.dataIndex),
    );

    // 为每个新列找到合适的插入位置
    newColumns.forEach((newCol) => {
      const newColIndex = sourceColumns.findIndex(
        (col) => col.dataIndex === newCol.dataIndex,
      );

      // 找到新列在源数据中的前后相邻列
      let prevCol: any = null;
      let nextCol: any = null;

      // 向前查找最近的列
      for (let i = newColIndex - 1; i >= 0; i--) {
        if (
          result.some((col) => col.dataIndex === sourceColumns[i].dataIndex)
        ) {
          prevCol = sourceColumns[i];
          break;
        }
      }

      // 向后查找最近的列
      for (let i = newColIndex + 1; i < sourceColumns.length; i++) {
        if (
          result.some((col) => col.dataIndex === sourceColumns[i].dataIndex)
        ) {
          nextCol = sourceColumns[i];
          break;
        }
      }

      // 确定插入位置
      let insertIndex;
      if (prevCol) {
        // 如果有前置列，插入到其后面
        insertIndex =
          result.findIndex((col) => col.dataIndex === prevCol.dataIndex) + 1;
      } else if (nextCol) {
        // 如果有后置列，插入到其前面
        insertIndex = result.findIndex(
          (col) => col.dataIndex === nextCol.dataIndex,
        );
      } else {
        // 如果都没有，插入到末尾
        insertIndex = result.length;
      }

      // 插入新列
      result.splice(insertIndex, 0, {
        ...newCol,
        hide: false,
      });
    });

    return result;
  };

  useEffect(() => {
    if (!snapSettings?.[instanceId]) {
      settingsStore[instanceId] = {
        cachePagination: isPageCacheEnabled || false,
        cacheSort: false,
        autoFetch: true,
        cacheColumnWidth: true,
        manualSearch: false,
      };
    }

    const storedColumns = snapColumnStore[instanceId] || [];
    const syncedColumns = syncColumns(columns, storedColumns);

    if (storedColumns.length !== syncedColumns.length) {
      setColumnsList(syncedColumns);
      columnStore[instanceId] = syncedColumns;
    }
  }, [snapSettings[instanceId], instanceId, columns]);

  /* 创建视图 */
  const { runAsync: updateViewFn } = useRequest(updateViewAPI, {
    manual: true,
  });

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleSelectView = (viewId: string) => {
    setSelectedViewId(viewId);
  };

  const handleOk = () => {
    updateViewFn({
      id: selectedViewId,
      key: instanceId,
      columns: [...columnsList],
      condition: {},
      isDef: 1,
      manualSearch: settings.manualSearch,
    }).then((res) => {
      if (res.status) {
        settingsStore[instanceId] = settings;
        if (columnsList?.length) {
          columnStore[instanceId] = columnsList;
        }

        if (!settings?.cachePagination) {
          paramsStore[instanceId] = {};
        }

        messageApi.success('设置保存成功，刷新页面后立即生效');
        setIsModalOpen(false);
      }
    });
  };
  const handleCancel = () => {
    setSettings(
      snapSettings[instanceId] || {
        cachePagination: isPageCacheEnabled || false,
        cacheSort: false,
        autoFetch: true,
        cacheColumnWidth: true,
        manualSearch: false,
      },
    );

    setIsModalOpen(false);
  };

  const handleSettingChange = (key: string) => (e: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [key]: e.target.checked,
    }));
  };

  const handleColumnChange = (newColumns: any) => {
    onChange?.(newColumns);
    setColumnsList(newColumns);
  };

  return (
    <>
      {contextHolder}
      <Tooltip title="设置" placement="bottom">
        <SettingOutlined
          onClick={showModal}
          className=" hover:text-blue-500 transition-colors duration-300 cursor-pointer"
          style={{ fontSize: 16 }}
        />
      </Tooltip>
      <Modal
        // title={<div className="text-lg font-medium pb-2 border-b">设置</div>}
        title="高级设置"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="保存设置"
        width={900}
        destroyOnClose={true}
      >
        <div className="">
          <div className="flex items-center gap-4 mb-4 px-4">
            <Tooltip title="启用后，表格将在初始化时自动请求数据，关闭则第一次不请求数据搜索条件改变后触发搜索">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.autoFetch}
                  onChange={handleSettingChange('autoFetch')}
                  className="mr-2"
                  // disabled={true}
                >
                  <span className="text-gray-700">默认请求数据</span>
                </Checkbox>
              </div>
            </Tooltip>
            <Tooltip title="启用后，需要手动点击搜索按钮来触发搜索">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.manualSearch}
                  onChange={handleSettingChange('manualSearch')}
                  className="mr-2"
                  // disabled={true}
                >
                  <span className="text-gray-700">是否手动搜索</span>
                </Checkbox>
              </div>
            </Tooltip>
            <Tooltip title="启用后，表格的分页状态会保持上次的分页设置">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.cachePagination}
                  onChange={handleSettingChange('cachePagination')}
                  className="mr-2"
                >
                  <span className="text-gray-700">缓存分页</span>
                </Checkbox>
              </div>
            </Tooltip>
            <Tooltip title="启用后，表格的排序状态下次搜索查询信息会保持上次的排序设置">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.cacheSort}
                  // checked={false}
                  onChange={handleSettingChange('cacheSort')}
                  className="mr-2"
                  // disabled={true}
                >
                  <span className="text-gray-700">缓存排序</span>
                </Checkbox>
              </div>
            </Tooltip>
            <Tooltip title="启用后，设置表格的列宽后会保持上次的列宽设置,关闭后，列宽会恢复到默认">
              <div className="flex-1 flex items-center p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-300">
                <Checkbox
                  checked={settings.cacheColumnWidth}
                  onChange={handleSettingChange('cacheColumnWidth')}
                  className="mr-2"
                >
                  <span className="text-gray-700">缓存列宽</span>
                </Checkbox>
              </div>
            </Tooltip>
          </div>
          <div className="h-[1px] bg-gray-200 my-4 pl-4"></div>
          <div>
            <ViewColumnList
              instanceId={instanceId}
              onSelect={handleSelectView}
            />
          </div>
          <div>
            <ColumnDisplay
              columns={columns}
              instanceId={instanceId}
              onChange={handleColumnChange}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SetUpModal;
