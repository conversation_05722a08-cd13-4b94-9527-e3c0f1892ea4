import { Checkbox, message, Space, Tooltip } from 'antd';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useState } from 'react';
import styled from 'styled-components';
import { useSnapshot } from 'umi';
import { columnStore } from '../../store/screenStore';
// import { restrictToVerticalAxis } from '@dnd-kit/modifiers';

interface ColumnItem {
  dataIndex: string;
  title: string;
  width?: number;
  hide?: boolean;
}

interface Props {
  columns: ColumnItem[];
  instanceId: string;
  onChange?: (columns: ColumnItem[]) => void;
}

const Container = styled.div`
  padding: 0 16px;
  height: 300px;
  overflow: auto;
`;

const ColumnList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StyledColumnItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #f0f0f0;

  &:hover {
    background: #f5f5f5;
  }
`;

const DragIcon = styled.div`
  margin-right: 8px;
  color: #999;
  cursor: move;
`;

const SortableItem = ({ column, index, onVisibilityChange }: any) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: column.dataIndex });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <StyledColumnItem ref={setNodeRef} style={style}>
      <DragIcon {...attributes} {...listeners}>
        ⋮⋮
      </DragIcon>
      <div className="w-full flex items-center">
        <div className="w-50%">
          <Checkbox
            checked={!column.hide}
            onChange={(e) => onVisibilityChange(index, !e.target.checked)}
          >
            <div className="w-full">{column.title}</div>
          </Checkbox>
        </div>
        <div className="w-50% text-right">
          <DragIcon {...attributes} {...listeners}>
            ⋮⋮
          </DragIcon>
        </div>
      </div>
    </StyledColumnItem>
  );
};

const ColumnDisplay = ({
  columns: initialColumns,
  instanceId,
  onChange,
}: Props) => {
  const [messageApi, contextHolder] = message.useMessage();
  const snapColumnStore = useSnapshot(columnStore);

  // const [initialState] = useState(
  //   snapColumnStore[instanceId]?.length > 0
  //     ? snapColumnStore[instanceId].map((col: any) => ({
  //         ...col,
  //         hide: col.hide ?? false,
  //       }))
  //     : initialColumns.map((col: any) => ({ ...col, hide: col.hide ?? false })),
  // );

  const [columns, setColumns] = useState<ColumnItem[]>(
    snapColumnStore[instanceId]?.length > 0
      ? snapColumnStore[instanceId].map((col: any) => ({
          ...col,
          hide: col.hide ?? false,
        }))
      : initialColumns.map((col: any) => ({ ...col, hide: col.hide ?? false })),
  );

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = columns.findIndex((col) => col.dataIndex === active.id);
      const newIndex = columns.findIndex((col) => col.dataIndex === over.id);

      const newColumns = arrayMove(columns, oldIndex, newIndex);
      setColumns(newColumns);
      onChange?.(newColumns);
    }
  };

  const handleVisibilityChange = (index: number, checked: boolean) => {
    const newColumns = columns.map((col, idx) =>
      idx === index ? { ...col, hide: checked } : col,
    );
    setColumns(newColumns);
    onChange?.(newColumns);
  };

  const handleReset = () => {
    setColumns([...initialColumns]);
    onChange?.([...initialColumns]);
    messageApi.success('已恢复默认列顺序和显示状态');
  };

  return (
    <div>
      {contextHolder}
      <div className="flex items-center justify-between">
        <Space>
          <div className="text-sm font-600 pl-16px pb-10px ">列展示</div>
          <div className="text-12px text-gray-500 pb-8px">
            点击 ⋮⋮ 或右半边区域拖动列顺序、点击☑️隐藏/显示列
          </div>
        </Space>
        <Tooltip title="恢复默认列顺序和显示状态">
          <div
            className="text-sm font-600 pr-16px pb-10px cursor-pointer text-blue-500"
            onClick={handleReset}
          >
            重置
          </div>
        </Tooltip>
      </div>
      <Container>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
          // modifiers={[restrictToVerticalAxis]} // 限制垂直拖动
        >
          <SortableContext
            items={columns.map((col) => col.dataIndex)}
            strategy={verticalListSortingStrategy}
          >
            <ColumnList>
              {columns.map((column, index) => (
                <SortableItem
                  key={column.dataIndex}
                  column={column}
                  index={index}
                  onVisibilityChange={handleVisibilityChange}
                />
              ))}
            </ColumnList>
          </SortableContext>
        </DndContext>
      </Container>
    </div>
  );
};

export default ColumnDisplay;
