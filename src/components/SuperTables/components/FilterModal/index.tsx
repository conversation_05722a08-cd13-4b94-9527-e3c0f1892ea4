import React, { useMemo, useState } from 'react';
import { Modal, Tooltip, Select, Input, Button, Row, Col, Space } from 'antd';
import {
  FileSearchOutlined,
  PlusOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons';

interface Props {
  refresh?: any;
  columns: any;
}
const FilterModal = ({ columns }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const selectedColumns = useMemo(() => {
    return columns.filter((item: any) => item.filters);
  }, [columns]);

  const [filterConditions, setFilterConditions] = useState([
    { field: '', condition: 'equals', value: '' },
  ]);
  const fieIdList = useMemo(() => {
    return selectedColumns.map((col: any) => ({
      value: col.filters.key,
      label: col.title,
    }));
  }, [selectedColumns]);

  const addCondition = () => {
    setFilterConditions([
      ...filterConditions,
      { field: '', condition: 'equals', value: '' },
    ]);
  };

  const removeCondition = (index: number) => {
    const newConditions = filterConditions.filter((_, i) => i !== index);
    setFilterConditions(newConditions);
  };

  const updateCondition = (index: number, key: string, value: string) => {
    const newConditions = [...filterConditions];
    newConditions[index] = {
      ...newConditions[index],
      [key]: value,
    };
    setFilterConditions(newConditions);
  };

  const conditionOptions = [
    { value: 'equals', label: '等于' },
    { value: 'notEquals', label: '不等于' },
    { value: 'contains', label: '包含' },
    { value: 'notContains', label: '不包含' },
    // { value: 'isEmpty', label: '为空' },
    // { value: 'isNotEmpty', label: '不为空' },
  ];

  return (
    <>
      <Tooltip title="高级筛选 🚧" placement="bottom">
        <FileSearchOutlined
          className="hover:text-#1677ff"
          style={{ fontSize: 16, fontWeight: 500 }}
          onClick={showModal}
        />
      </Tooltip>
      <Modal
        title={
          <div className="flex justify-between items-center">
            <div>设置筛选条件 🚧</div>
            {filterConditions.length > 1 && (
              <Space className="text-#57606f text-14px font-400">
                <div>符合以下</div>
                <Select
                  size="small"
                  style={{ width: 80 }}
                  defaultValue="all"
                  options={[
                    { value: 'all', label: '所有' },
                    { value: 'any', label: '任一' },
                  ]}
                />
                <div>条件</div>
              </Space>
            )}
          </div>
        }
        width={800}
        closeIcon={null}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div className="max-h-500px  min-h-200px overflow-y-auto w-100% mt-22px">
          {filterConditions.map((condition, index) => (
            <Row key={index} className="mb-8px">
              <Col span={6}>
                <Select
                  style={{ width: '90%' }}
                  value={condition.field || undefined}
                  onChange={(value) => updateCondition(index, 'field', value)}
                  placeholder="请选择"
                  options={fieIdList}
                />
              </Col>
              <Col span={4}></Col>
              <Col span={4}>
                <Select
                  style={{ width: '90%' }}
                  value={condition.condition || undefined}
                  onChange={(value) =>
                    updateCondition(index, 'condition', value)
                  }
                  placeholder="选择条件"
                  options={conditionOptions}
                />
              </Col>
              {condition.field &&
              selectedColumns.find(
                (col: any) => col.filters.key === condition.field,
              )?.filters.options ? (
                <Col span={8}>
                  <Select
                    style={{ width: '100%' }}
                    value={condition.value || undefined}
                    onChange={(value) => updateCondition(index, 'value', value)}
                    placeholder="选择值"
                    allowClear
                    options={selectedColumns
                      .find((col: any) => col.filters.key === condition.field)
                      ?.filters.options.map((option: any) => ({
                        value: option.value,
                        label: option.text,
                      }))}
                  />
                </Col>
              ) : (
                <Col span={8}>
                  <Input
                    style={{ width: '100%' }}
                    value={condition.value}
                    onChange={(e) =>
                      updateCondition(index, 'value', e.target.value)
                    }
                    placeholder="请输入"
                  />
                </Col>
              )}
              <Col span={2} className="flex justify-center items-center">
                <MinusCircleOutlined onClick={() => removeCondition(index)} />
              </Col>
            </Row>
          ))}
          <Button
            type="dashed"
            onClick={addCondition}
            block
            icon={<PlusOutlined />}
          >
            添加条件
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default React.memo(FilterModal);
