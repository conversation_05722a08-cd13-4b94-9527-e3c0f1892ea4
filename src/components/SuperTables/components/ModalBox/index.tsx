import React, { useEffect, useRef, useState } from 'react';
import { Button, Space } from 'antd';
import WinboxModal, { WinboxModalRef } from '@/components/SuperTables/components/WinModal';
interface Props {
  btnText: string;
  btnType: 'link' | 'text' | 'primary' | 'default' | 'dashed' | undefined;
  refresh?: any;
  record?: any;
  children?: React.ReactElement | React.ReactElement[];
  title?: string;
  width?: string | number;
  height?: string | number;
  isFooter?: boolean;
  noFull?: boolean;
  noMax?: boolean;
}
const ModalBox = ({
  btnText,
  btnType,
  title,
  width,
  height,
  isFooter = false,
  noFull = true,
  noMax = true,
}: Props) => {
  const modalRef = useRef<WinboxModalRef>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (isModalOpen) {
      modalRef.current?.open({
        title: title,
        width: width,
        height: height,
        noFull: noFull,
        noMax: noMax,
      });
    } else {
      modalRef.current?.close();
    }
  }, [isModalOpen]);
  return (
    <>
      <Button type={btnType} onClick={showModal}>
        {btnText}
      </Button>
      <WinboxModal
        ref={modalRef}
        title={title}
        width={width}
        height={height}
        onClose={() => {
          handleCancel();
        }}
        noFull={noFull}
        noMax={noMax}
      >
        <div
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        >
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {/* 可变区域 */}
          </div>
          {isFooter && (
            <div
              style={{
                borderTop: '1px solid #f0f0f0',
                padding: '10px 16px',
                textAlign: 'right',
                background: '#fff',
              }}
            >
              <Space>
                <Button onClick={handleCancel}>取消</Button>
                <Button onClick={handleOk} type="primary">
                  确定
                </Button>
              </Space>
            </div>
          )}
        </div>
      </WinboxModal>
    </>
  );
};
export default React.memo(ModalBox);
