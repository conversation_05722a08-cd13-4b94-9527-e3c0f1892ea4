import React, { useEffect, useState, useRef } from 'react';
import { Modal, Input, Button, Space, Typography } from 'antd';
import { SearchOutlined, CloseOutlined } from '@ant-design/icons';
import { SearchComponent } from '../../search';
import './index.less';

const { Text } = Typography;

interface SearchForFProps {
  visible: boolean;
  onClose: () => void;
  table: any; // SuperTable的实例
  autoJump?: boolean;
}

const SearchForF: React.FC<SearchForFProps> = ({
  visible,
  onClose,
  table,
  autoJump = true,
}) => {
  const [searchText, setSearchText] = useState('');
  const [searchResult, setSearchResult] = useState({
    index: 0,
    results: [] as any[],
  });
  const searchComponentRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    if (isFocused) {
      inputRef.current?.focus({
        cursor: 'all',
      });
    }
  }, [isFocused]);

  useEffect(() => {
    if (visible && table) {
      setIsFocused(true);
      searchComponentRef.current = new SearchComponent({
        table,
        autoJump,
      });
      // window.search = searchComponentRef.current;
    }

    return () => {
      setIsFocused(false);
      searchComponentRef.current = null;
    };
  }, [visible, table, autoJump]);

  const handleSearch = () => {
    if (!searchComponentRef.current) return;
    const result = searchComponentRef.current.search(searchText);
    setSearchResult(result);
  };

  const handleNext = () => {
    if (!searchComponentRef.current) return;
    const result = searchComponentRef.current.next();
    setSearchResult(result);
  };

  const handlePrev = () => {
    if (!searchComponentRef.current) return;
    const result = searchComponentRef.current.prev();
    setSearchResult(result);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleClear = () => {
    setSearchText('');
    setSearchResult({ index: 0, results: [] });
    searchComponentRef.current?.clear();
    onClose?.();
  };

  return (
    <Modal
      className="search-modal-container"
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>单元格内容检索定位</span>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClear}
            size="small"
          />
        </div>
      }
      open={visible}
      onCancel={handleClear}
      footer={null}
      width={400}
      destroyOnClose
      style={{ top: 20 }}
      maskClosable={false}
      mask={false}
      afterClose={() => {
        searchComponentRef.current?.clear();
      }}
      closeIcon={null}
      wrapClassName="search-modal-container"
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Input
          placeholder="模拟ctrl+f 查找单元格内容"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onKeyDown={handleKeyDown}
          prefix={<SearchOutlined />}
          autoFocus
          allowClear
          ref={inputRef}
        />
        <div className="search-result-info">
          <Text>
            {searchResult.results.length === 0
              ? '0/0'
              : `${searchResult.index + 1}/${searchResult.results.length}`}
          </Text>
        </div>
        <div className="search-buttons">
          <Button type="primary" onClick={handleSearch} size="middle">
            搜索
          </Button>
          <Space>
            <Button
              onClick={handlePrev}
              disabled={!searchResult.results.length}
              size="middle"
            >
              上一个
            </Button>
            <Button
              onClick={handleNext}
              disabled={!searchResult.results.length}
              size="middle"
            >
              下一个
            </Button>
            {searchResult.results.length > 0 && (
              <Button onClick={handleClear} size="middle">
                清除
              </Button>
            )}
          </Space>
        </div>
      </Space>
    </Modal>
  );
};

export default SearchForF;
