import React, { useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip, Tour } from 'antd';
const App = ({ refs, refs2, refs3 }: any) => {
  const [open, setOpen] = useState(false);
  const steps = [
    {
      title: '功能操作指南',
      description: (
        <>
          <p>1. asffffasd</p>
        </>
      ),
      target: () => refs?.current?.container,
    },
    {
      title: '操作指南',
      description: '点击刷新当前列表数据',
      target: () => refs2?.current,
    },
    {
      title: '设置',
      description: '点击可设置可对当前列表进行设置 表头排序、列显隐控制',
      target: () => refs3?.current,
    },
    {
      title: 'xxx',
      description: 'xxx',
    },
  ];
  return (
    <>
      <Tooltip title="使用指南" placement="bottom">
        <QuestionCircleOutlined
          className="hover:text-#1677ff"
          style={{ fontSize: 16, fontWeight: 500 }}
          onClick={() => setOpen(true)}
        />
      </Tooltip>
      <Tour open={open} onClose={() => setOpen(false)} steps={steps} />
    </>
  );
};
export default App;