const Loding = () => {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        marginTop: '-30px',
        position: 'relative',
        transform: 'scale(0.9)',
      }}
    >
      {[...Array(12)].map((_, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            width: `${5 + Math.sin(index) * 2}px`,
            height: `${5 + Math.sin(index) * 2}px`,
            background: '#4071FF',
            borderRadius: '50%',
            animation: `loadingDot${index} 1.2s linear infinite`,
            animationDelay: `${index * 0.1}s`,
            opacity: 1 - (index * 0.075),
            transform: `rotate(${index * 30}deg) translateY(-22px) scale(${0.8 + Math.sin(index) * 0.2})`
          }}
        />
      ))}
      
      <svg
        width="26px"
        height="26px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <defs>
          <linearGradient
            x1="8.47527459%"
            y1="8.5179084%"
            x2="94.6649236%"
            y2="82.997928%"
            id="linearGradient-1"
          >
            <stop stopColor="#68ABFC" offset="0%"></stop>
            <stop stopColor="#4071FF" offset="100%"></stop>
          </linearGradient>
          <circle id="path-2" cx="10" cy="10" r="10"></circle>
          <filter colorInterpolationFilters="auto" id="filter-4">
            <feColorMatrix
              in="SourceGraphic"
              type="matrix"
              values="0 0 0 0 1.000000 0 0 0 0 1.000000 0 0 0 0 1.000000 0 0 0 1.000000 0"
            ></feColorMatrix>
          </filter>
          <linearGradient
            x1="0.000751123357%"
            y1="49.9992828%"
            x2="99.9998173%"
            y2="49.9992828%"
            id="linearGradient-5"
          >
            <stop stopColor="#274AFF" offset="0%"></stop>
            <stop stopColor="#5081FF" offset="100%"></stop>
          </linearGradient>
          <linearGradient
            x1="100%"
            y1="50%"
            x2="0%"
            y2="50%"
            id="linearGradient-6"
          >
            <stop stopColor="#3197FF" offset="0%"></stop>
            <stop stopColor="#204AFF" offset="100%"></stop>
          </linearGradient>
        </defs>
        <g
          id="页面-1"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
        >
          <g id="编组-2备份-12">
            <mask id="mask-3" fill="white">
              <use xlinkHref="#path-2"></use>
            </mask>
            <use
              id="蒙版"
              fill="url(#linearGradient-1)"
              xlinkHref="#path-2"
            ></use>
            <g filter="url(#filter-4)" id="编组-2备份-3">
              <g mask="url(#mask-3)">
                <g transform="translate(3.005319, 4.689716)" id="编组">
                  <path
                    d="M4.10114759,5.56920681 C4.47107005,7.1490183 5.79900696,8.12965834 7.04732284,8.12965834 L7.04732284,8.12965834 L13.0825576,8.12965834 C13.3961147,8.12965834 13.6966644,8.01064483 13.9166077,7.79958639 L13.9166077,7.79958639 L14.3121508,7.42026748 L3.65416615,3.65834625 L4.10114759,5.56920681 Z"
                    id="Fill-6"
                    fill="url(#linearGradient-5)"
                  ></path>
                  <path
                    d="M11.4856802,8.43452053 L5.36940157,8.43452053 C4.87499649,8.43452053 4.06372539,8.83142764 3.82426324,9.47046569 L3.45115692,10.4669351 L12.2820584,8.72159909 L12.0257438,8.56819087 C11.8834774,8.48259495 11.6888899,8.43452053 11.4856802,8.43452053"
                    id="Fill-8"
                    fill="#3296FF"
                  ></path>
                  <path
                    d="M4.11576938,0.0985432031 C2.81905968,0.363882087 1.80008999,0.955141306 1.01483074,1.96594674 L1.01483074,1.96594674 C0.451881436,2.69036713 -0.465073353,4.5510831 0.279301307,6.2475604 L0.279301307,6.2475604 C1.18249686,8.30634624 3.53296746,8.3328998 3.53296746,8.3328998 L3.53296746,8.3328998 C3.53296746,8.3328998 2.90888784,8.09726156 2.34849382,7.55281528 L2.34849382,7.55281528 C1.46180935,6.69129985 1.13080145,4.41418476 2.72765905,2.81271019 L2.72765905,2.81271019 C3.2261365,2.31271653 3.91586734,1.94332704 4.61817805,1.82708146 L4.61817805,1.82708146 C7.5791656,1.338496 9.2721413,4.78809819 9.2721413,4.78809819 L9.2721413,4.78809819 C9.2721413,4.78809819 12.8990759,2.89925512 14.2559331,7.03905311 L14.2559331,7.03905311 C14.291314,6.84609726 14.3123459,6.64940424 14.3121508,6.44877736 L14.3121508,6.44877736 C14.3113631,5.01999926 12.9423192,2.86227683 10.0127813,3.11365051 L10.0127813,3.11365051 C9.035679,1.6626461 7.32520942,0.00019669302 5.07242939,0 L5.07242939,0 C4.76343627,0 4.44481168,0.0310774972 4.11576938,0.0985432031"
                    id="Clip-11"
                    fill="#00AEEA"
                  ></path>
                  <path
                    d="M4.11576938,0.0985432031 C2.81905968,0.363882087 1.80008999,0.955141306 1.01483074,1.96594674 L1.01483074,1.96594674 C0.451881436,2.69036713 -0.465073353,4.5510831 0.279301307,6.2475604 L0.279301307,6.2475604 C1.18249686,8.30634624 3.53296746,8.3328998 3.53296746,8.3328998 L3.53296746,8.3328998 C3.53296746,8.3328998 2.90888784,8.09726156 2.34849382,7.55281528 L2.34849382,7.55281528 C1.46180935,6.69129985 1.13080145,4.41418476 2.72765905,2.81271019 L2.72765905,2.81271019 C3.2261365,2.31271653 3.91586734,1.94332704 4.61817805,1.82708146 L4.61817805,1.82708146 C7.5791656,1.338496 9.2721413,4.78809819 9.2721413,4.78809819 L9.2721413,4.78809819 C9.2721413,4.78809819 12.8990759,2.89925512 14.2559331,7.03905311 L14.2559331,7.03905311 C14.291314,6.84609726 14.3123459,6.64940424 14.3121508,6.44877736 L14.3121508,6.44877736 C14.3113631,5.01999926 12.9423192,2.86227683 10.0127813,3.11365051 L10.0127813,3.11365051 C9.035679,1.6626461 7.32520942,0.00019669302 5.07242939,0 L5.07242939,0 C4.76343627,0 4.44481168,0.0310774972 4.11576938,0.0985432031"
                    id="Fill-10"
                    fill="url(#linearGradient-6)"
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>

      <style>
        {[...Array(12)].map((_, index) => `
          @keyframes loadingDot${index} {
            0% {
              opacity: 0.3;
              transform: rotate(${index * 30}deg) translateY(-22px) scale(0.8);
            }
            50% {
              opacity: 1;
              transform: rotate(${index * 30}deg) translateY(-22px) scale(1.2);
            }
            100% {
              opacity: 0.3;
              transform: rotate(${index * 30}deg) translateY(-22px) scale(0.8);
            }
          }
        `).join('\n')}
      </style>
    </div>
  );
};

export default Loding;
