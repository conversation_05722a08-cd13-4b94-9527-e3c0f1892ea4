import React, { forwardRef, useImperativeHandle, useState, useCallback, useMemo } from 'react';
import WinBox from 'react-winbox';
import 'winbox/dist/css/winbox.min.css';
import 'winbox/dist/css/themes/modern.min.css';
import 'winbox/dist/css/themes/white.min.css';
import './index.less';

/**
 * WinboxModal 的 ref 方法接口定义
 */
export interface WinboxModalRef {
  /** 打开弹窗，可以传入新的配置参数 */
  open: (config?: Partial<WinboxModalProps>) => void;
  /** 关闭弹窗 */
  close: () => void;
  /** 获取弹窗位置信息 */
  getPosition: () => any;
  /** 获取弹窗尺寸信息 */
  getSize: () => any;
}


interface WinboxModalProps {
  /** 弹窗标题 */
  title?: string;
  /** 弹窗宽度 */
  width?: string | number;
  /** 弹窗高度 */
  height?: string | number;
  /** 弹窗X轴位置，可以是数字或'center' */
  x?: string | number;
  /** 弹窗Y轴位置，可以是数字或'center' */
  y?: string | number;
  /** 弹窗内容 - 修改类型定义 */
  children?: React.ReactElement | React.ReactElement[];
  /** 关闭回调函数 */
  onClose?: () => void;
  /** 弹窗主题：'modern' | 'white' | '' */
  theme?: 'modern' | 'white' | '';
  /** 弹窗图标 */
  icon?: string;
  /** 是否禁用最小化按钮 */
  noMin?: boolean;
  /** 是否禁用最大化按钮 */
  noMax?: boolean;
  /** 是否禁用全屏按钮 */
  noFull?: boolean;
  /** 是否禁用关闭按钮 */
  noClose?: boolean;
  /** 是否禁用移动功能 */
  noMove?: boolean;
  /** 是否禁用调整大小功能 */
  noResize?: boolean;
  /** 是否隐藏标题栏 */
  noHeader?: boolean;
  /** 距离顶部距离 */
  top?: number;
  /** 距离左侧距离 */
  left?: number;
  /** 距离右侧距离 */
  right?: number;
  /** 距离底部距离 */
  bottom?: number;
  /** 自定义类名 */
  className?: string;
  /** z-index层级 */
  index?: number;
  /** 最大化回调 */
  onMaximize?: () => void;
  /** 最小化回调 */
  onMinimize?: () => void;
  /** 还原回调 */
  onRestore?: () => void;
  /** 移动回调 */
  onMove?: () => void;
  /** 调整大小回调 */
  onResize?: (w: number, h: number) => void;
  /** 获得焦点回调 */
  onFocus?: () => void;
  /** 失去焦点回调 */
  onBlur?: () => void;
}

const WinboxModal = forwardRef<WinboxModalRef, WinboxModalProps>((props, ref) => {
  const {
    title = '弹窗',
    width = 500,
    height = 500,
    x = 'center',
    y = 'center',
    children,
    onClose,
    theme = 'modern',
    icon,
    noMin = false,
    noMax = false,
    noFull = false,
    noClose = false,
    noMove = false,
    noResize = false,
    noHeader = false,
    top,
    left,
    right,
    bottom,
    className = '',
    index = 100, // 默认层级
    onMaximize,
    onMinimize,
    onRestore,
    onMove,
    onResize,
    onFocus,
    onBlur,
  } = props;

  const [visible, setVisible] = useState(false);
  // 保存 WinBox 实例引用
  const winboxRef = React.useRef<any>(null);
  const [dynamicProps, setDynamicProps] = useState<Partial<WinboxModalProps>>({});

  useImperativeHandle(ref, () => ({
    open: (config?: Partial<WinboxModalProps>) => {
      setDynamicProps(config || {});
      setVisible(true);
    },
    close: () => setVisible(false),
    getPosition: () => winboxRef.current?.getPosition(),
    getSize: () => winboxRef.current?.getSize(),
  }));

  // 处理关闭事件
  const handleClose = useCallback(() => {
    setVisible(false);
    onClose?.();
  }, [onClose]);

  // 合并默认props和动态props
  const mergedProps = useMemo(() => ({
    title,
    width,
    height,
    x,
    y,
    icon,
    index,
    noMin,
    noMax,
    noFull,
    noClose,
    noMove,
    noResize,
    noHeader,
    top,
    left,
    right,
    bottom,
    className,
    theme,
    children,
    ...dynamicProps 
  }), [props, dynamicProps]);

  const winboxProps = useMemo(() => ({
    ref: winboxRef,
    ...mergedProps,
    className: `${mergedProps.theme} ${mergedProps.className}`.trim(),
    onclose: handleClose,
    onmaximize: onMaximize,
    onminimize: onMinimize,
    onrestore: onRestore,
    onmove: onMove,
    onresize: onResize,
    onfocus: onFocus,
    onblur: onBlur,
  }), [mergedProps, handleClose, onMaximize, onMinimize, onRestore, onMove, onResize, onFocus, onBlur]);

  return visible ? <WinBox {...winboxProps}>{children || mergedProps.children}</WinBox> : null;
});

// 添加显式的组件显示名称
WinboxModal.displayName = 'WinboxModal';

export default WinboxModal;