// 收起
.share_search_unfold {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 16px 24px 0 24px;
  margin-bottom: 16px;
  max-height: 67px;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 4px 4px;

  .unfold {
    position: absolute;
    top: 24px;
    right: 10px;
    cursor: pointer;
    z-index: 99;
  }

  .pack {
    position: absolute;
    bottom: 15px;
    right: 10px;
    cursor: pointer;
    z-index: 99;
  }

  .clientele {
    width: 348px;
    height: 40px !important;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    display: flex;
    float: left;
    margin-bottom: 16px;
    margin-right: 16px;

    .organization {
      display: flex;
      // justify-content: center;
      justify-content: space-between;
      align-items: center;
      width: 100px !important;
      height: 38px;
      border-radius: 4px 0px 0px 4px;
      border-right: 1px solid #e5e5e5;
      font-weight: 400;
      font-size: 14px;
      color: #707070;
      box-sizing: border-box;
      padding: 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    :global {
      .ant-form-item {
        width: calc(100% - 100px) !important;
      }

      .ant-pro-table {
        height: calc(100% - 32px);
      }

      .ant-input-affix-wrapper {
        border: none !important;
      }

      .ant-select {
        width: calc(100% - 100px);
        border: none;

        .ant-select-selector {
          width: 100%;
          height: 36px !important;
          border: none !important;

          .ant-select-selection-overflow {
            flex-wrap: nowrap;
            white-space: nowrap;
            /* 防止内容换行 */
            overflow: hidden;
            /* 隐藏超出盒子的内容 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
          }
        }
      }

      .ant-cascader {
        width: 100% !important;
      }

      // .ant-select-single {
      //     width: calc(100% - 100px) !important;
      //     height: 100% !important;
      //     .ant-select-selector {
      //         border: none;
      //     }
      // }
      .ant-picker,
      .ant-input {
        // width: 100%;
        width: calc(100% - 20px) !important;
        height: 100%;
        border: none !important;
      }
    }
  }

  .search_left_wrap {
    flex: 1;
    margin-right: 30px;

    .search_wrap {
      border: 1px solid #e8e8e8;
      height: 40px;
      display: flex;
      width: 348px;
      margin-right: 16px;
      border-radius: 4px;
      float: left;
      margin-bottom: 16px;

      .text_area {
        overflow-y: hidden;
        height: 100%;
        border: none;
        resize: none;
        border-radius: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        padding-top: 8px;
      }

      :global {
        .ant-select-selector {
          height: 100%;
          background-color: #fff;
          border-right: 1px solid #e5e5e5;
          border-radius: 0px;
          color: #707070;
        }

        .ant-select-selection-item {
          display: flex;
          align-items: center;
        }
      }
    }

    .clientele {
      width: 100% !important;
      height: 38px;
      border-radius: 4px;
      border: 1px solid #e5e5e5;
      display: flex;
      margin-bottom: 16px;
      margin-right: 16px;

      .organization {
        display: flex;
        padding: 0 6px;
        align-items: center;
        width: 104px;
        height: 38px;
        border-radius: 4px 0px 0px 4px;
        border-right: 1px solid #e5e5e5;
        font-weight: 400;
        font-size: 14px;
        color: #707070;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      :global {
        .ant-select {
          width: calc(100% - 104px);
          border: none;
        }

        .ant-select-single {
          width: 100% !important;
          height: 100% !important;

          .ant-select-selector {
            border: none;
          }
        }

        .ant-input-affix-wrapper {
          border: none !important;
        }

        .ant-picker,
        .ant-input {
          border: none !important;
        }
      }
    }
  }

  :global {
    .ant-table-header {
      .resizable-handler:hover .resizable-line,
      .resizable-handler:active .resizable-line {
        background-color: #1677ff !important;
      }
    }
  }
}

// 展开
.share_search_pack {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 16px 24px 0 24px;
  margin-bottom: 16px;
  max-height: 1000px;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 4px 4px;

  .unfold {
    position: absolute;
    top: 24px;
    right: 10px;
    cursor: pointer;
    z-index: 99;
  }

  .pack {
    position: absolute;
    // bottom: 15px;
    // right: 10px;
    top: 24px;
    right: 10px;
    cursor: pointer;
    z-index: 99;
  }

  .clientele {
    width: 348px;
    height: 40px !important;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    display: flex;
    float: left;
    margin-bottom: 16px;
    margin-right: 16px;

    .organization {
      display: flex;
      // justify-content: center;
      justify-content: space-between;
      align-items: center;
      // width: 146px;
      width: 100px !important;

      height: 38px;
      border-radius: 4px 0px 0px 4px;
      border-right: 1px solid #e5e5e5;
      font-weight: 400;
      font-size: 14px;
      color: #707070;
      box-sizing: border-box;
      padding: 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    :global {
      .ant-form-item {
        width: calc(100% - 100px) !important;
      }

      .ant-pro-table {
        height: calc(100% - 32px);
      }

      .ant-input-affix-wrapper {
        border: none !important;
      }

      .ant-select {
        width: calc(100% - 100px);
        border: none;

        .ant-select-selector {
          width: 100%;
          height: 36px !important;
          border: none !important;

          .ant-select-selection-overflow {
            flex-wrap: nowrap;
            white-space: nowrap;
            /* 防止内容换行 */
            overflow: hidden;
            /* 隐藏超出盒子的内容 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
          }
        }
      }

      .ant-cascader {
        width: 100% !important;
      }

      // .ant-select-single {
      //     width: calc(100% - 100px) !important;
      //     height: 100% !important;
      //     .ant-select-selector {
      //         border: none;
      //     }
      // }
      .ant-picker,
      .ant-input {
        width: 100%;
        // width: calc(100% - 100px) !important;
        height: 100%;
        border: none !important;
      }
    }
  }

  .search_left_wrap {
    flex: 1;
    margin-right: 30px;

    .search_wrap {
      border: 1px solid #e8e8e8;
      height: 91px;
      display: flex;
      width: 348px;
      margin-right: 16px;
      border-radius: 4px;
      float: left;
      margin-bottom: 16px;

      .text_area {
        // overflow-y: hidden;
        height: 100%;
        border: none;
        resize: none;
        border-radius: 0;
        width: 100%;
        padding-top: 35px;
        overflow: auto;
      }

      :global {
        .ant-select-selector {
          height: 100%;
          background-color: #fff;
          border-right: 1px solid #e5e5e5;
          border-radius: 0px;
          color: #707070;
        }

        .ant-select-selection-item {
          display: flex;
          align-items: center;
        }
      }

      // :global(.ant-select-selector) {
      //     height: 100%;

      //     :global(.ant-select-selection-item) {
      //         display: flex;
      //         align-items: center;
      //     }
      // }
    }

    .clientele {
      width: 100% !important;
      // max-width: 430px !important;
      height: 38px;
      border-radius: 4px;
      border: 1px solid #e5e5e5;
      display: flex;
      margin-bottom: 16px;
      margin-right: 16px;

      .organization {
        display: flex;
        // justify-content:center;
        padding: 0 6px;
        align-items: center;
        width: 104px;
        height: 38px;
        border-radius: 4px 0px 0px 4px;
        border-right: 1px solid #e5e5e5;
        font-weight: 400;
        font-size: 14px;
        color: #707070;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      :global {
        .ant-select {
          width: calc(100% - 104px) !important;
          border: none;
        }

        .ant-select-single {
          width: 100% !important;
          height: 100% !important;

          .ant-select-selector {
            border: none;
          }
        }

        .ant-picker,
        .ant-input {
          border: none !important;
        }
      }
    }
  }

  :global {
    .ant-table-header {
      .resizable-handler:hover .resizable-line,
      .resizable-handler:active .resizable-line {
        background-color: #1677ff !important;
      }
    }
  }
}

// 展开 keyword
.share_keyword_unfold {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24px 24px 6px 24px;
  margin-bottom: 16px;
  max-height: 1000px;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 4px 4px;

  .unfold {
    position: absolute;
    top: 24px;
    right: 10px;
    cursor: pointer;
    z-index: 99;
  }

  .pack {
    position: absolute;
    bottom: 15px;
    right: 10px;
    cursor: pointer;
    z-index: 99;
  }

  .clientele {
    width: 348px;
    height: 40px !important;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    display: flex;
    float: left;
    margin-bottom: 16px;
    margin-right: 16px;

    .organization {
      display: flex;
      // justify-content: center;
      justify-content: space-between;
      align-items: center;
      // width: 146px;
      width: 100px !important;

      height: 38px;
      border-radius: 4px 0px 0px 4px;
      border-right: 1px solid #e5e5e5;
      font-weight: 400;
      font-size: 14px;
      color: #707070;
      box-sizing: border-box;
      padding: 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    span {
      border: none !important;
      border-radius: 0 !important;
    }
    :global {
      .ant-form-item {
        width: calc(100% - 100px) !important;
      }

      .ant-pro-table {
        height: calc(100% - 32px);
      }

      .ant-select {
        width: calc(100% - 100px);
        border: none;

        .ant-select-selector {
          width: 100%;
          height: 36px !important;
          border: none !important;

          .ant-select-selection-overflow {
            flex-wrap: nowrap;
            white-space: nowrap;
            /* 防止内容换行 */
            overflow: hidden;
            /* 隐藏超出盒子的内容 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
          }
        }
      }

      // .ant-select-single {
      //     width: calc(100% - 100px) !important;
      //     height: 100% !important;
      //     .ant-select-selector {
      //         border: none;
      //     }
      // }
      .ant-picker,
      .ant-input {
        // width: 100%;
        width: calc(100% - 100px) !important;
        height: 100%;
        border: none !important;
      }
    }
  }

  .search_left_wrap {
    flex: 1;
    margin-right: 30px;

    .search_wrap {
      border: 1px solid #e8e8e8;
      height: 40px;
      display: flex;
      width: 348px;
      margin-right: 16px;
      border-radius: 4px;
      float: left;
      margin-bottom: 16px;

      .text_area {
        // overflow-y: hidden;
        height: 100%;
        border: none;
        resize: none;
        border-radius: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        padding-top: 8px;
        overflow: auto;
      }

      :global(.ant-select-selector) {
        height: 100%;

        :global(.ant-select-selection-item) {
          display: flex;
          align-items: center;
        }
      }
    }

    .clientele {
      width: 100% !important;
      height: 38px;
      border-radius: 4px;
      border: 1px solid #e5e5e5;
      display: flex;
      margin-bottom: 16px;
      margin-right: 16px;

      .organization {
        display: flex;
        padding: 0 6px;
        align-items: center;
        width: 104px;
        height: 38px;
        border-radius: 4px 0px 0px 4px;
        border-right: 1px solid #e5e5e5;
        font-weight: 400;
        font-size: 14px;
        color: #707070;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      :global {
        .ant-select {
          width: calc(100% - 104px) !important;
          border: none;
        }

        .ant-select-single {
          width: 100% !important;
          height: 100% !important;

          .ant-select-selector {
            border: none;
          }
        }

        .ant-picker,
        .ant-input {
          border: none !important;
        }
      }
    }
  }

  :global {
    .ant-cascader {
      width: 100% !important;
    }

    .ant-table {
      height: 100% !important;
    }

    .ant-table-header {
      .resizable-handler:hover .resizable-line,
      .resizable-handler:active .resizable-line {
        background-color: #1677ff !important;
      }
    }
  }
}

.tableWrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  // height: calc(100vh - 49px);
  height: 100%;
  overflow: auto;

  .customTable {
    flex: 1;
    // overflow: auto;
    position: relative;

    .table_view {
      display: block;
      position: absolute;
      top: 56px;
      right: 0;
      width: 200px;
      height: 311px;
      background: #ffffff;
      box-shadow: 0px 4px 14px 0px rgba(51, 51, 51, 0.3);
      z-index: 9;

      > div {
        width: 100%;
        height: calc(100% - 46px);
        // display: flex;
        // align-items: center;
        // width: 100%;
        // height: 26px;
        // background: #FFFFFF;
        overflow: auto;
      }

      .addView {
        display: flex;
        // justify-content: center;
        align-items: center;
        width: 100%;
        height: 46px;
        background: #ffffff;
        box-sizing: border-box;
        padding-left: 18px;
        // position: fixed;
      }

      .viewList {
        width: 100%;
        height: 56px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        padding-left: 18px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        cursor: pointer;
      }

      .viewList:hover {
        background: #f3f6ff;
      }

      // .upTriangle {
      //     position: absolute;
      //     top: 0;
      //     right: 28px;
      //     width: 0;
      //     height: 0;
      //     border-left: 10px solid transparent;
      //     border-right: 10px solid transparent;
      //     border-bottom: 15px solid red;
      //     // margin: 20px auto;
      // }
    }

    :global {
      .ant-pro-table {
        height: calc(100% - 32px);
        overflow: auto;
      }

      .ant-pro-card {
        border-radius: 6px 6px 0 0;
      }
    }

    .paging {
      position: absolute;
      width: 100%;
      height: 48px;
      padding: 0px 24px 10px;
      box-sizing: border-box;
      background-color: rgb(255, 255, 255);
      margin-top: -16px;
      z-index: 99;

      .footer_page {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fbfbfd;
        box-sizing: border-box;
        padding: 0 10px;
        border: 1px solid #e2e4e6;

        // border-bottom: 1px solid #e8e8e8;
        > div {
          // flex: 1;
        }

        :global {
          // .ant-space {
          //     width: 100%;

          //     .ant-space-item {
          //         >div {
          //             // width: 40px;
          //             display: flex;
          //             white-space: nowrap;
          //             overflow: hidden;
          //             text-overflow: ellipsis;
          //         }

          //     }
          // }

          // .ant-pagination-total-text  {
          //     color: #e8e8e8;
          // }
          .ant-pagination {
            display: flex;
            justify-content: end;
            width: 500px;
          }
        }
      }
    }
  }
}

.share_proTable {
  .toolBar {
    position: absolute;
    left: 0;
    display: flex;
    width: calc(100% - 168px);

    .view_btn {
      position: absolute;
      right: -162px;
      bottom: 7px;
      font-size: 18px;
      cursor: pointer;

      :hover {
        color: #1677ff;
      }
    }

    :global {
      .ant-space,
      .ant-btn {
        margin-right: 10px;
      }

      // style={{ position: "absolute", right: "-162px", fontSize: '18px', cursor: 'pointer' }}
    }

    > div {
      display: flex;
      align-items: center;
    }
  }

  :global {
    .rc-virtual-lis {
      flex: 1;
      overflow: hidden !important;
    }

    .ant-pro-table-list-toolbar-container {
      position: relative;
      height: 56px;
    }

    .ant-pro-table-list-toolbar-setting-items {
      position: absolute;
      right: 32px;
      gap: 0;
    }

    // .drag-handle:active {
    //     color: #FBFBFD !important;
    //     /* 鼠标按住时的背景色 */
    // }

    .ant-picker {
      width: 250px;
    }

    .ant-table-header {
      min-height: 40px;
    }

    .rc-virtual-list-holder-inner {
      .ant-table-row {
        > div {
          display: flex;
          align-items: center;
        }
      }

      // >div {
      //     >div {
      //         display: flex;
      //         align-items: center;
      //     }
      // }
    }

    // .ant-table-row {
    //     .ant-table-cell {
    // display: flex;
    // align-items: center;
    //     }
    // }

    .ant-input-outlined {
      // border: none !important;
    }

    // .ant-table-header {
    //     height: 55px !important;
    // }

    .rc-virtual-list {
      overflow: hidden !important;
    }

    .rc-virtual-list-scrollbar {
      height: 12px !important;
    }

    .rc-virtual-list-scrollbar-thumb {
      background-color: rgba(216, 225, 238, 1) !important;
    }

    // .ant-table-header {
    //     height: 55px;
    // }

    .ant-pro-table,
    .ant-pro-card {
      height: 100%;
    }

    .ant-pro-table-list-toolbar-container {
      padding-block: 12px;
    }

    .ant-btn {
      line-height: 1.5;
      height: 30px;
      box-sizing: border-box;
    }

    .operations {
      :global {
        .ant-btn {
          padding: 5px !important;
        }
      }
    }

    .ant-pro-card-body {
      height: 100% !important;
      display: flex;
      flex-direction: column;
    }

    .ant-table-wrapper {
      // flex: 1;
      overflow: hidden;
    }

    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100% !important;
    }

    .ant-spin-container {
      display: flex;
      flex-direction: column;
    }

    .ant-spin-container .ant-table {
      flex: 1;
      overflow: hidden;
    }

    .ant-spin-container .ant-table .ant-table-container {
      height: 100% !important;
      display: flex;
      flex-direction: column;
    }

    .ant-spin-container .ant-table .ant-table-container .ant-table-body {
      flex: 1;
      overflow: auto !important;
      max-height: auto !important;
    }

    .ant-table-header {
      .resizable-handler:hover .resizable-line,
      .resizable-handler:active .resizable-line {
        background-color: #1677ff !important;
      }
    }

    .ant-table-thead {
      .ant-table-cell {
        background-color: #fbfbfd !important;
        color: #556c7e !important;
        font-weight: 400;
      }
    }

    .ant-table-row {
      transition: none !important;
    }

    .ant-pagination {
      background-color: #fbfbfd;
    }

    .ant-table-cell {
      transition: none !important;
      background-color: white !important;
      box-sizing: border-box !important;
    }

    .ant-table-cell:not(.nonSelectable)[selectState*='sel'] {
      background-color: rgba(64, 113, 255, 0.1) !important;
      //            transition: none !important;
    }

    .ant-table-cell:not(.nonSelectable)[selectState*='Top'] {
      border-top: 1px solid rgba(64, 113, 255, 1) !important;
    }

    .ant-table-cell:not(.nonSelectable)[selectState*='Left'] {
      border-left: 1px solid rgba(64, 113, 255, 1) !important;
    }

    .ant-table-cell:not(
        .nonSelectable
      )[selectState*='Bottom'][selectState*='Bottom'] {
      border-bottom: 1px solid rgba(64, 113, 255, 1) !important;
    }

    .ant-table-cell:not(.nonSelectable)[selectState*='Right'] {
      border-right: 1px solid rgba(64, 113, 255, 1) !important;
    }

    .ant-table-cell {
      user-select: none !important;
    }
  }

  .become {
    background: #ffeae9;

    :global {
      .ant-table-cell-row-hover {
        // background: #FFE1E0 !important;
        background: #e34343 !important;
        color: #fff !important;
      }

      .ant-table-cell,
      .ant-table-cell-fix-right {
        background: #e34343 !important;
        color: #fff !important;
      }
    }

    :global(.ant-table-cell-fix-right) {
      background: #ffeae9;
    }

    > div {
      // border: #C63333 !important;
      border-right: 1px solid #c63333 !important;
      border-bottom: 1px solid #c63333 !important;
      // border: #333 !important;
    }
  }

  .bottom_statistics {
    position: fixed;
    bottom: -38px;
    z-index: 999999;
    left: 0;

    :global {
      .ant-table-cell-row-hover {
        // background: #FFE1E0 !important;
        background: #760404 !important;
        color: #fff !important;
      }

      .ant-table-cell,
      .ant-table-cell-fix-right {
        background: #760404 !important;
        color: #fff !important;
      }
    }

    :global(.ant-table-cell-fix-right) {
      background: #760404 !important;
    }
  }
}

.compact {
  :global {
    .ant-table-row {
      .ant-table-cell {
        height: 30px !important;
      }
    }
  }
}

.customSearch {
  .cousInput {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    overflow: hidden;

    > div {
      display: flex;
      width: 100%;
      height: 32px;
      background-color: #fff;
      border-width: 1px;
      border-style: solid;
      border-color: #d9d9d9;
      border-radius: 8px;
      padding-left: 10px;
    }
  }

  .cousCount {
    max-height: 264px;
    padding: 2px 10px;
    overflow: auto;
  }

  .cousFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 8px;
    border-top: 1px solid #f0f0f0;
    overflow: hidden;
  }

  // style={{
  //     width: '100%', height: '32px', display: 'flex',
  //     backgroundColor: '#ffffff', borderWidth: '1px',
  //     borderStyle: 'solid', borderColor: '#d9d9d9',
  //     borderRadius: '8px',
  //     paddingLeft: '10px',
  // }}

  :global {
    .ant-input-outlined {
      border: none !important;
    }

    .ant-input-outlined:focus {
      box-shadow: none;
    }
  }

  width: 180px;

  svg {
    color: #d9d9d9;
  }
}

.prompt_box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 160px;
  // height: 208px;
  background-color: #fff;
  position: absolute;
  border: 1px solid #ebebeb;
  border-radius: 5px;
  z-index: 999;
  box-sizing: border-box;
  padding: 5px;

  > div {
    box-sizing: border-box;
    padding: 8px 0;
  }

  :global {
    .ant-image .ant-image-mask:hover {
      opacity: 0;
    }
  }

  > div {
    cursor: pointer;
    display: flex;
    // justify-content: center;
    align-items: center;
    width: 100%;
    height: 25%;
    box-sizing: border-box;
    padding-left: 5px;
    font-weight: 400;
    font-size: 16px;
    color: #333333;

    svg {
      margin-right: 5px;
    }
  }

  > div:hover {
    background: #f0f5fa;
  }

  > span {
    display: none !important;
  }
}
.clear {
  :global {
    .ant-input-suffix {
      position: absolute;
      top: 9px;
      right: 9px;
    }
  }
}
