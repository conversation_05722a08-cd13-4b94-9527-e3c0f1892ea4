import { Button, Input, Modal, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import waybillImg from '@/assets/icon/muns.png';
import { checkBlnoExistsAPI, checkExistsAPI } from '@/services/intermodal';
const { TextArea } = Input;
const WaybillNoBatchModal = (props: any) => {
  const { item, getTextValue, batchValue, batchCallback, desc } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState('');
  const [nonExistsData, setNonExistsData] = useState([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    setValue(batchValue);
  }, [batchValue]);
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      let res: any;
      if (desc === '运单号') {
        res = await checkExistsAPI({
          nos: value,
        });
      } else {
        res = await checkBlnoExistsAPI({
          nos: value,
        });
      }
      if (res?.status) {
        if (!res?.data?.nonExists?.length) {
          setIsModalOpen(false);
          batchCallback(value);
          getTextValue({ target: { value } }, item);
          setLoading(false);
        }
        setNonExistsData(res?.data?.nonExists);
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
    }
    // setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const onChange = (e: any) => {
    setValue(e?.target?.value);
    // batchCallback(e?.target?.value);
    // getTextValue(e, item);
  };
  // 清空
  const empty = () => {
    setNonExistsData([]);
    setValue('');
    batchCallback('');
    getTextValue({ target: { value: '' } }, item);
  };
  return (
    <>
      <a
        onClick={showModal}
        style={{ width: '56px', display: 'flex', alignItems: 'center' }}
      >
        批量
      </a>
      <Modal
        width={550}
        title="批量添加"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Space
            key="btn"
            style={{ display: 'flex', justifyContent: 'center' }}
          >
            {nonExistsData?.length ? (
              <Button
                key="back"
                onClick={() => {
                  setIsModalOpen(false);
                  setNonExistsData([]);
                  batchCallback(value);
                  getTextValue({ target: { value } }, item);
                }}
              >
                关闭
              </Button>
            ) : (
              <>
                <Button key="back" onClick={empty}>
                  清空
                </Button>
                <Button key="back" onClick={handleCancel}>
                  取消
                </Button>
                <Button
                  key="submit"
                  type="primary"
                  onClick={handleOk}
                  loading={loading}
                >
                  确定
                </Button>
              </>
            )}
          </Space>,
        ]}
      >
        <div style={{ display: 'flex' }}>
          {nonExistsData?.length ? (
            <div>
              <div
                style={{
                  width: '500px',
                  marginBottom: '10px',
                  color: 'rgb(204, 0, 0)',
                }}
              >
                错误的单号：
              </div>
              <div
                style={{ height: '400px', overflowY: 'auto', width: '500px' }}
              >
                {nonExistsData?.map((item: any, index: any) => (
                  <div key={index} className="text-ellipsis">
                    {item}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <TextArea
              value={value}
              style={{ marginRight: '20px' }}
              rows={20}
              placeholder={`请批量输入${desc}号`}
              onChange={onChange}
            />
          )}
        </div>
      </Modal>
    </>
  );
};
export default WaybillNoBatchModal;
