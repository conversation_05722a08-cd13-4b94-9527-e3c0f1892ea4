import {
  Button,
  Cascader,
  DatePicker,
  Dropdown,
  Input,
  Select,
  Space,
} from 'antd';
import { ProFormCascader, ProFormSelect } from '@ant-design/pro-components';
// import { useDispatch, useSelector } from 'react-redux';
import { batch, tableSearch } from '@/store/actions/waybill';
import { formatTime, formatTimes } from '@/utils/format';
import _ from 'lodash';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import {
  getAddressInformationAPI,
  getClientListAPI,
  getModuleList,
  getSearchAddressAPI,
  getTagListAPI,
  getWarehouseListAPI,
} from '@/services/home/<USER>';
import {
  getProviderType,
  getUserListAPI,
  shippingAgencyListAPI,
  shippingListAPI,
} from '@/services/booking';
import {
  getBankAccountListAPI,
  getFinanceSubjectListAPI,
  getIDCodeRule,
  getProductChannelList2,
  getProductList,
} from '@/services/productAndPrice/api';
import {
  getBrokerList,
  getCustomGroupAPI,
  getLabelListAPI,
} from '@/services/customer/CustomerHome';
import search_arrows from '@/assets/icon/箭头.png';
import { ProviderType } from '@/shared/ProviderTypeFn';
import { CostSubjectType, CounterpartyType } from '@/shared/EnumerationTypes';
import usePermissionFiltering from '@/hooks/usePermissionFiltering';
import { getFeeTypeAPI, getServiceProviderAPI } from '@/services/financeApi';
const { TextArea } = Input;
const { RangePicker } = DatePicker;
import styles from './index.less';
import { carrierDriverAPI } from '@/services/carriers';
import { getListProviderAndProductAPI } from '@/services/intermodal';
import dayjs from 'dayjs';
import { getAllGroupListData } from '@/services/authority';
// import BatchModal from './BatchModal';
import moment from 'moment';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
// import BatchModal from '@/components/MrTable/BatchModal';
import BatchModal from './BatchModal';
import WaybillNoBatchModal from './WaybillNoBatchModal';
const CustomizeComponent = memo((props: any): any => {
  const {
    refresh,
    keyID,
    getStart,
    getFirst,
    tableFilter,
    callBack,
    autoQueryEnabled,
  } = props;
  // const tableFilter = useSelector((state: any) => state?.tableFilter[keyID]);
  const [flag, setFlag] = useState(false);
  // const value = useSelector((state: any) => state?.tableFilter.batchValue);
  // const dispatch = useDispatch();
  const [valueEnum, setValueEnum] = useState<any>({});
  const [toList] = usePermissionFiltering();
  const searchRef = useRef<any>(null);
  const waybillSearchRef = useRef<any>(null);
  const blnoSearchRef = useRef<any>(null);
  const [batchValue, setBatchValue] = useState('');
  const [waybillbatchValue, setWaybillbatchValue] = useState('');
  const [blnobatchValue, setBlnobatchValue] = useState('');
  // 手动查询的条件存储
  const [manualCondition, setManualCondition] = useState({});
  const newCounterpartyType = useMemo(() => {
    return toList(CounterpartyType);
  }, []);
  const [options, setOptions] = useState(newCounterpartyType);
  const [ageOptions, setAgeOptions] = useState([
    {
      value: ProviderType.FreightForwarder.getCode(),
      label: ProviderType.FreightForwarder.getDesc(),
      isLeaf: false,
    },
    {
      value: ProviderType.DropShipping.getCode(),
      label: ProviderType.DropShipping.getDesc(),
      isLeaf: false,
    },
    {
      value: ProviderType.AirTransport.getCode(),
      label: ProviderType.AirTransport.getDesc(),
      isLeaf: false,
    },
  ]);
  const [termQueryState, setTermQueryState] = useState(false);
  // useEffect(() => {
  //   dispatch(batch(null));
  // }, [keyID])
  // 代理商单独处理
  const getAgentData = async (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    try {
      const { status, data } = await getServiceProviderAPI({
        counterpartyType: targetOption.value,
      });
      if (status) {
        targetOption.children = data?.list?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
        setOptions([...options]);
      }
    } catch (error) {}
  };
  // 代理仓库间转运 服务商类型
  const getProviderData = async (selectedOptions: any) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    try {
      const { status, data } = await getListProviderAndProductAPI({
        providerType: targetOption.value,
      });
      if (status) {
        targetOption.children = data?.providerList?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
        setAgeOptions([...ageOptions]);
      }
    } catch (error) {}
  };

  // 查询条件不同的渲染
  const cutRender = (item: any) => {
    if (item?.multistage) {
      return (
        <Dropdown
          menu={{
            items: item.multistage,
            onClick: ({ key }) => {
              searchCut(item, key);
            },
          }}
        >
          <div style={{ width: '100px' }}>
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                <div className={styles.organization}>
                  {
                    item.multistage?.filter(
                      (e: any) => e?.key === item?.defaultKey,
                    )[0]?.label
                  }
                  &nbsp;&nbsp;
                  <img src={search_arrows} style={{ width: '10px' }} />
                </div>
              </Space>
            </a>
          </div>
        </Dropdown>
      );
    } else {
      return <div className={styles.organization}>{item?.desc}</div>;
    }
  };
  // 查询条件切换字段的情况
  const searchCut = (item: any, key: string) => {
    for (const keys in tableFilter) {
      if (keys === item?.userSoleId) {
        item.defaultKey = key;
      }
    }
    // dispatch(tableSearch(keyID, tableFilter));
    callBack(tableFilter);
    getEditValue(item);
  };
  const formSelectData = (ele: any): any => {
    return (
      <div
        className={styles.clientele}
        style={{
          width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
        }}
      >
        {cutRender(ele)}
        <ProFormSelect
          fieldProps={{
            filterOption:
              ele?.type === 'feeType'
                ? (input, option: any) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                : false,
            onChange: (e: any) => {
              editParameter(e, ele);
              if (ele?.onChange) {
                ele?.onChange(Array.isArray(e) ? e?.join(',') : e);
              }
            },
            disabled: ele?.disabled ?? false,
            autoClearSearchValue: false,
            defaultValue: ele?.defaultValue,
            onSearch: (e) => {
              searchShake(e, ele);
            },
          }}
          mode={ele?.multi ? 'multiple' : (undefined as any)}
          showSearch
          placeholder="请选择，支持模糊搜索"
          debounceTime={1000}
          style={{ width: '100%' }}
          options={valueEnum[ele?.userSoleId]}
        />
      </div>
    );
  };
  useEffect(() => {
    if (tableFilter) {
      if (Object.values(tableFilter)?.length) {
        Object.values(tableFilter).forEach((item: any) => {
          getEditValue(item);
        });
      }
    }
  }, []);
  // 修改值
  const getEditValue = (item: any, keywords?: string) => {
    requestData(item, keywords).then((res: any) => {
      setValueEnum((data: any) => {
        return {
          ...data,
          [item?.userSoleId]: res,
        };
      });
    });
  };
  const requestData = async (ele: any, keyWords?: string) => {
    // 地址
    if (ele?.type === 'address') {
      const { status, data } = await getAddressInformationAPI({
        token: keyWords,
      });
      if (status) {
        return data.list.map((item: any) => {
          return {
            label: `${item.country.replaceAll('CN', '中国')} - ${
              item.provinceShortName
            } / ${item.province} - ${item.city} ${item.county ? '-' : ''} ${
              item.county
            } / ${item.zipCode}`,
            value: item?.id,
          };
        });
      }
      // 仓库
    } else if (ele?.type === 'warehouse') {
      const { status, data } = await getWarehouseListAPI({
        keyword: keyWords,
        type: ele?.agentWarehouse ?? 0,
        isReportTag: ele?.isReportTag,
      });
      if (status) {
        return data?.list?.map((item: any) => ({
          value: item?.id,
          label: `【${item.name}】 - ${item.city} - ${item.county},${item.street}`,
        }));
        // return newData(data)
      }
      // 服务商
    } else if (ele?.type === 'provider') {
      const { status, data } = await shippingAgencyListAPI({
        type: ele?.providerType,
        keyword: keyWords,
      });
      if (status) {
        return newData(data);
      }
      // 产品
    } else if (ele?.type === 'product') {
      const { status, data } = await getProductList({
        // keyword: keyWords,
        condition: {
          keyword: {
            value: keyWords,
          },
          category: {
            value: ele?.category,
          },
        },
        len: 20,
        start: 0,
      });
      if (status) {
        return newData(data);
      }
      // 客户
    } else if (ele?.type === 'client') {
      const { status, data } = await getClientListAPI({
        keyword: keyWords,
      });
      if (status) {
        return newData(data);
      }
      // 代理商
    } else if (ele?.type === 'providerId') {
      const { status, data } = await getBrokerList({
        keyword: keyWords ?? ele?.keyword,
        enabled: 1,
      });
      if (status) {
        return newData(data);
      }
      // 客户代码
    } else if (ele?.type === 'clientCode') {
      const { status, data } = await getClientListAPI({
        keyword: keyWords,
      });
      if (status) {
        return data.list.map((item: any) => {
          return {
            label: item?.code,
            value: item?.code,
          };
        });
      }
      // 客户分组
    } else if (ele?.type === 'clientGroup') {
      const { status, data } = await getCustomGroupAPI({
        keyword: keyWords,
      });
      if (status) {
        return newData(data);
      }
    }
    // ID编码规则
    else if (ele?.type === 'idRule') {
      const { status, data } = await getIDCodeRule({
        keyword: keyWords,
      });
      if (status) {
        return newData(data);
      }
    } else if (ele?.type === 'clientTags') {
      const { status, data } = await getLabelListAPI({
        keyword: keyWords,
      });
      if (status) {
        return data.map((item: any) => {
          return {
            label: item?.tag,
            value: item?.tag,
          };
        });
      }

      // 城市
    } else if (ele?.type === 'city') {
      const { status, data } = await getSearchAddressAPI({
        keyword: keyWords,
      });
      if (status) {
        return data.list?.map((item: any) => {
          return {
            label: `${item.country === 'CN' ? '中国' : item.country} - ${
              item.provinceShortName
            } / ${item.province} - ${item.city} -${item.county}/ ${
              item.zipCode
            }`,
            value: item?.id,
          };
        });
      }
      // 服务商类型
    } else if (ele?.type === 'providerType') {
      const { status, data } = await getProviderType({
        keyword: keyWords,
      });
      if (status) {
        return newData(
          data?.list?.map((fid: any) => ({
            ...fid,
            id: fid?.type + '',
            name: fid?.desc,
          })),
        );
      }
      // FBA仓库
    } else if (ele?.type === 'FBA') {
      const { status, data } = await getWarehouseListAPI({
        keyword: keyWords,
        type: 1,
      });
      if (status) {
        return data.list.map((item: any) => {
          return {
            label: item?.name,
            value: item?.name,
          };
        });
      }
      // 财务主体
    } else if (ele?.type === 'organization') {
      const { status, data } = await getFinanceSubjectListAPI({
        keyword: keyWords,
      });
      if (status) {
        return data?.list?.map((item: any) => {
          return {
            label: item?.name,
            value: item?.id,
          };
        });
      }
      // 岗位人员
    } else if (ele?.type === 'user') {
      const { status, data } = await getUserListAPI({
        keyword: keyWords,
        start: 0,
        len: 20,
        dutyId: ele?.defaultKey ?? ele?.dutyId,
      });
      if (status) {
        return data.list.map((item: any) => {
          return {
            label: item?.user?.name,
            value: item?.user?.id,
          };
        });
      }
      // 车队
    } else if (ele?.type === 'overseas') {
      const { status, data } = await getWarehouseListAPI({
        keyword: keyWords,
        type: 3,
      });
      if (status) {
        return data?.list?.map((item: any) => {
          return {
            label: item?.name,
            value: item?.id,
          };
        });
      }
      // 岗位人员
    } else if (ele?.type === 'fullName') {
      const { status, data } = await shippingAgencyListAPI({
        keyword: keyWords,
        type: ProviderType.Trailer.getCode(),
      });
      if (status) {
        return data.list.map((item: any) => {
          return {
            label: item?.fullName,
            value: item?.id,
          };
        });
      }
      // 收款银行
    } else if (ele?.type === 'gatheringBank') {
      const { status, data } = await getBankAccountListAPI({
        keyword: keyWords,
        enabled: 1,
        len: 200,
      });
      if (status) {
        return data.list.map((item: any) => {
          return {
            label: `${item?.accountCode} ${item?.account}`,
            value: item?.id,
          };
        });
      }
      // 账目
    } else if (ele?.type === 'feeName') {
      const { status } = await getBankAccountListAPI({
        keyword: keyWords,
        enabled: 1,
      });
      if (status) {
        return CostSubjectType;
      }
      // 船司
    } else if (ele?.type === 'shipping') {
      const { status, data } = await shippingListAPI({
        keyword: keyWords,
        type: 2,
        len: 100,
        start: 0,
      });
      if (status) {
        return data?.list?.map((item: any) => ({
          value: item?.provider?.id,
          label: item?.name,
        }));
      }
      // 司机
    } else if (ele?.type === 'driver') {
      const { status, data } = await carrierDriverAPI({
        keyword: keyWords,
      });
      if (status) {
        return data?.list?.map((item: any) => ({
          value: item.uid,
          label: item?.user?.name,
        }));
      }
    }
    // 日志模块
    else if (ele?.type === 'logModule') {
      const { status, data } = await getModuleList({
        keyword: keyWords,
      });
      if (status) {
        return data?.list?.map((item: any) => ({
          label: item?.desc,
          value: item?.name,
        }));
      }
    }
    // 渠道
    else if (ele?.type === 'channel') {
      const { status, data } = await getProductChannelList2({
        start: 0,
        len: 100,
        columnsFilter: {},
        condition: {
          keyword: {
            // type: 'search',
            value: keyWords,
            termQuery: true,
          },
          type: {
            value: ele?.channelId,
          },
        },
        counterpartyType: 'FreightForwarder',
      });
      if (status) {
        return data?.list?.map((item: any) => ({
          label: item?.name,
          value: item?.id,
        }));
      }
    }
    // 费用类型
    else if (ele?.type === 'feeType') {
      const { status, data } = await getFeeTypeAPI({
        keyword: keyWords,
        counterpartyType: ele?.argument,
      });
      if (status) {
        return (
          data?.map((item: any) => {
            return {
              label: Object.values(item)[0],
              value: Object.keys(item)[0],
            };
          }) || []
        );
      }
    }
    // 自定义类型
    else if (ele?.type === 'custom') {
      const res = await ele?.generate(keyWords);
      return res;
    }
    // 新补的类型
    else if (ele?.type == 'UserTag') {
      const { status, data } = await getTagListAPI({
        keyword: keyWords,
        groupId: ele?.groupId,
      });
      if (status) {
        return data?.list?.map((item: any) => ({
          value: item.id,
          label: item?.name,
        }));
      }
    }

    return [];
  };
  // 修改参数
  const editParameter = (id: any, data: any) => {
    // getStart(0);
    // 时间 改两个字段的值分开写
    if (data?.type === 'dateTimeRange') {
      for (const key in tableFilter) {
        // if (data?.type === tableFilter[key]?.type) {
        //     tableFilter[key].startTime = id ? formatTimes(id[0]) + ' 00:00:00' : ''
        //     tableFilter[key].endTime = id ? formatTimes(id[1]) + ' 23:59:59' : ''
        // }
        if (data?.type === tableFilter[key]?.type) {
          if (data?.userSoleId === tableFilter[key]?.userSoleId) {
            tableFilter[key].startTime = id
              ? formatTimes(id[0]) + ' 00:00:00'
              : '';
            tableFilter[key].endTime = id
              ? formatTimes(id[1]) + ' 23:59:59'
              : '';
          }
        }
      }
    } else if (
      [
        'address',
        'warehouse',
        'provider',
        'product',
        'city',
        'dateTime',
        'date',
        'client',
        'fullName',
        'clientCode',
        'providerType',
        'gatheringBank',
        'feeName',
        'user',
        'organization',
        'FBA',
        'clientTags',
        'providerId',
        'shipping',
        'driver',
        'clientGroup',
        'custom',
        'idRule',
        'userTag',
        'UserTag',
        'logModule',
        'feeType',
        'channel',
        'overseas',
      ].includes(data?.type)
    ) {
      for (const key in tableFilter) {
        if (data?.userSoleId === tableFilter[key]?.userSoleId) {
          //  日期
          if (['dateTime', 'date'].includes(data?.type)) {
            tableFilter[key].value = id ? formatTime(id) : '';
          } else {
            if (
              [
                'address',
                'city',
                'product',
                'provider',
                'warehouse',
                'client',
                'providerType',
                'organization',
                'idRule',
                'clientCode',
                'providerId',
                'fullName',
                'gatheringBank',
                'feeName',
                'user',
                'FBA',
                'shipping',
                'driver',
                'custom',
                'clientTags',
                'userTag',
                'UserTag',
                'logModule',
                'feeType',
                'channel',
                'overseas',
              ].includes(data?.type)
            ) {
              tableFilter[key].value = tableFilter[key]?.multi
                ? id?.join(',')
                : id;
            }
          }
        }
      }
      // 这里还有一种情况，比如出现多个 select 等等，就不能以 类型 去对比改 value 只有唯一的 desc 没有其他唯一， 一切兼容后端
    } else if (
      ['search', 'text', 'keyword', 'select', 'number', 'textarea'].includes(
        data?.type,
      )
    ) {
      for (const key in tableFilter) {
        if (data?.userSoleId === tableFilter[key]?.userSoleId) {
          if (data?.type === 'select') {
            if (tableFilter[key]?.multi && selectUnique(data, id)?.unique) {
              if (id?.length > 1 && id[0] === selectUnique(data, id)?.value) {
                tableFilter[key].value = id
                  ?.filter((fid: any) => fid !== selectUnique(data, id)?.value)
                  ?.join(',');
              } else {
                tableFilter[key].value = selectUnique(data, id)?.value;
              }
            } else {
              tableFilter[key].value = tableFilter[key]?.multi
                ? id?.join(',')
                : id;
            }
          } else {
            // type不是 select 单选或者多选
            tableFilter[key].value = tableFilter[key]?.multi
              ? id?.join(',')
              : id;
          }
        }
      }
    }
    // dispatch(tableSearch(keyID, tableFilter));
    if (!autoQueryEnabled) {
      callBack(tableFilter);
    } else {
      setManualCondition(tableFilter);
    }

    // 更新最新值
    // setSearchParameter({ ...searchParameter })
    // refresh();
  };
  // 一样的渲染字段，统一写
  const newData = (data: any) => {
    return data?.list?.map((item: any) => {
      return {
        label: item?.name,
        value: item?.id,
      };
    });
  };
  //  修改值防抖
  const getTextValue = _.debounce((e: any, item: any) => {
    editParameter(e?.target?.value, item);
    if (item?.onChange) {
      item?.onChange(e?.target?.value);
    }
  }, 500);
  //  修改值防抖
  const searchShake = _.debounce((e: any, item: any) => {
    getEditValue(item, e);
  }, 300);
  const selectUnique = (data: any, id: any) => {
    let result;
    if (!data?.multi) {
      result = data?.range?.filter(
        (item: any) =>
          id?.split(',')?.filter(Boolean).includes(item?.value) && item?.unique,
      );
    } else {
      result = data?.range?.filter(
        (item: any) =>
          id?.filter(Boolean).includes(item?.value) && item?.unique,
      );
    }

    if (result?.length) {
      return result[0];
    }
    return false;
  };
  const batchCallback = (value: string) => {
    setBatchValue(value);
  };
  const waybillBatchCallback = (value: string) => {
    setWaybillbatchValue(value);
  };
  const blnoBatchCallback = (value: string) => {
    setBlnobatchValue(value);
  };
  // 批量的
  const getBatch = (type: string) => {
    if (type === '运单号') {
      return waybillbatchValue;
    } else if (type === '提单号' || type === 'blno') {
      return blnobatchValue;
    } else {
      return undefined;
    }
  };
  const CustomizeComponents = (item: any) => {
    if (['search', 'keyword'].includes(item?.type)) {
      searchRef.current = item;
      // setTermQueryState(item?.termQuery)
      return (
        <div className={styles.search_left_wrap}>
          <div
            className={styles.search_wrap}
            style={{
              width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
            }}
          >
            {item?.type === 'search' && (
              <Select
                // defaultValue="精确"
                bordered={false}
                style={{
                  height: '100%',
                  border: 'none',
                  backgroundColor: '#f7f7f7',
                }}
                value={termQueryState}
                onChange={(e: any) => {
                  for (const key in tableFilter) {
                    if (tableFilter[key]?.userSoleId === 'keyword') {
                      tableFilter[key].termQuery = e;
                    }
                  }
                  setTermQueryState(e);
                  // dispatch(tableSearch(keyID, tableFilter));
                  callBack(tableFilter);

                  // getStart(0);
                  // refresh();
                }}
                options={[
                  { label: '精确', value: true },
                  { label: '批量', value: false },
                ]}
              ></Select>
            )}
            <TextArea
              className={styles.text_area}
              value={batchValue}
              id="search_text"
              onChange={(e: any) => {
                // dispatch(batch(e?.target?.value));
                setBatchValue(e?.target?.value);
                getTextValue(e, item);
              }}
              placeholder={item?.placeholder ?? '请输入关键词搜索'}
              onPressEnter={() => {}}
            />
            <BatchModal
              batchValue={batchValue}
              item={searchRef.current}
              batchCallback={batchCallback}
              getTextValue={getTextValue}
            />
            <Button
              style={{ height: '100%', color: '#537ffd', border: 'none' }}
              className={styles.search_back}
              onClick={() => {
                if (keyID == 'waybillTable') {
                  // getFirst();
                }
                // refresh();
              }}
            >
              搜索
            </Button>
          </div>
        </div>
      );
    } else if (item?.type === 'text') {
      if (item?.desc === '运单号') {
        waybillSearchRef.current = item;
      }
      if (item?.defaultKey === 'blno' || item?.desc === '提单号') {
        blnoSearchRef.current = item;
      }
      return (
        <div
          className={styles.clientele}
          style={{
            width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
          }}
        >
          {cutRender(item)}
          <Input
            placeholder="请输入关键字"
            className={styles.clear}
            value={getBatch(item?.desc || item?.defaultKey)}
            // ant-input-suffix
            type="text"
            defaultValue={item?.value}
            allowClear
            onChange={(e: any) => {
              if (item?.desc === '运单号') {
                setWaybillbatchValue(e?.target?.value);
              }
              if (item?.defaultKey === 'blno' || item?.desc === '提单号') {
                setBlnobatchValue(e?.target?.value);
              }
              getTextValue(e, item);
            }}
          />
          {item?.desc === '运单号' && (
            <WaybillNoBatchModal
              batchValue={waybillbatchValue}
              item={waybillSearchRef.current}
              batchCallback={waybillBatchCallback}
              getTextValue={getTextValue}
              desc="运单号"
            />
          )}
          {(item?.defaultKey === 'blno' || item?.desc === '提单号') && (
            <WaybillNoBatchModal
              batchValue={blnobatchValue}
              item={blnoSearchRef.current}
              batchCallback={blnoBatchCallback}
              getTextValue={getTextValue}
              desc="提单号"
            />
          )}
          {/* {item?.defaultKey == 'blno' ||
            (item?.desc === '提单号' && (
            
            ))} */}
        </div>
      );
    } else if (item?.type === 'select') {
      return (
        <div
          className={styles.clientele}
          style={{
            width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
          }}
        >
          <div className={styles.organization} style={{ width: '136px' }}>
            {item?.desc}
          </div>
          <Select
            showSearch
            disabled={item?.disabled}
            mode={item?.multi ? 'multiple' : (undefined as any)}
            filterOption={(input, option: any) => true}
            options={item.range}
            placeholder="请选择"
            defaultValue={item?.defaultValue}
            // value={item?.value}
            allowClear
            onChange={(e) => {
              editParameter(e, item);
            }}
          />
        </div>
      );
    } else if (item?.type === 'dateTimeRange') {
      return (
        <div
          className={styles.clientele}
          style={{
            width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
          }}
        >
          {cutRender(item)}
          <RangePicker
            defaultValue={
              item?.startTime && item?.endTime
                ? [dayjs(item?.startTime), dayjs(item?.endTime)]
                : (['', ''] as any)
            }
            showTime={
              item?.showTime
                ? {
                    defaultValue: [
                      dayjs('00:00:00', 'HH:mm:ss'),
                      dayjs('23:59:59', 'HH:mm:ss'),
                    ],
                  }
                : false
            }
            onChange={(e: any) => {
              editParameter(e, item);
            }}
          />
        </div>
      );
    } else if (item?.type === 'dateTime') {
      return (
        <div className={styles.clientele}>
          {cutRender(item)}
          <DatePicker
            showTime={
              item?.showTime
                ? {
                    defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
                  }
                : false
            }
            defaultValue={item?.value ? moment(item?.value) : undefined}
            style={{ width: '100%' }}
            onChange={(e: any) => {
              editParameter(e, item);
            }}
          />
        </div>
      );
      //   // 代理商
      // } else if (item?.type == 'providerId') {
      //   return (
      //     <div
      //       className={styles.clientele}
      //       style={{
      //         width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
      //       }}
      //     >
      //       <div className={styles.organization} style={{ width: '136px' }}>
      //         {item?.desc}
      //       </div>
      //       <ProFormCascader
      //         className={styles.ProFormCascader}
      //         fieldProps={{
      //           options: options,
      //           onChange: (e: any) => {
      //             if (item?.type == 'providerId') {
      //               editParameter(e ? e[1] : '', item);
      //             } else {
      //               editParameter(e, item);
      //             }
      //           },
      //           loadData: (e) => {
      //             getAgentData(e);
      //           },
      //         }}
      //         // label="代理商"
      //       />
      //     </div>
      //   );
    } else if (item?.type == 'userTag') {
      //业务标签
      return (
        <div className={styles.clientele}>
          <div className={styles.organization} style={{ width: '136px' }}>
            {item?.desc}
          </div>
          <ProFormCascader
            className={styles.ProFormCascader}
            fieldProps={{
              options: options,
              multiple: true,
              maxTagCount: 'responsive',
              showCheckedStrategy: Cascader.SHOW_CHILD,
              onChange: (e: any) => {
                // console.log('e', e);
                const str = e.map((ite: any) => ite[1]).join(',');
                editParameter(str, item);
              },
            }}
            request={async ({ keyWords }) => {
              const { status, data } = await getAllGroupListData({});
              // console.log('data', data);
              if (status) {
                return data.list.map((item: any) => {
                  return {
                    label: item?.group?.name,
                    children: item?.list?.map((item2: any) => {
                      return {
                        label: item2?.name,
                        value: item2?.id,
                      };
                    }),
                    value: item.group?.id,
                  };
                });
              }
              return [];
            }}
            // label="代理商"
          />
        </div>
      );
    } else if (item?.type == 'agentIdType') {
      // 代理仓库转运 类型
      return (
        <div
          className={styles.clientele}
          style={{
            width: keyID == 'BOL/PreplanCabin/waybill' ? '280px' : '348px',
          }}
        >
          <div className={styles.organization} style={{ width: '136px' }}>
            {item?.desc}
          </div>
          <ProFormCascader
            className={styles.ProFormCascader}
            fieldProps={{
              options: ageOptions,
              onChange: (e: any) => {
                editParameter(e, item);
              },
              loadData: (e) => {
                getProviderData(e);
              },
            }}
            // label="代理商"
          />
        </div>
      );
    } else if (item?.type === 'date') {
      return (
        <DatePicker
          onChange={(e: any) => {
            editParameter(e, item);
          }}
        />
      );
      // 收件地址
    } else if (item?.type === 'address') {
      return formSelectData(item);
      // 揽件仓库
    } else if (item?.type === 'warehouse') {
      return formSelectData(item);
    }
    // 服务商
    else if (item?.type === 'provider') {
      return formSelectData(item);
    }
    // 销售产品
    else if (item?.type === 'product') {
      return formSelectData(item);
    }
    // 城市
    else if (item?.type === 'city') {
      return formSelectData(item);
    } // 代理商
    else if (item?.type === 'providerId') {
      return formSelectData(item);
    }
    // 客户
    else if (item?.type === 'client') {
      return formSelectData(item);
    }
    // 客户代码
    else if (item?.type === 'clientCode') {
      return formSelectData(item);
    }
    // 客户标签
    else if (item?.type === 'clientTags') {
      return formSelectData(item);
    }
    // ID编码规则
    else if (item?.type === 'idRule') {
      return formSelectData(item);
    }
    // 客户分组
    else if (item?.type === 'clientGroup') {
      return formSelectData(item);
    }
    // 服务商类型
    else if (item?.type === 'providerType') {
      return formSelectData(item);
    }
    // FBA仓库
    else if (item?.type === 'FBA') {
      return formSelectData(item);
    }
    // 财务主体
    else if (item?.type === 'organization') {
      return formSelectData(item);
    } else if (item?.type === 'overseas') {
      return formSelectData(item);
    }
    // 岗位人员
    else if (item?.type === 'user') {
      return formSelectData(item);
    }
    // 车队
    else if (item?.type === 'fullName') {
      return formSelectData(item);
    }
    // 收款银行
    else if (item?.type === 'gatheringBank') {
      return formSelectData(item);
    } // 账目
    else if (item?.type === 'feeName') {
      return formSelectData(item);
      // 船司
    } else if (item?.type === 'shipping') {
      return formSelectData(item);
    } // 司机
    else if (item?.type === 'driver') {
      return formSelectData(item);
    }
    // 日志模块
    else if (item?.type === 'logModule') {
      return formSelectData(item);
    }
    // 费用类型
    else if (item?.type === 'feeType') {
      return formSelectData(item);
    }
    // 渠道
    else if (item?.type === 'channel') {
      return formSelectData(item);
    }
    // 自定义 type(张要求这样写的)
    else if (item?.type === 'custom') {
      return formSelectData(item);
    }
    // 新补的类型
    else if (item?.type === 'UserTag') {
      return formSelectData(item);
    } else if (item?.type === 'button') {
      return (
        <div
          style={{
            // width: '300px',
            height: '40px',
            marginBottom: '16px',
            textAlign: 'end',
            paddingRight: '40px',
          }}
        >
          <Button
            type="primary"
            onClick={() => {
              callBack(manualCondition);
            }}
          >
            查询
          </Button>
        </div>
      );
    }
    return <div></div>;
  };
  const flagStyleClass = () => {
    if (!tableFilter) return;
    return Object.values(tableFilter)?.some(
      (item: any) => item?.type === 'search',
    )
      ? flag
        ? styles['share_search_pack']
        : styles['share_search_unfold']
      : flag
      ? styles['share_keyword_unfold']
      : styles['share_search_unfold'];
  };

  // return Object.values(tableFilter)?.length
  //   ? Object.values(tableFilter).map((item: any, index: number) => (
  //       <div key={index}>{CustomizeComponents(item)}</div>
  //     ))
  //   : '';
  // console.log(tableFilter, 'tableFiltertableFilter');
  const modifyArray = (arr: any) => {
    if (autoQueryEnabled) {
      arr.length >= 3
        ? arr.splice(2, 0, { type: 'button' })
        : arr.push({ type: 'button' });
    }
    return arr;
  };
  return (
    <div className={flagStyleClass()}>
      {tableFilter &&
        (Object.values(tableFilter)?.length >= 3 ||
          Object.values(tableFilter)
            ?.map((item: any) => item?.type)
            .includes('search')) && (
          <>
            {!flag && (
              <div
                className={styles.unfold}
                onClick={() => {
                  setFlag(true);
                }}
              >
                {/* {autoQueryEnabled && (
                  <Button
                    type="primary"
                    style={{ marginRight: '15px' }}
                    onClick={() => {
                      callBack(manualCondition);
                    }}
                  >
                    查询
                  </Button>
                )} */}
                <a>展开</a> <DownOutlined style={{ color: '#1677ff' }} />
              </div>
            )}
            {flag && (
              <div
                className={styles.pack}
                onClick={() => {
                  setFlag(false);
                }}
              >
                {/* {autoQueryEnabled && (
                  <Button
                    type="primary"
                    style={{ marginRight: '15px' }}
                    onClick={() => {
                      callBack(manualCondition);
                    }}
                  >
                    查询
                  </Button>
                )} */}
                <a>收起</a> <UpOutlined style={{ color: '#1677ff' }} />
              </div>
            )}
          </>
        )}
      {tableFilter && Object.values(tableFilter)?.length
        ? modifyArray(Object.values(tableFilter)).map(
            (item: any, index: number) => (
              <div key={index}>{CustomizeComponents(item)}</div>
            ),
          )
        : ''}
    </div>
  );
});
export default CustomizeComponent;
