import { Button, Input, Modal, Space } from 'antd';
import { useEffect, useState } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
const { TextArea } = Input;
const BatchModal = (props: any) => {
  const { item, getTextValue, batchValue, batchCallback } = props;
  // const value = useSelector((state: any) => state?.tableFilter.batchValue);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState('');
  // const dispatch = useDispatch();
  useEffect(() => {
    setValue(batchValue);
  }, [batchValue]);
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    batchCallback(value);
    getTextValue({ target: { value } }, item);
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const onChange = (e: any) => {
    setValue(e?.target?.value);
    // dispatch(batch(e?.target?.value));
    // batchCallback(e?.target?.value);
    // getTextValue(e, item);
  };
  // 清空
  const empty = () => {
    setValue('');
    batchCallback('');
    getTextValue({ target: { value: '' } }, item);
    // setIsModalOpen(false);
  };
  return (
    <>
      <a
        onClick={showModal}
        style={{ width: '56px', display: 'flex', alignItems: 'center' }}
      >
        批量
      </a>
      <Modal
        width={550}
        title="批量添加"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Space
            key="btn"
            style={{ display: 'flex', justifyContent: 'center' }}
          >
            <Button key="back" onClick={empty}>
              清空
            </Button>
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          </Space>,
        ]}
      >
        <TextArea
          value={value}
          rows={20}
          placeholder="请批量输入"
          onChange={onChange}
        />
      </Modal>
    </>
  );
};
export default BatchModal;
