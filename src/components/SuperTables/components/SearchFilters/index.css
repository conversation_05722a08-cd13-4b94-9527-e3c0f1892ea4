.share_search_unfold {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 16px 24px 0 24px;
  margin-bottom: 16px;
  max-height: 67px;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 4px 4px;
}
.share_search_unfold .unfold {
  position: absolute;
  top: 24px;
  right: 10px;
  cursor: pointer;
  z-index: 99;
}
.share_search_unfold .pack {
  position: absolute;
  bottom: 15px;
  right: 10px;
  cursor: pointer;
  z-index: 99;
}
.share_search_unfold .clientele {
  width: 348px;
  height: 40px !important;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  float: left;
  margin-bottom: 16px;
  margin-right: 16px;
}
.share_search_unfold .clientele .organization {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100px !important;
  height: 38px;
  border-radius: 4px 0px 0px 4px;
  border-right: 1px solid #e5e5e5;
  font-weight: 400;
  font-size: 14px;
  color: #707070;
  box-sizing: border-box;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.share_search_unfold .clientele :global .ant-form-item {
  width: calc(100% - 100px) !important;
}
.share_search_unfold .clientele :global .ant-pro-table {
  height: calc(100% - 32px);
}
.share_search_unfold .clientele :global .ant-input-affix-wrapper {
  border: none !important;
}
.share_search_unfold .clientele :global .ant-select {
  width: calc(100% - 100px);
  border: none;
}
.share_search_unfold .clientele :global .ant-select .ant-select-selector {
  width: 100%;
  height: 36px !important;
  border: none !important;
}
.share_search_unfold .clientele :global .ant-select .ant-select-selector .ant-select-selection-overflow {
  flex-wrap: nowrap;
  white-space: nowrap;
  /* 防止内容换行 */
  overflow: hidden;
  /* 隐藏超出盒子的内容 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}
.share_search_unfold .clientele :global .ant-cascader {
  width: 100% !important;
}
.share_search_unfold .clientele :global .ant-picker,
.share_search_unfold .clientele :global .ant-input {
  width: calc(100% - 20px) !important;
  height: 100%;
  border: none !important;
}
.share_search_unfold .search_left_wrap {
  flex: 1;
  margin-right: 30px;
}
.share_search_unfold .search_left_wrap .search_wrap {
  border: 1px solid #e8e8e8;
  height: 40px;
  display: flex;
  width: 348px;
  margin-right: 16px;
  border-radius: 4px;
  float: left;
  margin-bottom: 16px;
}
.share_search_unfold .search_left_wrap .search_wrap .text_area {
  overflow-y: hidden;
  height: 100%;
  border: none;
  resize: none;
  border-radius: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  padding-top: 8px;
}
.share_search_unfold .search_left_wrap .search_wrap :global .ant-select-selector {
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e5e5e5;
  border-radius: 0px;
  color: #707070;
}
.share_search_unfold .search_left_wrap .search_wrap :global .ant-select-selection-item {
  display: flex;
  align-items: center;
}
.share_search_unfold .search_left_wrap .clientele {
  width: 100% !important;
  height: 38px;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  margin-bottom: 16px;
  margin-right: 16px;
}
.share_search_unfold .search_left_wrap .clientele .organization {
  display: flex;
  padding: 0 6px;
  align-items: center;
  width: 104px;
  height: 38px;
  border-radius: 4px 0px 0px 4px;
  border-right: 1px solid #e5e5e5;
  font-weight: 400;
  font-size: 14px;
  color: #707070;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.share_search_unfold .search_left_wrap .clientele :global .ant-select {
  width: calc(100% - 104px);
  border: none;
}
.share_search_unfold .search_left_wrap .clientele :global .ant-select-single {
  width: 100% !important;
  height: 100% !important;
}
.share_search_unfold .search_left_wrap .clientele :global .ant-select-single .ant-select-selector {
  border: none;
}
.share_search_unfold .search_left_wrap .clientele :global .ant-input-affix-wrapper {
  border: none !important;
}
.share_search_unfold .search_left_wrap .clientele :global .ant-picker,
.share_search_unfold .search_left_wrap .clientele :global .ant-input {
  border: none !important;
}
.share_search_unfold :global .ant-table-header .resizable-handler:hover .resizable-line,
.share_search_unfold :global .ant-table-header .resizable-handler:active .resizable-line {
  background-color: #1677ff !important;
}
.share_search_pack {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 16px 24px 0 24px;
  margin-bottom: 16px;
  max-height: 1000px;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 4px 4px;
}
.share_search_pack .unfold {
  position: absolute;
  top: 24px;
  right: 10px;
  cursor: pointer;
  z-index: 99;
}
.share_search_pack .pack {
  position: absolute;
  top: 24px;
  right: 10px;
  cursor: pointer;
  z-index: 99;
}
.share_search_pack .clientele {
  width: 348px;
  height: 40px !important;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  float: left;
  margin-bottom: 16px;
  margin-right: 16px;
}
.share_search_pack .clientele .organization {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100px !important;
  height: 38px;
  border-radius: 4px 0px 0px 4px;
  border-right: 1px solid #e5e5e5;
  font-weight: 400;
  font-size: 14px;
  color: #707070;
  box-sizing: border-box;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.share_search_pack .clientele :global .ant-form-item {
  width: calc(100% - 100px) !important;
}
.share_search_pack .clientele :global .ant-pro-table {
  height: calc(100% - 32px);
}
.share_search_pack .clientele :global .ant-input-affix-wrapper {
  border: none !important;
}
.share_search_pack .clientele :global .ant-select {
  width: calc(100% - 100px);
  border: none;
}
.share_search_pack .clientele :global .ant-select .ant-select-selector {
  width: 100%;
  height: 36px !important;
  border: none !important;
}
.share_search_pack .clientele :global .ant-select .ant-select-selector .ant-select-selection-overflow {
  flex-wrap: nowrap;
  white-space: nowrap;
  /* 防止内容换行 */
  overflow: hidden;
  /* 隐藏超出盒子的内容 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}
.share_search_pack .clientele :global .ant-cascader {
  width: 100% !important;
}
.share_search_pack .clientele :global .ant-picker,
.share_search_pack .clientele :global .ant-input {
  width: calc(100% - 100px) !important;
  height: 100%;
  border: none !important;
}
.share_search_pack .search_left_wrap {
  flex: 1;
  margin-right: 30px;
}
.share_search_pack .search_left_wrap .search_wrap {
  border: 1px solid #e8e8e8;
  height: 91px;
  display: flex;
  width: 348px;
  margin-right: 16px;
  border-radius: 4px;
  float: left;
  margin-bottom: 16px;
}
.share_search_pack .search_left_wrap .search_wrap .text_area {
  height: 100%;
  border: none;
  resize: none;
  border-radius: 0;
  width: 100%;
  padding-top: 35px;
  overflow: auto;
}
.share_search_pack .search_left_wrap .search_wrap :global .ant-select-selector {
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e5e5e5;
  border-radius: 0px;
  color: #707070;
}
.share_search_pack .search_left_wrap .search_wrap :global .ant-select-selection-item {
  display: flex;
  align-items: center;
}
.share_search_pack .search_left_wrap .clientele {
  width: 100% !important;
  height: 38px;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  margin-bottom: 16px;
  margin-right: 16px;
}
.share_search_pack .search_left_wrap .clientele .organization {
  display: flex;
  padding: 0 6px;
  align-items: center;
  width: 104px;
  height: 38px;
  border-radius: 4px 0px 0px 4px;
  border-right: 1px solid #e5e5e5;
  font-weight: 400;
  font-size: 14px;
  color: #707070;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.share_search_pack .search_left_wrap .clientele :global .ant-select {
  width: calc(100% - 104px) !important;
  border: none;
}
.share_search_pack .search_left_wrap .clientele :global .ant-select-single {
  width: 100% !important;
  height: 100% !important;
}
.share_search_pack .search_left_wrap .clientele :global .ant-select-single .ant-select-selector {
  border: none;
}
.share_search_pack .search_left_wrap .clientele :global .ant-picker,
.share_search_pack .search_left_wrap .clientele :global .ant-input {
  border: none !important;
}
.share_search_pack :global .ant-table-header .resizable-handler:hover .resizable-line,
.share_search_pack :global .ant-table-header .resizable-handler:active .resizable-line {
  background-color: #1677ff !important;
}
.share_keyword_unfold {
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24px 24px 6px 24px;
  margin-bottom: 16px;
  max-height: 1000px;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 4px 4px;
}
.share_keyword_unfold .unfold {
  position: absolute;
  top: 24px;
  right: 10px;
  cursor: pointer;
  z-index: 99;
}
.share_keyword_unfold .pack {
  position: absolute;
  bottom: 15px;
  right: 10px;
  cursor: pointer;
  z-index: 99;
}
.share_keyword_unfold .clientele {
  width: 348px;
  height: 40px !important;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  float: left;
  margin-bottom: 16px;
  margin-right: 16px;
}
.share_keyword_unfold .clientele .organization {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100px !important;
  height: 38px;
  border-radius: 4px 0px 0px 4px;
  border-right: 1px solid #e5e5e5;
  font-weight: 400;
  font-size: 14px;
  color: #707070;
  box-sizing: border-box;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.share_keyword_unfold .clientele :global .ant-form-item {
  width: calc(100% - 100px) !important;
}
.share_keyword_unfold .clientele :global .ant-pro-table {
  height: calc(100% - 32px);
}
.share_keyword_unfold .clientele :global .ant-select {
  width: calc(100% - 100px);
  border: none;
}
.share_keyword_unfold .clientele :global .ant-select .ant-select-selector {
  width: 100%;
  height: 36px !important;
  border: none !important;
}
.share_keyword_unfold .clientele :global .ant-select .ant-select-selector .ant-select-selection-overflow {
  flex-wrap: nowrap;
  white-space: nowrap;
  /* 防止内容换行 */
  overflow: hidden;
  /* 隐藏超出盒子的内容 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}
.share_keyword_unfold .clientele :global .ant-picker,
.share_keyword_unfold .clientele :global .ant-input {
  width: calc(100% - 100px) !important;
  height: 100%;
  border: none !important;
}
.share_keyword_unfold .search_left_wrap {
  flex: 1;
  margin-right: 30px;
}
.share_keyword_unfold .search_left_wrap .search_wrap {
  border: 1px solid #e8e8e8;
  height: 40px;
  display: flex;
  width: 348px;
  margin-right: 16px;
  border-radius: 4px;
  float: left;
  margin-bottom: 16px;
}
.share_keyword_unfold .search_left_wrap .search_wrap .text_area {
  height: 100%;
  border: none;
  resize: none;
  border-radius: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  padding-top: 8px;
  overflow: auto;
}
.share_keyword_unfold .search_left_wrap .search_wrap :global(.ant-select-selector) {
  height: 100%;
}
.share_keyword_unfold .search_left_wrap .search_wrap :global(.ant-select-selector) :global(.ant-select-selection-item) {
  display: flex;
  align-items: center;
}
.share_keyword_unfold .search_left_wrap .clientele {
  width: 100% !important;
  height: 38px;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  margin-bottom: 16px;
  margin-right: 16px;
}
.share_keyword_unfold .search_left_wrap .clientele .organization {
  display: flex;
  padding: 0 6px;
  align-items: center;
  width: 104px;
  height: 38px;
  border-radius: 4px 0px 0px 4px;
  border-right: 1px solid #e5e5e5;
  font-weight: 400;
  font-size: 14px;
  color: #707070;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.share_keyword_unfold .search_left_wrap .clientele :global .ant-select {
  width: calc(100% - 104px) !important;
  border: none;
}
.share_keyword_unfold .search_left_wrap .clientele :global .ant-select-single {
  width: 100% !important;
  height: 100% !important;
}
.share_keyword_unfold .search_left_wrap .clientele :global .ant-select-single .ant-select-selector {
  border: none;
}
.share_keyword_unfold .search_left_wrap .clientele :global .ant-picker,
.share_keyword_unfold .search_left_wrap .clientele :global .ant-input {
  border: none !important;
}
.share_keyword_unfold :global .ant-cascader {
  width: 100% !important;
}
.share_keyword_unfold :global .ant-table {
  height: 100% !important;
}
.share_keyword_unfold :global .ant-table-header .resizable-handler:hover .resizable-line,
.share_keyword_unfold :global .ant-table-header .resizable-handler:active .resizable-line {
  background-color: #1677ff !important;
}
.tableWrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  overflow: auto;
}
.tableWrapper .customTable {
  flex: 1;
  position: relative;
}
.tableWrapper .customTable .table_view {
  display: block;
  position: absolute;
  top: 56px;
  right: 0;
  width: 200px;
  height: 311px;
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px rgba(51, 51, 51, 0.3);
  z-index: 9;
}
.tableWrapper .customTable .table_view > div {
  width: 100%;
  height: calc(100% - 46px);
  overflow: auto;
}
.tableWrapper .customTable .table_view .addView {
  display: flex;
  align-items: center;
  width: 100%;
  height: 46px;
  background: #ffffff;
  box-sizing: border-box;
  padding-left: 18px;
}
.tableWrapper .customTable .table_view .viewList {
  width: 100%;
  height: 56px;
  padding-left: 18px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  cursor: pointer;
}
.tableWrapper .customTable .table_view .viewList:hover {
  background: #f3f6ff;
}
.tableWrapper .customTable :global .ant-pro-table {
  height: calc(100% - 32px);
  overflow: auto;
}
.tableWrapper .customTable :global .ant-pro-card {
  border-radius: 6px 6px 0 0;
}
.tableWrapper .customTable .paging {
  position: absolute;
  width: 100%;
  height: 48px;
  padding: 0px 24px 10px;
  box-sizing: border-box;
  background-color: #ffffff;
  margin-top: -16px;
  z-index: 99;
}
.tableWrapper .customTable .paging .footer_page {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fbfbfd;
  box-sizing: border-box;
  padding: 0 10px;
  border: 1px solid #e2e4e6;
}
.tableWrapper .customTable .paging .footer_page :global .ant-pagination {
  display: flex;
  justify-content: end;
  width: 500px;
}
.share_proTable .toolBar {
  position: absolute;
  left: 0;
  display: flex;
  width: calc(100% - 168px);
}
.share_proTable .toolBar .view_btn {
  position: absolute;
  right: -162px;
  bottom: 7px;
  font-size: 18px;
  cursor: pointer;
}
.share_proTable .toolBar .view_btn :hover {
  color: #1677ff;
}
.share_proTable .toolBar :global .ant-space,
.share_proTable .toolBar :global .ant-btn {
  margin-right: 10px;
}
.share_proTable .toolBar > div {
  display: flex;
  align-items: center;
}
.share_proTable :global .rc-virtual-lis {
  flex: 1;
  overflow: hidden !important;
}
.share_proTable :global .ant-pro-table-list-toolbar-container {
  position: relative;
  height: 56px;
}
.share_proTable :global .ant-pro-table-list-toolbar-setting-items {
  position: absolute;
  right: 32px;
  gap: 0;
}
.share_proTable :global .ant-picker {
  width: 250px;
}
.share_proTable :global .ant-table-header {
  min-height: 40px;
}
.share_proTable :global .rc-virtual-list-holder-inner .ant-table-row > div {
  display: flex;
  align-items: center;
}
.share_proTable :global .rc-virtual-list {
  overflow: hidden !important;
}
.share_proTable :global .rc-virtual-list-scrollbar {
  height: 12px !important;
}
.share_proTable :global .rc-virtual-list-scrollbar-thumb {
  background-color: #d8e1ee !important;
}
.share_proTable :global .ant-pro-table,
.share_proTable :global .ant-pro-card {
  height: 100%;
}
.share_proTable :global .ant-pro-table-list-toolbar-container {
  padding-block: 12px;
}
.share_proTable :global .ant-btn {
  line-height: 1.5;
  height: 30px;
  box-sizing: border-box;
}
.share_proTable :global .operations :global .ant-btn {
  padding: 5px !important;
}
.share_proTable :global .ant-pro-card-body {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}
.share_proTable :global .ant-table-wrapper {
  overflow: hidden;
}
.share_proTable :global .ant-spin-nested-loading,
.share_proTable :global .ant-spin-container {
  height: 100% !important;
}
.share_proTable :global .ant-spin-container {
  display: flex;
  flex-direction: column;
}
.share_proTable :global .ant-spin-container .ant-table {
  flex: 1;
  overflow: hidden;
}
.share_proTable :global .ant-spin-container .ant-table .ant-table-container {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}
.share_proTable :global .ant-spin-container .ant-table .ant-table-container .ant-table-body {
  flex: 1;
  overflow: auto !important;
  max-height: auto !important;
}
.share_proTable :global .ant-table-header .resizable-handler:hover .resizable-line,
.share_proTable :global .ant-table-header .resizable-handler:active .resizable-line {
  background-color: #1677ff !important;
}
.share_proTable :global .ant-table-thead .ant-table-cell {
  background-color: #fbfbfd !important;
  color: #556c7e !important;
  font-weight: 400;
}
.share_proTable :global .ant-table-row {
  transition: none !important;
}
.share_proTable :global .ant-pagination {
  background-color: #fbfbfd;
}
.share_proTable :global .ant-table-cell {
  transition: none !important;
  background-color: white !important;
  box-sizing: border-box !important;
}
.share_proTable :global .ant-table-cell:not(.nonSelectable)[selectState*='sel'] {
  background-color: rgba(64, 113, 255, 0.1) !important;
}
.share_proTable :global .ant-table-cell:not(.nonSelectable)[selectState*='Top'] {
  border-top: 1px solid #4071ff !important;
}
.share_proTable :global .ant-table-cell:not(.nonSelectable)[selectState*='Left'] {
  border-left: 1px solid #4071ff !important;
}
.share_proTable :global .ant-table-cell:not(
        .nonSelectable
      )[selectState*='Bottom'][selectState*='Bottom'] {
  border-bottom: 1px solid #4071ff !important;
}
.share_proTable :global .ant-table-cell:not(.nonSelectable)[selectState*='Right'] {
  border-right: 1px solid #4071ff !important;
}
.share_proTable :global .ant-table-cell {
  user-select: none !important;
}
.share_proTable .become {
  background: #ffeae9;
}
.share_proTable .become :global .ant-table-cell-row-hover {
  background: #e34343 !important;
  color: #fff !important;
}
.share_proTable .become :global .ant-table-cell,
.share_proTable .become :global .ant-table-cell-fix-right {
  background: #e34343 !important;
  color: #fff !important;
}
.share_proTable .become :global(.ant-table-cell-fix-right) {
  background: #ffeae9;
}
.share_proTable .become > div {
  border-right: 1px solid #c63333 !important;
  border-bottom: 1px solid #c63333 !important;
}
.share_proTable .bottom_statistics {
  position: fixed;
  bottom: -38px;
  z-index: 999999;
  left: 0;
}
.share_proTable .bottom_statistics :global .ant-table-cell-row-hover {
  background: #760404 !important;
  color: #fff !important;
}
.share_proTable .bottom_statistics :global .ant-table-cell,
.share_proTable .bottom_statistics :global .ant-table-cell-fix-right {
  background: #760404 !important;
  color: #fff !important;
}
.share_proTable .bottom_statistics :global(.ant-table-cell-fix-right) {
  background: #760404 !important;
}
.compact :global .ant-table-row .ant-table-cell {
  height: 30px !important;
}
.customSearch {
  width: 180px;
}
.customSearch .cousInput {
  padding: 6px;
  border-bottom: 1px solid #f0f0f0;
  overflow: hidden;
}
.customSearch .cousInput > div {
  display: flex;
  width: 100%;
  height: 32px;
  background-color: #fff;
  border-width: 1px;
  border-style: solid;
  border-color: #d9d9d9;
  border-radius: 8px;
  padding-left: 10px;
}
.customSearch .cousCount {
  max-height: 264px;
  padding: 2px 10px;
  overflow: auto;
}
.customSearch .cousFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 7px 8px;
  border-top: 1px solid #f0f0f0;
  overflow: hidden;
}
.customSearch :global .ant-input-outlined {
  border: none !important;
}
.customSearch :global .ant-input-outlined:focus {
  box-shadow: none;
}
.customSearch svg {
  color: #d9d9d9;
}
.prompt_box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 160px;
  background-color: #fff;
  position: absolute;
  border: 1px solid #ebebeb;
  border-radius: 5px;
  z-index: 999;
  box-sizing: border-box;
  padding: 5px;
}
.prompt_box > div {
  box-sizing: border-box;
  padding: 8px 0;
}
.prompt_box :global .ant-image .ant-image-mask:hover {
  opacity: 0;
}
.prompt_box > div {
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 100%;
  height: 25%;
  box-sizing: border-box;
  padding-left: 5px;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
}
.prompt_box > div svg {
  margin-right: 5px;
}
.prompt_box > div:hover {
  background: #f0f5fa;
}
.prompt_box > span {
  display: none !important;
}
.clear :global .ant-input-suffix {
  position: absolute;
  top: 9px;
  right: 9px;
}
