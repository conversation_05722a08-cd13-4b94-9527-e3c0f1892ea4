import React, { useMemo, useState } from 'react';
import { ColumnHeightOutlined, LineHeightOutlined } from '@ant-design/icons';
import {Dropdown, theme, Tooltip } from 'antd';
import { rowHeightStore } from '../../store/screenStore';

interface RowHeightSettingProps {
  instanceId: string;
  onChange?: any;
}

const RowHeightSetting = ({ instanceId, onChange }: RowHeightSettingProps) => {
  const { token } = theme.useToken();
  const contentStyle = useMemo(
    () => ({
      backgroundColor: token.colorBgElevated,
      borderRadius: token.borderRadiusLG,
      boxShadow: token.boxShadowSecondary,
    }),
    [token],
  );

  const [isOpen, setIsOpen] = useState(false);
  const [selectedRowHeight, setSelectedRowHeight] = useState(rowHeightStore[instanceId] || 40);

  // const saveRowHeightToView = (heightValue: string) => {
  //   console.log(`保存行高设置 ${heightValue} 到视图 ${instanceId}`);
  //   // dispatch(saveTableRowHeight({ instanceId, rowHeight: heightValue }));
  // };

  return (
    <>
      <Dropdown
        placement="bottomRight"
        dropdownRender={() => {
          return (
            <div style={contentStyle} className="p-3 transition-all">
              <div className="flex flex-col space-y-2">
                <div className="text-sm font-medium mb-1">行高设置</div>
                <div className="flex flex-col space-y-1">
                  {[
                    { label: '默认', value: 'default', height: 40 },
                    { label: '小', value: 'small', height: 35 },
                    { label: '很小', value: 'smaller', height: 28 },
                    { label: '非常小', value: 'smallest', height: 20 },
                  ].map((option) => (
                    <div
                      key={option.height}
                      className={`px-2 py-1.5 rounded cursor-pointer hover:bg-gray-100 flex items-center justify-between text-sm ${
                        selectedRowHeight === option.height
                          ? 'bg-blue-50 text-blue-500'
                          : ''
                      }`}
                      onClick={() => {
                        rowHeightStore[instanceId] = option.height;
                        setSelectedRowHeight(option.height);
                        onChange?.(option);
                      }}
                    >
                      <span>{option.label}</span>
                      <span className="text-gray-400 text-xs ">
                        <LineHeightOutlined className="mr-1" />
                      </span>
                    </div>
                  ))}
                </div>
                {/* <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    
                    // rowHeightStore[instanceId] = selectedRowHeight;
                    setIsOpen(false);
                  }}
                  className="mt-2 w-full bg-blue-500 hover:bg-blue-600 transition-colors"
                >
                  保存到视图
                </Button> */}
              </div>
            </div>
          );
        }}
        trigger={['click']}
        // arrow
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <Tooltip title="大小调整" placement="top">
          <ColumnHeightOutlined
            onClick={(e) => e.preventDefault()}
            className=" hover:text-blue-500 transition-colors duration-300 cursor-pointer"
            style={{ fontSize: 16 }}
          />
        </Tooltip>
      </Dropdown>
    </>
  );
};
export default React.memo(RowHeightSetting);
