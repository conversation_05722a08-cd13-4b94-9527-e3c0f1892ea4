import { DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Button, Dropdown, Popover, Space } from 'antd';
import React, { useState, useMemo } from 'react';
import { StatsSummaryProps, TotalNumberOfPagesProps } from './typeTs';

const StatsSummary: React.FC<StatsSummaryProps> = ({
  TotalConfiguration,
  selectedData,
  dataSource,
}) => {
  const [summaryType, setSummaryType] = useState<'page' | 'all' | 'selected'>(
    'page',
  );

  const calculatedStats = useMemo(() => {
    return TotalConfiguration.map((item) => {
      let value: number | string = 0;
      if (summaryType === 'all') {
        value = item.value ?? 0;
      } else {
        const dataToUse =
          summaryType === 'selected' ? selectedData : dataSource;
        value = dataToUse.reduce(
          (sum, row) => sum + (Number(row[item.field]) || 0),
          0,
        );
      }

      return {
        ...item,
        calculatedValue: typeof value === 'number' ? value.toFixed(2) : value,
      };
    });
  }, [TotalConfiguration, selectedData, dataSource, summaryType]);

  const handleMenuClick = (e: { key: string }) => {
    setSummaryType(e.key as 'page' | 'all' | 'selected');
  };

  const menuProps = {
    items: [
      { label: '本页总计', key: 'page' },
      { label: '选中总计', key: 'selected' },
      { label: '全部总计', key: 'all' },
    ],
    onClick: handleMenuClick,
  };

  return (
    <Space>
      <Dropdown menu={menuProps}>
        <Button size="small">
          <Space className="color-#333">
            {summaryType === 'page'
              ? '本页总计'
              : summaryType === 'all'
              ? '全部总计'
              : '选中总计'}
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown>
      <div className="w-100% overflow-x-auto">
        {/* <Space size="large">
          {calculatedStats.map((item, index) => (
            <div key={index} className="font-400">
              <span className="">{item.desc}：</span>
              <span className="color-#4071FF font-600">
                {item.calculatedValue}
              </span>
            </div>
          ))}
        </Space> */}
        {/* <Space size="large" className='max-w-460px'>
          {calculatedStats.map((item, index) => (
            <div key={index} className="font-400 flex items-center">
              <span className="inline-block whitespace-nowrap">{item.desc}：</span>
              <span className="color-#4071FF font-600  overflow-hidden inline-block" >
                {item.calculatedValue}
              </span>
            </div>
          ))}
        </Space> */}
        {/* <div className="relative min-w-687px w-100px">
          <div
            className="overflow-x-scroll max-w-full"
            style={{
              maskImage:
                'linear-gradient(to right, black 90%, transparent 100%)',
              WebkitMaskImage:
                'linear-gradient(to right, black 90%, transparent 100%)',
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }}
          >
            <div className="inline-block min-w-max">
              <Space size="large" className="flex-nowrap whitespace-nowrap">
                {calculatedStats.map((item, index) => (
                  <div
                    key={index}
                    className="font-400 inline-flex items-center flex-shrink-0"
                  >
                    <span className="whitespace-nowrap">{item.desc}：</span>
                    <span className="color-#4071FF font-600 overflow-hidden whitespace-nowrap">
                      {item.calculatedValue}
                    </span>
                  </div>
                ))}
              </Space>
            </div>
          </div>
          <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-#fff to-transparent pointer-events-none"></div>
        </div> */}
        <Space size="middle" className="flex-nowrap whitespace-nowrap">
          <Popover
            content={
              <React.Fragment>
                <Space size="large">
                  {calculatedStats.map((item, index) => (
                    <div key={index}>
                      <span>{item.desc}：</span>
                      <span className="color-#4071FF font-600 ">
                        {item.calculatedValue}
                      </span>
                    </div>
                  ))}
                </Space>
              </React.Fragment>
            }
          >
            <InfoCircleOutlined />
          </Popover>
          {calculatedStats.map((item, index) => (
            <div key={index}>
              <span>{item.desc}：</span>
              <span className="color-#4071FF font-600 ">
                {item.calculatedValue}
              </span>
            </div>
          ))}
        </Space>
      </div>
    </Space>
  );
};

const TotalNumberOfPages = ({
  rowSelection,
  selectedData,
  TotalConfiguration,
  dataSource,
}: TotalNumberOfPagesProps) => {
  return (
    <div>
      <Space>
        {rowSelection && (
          <>
            <div className="color-#707070 font-500 text-14px min-w-80px">
              已选
              <span> {selectedData.length} </span>条
            </div>
            {TotalConfiguration && TotalConfiguration.length > 0 && (
              <>
                {/* <Divider type="vertical" className="font-600" /> */}
                <StatsSummary
                  TotalConfiguration={TotalConfiguration}
                  selectedData={selectedData}
                  dataSource={dataSource}
                />
              </>
            )}
          </>
        )}
      </Space>
    </div>
  );
};

export default React.memo(TotalNumberOfPages);
