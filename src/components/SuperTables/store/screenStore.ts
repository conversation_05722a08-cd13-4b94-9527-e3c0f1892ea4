import { proxy, subscribe } from 'umi';

/**
 * @description 搜索框的值
 * <AUTHOR>
 * @date 2024-09-20
 * @export
 * @interface ScreenState
 * @property {string} key - 键
 * @property {string} value - 值
 */

const initialColumnStore = JSON.parse(
  localStorage.getItem('columnStore') || '{}',
);

const initialParamsStore = JSON.parse(
  localStorage.getItem('paramsStore') || '{}',
);

const initialSettings = JSON.parse(
  localStorage.getItem('settingsStore') ||
    '{"cachePagination":false,"cacheSort":false,"autoFetch":true,"cacheColumnWidth":true,"sortState":null,"manualSearch":false,"sortMode":"local","filterMode":"local"}',
);

const initialRowHeightStore = JSON.parse(
  localStorage.getItem('rowHeightStore') || '{}',
);

export const screenStore = proxy<any>({});
export const screenValue = proxy<any>({});
export const columnStore = proxy<any>(initialColumnStore);
export const paramsStore = proxy<any>(initialParamsStore);
export const settingsStore = proxy<any>(initialSettings);
export const rowHeightStore = proxy<any>(initialRowHeightStore);

subscribe(columnStore, () => {
  localStorage.setItem('columnStore', JSON.stringify(columnStore));
});

subscribe(paramsStore, () => {
  localStorage.setItem('paramsStore', JSON.stringify(paramsStore));
});

subscribe(settingsStore, () => {
  localStorage.setItem('settingsStore', JSON.stringify(settingsStore));
});

subscribe(rowHeightStore, () => {
  localStorage.setItem('rowHeightStore', JSON.stringify(rowHeightStore));
});
