/**
 * columns 类型
 * @desc 列描述
 * @dataIndex 数据索引
 * @title 标题
 * @width 宽度
 * @sorter 排序
 * @render 渲染
 * @fieldFormat 字段格式化
 * @renderType 渲染类型
 * @hide 隐藏
 * @filters 表头筛选配置
 * @style 样式
 */
interface ColumnsType {
  tree?: boolean;
  dataIndex: string;
  title: string;
  width?: number | string;
  sorter?: boolean | ((a: any, b: any) => number);
  render?: (text: any, record: any, index: number) => React.ReactNode;
  fieldFormat?: (value: any) => any;
  renderType?: string;
  hide?: boolean;
  style?: (value: any, record: any) => React.CSSProperties;
  filters?: {
    key: string;
    type: string;
    options: Array<{ text: string; value: string | number }>;
  };
}

/**
 * 分页统计项类型
 * @desc 分页统计项描述
 * @rowSelection 行选择
 * @selectedData 选中数据
 * @TotalConfiguration 统计项
 * @dataSource 数据源
 */
interface TotalNumberOfPagesProps {
  rowSelection: Record<string, any>;
  selectedData: Array<Record<string, any>>;
  TotalConfiguration?: Array<StatItem>;
  dataSource: Array<Record<string, any>>;
}

/**
 * 统计项类型
 * @desc 统计项描述
 * @field 统计字段 本页和选中统计项
 * @value 统计值 全部统计项 此项必填
 */
interface StatItem {
  desc: string;
  field: string;
  value?: number | string;
}

/**
 * 统计项类型
 * @desc 统计项描述
 * @TotalConfiguration 统计项
 * @selectedData 选中数据
 * @dataSource 数据源
 */
interface StatsSummaryProps {
  TotalConfiguration: Array<StatItem>;
  selectedData: Array<Record<string, any>>;
  dataSource: Array<Record<string, any>>;
}

export { ColumnsType, StatItem, TotalNumberOfPagesProps, StatsSummaryProps };
