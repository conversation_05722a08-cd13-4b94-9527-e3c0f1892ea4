import { ListColumn, Group, Text } from '@xiaoshengwpp/react-super-table';
import ScreenModal from './components/ScreenModal';
import { ColumnsType } from './typeTs';
const RenderComponent = (props: any) => {
  if (!props) return null;
  const { table, row, col, rect, dataValue, render } = props;
  if (!table || row === undefined || col === undefined) {
    return null;
  }
  const { height, width } = rect || table.getCellRect(col, row);
  const record = table.getRecordByCell(col, row);
  if (table.isAggregation(col, row)) {
    return null;
  }
  return (
    <Group
      attribute={{
        width,
        height,
        display: 'flex',
        alignItems: 'center',
        dx: 10,
        cursor: 'pointer',
      }}
    >
      <Group
        attribute={{
          width: 10,
          height: 25,
          // width,
          // height,
          // fill: '#ccc',
          display: 'flex',
          react: {
            pointerEvents: true,
            container: table.bodyDomContainer,
            element: render(dataValue, record),
          },
        }}
      />
    </Group>
  );
};

const RenderText = (props: any) => {
  if (!props) return null;
  const { table, row, col, rect, dataValue, render, value } = props;
  if (!table || row === undefined || col === undefined) {
    return null;
  }
  const { height, width } = rect || table.getCellRect(col, row);
  const record = table.getRecordByCell(col, row);
  if (table.isAggregation(col, row)) {
    return null;
  }

  return (
    <Group
      attribute={{
        id: 'container',
        width,
        height,
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'nowrap',
        dx: 10,
        // dy: 3,
      }}
    >
      <Group
        attribute={{
          id: 'container-left',
          // width: 10,
          // height,
          width: 10,
          height: 25,
          // display: 'flex',
          react: {
            pointerEvents: true,
            container: table.bodyDomContainer,
            element: render(dataValue, record),
          },
          // boundsPadding: [0, 0, 0, 0],
        }}
      ></Group>
      <Text
        attribute={{
          id: 'container-right',
          text: value,
          fontSize: 13,
          type: 'text',
          // fontFamily: 'sans-serif',
          fill: record?.holdFlag ? '#f1f2f6' : '#333',
          textAlign: 'left',
          textBaseline: 'top',
          // pickable: true,
          boundsPadding: [0, 0, 0, 16],
          // ellipsis: true,
          maxLineWidth: width - 42,
          // dx: 28,
          // dy: 10,
          // disableAutoClipedPoptip: true,
          // style: {
          //   bgColor: '#000',
          //   color: '#fff',
          //   fontSize: 12,
          //   fontFamily: 'Arial,sans-serif'
          // }
          // hover: {
          //   color: '#000',
          //   bgColor: '#000',
          // },
          // ellipsis:{
          //   poptip: {
          //     position: 'bottom',
          //     bgColor: '#000',
          //   },
          // }
          poptip: {
            position: 'bottom',
            dy: 10,
            dx: 26,
            contentStyle: {
              fontSize: 12,
              // fontFamily: 'PingFang SC, Arial, sans-serif',
              fill: '#fff',
            },
            panel: {
              fill: '#000',
            },
          },
          // poptip: {
          //   position: 'tl',
          //   maxWidth: 320,
          //   padding: 16,
          //   contentStyle: {
          //     fontSize: 12,
          //     lineHeight: 18,
          //   },
          // }
        }}
      ></Text>
    </Group>
  );
};

const HeaderRender = (props: any) => {
  const { table, row, col, rect, filters, instanceId } = props;
  if (!table || row === undefined || col === undefined) {
    return null;
  }
  const { height, width } = rect || table.getCellRect(col, row);
  // const record = table.getRecordByCell(col, row);
  return (
    <Group
      attribute={{
        width,
        height,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-end',
      }}
    >
      <Group
        attribute={{
          width: 20,
          height: 20,
          react: {
            pointerEvents: true,
            container: table.headerDomContainer,
            element: (
              <ScreenModal dataProps={{ filters, instanceId: instanceId }} />
            ),
          },
          boundsPadding: [0, 12, 5, 0],
        }}
      />
    </Group>
  );
};

const renderSuperTableColumns = (
  columns: ColumnsType[],
  instanceId: string,
  isApiSorter?: boolean,
  isTree?: boolean,
) => {
  if (isTree) {
    columns[0].tree = true;
  }
  return columns.map(
    (
      {
        dataIndex,
        title,
        width,
        sorter,
        render,
        fieldFormat,
        filters,
        renderType,
        style,
        ...restProps
      },
      index,
    ) => {
      const wrappedStyle = style
        ? (value: any) => {
            const { col, row, table } = value;
            const record = table.getRecordByCell(col, row);
            return style(value, record);
          }
        : undefined;
      const commonProps = {
        ...restProps,
        field: dataIndex,
        title,
        width,
        // ...(sorter ? {
        //   [isApiSorter ? 'showSort' : 'sort']: true
        // } : {}),
        ...(sorter
          ? {
              [isApiSorter ? 'showSort' : 'sort']:
                typeof sorter === 'function' ? sorter : true,
            }
          : false),
        fieldFormat,
        style: wrappedStyle,
      };
      let children = [];

      if (filters && typeof filters === 'object') {
        children.push(
          <HeaderRender
            role="header-custom-layout"
            filters={filters}
            key={`header-${dataIndex}-${index}`}
            renderDefault={true}
            instanceId={instanceId}
          />,
        );
      }

      if (renderType === 'text') {
        children.push(
          <RenderText
            role="custom-layout"
            render={render}
            key={`text-${dataIndex}-${index}`}
          />,
        );
      } else if (render && typeof render === 'function') {
        children.push(
          <RenderComponent
            role="custom-layout"
            render={render}
            key={`render-${dataIndex}-${index}`}
          />,
        );
      }
      return (
        <ListColumn {...commonProps} key={`column-${dataIndex}-${index}`}>
          {children}
        </ListColumn>
      );
    },
  );
};

export default renderSuperTableColumns;
