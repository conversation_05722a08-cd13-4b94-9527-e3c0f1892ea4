import { Button, Upload } from 'antd';
import { REQUESTADDRESS_W } from '@/globalData';
import React from 'react';

const MyUpload = (props: any) => {
  return (
    <Upload
      key={'upload'}
      disabled={props?.disabled}
      showUploadList={false}
      name={props?.name}
      action={`${REQUESTADDRESS_W}` + props?.action + '?service_id=TMS'}
      headers={{ token: localStorage.getItem('token') || '' }}
      data={{ id: props.id }}
      multiple={true}
      onChange={() => {
        props?.getDetail();
      }}
    >
      {props?.type ? <a style={{ marginRight: '10px' }}>回传报关文件</a> : <Button disabled={props?.disabled}>上传文件</Button>}
    </Upload>
  );
};

export default MyUpload;
