import React, { useEffect, useState } from 'react';
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Button,
  Radio,
  Select,
  message,
  Upload,
} from 'antd';
import { ProFormSelect } from '@ant-design/pro-components';
import { addressFuzzySearchAPI } from '@/services/home/<USER>';
import {
  addWaybillTrackAPI,
  getInvisiblePostAPI,
} from '@/services/productAndPrice/api';
import { UploadOutlined } from '@ant-design/icons';

interface Props {
  btnText: string;
  btnType?: any;
  spaceId?: string | undefined;
  getTrackList?: any;
}

const TrackFormModal = ({ btnText, spaceId, getTrackList }: Props) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm<any>();
  const [invisibleCustomerList, setInvisibleCustomerList] = useState<any>([]);
  const [messageApi, contextHolder] = message.useMessage();
  const [fileList, setFileList] = useState<any>([]);
  /* 查询不可见岗位 */
  const getInvisiblePosition = async () => {
    try {
      const { data, status } = await getInvisiblePostAPI({});
      if (status) {
        setInvisibleCustomerList(data?.list || []);
      }
    } catch (err) {
      console.error('获取不可见岗位失败', err);
    }
  };

  /* 添加轨迹 */
  const addTrack = async (val: any) => {
    try {
      const { status } = await addWaybillTrackAPI(val);
      if (status) {
        setVisible(false);
        if (getTrackList) {
          getTrackList();
        }
        form.resetFields();
        setFileList([])
      }
    } catch (err) {
      console.error('获取不可见岗位失败', err);
    }
  };

  const showModal = () => {
    if (!spaceId) {
      message.error(`请选择需要${btnText}的运单`);
    } else {
      setVisible(true);
    }
  };

  const handleOk = () => {
    // form.validateFields().then((values) => {
    //   const formData = new FormData();
    //   formData.append('attachment', values?.attachment?.file || null);
    //   formData.append('time', values.time.format('YYYY-MM-DD HH:mm:ss') || null);
    //   formData.append('excludeDutyIds', values.excludeDutyIds?.join(',') || null);
    //   formData.append('excludeClient', values.excludeClient || null);
    //   formData.append('state', values.state || null);
    //   formData.append('cityId', JSON.parse(values.province)?.id);
    //   formData.append('outId', spaceId as string);
    //   formData.append('content', values?.content || null);
    //   addTrack(formData);
    // });

    form.validateFields().then((values) => {
      const formData = new FormData();
      if (values?.attachment?.file) {
        formData.append('attachment', values.attachment.file);
      }
      if (values?.time) {
        formData.append('time', values.time.format('YYYY-MM-DD HH:mm:ss'));
      }
      if (values?.excludeDutyIds?.length) {
        formData.append('excludeDutyIds', values.excludeDutyIds.join(','));
      }
      if (values?.excludeClient) {
        formData.append('excludeClient', values.excludeClient);
      }
      if (values?.state) {
        formData.append('state', values.state);
      }
      if (values?.province) {
        formData.append('cityId', JSON.parse(values.province)?.id);
      }
      if (spaceId) {
        formData.append('outId', spaceId);
      }
      if (values?.content) {
        formData.append('content', values.content);
      }
      addTrack(formData);
    });
  };

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  useEffect(() => {
    if (visible) {
      getInvisiblePosition();
    }
  }, [visible]);
  const handleChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };
  const beforeUpload = (file: any) => {
    const isLt1G = file.size / 1024 / 1024 / 1024 < 1;
    if (!isLt1G) {
      messageApi.error('上传文件大小不能超过1G!');
    }
    return false;
  };
  const props = {
    name: 'file',
    // accept: '.xls,.xlsx,',
    maxCount: 1,
    fileList: fileList,
    onChange: handleChange,
    beforeUpload: beforeUpload,
  };
  return (
    <>
      {contextHolder}
      <Button onClick={showModal} type={'primary'} ghost>
        {btnText}
      </Button>
      <Modal
        title="添加轨迹"
        open={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
      >
        <Form layout="vertical" form={form} initialValues={{ state: 1 }}>
          <Form.Item
            label="轨迹发生时间"
            name="time"
            rules={[{ required: true, message: '必填不能为空' }]}
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
          <ProFormSelect
            name="province"
            label="轨迹发生地点"
            fieldProps={{
              // labelInValue: true,
              style: {
                minWidth: 140,
              },
              filterOption: false,
            }}
            showSearch
            placeholder="请选择，支持模糊搜索"
            rules={[{ required: true, message: '必填不能为空' }]}
            debounceTime={300}
            request={async ({ keyWords }) => {
              const { status, data } = await addressFuzzySearchAPI({
                token: keyWords,
              });
              if (status) {
                // console.log('查询结果', data);
                //country - short_province_name / province - city
                return data.list.map((item: any) => {
                  return {
                    label: `${item.country.replaceAll('CN', '中国')} - ${
                      item.provinceShortName
                    } / ${item.province} - ${item.city} ${
                      item.county ? '-' : ''
                    } ${item.county} / ${item.zipCode}`,
                    value: JSON.stringify(item),
                  };
                });
              }
              return [];
            }}
          />
          <Form.Item
            label="状态"
            name="state"
            rules={[{ required: true, message: '必填不能为空' }]}
          >
            <Select
              allowClear
              placeholder="请选择状态"
              options={[
                { value: 1, label: '运输中' },
                { value: 2, label: '已送达' },
                { value: 0, label: '异常' },
              ]}
            />
          </Form.Item>
          <Form.Item
            label="附件"
            name="attachment"
            // rules={[
            //   {
            //     required: true,
            //   },
            // ]}
          >
            <Upload {...props}>
              <Button icon={<UploadOutlined />}>上传文件</Button>
            </Upload>
          </Form.Item>
          <Form.Item label="不可见岗位" name="excludeDutyIds">
            <Select
              mode="multiple"
              allowClear
              maxTagCount={1}
              placeholder="请选择不可见岗位"
              options={invisibleCustomerList}
              fieldNames={{ label: 'desc', value: 'id' }}
            />
          </Form.Item>
          <Form.Item label="客户是否可见" name="excludeClient">
            <Radio.Group>
              <Radio value={0}>可见</Radio>
              <Radio value={1}>不可见</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="提单状态" name="content1">
            <Select options={[
              { value: '已进港', label: '已进港' },
              { value: '到达目的地【纽约】.', label: '到达目的地' },
              { value: '到达目的地【芝加哥】', label: '已到目的机场/港口' },
              { value: '到达目的地【纽约】', label: '已下船（海运）' },
              { value: '海关查验中', label: '清关查验' },
              { value: '清关检验完成，已提柜，正转往海外仓', label: '海外提柜【清关】' },
              { value: '已提柜，正转往海外仓', label: '海外提柜【无清关】' },
              { value: '在【义乌深国际】装柜完成，准备前往上海港口', label: '已出库' },
            ]} onChange={(e)=>{
              form.setFieldsValue({
                content: e
              })
            }} />
          </Form.Item>
          <Form.Item label="轨迹内容" name="content" rules={[{ required: true, message: '必填不能为空' }]}>
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TrackFormModal;
