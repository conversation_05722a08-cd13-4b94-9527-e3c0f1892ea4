import {
  deleteWaybillTrackAPI,
  getWaybillTrackListAPI,
} from '@/services/productAndPrice/api';
import { Avatar, Modal, Space, Timeline } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import TrackFormModal from './TrackFormModal';
import styles from './index.less';
import classNames from 'classnames';
import AccessCard from '@/AccessCard';
import { REQUESTADDRESS_W } from '@/globalData';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { confirm } = Modal;

interface Props {
  trackList: any[]; // 轨迹数据
  spaceId?: string; // 提单id
  accessible?:any//对应权限
}
const TrackList = ({ spaceId,accessible }: Props) => {
  // console.log(spaceId);
  const [trackListData, setTrackListData] = useState<any[]>([]); // 轨迹列表数据
  /* 获取轨迹列表 */
  const getTrackList = async () => {
    if (!spaceId) return;
    try {
      const { data, status } = await getWaybillTrackListAPI({
        id: spaceId,
      });
      if (status) {
        // console.log('获取轨迹列表成功', data);
        setTrackListData(data?.list || []);
      }
    } catch (err) {
      console.error('获取轨迹列表失败', err);
    }
  };

  /* 删除轨迹 */
  const deleteTrack = async (id: string) => {
    try {
      const { status } = await deleteWaybillTrackAPI({
        id: id,
        outId: spaceId,
      });
      if (status) {
        getTrackList();
      }
    } catch (err) {
      console.error('删除轨迹失败', err);
    }
  };

  useEffect(() => {
    getTrackList();
  }, [spaceId]);

  return (
    <>
      <div className="absolute right-8px top-15px">
        <AccessCard accessible={accessible}>
          <TrackFormModal
            btnText="添加轨迹"
            spaceId={spaceId}
            getTrackList={getTrackList}
          />
        </AccessCard>
      </div>
      <Timeline
        items={trackListData?.map((item: any) => {
          return {
            children: (
              <div>
                <div className={styles['boxList']}>
                  <Space>
                    <span className="color-#AEAEAE">
                      {dayjs(item?.createTime).format('YYYY-MM-DD HH:mm:ss')}
                    </span>
                    <span className="color-#4071FF font-600">
                      {item?.location}
                    </span>
                    <span className="color-#2f3542">{item?.content}</span>
                    {item?.attachmentURL && <span><a href={`${REQUESTADDRESS_W}${item?.attachmentURL}`} target="_blank" rel="noreferrer">附件</a></span> }
                    {/* 鼠标滑过展示 */}
                    <AccessCard  accessible={accessible}>
                    <div
                      className={classNames(
                        'ml-10px text-12px color-coolgray cursor-pointer',
                        styles['delBox'],
                      )}
                      onClick={() => {
                        confirm({
                          title: '删除确认',
                          icon: <ExclamationCircleFilled />,
                          content: '删除',
                          onOk() {
                            deleteTrack(item?.id)
                          },
                          onCancel() {
                            
                          },
                        });
                      }}
                    >
                      删除
                    </div>
                    </AccessCard>
                  </Space>
                </div>
                <div>
                  <Space className="mt-10px">
                    {item?.user?.avatar && (
                      <Avatar
                        size={24}
                        src={`https://static.kmfba.com/${item?.user?.avatar}`}
                      />
                    )}
                    <span className="color-coolgray text-12px font-600">
                      {item?.user?.name}
                    </span>
                    <span>
                      <span>
                        {item?.excludeClient === 1 && (
                          <span className="color-red text-12px font-600">
                            客户不可见
                          </span>
                        )}
                      </span>
                    </span>
                    <div>
                      {item?.excludeDuties && (
                        <span className="color-#707070 text-12px">
                          屏蔽岗位：{item?.excludeDuties}
                        </span>
                      )}
                    </div>
                  </Space>
                </div>
              </div>
            ),
          };
        })}
      />
    </>
  );
};

export default TrackList;
