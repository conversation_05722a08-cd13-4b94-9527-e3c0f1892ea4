import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, DatePicker, Drawer, message, Tag } from 'antd';

import { waybillListAPIList } from '@/services/preplanCabin';
import {
  appendPallets,
  checkoutPallets,
  getReadyList,
} from '@/services/ConfiguredCar';
import {
  deliveryMap,
  getObjectByValue,
  ProgressStateType2,
} from '@/utils/constant';
import SuperTables from '@/components/SuperTables';
import { history } from '@@/core/history';
import dayjs from 'dayjs';

const AddCar = (props: any) => {
  const { btnTitle, DefaultSelected, details, handleAdd, selectData } = props;
  console.log('details', details);
  console.log('selectData', selectData);
  // console.log('props: ', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 被选中的运单信息
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  /*选中项*/
  const [checkData, setCheckData] = useState<any>([]);
  // 被选中的运单信息
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [total, setTotal] = useState({
    number: 0,
    weight: 0,
    squares: 0,
  });
  const actionRef = useRef<any>();

  useEffect(() => {
    getTotal();
  }, [selectedRows?.length]);
  useEffect(() => {
    /*设置回显选中项目*/
    if (isModalOpen && DefaultSelected) {
      setSelectedRowKeys(DefaultSelected);
    }
    if (isModalOpen) {
      if (selectData.length === 0) {
        message.warning('请先选择托盘');
        setIsModalOpen(false);
      }
    }
  }, [isModalOpen]);
  const getTotal = () => {
    const pieceNum = selectedRows?.reduce(
      (pre: any, cur: any) => pre + cur?.pieceNum,
      0,
    );
    const weight = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.weight),
      0,
    );
    const squares = selectedRows?.reduce(
      (pre: any, cur: any) => pre + Number(cur?.squares),
      0,
    );
    setTotal({
      number: pieceNum,
      weight: weight,
      squares: squares,
    });
  };
  /* 刷新表格 */
  const columns: any = [
    {
      title: '派送单号',
      dataIndex: 'no',
      hideInSearch: true,
      width: 200,
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Button
            key="edit"
            type="link"
            onClick={() => {
              history.push(`/PreConfiguredCar/detail`, record);
            }}
          >
            {record?.no}
          </Button>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      hideInSearch: true,
      width: 100,
      render: (text: any, record: any) => {
        const obj = getObjectByValue(record?.state, ProgressStateType2);
        return (
          <Tag
            style={{
              background: obj?.background,
              color: obj?.color,
              border: 'none',
            }}
          >
            {obj?.label}
          </Tag>
        );
      },
    },
    {
      title: '所在仓库',
      dataIndex: 'warehouse',
      width: 200,
      hideInSearch: true,
      fieldFormat: (record: any) => record?.warehouse?.name,
    },

    {
      title: 'ISA',
      hideInSearch: true,
      dataIndex: 'isa',
      width: 200,
    },
    {
      title: '送货时间',
      hideInSearch: true,
      dataIndex: 'appointmentTime',
      width: 200,
      fieldFormat: (value: any) =>
        value?.appointmentTime
          ? dayjs(value?.appointmentTime).format('YYYY-MM-DD HH:mm:ss')
          : '',
    },
    {
      title: '承运商',
      hideInSearch: true,
      dataIndex: 'providerName',
      width: 200,
    },
    {
      title: '装车时间',
      hideInSearch: true,
      dataIndex: 'estimateLoadingTime',
      width: 200,
      fieldFormat: (value: any) =>
        value?.estimateLoadingTime
          ? dayjs(value?.estimateLoadingTime).format('YYYY-MM-DD HH:mm:ss')
          : '',
    },
    {
      title: '联系人',
      hideInSearch: true,
      dataIndex: 'driver',
      width: 200,
    },
    {
      title: '联系电话',
      hideInSearch: true,
      dataIndex: 'phone',
    },
    {
      title: '车牌/车次',
      hideInSearch: true,
      dataIndex: 'licensePlateNumber',
    },
    {
      title: '备注',
      hideInSearch: true,
      dataIndex: 'remark',
    },
    {
      title: '送达标识',
      hideInSearch: true,
      dataIndex: 'dest',
    },
    {
      title: 'POD',
      hideInSearch: true,
      dataIndex: 'podName',
    },
    {
      title: '出库时间',
      hideInSearch: true,
      dataIndex: 'outboardTime',
      fieldFormat: (value: any) =>
        value?.outboardTime
          ? dayjs(value?.outboardTime).format('YYYY-MM-DD HH:mm:ss')
          : '',
    },
    {
      title: '送达时间',
      hideInSearch: true,
      dataIndex: 'deliveriedTime',
      width: 200,
      fieldFormat: (value: any) =>
        value?.deliveriedTime
          ? dayjs(value?.deliveriedTime).format('YYYY-MM-DD HH:mm:ss')
          : '',
    },
    /*  {
      title: '创建时间',
      hideInSearch: true,
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 200,
    },*/
  ];

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    console.log('check', checkData);
    console.log('ids', ids);
    if (checkData.length > 1) {
      message.warning('只能选择一个配送单');
      return;
    }
    const ids = selectData?.map((item: any) => item.id).join(',');
    const res = await checkoutPallets({
      id: checkData[0]?.id,
      code: ids,
      force: 1,
    });
    if (res?.status) {
      handleAdd();
      setIsModalOpen(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div onClick={showModal}>
        {/* <IconItem name="#icon-dizhibao" style={{ marginRight: '6px' }} /> */}
        {!btnTitle ? (
          <div>
            <Button type={'primary'}>签出</Button>
          </div>
        ) : (
          <div
            style={{
              display: 'inline-block',
              cursor: 'pointer',
              position: 'absolute',
              top: '374px',
              right: '50px',
              zIndex: 999,
              //       top: '319px',
              // right: '50px',
              // z-index: 999
            }}
          >
            <a style={{ display: 'inline-block' }}>{btnTitle}</a>
          </div>
        )}
      </div>
      <Drawer
        title="选择配送单"
        style={{
          width: '100vw',
          position: 'fixed',
          top: '1px',
          bottom: '1px',
          // marginBottom: '-1em',
          right: '1px',
          left: '1px',
          zIndex: 999999,
        }}
        destroyOnClose
        open={isModalOpen}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <div style={{ height: 'calc(100% - 40px)' }}>
          {/* <div style={{ marginLeft: '25px' }}>
            <span>件数：<span style={{ color: 'rgb(100, 64, 255)' }}>{total?.number}</span></span>
            <span style={{ margin: '0 20px' }}>重量：<span style={{ color: 'rgb(0, 186, 217)' }}>{total?.weight}</span></span>
            <span>放数：<span style={{ color: 'rgb(255, 64, 165)' }}>{total?.squares}</span></span>
          </div> */}
          <>
            <SuperTables
              instanceId="addcar222"
              columns={columns}
              rowSelection={(value: any) => {
                setCheckData(value);
                console.log('value', value);
              }}
              warpHeight={300}
              request={async (params: any, action: any) => {
                // console.log('tableactiveKey',activeKey);
                let msg = await getReadyList({
                  condition: params?.condition || {},
                  start: (params.current - 1) * params.pageSize,
                  len: params.pageSize,
                  type: 1,
                });
                return {
                  data: msg?.data?.list || [],
                  success: msg?.status,
                  total: msg?.data?.amount,
                };
              }}
              filters={{
                keyword: {
                  type: 'search',
                  value: '',
                  termQuery: false,
                },
                warehouseId: {
                  desc: '交货仓库',
                  type: 'overseas',
                  value: '',
                },
                /*    shipmentMethod: {
                  desc: '派送方式',
                  type: 'select',
                  range: DispatchMap,
                  value: '',
                },
                clientId: {
                  desc: '客户',
                  type: 'client',
                  value: '',
                },
                fbaCode: {
                  desc: 'FBA仓库',
                  type: 'FBA',
                  value: '',
                },*/
                state: {
                  desc: '状态',
                  type: 'select',
                  range: deliveryMap,
                  value: '',
                },
                appointmentTime: {
                  desc: '预约送货时间',
                  type: 'dateTimeRange',
                  value: '',
                },
              }}
              /* toolBarRender={<Button
                   key="primary"
                   type="primary"
                   onClick={() => setIsModalOpen(true)}
                 >
                   新建规则
                 </Button>}*/
            />
          </>
        </div>
        <div
          key="btn"
          style={{
            position: 'absolute',
            bottom: '16px',
            right: '40px',
            zIndex: '10',
          }}
        >
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>
          <Button
            key="submit"
            type="primary"
            onClick={handleOk}
            style={{ marginLeft: 10 }}
          >
            确认
          </Button>
        </div>
      </Drawer>
    </>
  );
};

export default React.memo(AddCar);
