/*成本分摊*/
import { TableRowSelection } from 'antd/es/table/interface';
import { Col, Form, Radio, Row, Select, Table } from 'antd';
import { OtherColumns, SharingColumns, SharingColumns2 } from './columns';
import React from 'react';
import { calculateSingSum } from '@/utils/utils';
import {
  averageType,
  averageTypeMap,
  convertObjectToArray,
} from '@/utils/constant';
import Decimal from 'decimal.js';

const OtherCostSharing = (props: any) => {
  const {
    isDetail,
    fees,
    myList,
    costReconciliationType,
    costAverageType,
    totalMoney,
    spaceFee,
  } = props;
  /*计算选中总价 */
  console.log('fees', fees);
  const countTotal = (type: any, array: any) => {
    let total: any = 0;
    array?.map((item: any) => {
      if (myList.includes(item.waybillId)) {
        total += Number(item[type]);
      }
      return total;
      // 对象拥有该属性
    });
    return total;
  };
  /*选中的选项*/
  const rowSelection: TableRowSelection<any> = {
    checkStrictly: false,
    onChange: (selectedRowKeys) => {
      console.log('key', selectedRowKeys);
      props?.setMyList(selectedRowKeys);
    },
    selectedRowKeys: myList,
  };
  /*选中的总额*/
  const SelectTotal = fees
    ? countTotal(averageTypeMap[costAverageType], fees)
    : '';
  console.log('SelectTotal', SelectTotal);
  return (
    <div>
      <Row>
        <Form.Item
          name={['costReconciliationFee', 'costReconciliationType']}
          noStyle
        >
          <Radio.Group style={{ marginBottom: 8 }} buttonStyle="solid">
            <Radio.Button value={1}>不分摊</Radio.Button>
            <Radio.Button value={2}>分摊到运单</Radio.Button>
          </Radio.Group>
        </Form.Item>
      </Row>
      <Row>
        <Col>
          <Form.Item name={['costReconciliationFee', 'averageType']} noStyle>
            <Select
              style={{ marginRight: '5px' }}
              options={convertObjectToArray(averageType)}
            />
          </Form.Item>
          {/* <Button type={'primary'}>计算</Button>*/}
        </Col>
      </Row>
      {/*结构类型*/}
      {props?.costReconciliationType !== 1 && (
        <Table
          columns={
            props?.costReconciliationType === 2
              ? OtherColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: totalMoney,
                  costReconciliationType: props?.costReconciliationType,
                  myList: myList,
                  spaceFee: spaceFee,
                })
              : OtherColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: props?.totalMoney,
                  myList: myList,
                  costReconciliationType: props?.costReconciliationType,
                  spaceFee: spaceFee,
                }).filter((i: any) => i.title !== '运单号')
          }
          // @ts-ignore
          rowSelection={
            isDetail
              ? false
              : {
                  ...rowSelection,
                }
          }
          dataSource={fees}
          rowKey="waybillId"
          expandable={{
            childrenColumnName: costReconciliationType === 2 ? 'subList' : 'x',
            defaultExpandAllRows: true,
          }}
        />
      )}
    </div>
  );
};
export default OtherCostSharing;
