/*成本分摊*/
import { TableRowSelection } from 'antd/es/table/interface';
import { Col, Form, Radio, Row, Select, Table } from 'antd';
import { SharingColumns, SharingColumns2 } from './columns';
import React from 'react';
import { calculateSingSum } from '@/utils/utils';
import {
  averageType,
  averageTypeMap,
  convertObjectToArray,
} from '@/utils/constant';
import Decimal from 'decimal.js';

const CostSharing = (props: any) => {
  const {
    fees,
    myList,
    costReconciliationType,
    costAverageType,
    totalMoney,
    counterpartyType,
    spaceFee,
    currency,
  } = props;
  /*计算选中总价 */
  console.log('fees', fees);
  const countTotal = (type: any, array: any) => {
    let total: any = 0;
    array?.map((item: any) => {
      if (myList.includes(item.id)) {
        total += Number(item['fee'][type]);
      }
      return total;
      // 对象拥有该属性
    });
    return total;
  };
  /*选中的选项*/
  const rowSelection: TableRowSelection<any> = {
    checkStrictly: false,
    onChange: (selectedRowKeys) => {
      props?.setMyList(selectedRowKeys);
    },
    selectedRowKeys: myList,
  };
  /*选中的总额*/
  const SelectTotal = fees
    ? countTotal(averageTypeMap[costAverageType], fees[0]?.subList)
    : '';
  console.log('SelectTotal', SelectTotal);
  return (
    <div>
      <Row>
        <Form.Item
          name={['costReconciliationFee', 'costReconciliationType']}
          noStyle
        >
          <Radio.Group style={{ marginBottom: 8 }} buttonStyle="solid">
            <Radio.Button value={1}>不分摊</Radio.Button>
            <Radio.Button value={2}>
              {counterpartyType === 'Declaration'
                ? '分摊到运单+主/子提单'
                : '分摊到运单+主提单'}
            </Radio.Button>
            <Radio.Button value={3}>
              {' '}
              {counterpartyType === 'Declaration'
                ? '分摊到主/子提单'
                : '分摊到主提单'}
            </Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Col className='ml-10px'>
          <Form.Item name={['costReconciliationFee', 'averageType']} noStyle>
            <Select
              style={{ marginRight: '5px' }}
              options={convertObjectToArray(averageType)}
            />
          </Form.Item>
          {/* <Button type={'primary'}>计算</Button>*/}
        </Col>
      </Row>
      {/* <Row>
        <Col>
          <Form.Item name={['costReconciliationFee', 'averageType']} noStyle>
            <Select
              style={{ marginRight: '5px' }}
              options={convertObjectToArray(averageType)}
            />
          </Form.Item>
        </Col>
      </Row> */}
      {/*结构类型*/}
      {props?.costReconciliationType !== 1 && (
        <Table
          columns={
            props?.costReconciliationType === 2
              ? SharingColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: totalMoney,
                  costReconciliationType: props?.costReconciliationType,
                  myList: myList,
                  spaceFee: spaceFee,
                  currency: currency,
                })
              : SharingColumns({
                  type: props?.costAverageType,
                  SelectTotal: SelectTotal,
                  totalMoney: props?.totalMoney,
                  myList: myList,
                  spaceFee: spaceFee,
                  currency: currency,
                  costReconciliationType: props?.costReconciliationType,
                }).filter((i: any) => i.title !== '运单号')
          }
          // @ts-ignore
          rowSelection={{
            ...rowSelection,
          }}
          dataSource={fees}
          rowKey={'id'}
          expandable={{
            childrenColumnName: costReconciliationType === 2 ? 'subList' : 'x',
            defaultExpandAllRows: true,
          }}
        />
      )}
    </div>
  );
};
export default CostSharing;
