import {
  ModalForm,
  ProFormSelect,
  // ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Col, Form, message, Row, Tabs, Upload } from 'antd';
// import SelectionOfProvincesAndCities from '../components/SelectionOfProvincesAndCities';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { convertObjectToArray, currency_map } from '@/utils/constant';
import {
  GetCustomFee,
  GetCustomRate,
  getPreviewBill,
  showAddFeePreViews,
  showFeePreViews,
  UploadFeeFile,
} from '@/services/bol';
import WaybillSurcharge from './WaybillSurcharge';
import CostSharing from './CostSharing';
import { UploadOutlined } from '@ant-design/icons';
import Search from 'antd/es/input/Search';
import OtherCostSharing from '@/components/AddBillsFee/OtherCostSharing';
import { addFeeManually } from '@/services/financeApi';

const AddBillsFee = (props: any) => {
  const type = props?.type || 'primary';
  const title = props?.title || '添加费用';
  /*标题名称*/
  const TopTitle = props?.TopTitle || title;
  /*费用类型*/
  const counterpartyType = props?.types;

  /*是否是详情*/
  const isDetail = !!props.id;

  const [open, setOpen] = useState<any>(false);
  /*成本分摊的值*/
  const [fees, setFees] = useState<any>();
  /*运单加收的值*/
  const [addfees, setAddFees] = useState<any>();
  /*加收原始值*/
  const [originalData, setOriginalData] = useState<any>();

  /*1成分分摊2运单加收*/
  const [waybillType, setWaybillType] = useState<string>('1');
  /*加收选中项*/
  const [myAddList, setMyAddList] = useState<any>([]);
  /*均摊选中项*/
  const [myList, setMyList] = useState<any>([]);
  const [form] = Form.useForm<any>();
  const [fileList, setFileList] = useState<any>([]);
  /*费用名称选中*/
  const [feeOption, setFeeOption] = useState<any>([]);
  console.log('myAddList', myAddList);
  console.log('open', open);
  /*运单加收 运单是否加收*/
  const isAddedWaybillFee = Form.useWatch(
    ['addedWaybillFee', 'addedWaybillState'],
    form,
  );
  /*成本汇算 分摊类型*/
  const costAverageType = Form.useWatch(
    ['costReconciliationFee', 'averageType'],
    form,
  );
  /*运单加收 分摊类型*/
  const addAverageType = Form.useWatch(
    ['addedWaybillFee', 'averageType'],
    form,
  );
  /*成本汇算 成本汇算类型*/
  const costReconciliationType = Form.useWatch(
    ['costReconciliationFee', 'costReconciliationType'],
    form,
  );
  /*预算金额*/
  const totalMoney = Form.useWatch(['spaceFee', 'estimateFee'], form);
  /*汇率*/
  const spaceFee = Form.useWatch(['spaceFee', 'rate'], form);
  /*服务商*/
  const serviceName = Form.useWatch(['spaceFee', 'service'], form);
  /*币种*/
  const currency = Form.useWatch(['spaceFee', 'currency'], form);
  /*获取展示费用*/
  const getFees = async () => {
    try {
      if (!serviceName) {
        //没有编号不请求接口
        return;
      }
      /*成本分摊*/
      if (waybillType === '1') {
        if (
          counterpartyType === 'TailChannel' ||
          counterpartyType === 'TransChannel'
        ) {
          /*成本分摊 尾程和卖货一层结构20.3*/
          if (!!form.getFieldValue('keyword')) {
            const { status, data } = await showAddFeePreViews({
              keyword: form.getFieldValue('keyword'),
              costReconciliationType: costReconciliationType,
              counterpartyType: counterpartyType,
            });
            if (status) {
              setFees(data);
            }
          }
        } else {
          /*报关清关订舱拖车查 二层或者三层结构20.4*/
          const { status, data } = await showFeePreViews({
            keyword: form.getFieldValue('keyword'),
            type: waybillType,
            costReconciliationType: costReconciliationType,
            addedWaybillState: isAddedWaybillFee,
            counterpartyType: counterpartyType,
          });
          if (status) {
            let newData = [];
            /*三层结果后端字段错了转换为sublist*/
            if (counterpartyType === 'Declaration') {
              newData = data?.formList?.map((Fitem: any, Findex: any) => {
                return {
                  ...Fitem,
                  fee: Fitem?.fee,
                  subBlno: Fitem?.subBlno,
                  id: `${Findex}`,
                };
              });
            } else {
              /*二层结构不处理*/
              newData = data?.subList?.map((Fitem: any, Findex: any) => {
                return {
                  ...Fitem,
                  id: `${Findex}`,
                };
              });
            }
            if (data) {
              setFees([{ ...data, subList: newData, id: '10000000' }]);
            } else {
              setFees([]);
            }
          }
        }
      } else {
        /*运单加收*/
        const { status, data } = await showAddFeePreViews({
          keyword: form.getFieldValue('keyword'),
          type: waybillType,
          costReconciliationType: costReconciliationType,
          addedWaybillState: isAddedWaybillFee,
          counterpartyType: counterpartyType,
        });
        if (status) {
          setOriginalData(data);
          setAddFees(data);
        }
      }
    } catch (err) {
      console.log('获取信息抛出异常: ', err);
    }
  };

  /*获取费用列表*/
  useEffect(() => {
    getFees();
  }, [isAddedWaybillFee, costReconciliationType, waybillType]);
  /*每次打开设置选中项为空*/
  useEffect(() => {
    if (open) {
      setMyAddList([]);
      setMyList([]);
    }
  }, [open]);
  /*上传文件*/
  const uploadFile = async (res: any) => {
    //setLoading(true)
    const formData = new FormData();
    if (fileList.length === 0) {
      return;
    }
    fileList.forEach((item: any) => {
      formData.append('multipartFiles', item);
    });

    formData.append('id', res?.billId);
    try {
      const { status } = await UploadFeeFile(formData);
      if (status) {
        message.success('操作成功');
        //setLoading(false);
      }
    } catch (error) {
      //setLoading(false);
    }
  };
  const onFinish = async (values: any) => {
    /*初始值为init的值*/
    let costReconciliationFee = values.costReconciliationFee;
    if (costReconciliationType !== 1) {
      /*只有运单加收不为空的时候才计算*/
      costReconciliationFee = {
        ...values.costReconciliationFee,
        blno: fees[0].blno,
        spaceId: fees[0].spaceId,
        fee: fees[0].fee,
        select: true,
        costReconciliationType,
        averageType: costAverageType,
      };
      const result = fees[0]?.subList?.map((item: any) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { id, ...newItem } = item;
        return {
          ...newItem,
          subList: item?.subList?.map((i: any) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { id, ...newI } = i;
            return {
              ...newI,
              select: myList.includes(i?.id),
            };
          }),
          select: myList.includes(item?.id),
        };
      });
      if (counterpartyType === 'Declaration') {
        //报关三层结构
        costReconciliationFee.formList = result;
      } else if (
        /*尾程一层结构*/
        counterpartyType === 'TailChannel' ||
        counterpartyType === 'TransChannel'
      ) {
        costReconciliationFee.waybillFees = fees.map((i: any) => {
          const { waybillNo, waybillId, ...oldItem } = i;
          return {
            ...oldItem,
            waybillId: waybillId,
            select: myList.includes(waybillId),
          };
        });
      } else {
        //二层结构
        costReconciliationFee.subList = result;
      }
    }
    const params: any = {
      service_id: 'Waybill',
      keyword: form.getFieldValue('keyword'),
      counterpartyType: counterpartyType,
      spaceFee: values.spaceFee,
      costReconciliationFee: costReconciliationFee,
      /*运单加收*/
      addedWaybillFee: {
        addedWaybillState: isAddedWaybillFee || 0,
        averageType: addAverageType,
        feeName: values?.addedWaybillFee?.feeName,
        fee: originalData?.fee,
        select: true,
        waybillFees: originalData?.map((item: any) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...newItem } = item;
          return {
            ...item,
            select: myAddList.includes(item?.waybillId),
          };
        }),
      },
    };
    /*自主填写分摊逻辑*/
    if (values?.Sharing) {
      //获取自主填写的数据
      const keys = Object.keys(values?.Sharing);
      params.costReconciliationFee.subList = fees[0]?.subList?.map(
        (item: any) => {
          /*去掉id*/
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...newItem } = item;
          return {
            ...newItem,
            fee: {
              ...newItem.fee,
              amount: keys.includes(id) ? values?.Sharing[id] : '0',
            },
            select: myList.includes(item?.id),
          };
        },
      );
    }
    /*自主填写附件费逻辑*/
    if (values?.Surcharge) {
      //获取自主填写的数据
      const keys = Object.keys(values?.Surcharge);
      params.addedWaybillFee.waybillFees = originalData?.map((item: any) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { waybillId, ...newItem } = item;
        return {
          ...newItem,
          amount: keys.includes(waybillId) ? values?.Surcharge[waybillId] : '0',
          select: true,
        };
      });
    }
    /*如果是尾程或者卖货修改包裹对象的名称后端要求*/
    if (
      counterpartyType === 'TailChannel' ||
      counterpartyType === 'TransChannel'
    ) {
      params.waybillCostReconciliationFee = params.costReconciliationFee;
      delete params.costReconciliationFee;
    }
    const { status, data } = await addFeeManually(params);
    if (status) {
      await uploadFile(data);
      message.success('提交成功');
      props?.getDetail();
      return true;
    } else {
      return false;
    }
  };
  const tabChange = (key: any) => {
    setWaybillType(key);
  };
  /*费用名称改变*/
  const feeNameChange = async (v: any) => {
    /*调用接口获取预计值*/
    const { status, data } = await GetCustomFee({
      feeType: v,
      keyword: form.getFieldValue('keyword'),
      counterpartyType: counterpartyType,
    });
    if (status) {
      if (data === 'null') {
        form.setFieldValue(['spaceFee', 'estimateFee'], '');
      } else {
        /*设置默认价格*/
        form.setFieldValue(['spaceFee', 'estimateFee'], Object.keys(data)[0]);
        /*设置默认币种*/
        /*   form.setFieldValue(['spaceFee', 'currency'], {
          value: Object.values(data)[0],
          // @ts-ignore
          label: currency_map[Object.values(data)[0]],
        });*/
      }
    }
  };
  /*币种改变*/
  const currencyChange = async (v: any) => {
    /*调用接口获取预计值*/
    const { status, data } = await GetCustomRate({
      fromCode: v,
      toCode: 10,
    });
    if (status) {
      if (!data) {
        form.setFieldValue(['spaceFee', 'rate'], '');
      } else {
        form.setFieldValue(['spaceFee', 'rate'], data?.exchangeRate);
      }
    }
  };

  const getServiceInfo = async (id: any) => {
    const res = await getPreviewBill({
      keyword: id,
      counterpartyType: counterpartyType,
    });
    const { status, data } = res;
    if (status) {
      /*设置服务商*/
      form.setFieldValue(['spaceFee', 'service'], data?.agentName);
      const transformedData = data?.feeType?.map((item: any) => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return { label: value, value: key };
      });
      setFeeOption(transformedData);
    }
  };
  const items: any = [
    {
      key: '1',
      label: `成本分摊`,
      children:
        counterpartyType !== 'TransChannel' &&
        counterpartyType !== 'TailChannel' ? (
          <CostSharing
            setMyList={setMyList}
            myList={myList}
            costReconciliationType={costReconciliationType}
            costAverageType={costAverageType}
            fees={fees}
            isDetail={isDetail}
            totalMoney={totalMoney}
            spaceFee={spaceFee}
            currency={currency}
            counterpartyType={counterpartyType}
          />
        ) : (
          <OtherCostSharing
            setMyList={setMyList}
            myList={myList}
            costReconciliationType={costReconciliationType}
            costAverageType={costAverageType}
            fees={fees}
            spaceFee={spaceFee}
            isDetail={isDetail}
            totalMoney={totalMoney}
            currency={currency}
          />
        ),
    },
    {
      key: '2',
      label: `运单加收`,
      children: (
        <WaybillSurcharge
          isAddedWaybillFee={isAddedWaybillFee}
          myAddList={myAddList}
          addfees={addfees}
          setMyAddList={setMyAddList}
          isDetail={isDetail}
          totalMoney={totalMoney}
          spaceFee={spaceFee}
          currency={currency}
          addAverageType={addAverageType}
        />
      ),
    },
  ];
  return (
    <ModalForm
      title={TopTitle}
      trigger={
        <Button type={type} ghost={props?.ghost}>
          {title}
        </Button>
      }
      form={form}
      initialValues={{
        costReconciliationFee: {
          costReconciliationType: 1,
          averageType: '1',
        },
        addedWaybillFee: { addedWaybillState: 0, averageType: '1' },
        spaceFee: { currency: '10', rate: '1' },
      }}
      width={1200}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 8 }}
      autoComplete="off"
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      className={styles.wrap}
      layout="horizontal"
      submitTimeout={2000}
      onOpenChange={() => setOpen(!open)}
      onFinish={onFinish}
    >
      <Row>
        <Col span={8}>
          <Form.Item
            name={'keyword'}
            label={'编号'}
            rules={[{ required: true, message: '必填项不能为空' }]}
          >
            <Search
              placeholder="请输入编号"
              onSearch={getServiceInfo}
              style={{ width: 250 }}
            />
          </Form.Item>
          {/*   <ProFormText
            name={'keyword'}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="编号"
            fieldProps={{
              onBlur: (e) => {
                getServiceInfo(e.target.value);
              },
            }}
          />*/}
        </Col>
        <Col span={8}>
          <ProFormText
            name={['spaceFee', 'service']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="服务商"
            disabled={true}
            options={convertObjectToArray(currency_map)}
            fieldProps={{
              onChange: (value) => {
                currencyChange(value);
              },
              filterOption: false,
            }}
          />
        </Col>
        <Col span={8}>
          <ProFormSelect
            name={['spaceFee', 'feeType']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="费用名称"
            disabled={isDetail}
            options={feeOption}
            fieldProps={{
              onChange: (value) => {
                feeNameChange(value);
              },
              filterOption: false,
            }}
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name={['spaceFee', 'estimateFee']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="费用"
            fieldProps={{
              style: { width: 250 },
            }}
            disabled={isDetail}
          />
        </Col>
        <Col span={8}>
          <ProFormSelect
            name={['spaceFee', 'currency']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="币种"
            disabled={isDetail}
            options={convertObjectToArray(currency_map)}
            fieldProps={{
              onChange: (value) => {
                currencyChange(value);
              },
              filterOption: false,
            }}
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name={['spaceFee', 'rate']}
            rules={[{ required: true, message: '必填项不能为空' }]}
            label="汇率"
            disabled={isDetail}
          />
        </Col>
        <Col span={8}>
          <Form.Item label={'附件'}>
            <Upload
              name="certificateFiles"
              multiple
              fileList={fileList}
              onRemove={(e) => {
                setFileList(
                  fileList?.filter((item: any) => item?.uid !== e.uid),
                );
              }}
              customRequest={({ file }) => {
                fileList.push(file);
                setFileList([...fileList]);
              }}
            >
              {!isDetail && <Button icon={<UploadOutlined />}>上传文件</Button>}
            </Upload>
          </Form.Item>
        </Col>
      </Row>

      {serviceName && (
        <div
          style={{
            width: '1200px',
            background: '#f4f4f4',
            position: 'relative',
            right: '24px',
            height: '10px',
          }}
        ></div>
      )}
      <div>
        {serviceName && (
          <Tabs defaultActiveKey="1" items={items} onChange={tabChange} />
        )}
      </div>
    </ModalForm>
  );
};
export default AddBillsFee;
