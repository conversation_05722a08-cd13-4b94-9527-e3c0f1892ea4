.dynamic_search {
    width: 100%;
    padding: 30px 30px 0 30px;
    background-color: #fff;
    :global {
        .ant-form-item  {
            margin-bottom: 10px;
        }
    }
    .employee_avatar {
        display: flex;
        flex-wrap: wrap;
        .employee {
            position: relative;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            img:hover {
                cursor: pointer;
            }
            .groupRenderYounger {
                position: absolute;
                top: -6px;
                right: -6px;
                width: 16px;
                height: 16px;
                background-color: #707070;
                border-radius: 50%;
                z-index: 99;
                cursor: pointer;
                opacity: 0;
                transition: all 1s;
                text-align: center;
                line-height: 14px;
            }
        }
        .employee:hover {
            .groupRenderYounger {
                opacity: 1;
            }
        }
       
    }
}
.modal_style {
    width: 800px;
    min-height: 98px;
    background-color: #fff;
    border-radius: 10px;
    border: 1px solid #DEEFF5;
    padding: 22px 0 0 10px;
}