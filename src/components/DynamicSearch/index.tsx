import {
  getAddressInformationAPI,
  getClientListAPI,
  getSearchAddressAPI,
} from '@/services/home/<USER>';
import {
  getInstanceStatisticsAPI,
  getProviderType,
  getUserListAPI,
  getWarehouseListAPI,
  shippingAgencyListAPI,
} from '@/services/booking';
import { ProFormSelect } from '@ant-design/pro-components';
import {
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
  Avatar,
  Popconfirm,
  Modal,
  Checkbox,
  Button,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
const { RangePicker } = DatePicker;
import {
  getFinanceSubjectListAPI,
  getIDCodeRule,
  getProductList,
} from '@/services/productAndPrice/api';
import { formatTime } from '@/utils/format';
import UserSelector from '../UserSelector';
import { CloseOutlined } from '@ant-design/icons';
import _ from 'lodash';
import dayjs from 'dayjs';
const { TextArea } = Input;
import styles from './index.less';
import {
  getCustomGroupAPI,
  getLabelListAPI,
} from '@/services/customer/CustomerHome';
const DynamicSearch = React.memo((props: any) => {
  const { conditions, refresh, labelCol, colSpan, disabled, systemSetting } =
    props;
  const [form] = Form.useForm();
  const [searchParameter, setSearchParameter] = useState<any>({});
  const [userList, setUserList] = useState<any>([]);
  const [dutIds, setDutIds] = useState<any>({});
  const userRef = useRef<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [listData, setListData] = useState([]);
  const [statement, setStatement] = useState<any>([]);
  const [userSelector, setUserSelector] = useState([]);
  const [collectData, setCollectData] = useState<any>([]);
  const [report, setReport] = useState([]);
  const flagRequest = useRef<any>(true);
  const keyIndex = useRef<any>(0);
  useEffect(() => {
    if (conditions) {
      for (const key in conditions) {
        conditions[key]['userSoleId'] = key;
        if (!conditions[key]?.value) {
          conditions[key]['value'] = '';
        }
        if (!conditions[key]?.valueDesc) {
          conditions[key]['valueDesc'] = '';
        }
        if (
          [
            'address',
            'city',
            'product',
            'provider',
            'warehouse',
            'client',
            'providerType',
            'organization',
          ].includes(conditions[key]?.type)
        ) {
          conditions[key]['selector'] = 'type';
        }
      }
      let newConditions = Object.values(conditions);
      let user: any = newConditions?.filter((ele: any) => ele?.type === 'user');
      if (user?.length && typeof disabled !== 'undefined') {
        userData();
      }
      setSearchParameter(conditions);
      if (systemSetting) {
        refresh(
          Object.keys(searchParameter).length === 0
            ? conditions
            : searchParameter,
        );
      }
    }
  }, [conditions]);

  useEffect(() => {
    if (conditions && flagRequest.current) {
      // for (const key in conditions) {
      //     conditions[key]['key'] = generateUniqueId()
      // }
      let newConditions = Object.values(conditions);
      let user: any = newConditions?.filter((ele: any) => ele?.type === 'user');
      const report: any = newConditions?.filter(
        (ele: any) => ele?.type === 'statistics',
      );
      if (user?.length) {
        userData();
      }

      // let userValue: any = user.map((ele: any) => {
      //     // 多选回填
      //     if (ele?.value?.indexOf(',') !== -1) {
      //         return ele?.value?.split(',')
      //     } else {
      //         return ele?.value
      //     }
      // }).flat()

      // let newData = userSelector?.
      //     filter((ele: any) => {
      //         if (userValue.includes(ele?.user?.id)) {
      //             return {
      //                 ...ele,
      //                 userSoleId: user?.filter((item: any) => item?.value?.includes(item))[0]?.userSoleId
      //             }
      //         }
      //     })
      // setUserList(newData);
      // if (user?.length) {
      //     userData()
      // }
      if (report?.length) {
        setStatement(user[0]?.value?.split(',')?.filter(Boolean));
        getServiceStatistics(user[0]?.module);
      }
      const selectRange = Object?.values(conditions)
        ?.filter((ele: any) => ele?.type === 'select')
        ?.map((ele: any) => ele?.range)
        .flat()
        ?.map((fid: any) => ({ ...fid, id: fid.value, name: fid.label }));
      collectData.push(...selectRange);
      setCollectData([...collectData]);
    }
    if (conditions) {
      flagRequest.current = false;
    }
  }, [conditions]);

  const defaultTime = (time: string): any => (time ? dayjs(time) : time);
  const getDisabled = () => (disabled ? true : false);
  const CustomizeComponents = (item: any) => {
    if (['search', 'text'].includes(item?.type)) {
      return (
        <Input
          placeholder="请输入关键字"
          defaultValue={item?.value}
          allowClear
          onChange={(e: any) => {
            getTextValue(e, item);
          }}
          disabled={getDisabled()}
        />
      );
    } else if (item?.type === 'number') {
      return (
        <Input
          placeholder="请输入关键字"
          type="number"
          defaultValue={item?.value}
          onChange={(e: any) => {
            getTextValue(e, item);
          }}
          disabled={getDisabled()}
        />
      );
    } else if (item?.type === 'textarea') {
      return (
        <TextArea
          autoSize={{ minRows: 2, maxRows: 12 }}
          placeholder="请输入关键字"
          allowClear
          maxLength={100}
          defaultValue={item?.value}
          onChange={(e: any) => {
            getTextValue(e, item);
          }}
          disabled={getDisabled()}
        />
      );
    } else if (item?.type === 'select') {
      return (
        <Select
          mode={item?.multi ? 'multiple' : (undefined as any)}
          options={item.range}
          disabled={getDisabled()}
          allowClear
          // defaultValue={defaultValue(item)}
          value={defaultValue(item)}
          onChange={(e: any) => editParameter(e, item)}
          placeholder="请选择"
        />
      );
    } else if (item?.type === 'dateTimeRange') {
      return (
        <RangePicker
          showTime={{
            defaultValue:
              item?.startTime && item?.endTime
                ? [dayjs(item?.startTime), dayjs(item?.endTime)]
                : [
                    dayjs('00:00:00', 'HH:mm:ss'),
                    dayjs('23:59:59', 'HH:mm:ss'),
                  ],
          }}
          style={{ width: '100%' }}
          onChange={(e: any) => editParameter(e, item)}
          disabled={getDisabled()}
        />
      );
    } else if (item?.type === 'dateTime') {
      return (
        <DatePicker
          showTime
          defaultValue={defaultTime(item?.value)}
          style={{ width: '100%' }}
          onChange={(e: any) => editParameter(e, item)}
          disabled={getDisabled()}
        />
      );
    } else if (item?.type === 'date') {
      return (
        <DatePicker
          defaultValue={defaultTime(item?.value)}
          onChange={(e: any) => editParameter(e, item)}
          disabled={getDisabled()}
        />
      );
      // 收件地址
    } else if (item?.type === 'address') {
      return formSelectData(item);
      // 揽件仓库
    } else if (item?.type === 'warehouse') {
      return formSelectData(item);
    }
    // 服务商
    else if (item?.type === 'provider') {
      return formSelectData(item);
    }
    // 销售产品
    else if (item?.type === 'product') {
      return formSelectData(item);
    }
    // 城市
    else if (item?.type === 'city') {
      return formSelectData(item);
    }
    // 客户
    else if (item?.type === 'client') {
      return formSelectData(item);
    }
    // 客户标签
    else if (item?.type === 'clientTags') {
      return formSelectData(item);
    }
    // ID编码规则
    else if (item?.type === 'idRule') {
      return formSelectData(item);
    }
    // 客户分组
    else if (item?.type === 'clientGroup') {
      return formSelectData(item);
    }
    // 服务商类型
    else if (item?.type === 'providerType') {
      return formSelectData(item);
    }
    // FBA仓库
    else if (item?.type === 'FBA') {
      return formSelectData(item);
    }
    // 财务主体
    else if (item?.type === 'organization') {
      return formSelectData(item);
    }
    // 报表
    else if (item?.type === 'statistics') {
      return (
        <div>
          {report
            ?.map((fid: any) => fid?.name)
            ?.filter(Boolean)
            ?.join('，')}
          &nbsp;&nbsp;<a onClick={() => setIsModalOpen(true)}>选择</a>
        </div>
      );
    }
    return <div></div>;
  };

  const formSelectData = (ele: any): any => {
    return (
      <ProFormSelect
        fieldProps={{
          filterOption: false,
          // defaultValue: defaultValue(ele),
          value: defaultValue(ele),
          onChange: (e: any) => {
            editParameter(e, ele);
          },
        }}
        mode={ele?.multi ? 'multiple' : (undefined as any)}
        showSearch
        placeholder="请选择，支持模糊搜索"
        debounceTime={1000}
        disabled={getDisabled()}
        request={async ({ keyWords }) => {
          // 地址
          if (ele?.type === 'address') {
            const { status, data } = await getAddressInformationAPI({
              token: keyWords,
            });
            if (status) {
              return [
                { label: '全部', value: 'all' },
                ...data.list.map((item: any) => {
                  return {
                    label: `${item.country.replaceAll('CN', '中国')} - ${
                      item.provinceShortName
                    } / ${item.province} - ${item.city} ${
                      item.county ? '-' : ''
                    } ${item.county} / ${item.zipCode}`,
                    value: item?.id,
                  };
                }),
              ];
            }
            // 仓库
          } else if (ele?.type === 'warehouse') {
            const { status, data } = await getWarehouseListAPI({
              keyword: keyWords,
              type: 0,
            });
            if (status) {
              return newData(data);
            }
            // 服务商
          } else if (ele?.type === 'provider') {
            const { status, data } = await shippingAgencyListAPI({
              type: ele?.providerType,
              keyword: keyWords,
            });
            if (status) {
              return newData(data);
            }
            // 产品
          } else if (ele?.type === 'product') {
            const { status, data } = await getProductList({
              keyword: keyWords,
            });
            if (status) {
              return newData({
                list: [{ name: '全部', id: 'all' }, ...data?.list],
              });
            }
            // 客户
          } else if (ele?.type === 'client') {
            const { status, data } = await getClientListAPI({
              keyword: keyWords,
            });
            if (status) {
              return newData({
                list: [{ name: '全部', id: 'all' }, ...data?.list],
              });
            }
            // 客户分组
          } else if (ele?.type === 'clientGroup') {
            const { status, data } = await getCustomGroupAPI({
              keyword: keyWords,
            });
            if (status) {
              return newData(data);
            }
          }
          // ID编码规则
          else if (ele?.type === 'idRule') {
            const { status, data } = await getIDCodeRule({
              keyword: keyWords,
            });
            if (status) {
              return newData(data);
            }
          } else if (ele?.type === 'clientTags') {
            const { status, data } = await getLabelListAPI({
              keyword: keyWords,
            });
            if (status) {
              return data.map((item: any) => {
                return {
                  label: item?.tag,
                  value: item?.tag,
                };
              });
            }

            // 城市
          } else if (ele?.type === 'city') {
            const { status, data } = await getSearchAddressAPI({
              keyword: keyWords,
            });
            if (status) {
              return [
                { label: '全部', value: 'all' },
                ...data.list.map((item: any) => {
                  return {
                    label: `${
                      item.country === 'CN' ? '中国' : item.country
                    } - ${item.provinceShortName} / ${item.province} - ${
                      item.city
                    } -${item.county}/ ${item.zipCode}`,
                    value: item?.id,
                  };
                }),
              ];
            }
            // 服务商类型
          } else if (ele?.type === 'providerType') {
            const { status, data } = await getProviderType({
              keyword: keyWords,
            });
            if (status) {
              return newData({
                list: [
                  { name: '全部', id: 'all' },
                  ...data?.list?.map((fid: any) => ({
                    ...fid,
                    id: fid?.type + '',
                    name: fid?.desc,
                  })),
                ],
              });
            }
            // FBA仓库
          } else if (ele?.type === 'FBA') {
            const { status, data } = await getWarehouseListAPI({
              keyword: getDisabled() ? ele?.value : keyWords,
              type: 1,
            });
            if (status) {
              return data.list.map((item: any) => {
                return {
                  label: item?.name,
                  value: item?.name,
                };
              });
            }
            // 财务主体
          } else if (ele?.type === 'organization') {
            const { status, data } = await getFinanceSubjectListAPI({
              keyword: keyWords,
            });
            if (status) {
              return [
                { label: '全部', value: 'all' },
                ...data.map((item: any) => {
                  return {
                    label: item?.name,
                    value: item?.id,
                  };
                }),
              ];
            }
          }
          return [];
        }}
      />
    );
  };
  // 值回填
  const defaultValue = (item: any) => {
    if (item?.multi) {
      return item?.value?.split(',')?.filter(Boolean);
    } else {
      return item?.value || item?.value === 0 ? (
        item?.value
      ) : (
        <span style={{ color: '#d9d9d9' }}>请选择，支持模糊搜索</span>
      );
    }
  };

  // text 修改值防抖
  const getTextValue = _.debounce((e: any, item: any) => {
    editParameter(e?.target?.value, item);
  }, 1000);
  // 修改参数
  const editParameter = (id: any, data: any) => {
    // 时间 改两个字段的值分开写
    if (data?.type === 'dateTimeRange') {
      for (const key in searchParameter) {
        if (data?.userSoleId === searchParameter[key]?.userSoleId) {
          searchParameter[key].startTime = id ? formatTime(id[0]) : '';
          searchParameter[key].endTime = id ? formatTime(id[1]) : '';
        }
        // if (data?.type === searchParameter[key]?.type) {
        //     searchParameter[key].startTime = id ? formatTime(id[0]) : ''
        //     searchParameter[key].endTime = id ? formatTime(id[1]) : ''
        // }
      }
      // 所有改 value 字段统一写一起
    } else if (
      [
        'address',
        'warehouse',
        'provider',
        'product',
        'city',
        'dateTime',
        'date',
        'client',
        'providerType',
        'organization',
        'FBA',
        'clientTags',
        'clientGroup',
        'idRule',
      ].includes(data?.type)
    ) {
      for (const key in searchParameter) {
        if (data?.userSoleId === searchParameter[key]?.userSoleId) {
          //  日期
          if (['dateTime', 'date'].includes(data?.type)) {
            searchParameter[key].value = id ? formatTime(id) : '';
            // 服务商类型传给后端的字段不一样，真麻烦前端，要分开处理
            // } else if (['providerType'].includes(data?.type)) {
            //     // 单选或者多选
            //     searchParameter[key].value = searchParameter[key]?.multi ? collectData.filter((ele: any) => id.includes(ele?.id)).map((item: any) => item?.type)?.join(',') : id
            //     searchParameter[key]['valueDesc'] = getValueDesc(searchParameter[key], id)
          } else {
            if (
              [
                'address',
                'city',
                'product',
                'provider',
                'warehouse',
                'client',
                'providerType',
                'organization',
                'idRule',
              ].includes(data?.type)
            ) {
              if (searchParameter[key]?.multi && data?.selector === 'type') {
                if (id?.length > 1 && id[0] === 'all') {
                  searchParameter[key].value = id
                    ?.filter((ele: any) => ele !== 'all')
                    ?.join(',');
                } else {
                  if (id?.length > 0 && !id.includes('all')) {
                    searchParameter[key].value = id.join(',');
                  } else if (!id.length) {
                    searchParameter[key].value = id?.join(',');
                  } else {
                    searchParameter[key].value = 'all';
                  }
                  // searchParameter[key].value = id?.filter((ele: any) => ele !== 'all')?.join(',')
                }
              } else {
                searchParameter[key].value = searchParameter[key]?.multi
                  ? id?.join(',')
                  : id;
              }
            } else {
              // 单选或者多选
              searchParameter[key].value = searchParameter[key]?.multi
                ? id?.join(',')
                : id;
            }
            searchParameter[key]['valueDesc'] = getValueDesc(
              searchParameter[key],
              id,
            );
          }
        }
      }
      // 这里还有一种情况，比如出现多个 select 等等，就不能以 类型 去对比改 value 只有唯一的 desc 没有其他唯一， 一切兼容后端
    } else if (
      ['search', 'text', 'select', 'number', 'textarea'].includes(data?.type)
    ) {
      for (const key in searchParameter) {
        if (data?.userSoleId === searchParameter[key]?.userSoleId) {
          if (data?.type === 'select') {
            if (searchParameter[key]?.multi && selectUnique(data, id)?.unique) {
              if (id?.length > 1 && id[0] === selectUnique(data, id)?.value) {
                searchParameter[key].value = id
                  ?.filter((fid: any) => fid !== selectUnique(data, id)?.value)
                  ?.join(',');
              } else {
                searchParameter[key].value = selectUnique(data, id)?.value;
              }
            } else {
              searchParameter[key].value = searchParameter[key]?.multi
                ? id?.join(',')
                : id;
            }
            searchParameter[key]['valueDesc'] = getValueSelectDesc(data);
          } else {
            // type不是 select 单选或者多选
            searchParameter[key].value = searchParameter[key]?.multi
              ? id?.join(',')
              : id;
          }
        }
      }
    }
    // 传给自己需要的组件
    refresh?.(searchParameter);
    // 更新最新值
    setSearchParameter({ ...searchParameter });
  };
  const selectUnique = (data: any, id: any) => {
    let result;
    if (!data?.multi) {
      result = data?.range?.filter(
        (item: any) =>
          id?.split(',')?.filter(Boolean).includes(item?.value) && item?.unique,
      );
    } else {
      result = data?.range?.filter(
        (item: any) =>
          id?.filter(Boolean).includes(item?.value) && item?.unique,
      );
    }

    if (result?.length) {
      return result[0];
    }
    return false;
  };
  // 一样的渲染字段，统一写
  const newData = (data: any) => {
    const newData = Array.from(
      new Set([...collectData, ...data?.list].map((item: any) => item.id)),
    ).map((id) =>
      [...collectData, ...data?.list].find((item: any) => item.id === id),
    );
    setCollectData(newData);
    // if (['address', 'city', 'product', 'provider', 'user', 'warehouse', 'client', 'providerType', 'organization'].includes(type || '')) {
    //     return [{ label: '全部', value: 'all' }, ...data?.list?.map((item: any) => {
    //         return {
    //             label: item?.name,
    //             value: item?.id,
    //         };
    //     })];
    // } else {
    return data?.list?.map((item: any) => {
      return {
        label: item?.name,
        value: item?.id,
      };
    });
    // }
  };
  // 根据 id 找到对应的中文
  const getValueDesc = (item: any, itemValue: string | string[]) => {
    if (item?.multi) {
      return collectData
        ?.filter((ele: any) => itemValue?.includes(ele.id))
        ?.map((ele: any) => ele.name)
        ?.filter(Boolean)
        ?.join(',');
    } else {
      return collectData
        ?.filter((ele: any) => ele.id === itemValue)
        ?.map((ele: any) => ele.name)
        ?.filter(Boolean)
        ?.join(',');
    }
  };
  const getValueSelectDesc = (itemValue: any) => {
    if (itemValue?.multi) {
      return itemValue?.range
        .filter((fid: any) => itemValue?.value?.includes(fid.value))
        ?.map((item: any) => item?.label)
        ?.filter(Boolean)
        ?.join(',');
    } else {
      return itemValue?.range
        .filter((fid: any) => fid?.value === itemValue?.value)
        ?.map((ele: any) => ele.label)
        ?.filter(Boolean)
        ?.join(',');
    }
  };
  // 人员选择的回调
  const selectUserCallback = (data: any) => {
    const check = Selective();
    let userNewData: any = [];
    for (const key in searchParameter) {
      if (key === dutIds?.userSoleId) {
        if (check === 'radio') {
          searchParameter[key].value = data[0]?.user?.id;
          searchParameter[key]['valueDesc'] = data[0]?.user?.name;
          userNewData = data.map((ele: any) => ({ ...ele, userSoleId: key }));
        } else if (check === 'checkbox') {
          const dataValue = Array.from(
            new Set([
              ...searchParameter[key].value?.split(','),
              ...data?.map((item: any) => item?.user?.id),
            ]),
          ).filter(Boolean);
          const valueDesc = Array.from(
            new Set([
              ...searchParameter[key]['valueDesc']?.split(','),
              ...data?.map((item: any) => item?.user?.name),
            ]),
          ).filter(Boolean);
          searchParameter[key].value =
            dataValue.includes('0') && dataValue.length > 1
              ? dataValue?.filter((item: any) => item !== '0').join(',')
              : dataValue?.join(',');
          searchParameter[key]['valueDesc'] =
            valueDesc.includes('不限') && valueDesc.length > 1
              ? valueDesc?.filter((item: any) => item !== '不限').join(',')
              : valueDesc?.join(',');
          userNewData = data.map((ele: any) => ({ ...ele, userSoleId: key }));
        }
      }
    }
    // const newData = Array.from(new Set([...userList, ...userNewData].map((item: any) => item.id)))
    //     .map(id => [...userList, ...userNewData].find((item: any) => item.id === id));
    // console.log([...userList, ...userNewData], '[...userList, ...userNewData]');
    const uniqueArr = [...userList, ...userNewData].filter((item, index) => {
      return (
        [...userList, ...userNewData].findIndex(
          (element) =>
            element.userSoleId === item.userSoleId && element.id === item.id,
        ) === index
      );
    });
    setCollectData(uniqueArr);
    setUserList(uniqueArr);
    refresh?.(searchParameter);
    setSearchParameter({ ...searchParameter });
  };
  // 根据后端字段判断，多选还是单选
  const Selective = () => {
    return dutIds?.multi ? 'checkbox' : 'radio';
  };
  const userData = async (dutyId?: string) => {
    try {
      const { status, data } = await getUserListAPI({
        start: 0,
        len: 500,
        dutyId,
      });
      if (status) {
        setUserSelector(data?.list);
        let newConditions = Object.values(conditions);
        let user: any = newConditions?.filter(
          (ele: any) => ele?.type === 'user',
        );
        let userValue: any = user
          .map((ele: any) => {
            // 多选回填
            if (ele?.value?.indexOf(',') !== -1) {
              return ele?.value?.split(',');
            } else {
              return ele?.value;
            }
          })
          .flat();
        setUserList(echoData(user, data?.list, userValue));
      }
    } catch (error) {}
  };
  const echoData = (user: any, list: any, userValue: string[]) => {
    const arr = [];
    for (let index = 0; index < list.length; index++) {
      for (let i = 0; i < userValue.length; i++) {
        const ele = list[index]?.user?.id;
        if (ele === userValue[i]) {
          arr.push({
            ...list[index],
            userSoleId: dutiKey(
              user?.filter((item: any) => item?.value?.includes(ele)),
            ),
          });
        }
      }
    }
    return arr;
  };
  const dutiKey = (data: any) => {
    if (data?.length > 1) {
      let userSoleId: any;
      data?.forEach((_: any, index: number) => {
        userSoleId = data[keyIndex.current]?.userSoleId;
      });
      keyIndex.current++;
      return userSoleId;
    } else if (data?.length === 1) {
      return data[0]?.userSoleId;
    }
  };
  // 获取添报表的数据
  const getServiceStatistics = async (module?: string) => {
    try {
      const { status, data } = await getInstanceStatisticsAPI({ module });
      if (status) {
        collectData.push(...data?.list);
        setCollectData([...collectData]);
        setListData(data?.list);
      }
    } catch (error) {}
  };
  // 删除人员
  const deleteStaff = (item: any, itemValue: any) => {
    const filteredArr = userList.filter(
      (ele: any) =>
        ele.userSoleId !== itemValue.userSoleId || ele.id !== itemValue.id,
    );
    for (const key in searchParameter) {
      if (key === item?.userSoleId) {
        if (item?.userSoleId === itemValue?.userSoleId) {
          searchParameter[key].value = searchParameter[key].value
            ?.split(',')
            .filter(Boolean)
            .filter((item: any) => item !== itemValue?.id)
            ?.join(',');
          searchParameter[key]['valueDesc'] = filteredArr
            ?.filter(
              (item: any) =>
                searchParameter[key].value
                  ?.split(',')
                  .includes(item?.user?.id) &&
                item?.userSoleId === itemValue?.userSoleId,
            )
            .filter(Boolean)
            ?.map((item: any) => item?.user?.name)
            .join(',');
        }
      }
    }
    setUserList(filteredArr);
    refresh?.(searchParameter);
    setSearchParameter({ ...searchParameter });
  };
  // 确定的回调
  const handleOk = () => {
    for (const key in searchParameter) {
      if (searchParameter[key].type === 'statistics') {
        searchParameter[key].value = statement?.join(',');
        searchParameter[key]['valueDesc'] = listData
          ?.filter((ele: any) => statement.includes(ele?.key))
          ?.map((item: any) => item?.name)
          .filter(Boolean)
          ?.join(',');
      }
    }
    refresh?.(searchParameter);
    setSearchParameter({ ...searchParameter });
    setReport(listData?.filter((ele: any) => statement?.includes(ele?.key)));
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  // 生成不重复的id
  function generateUniqueId() {
    return Math.random().toString(36).substr(2, 9);
  }
  return (
    <div className={styles.dynamic_search}>
      <UserSelector
        ref={userRef}
        control={dutIds?.desc && Selective()}
        selectUserCallback={selectUserCallback}
        dutyId={dutIds?.dutyId as any}
      />
      <Form form={form} labelCol={{ span: labelCol || 6 }}>
        {conditions && (
          <Row>
            {Object.values(conditions)?.map((item: any, index: number) => (
              <Col
                key={index}
                span={colSpan || 8}
                style={{ minHeight: '60px' }}
              >
                <Form.Item label={item?.desc || '条件'}>
                  {item?.type === 'user' ? (
                    <div className={styles.employee_avatar}>
                      {userList.map((ele: any) => {
                        return (
                          <div key={`${ele?.userSoleId}_${ele?.id}`}>
                            {ele?.userSoleId === item?.userSoleId && (
                              <div className={styles.employee}>
                                <Avatar
                                  size="large"
                                  src={`https://static.kmfba.com/${ele?.user?.avatar}`}
                                  style={{ width: '30px', height: '30px' }}
                                />
                                &nbsp;
                                <span
                                  style={{ fontSize: '13px', color: '#707070' }}
                                >
                                  {ele?.user?.name}
                                </span>
                                <Popconfirm
                                  title="想好了确认删除该人员吗?"
                                  okText="确认"
                                  cancelText="再想想"
                                  onConfirm={() => {
                                    deleteStaff(item, ele);
                                  }}
                                >
                                  {!getDisabled() && (
                                    <span className={styles.groupRenderYounger}>
                                      <CloseOutlined
                                        style={{ color: '#fff', fontSize: 10 }}
                                        rev={undefined}
                                      />
                                    </span>
                                  )}
                                </Popconfirm>
                              </div>
                            )}
                          </div>
                        );
                      })}
                      {!getDisabled() && (
                        <a
                          onClick={() => {
                            setDutIds(item);
                            userRef?.current?.control();
                          }}
                          style={{ marginTop: '3px' }}
                        >
                          选择
                        </a>
                      )}
                    </div>
                  ) : (
                    CustomizeComponents(item)
                  )}
                </Form.Item>
              </Col>
            ))}
          </Row>
        )}
      </Form>
      <Modal
        title="选择报表"
        destroyOnClose
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        style={{ minWidth: '900px', top: '30px' }}
        footer={[
          <div key="btn" style={{ display: 'flex', justifyContent: 'center' }}>
            <Button key="back" onClick={handleCancel}>
              取消
            </Button>
            <Button key="submit" type="primary" onClick={handleOk}>
              确认
            </Button>
          </div>,
        ]}
      >
        <div
          style={{
            width: '100%',
            maxHeight: '550px',
            overflowY: 'auto',
            paddingBottom: '20px',
          }}
        >
          <Checkbox.Group
            value={statement}
            onChange={(e: any) => {
              setStatement(e);
            }}
          >
            {listData?.map((item: any, i: number) => (
              <div
                key={item.key}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignContent: 'center',
                  marginTop: '20px',
                }}
              >
                <Checkbox
                  style={{ marginRight: '20px' }}
                  value={item?.key}
                ></Checkbox>
                <div className={styles.modal_style}>
                  <h4 style={{ marginBottom: '5px' }}>
                    {i + 1}.{item?.name}
                  </h4>
                  <p style={{ marginBottom: '0px' }}>{item?.content}</p>
                </div>
              </div>
            ))}
          </Checkbox.Group>
        </div>
      </Modal>
    </div>
  );
});
export default DynamicSearch;
