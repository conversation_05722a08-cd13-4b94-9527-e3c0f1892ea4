import { useEffect, useState } from "react";
import { identityAPI } from "@/services/carriers";
function useCarriers() {
    const [carRef, setCarRef] = useState<any>();
    return {
        carRef,
        setCarRef,
    }
}
 function useUserMes() {
    const [user, setUser] = useState<any>([]);
    useEffect(() => {
        userMessage()
    },[])
    const userMessage = async () => {
        await identityAPI({
            type: 'json'
        }).then(res =>{
            setUser(res?.data?.duties?.map((item: any) => item?.id));
        })
    }
    return {
        user,
        setUser,
    }
}
export default {
    useCarriers,
    useUserMes
}