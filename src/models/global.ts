// 全局共享数据示例
import { DEFAULT_NAME } from '@/constants';
import { useState } from 'react';
import { useLocalStorageState } from 'ahooks';
const useUser = () => {
  const [name, setName] = useState<string>(DEFAULT_NAME);
  return {
    name,
    setName,
  };
};

const useTabList = () => {
  const [tabList, setTabList] = useLocalStorageState<any[]>('tabList', {
    defaultValue: [],
  });
  return {
    tabList,
    setTabList,
  };
};

const useDelTabList = () => {
  const [delValue, setDelValue] = useState<any>(null);
  return {
    delValue,
    setDelValue,
  };
};

export default {
  useUser,
  useTabList,
  useDelTabList,
};
