import { useRequest } from 'ahooks';
import { message } from 'antd';
import { saveAs } from 'file-saver';
/**
 * @导出数据
 * @param requestFn 导出请求函数
 * @returns run 执行导出
 */
export const useExportHook = (requestFn: any) => {
  const { run, loading, error } = useRequest(requestFn, {
    manual: true,
    onSuccess: async (result: any) => {
      //处理文件流 请款单导出 会存在 导出接口 但是返回json 的情况
      if (result.headers['content-type'] === 'application/json;charset=UTF-8') {
        let reader = new FileReader();
        // 使用 Promise 来等待读取完成 异步改同步
        let readPromise = new Promise((resolve, reject) => {
          reader.onload = async function (e: any) {
            if (e.target?.result) {
              let json = await JSON.parse(e.target.result);
              // console.log(json);
              result.data = await json;
              resolve(null);
            }
          };
          reader.onerror = reject;
        });

        reader.readAsText(result.data);
        // 等待读取完成
        await readPromise;
      }

      if (result?.data?.status === false) {
        message.warning(`请求出错：${result?.data?.errorCode}-${result?.data?.errorDesc}`);
        return false;
      }
      const fileName = decodeURI(
        result.headers['content-disposition']?.match(/filename=(.*)/)[1],
      );
      const newFileName = Buffer.from(fileName, 'base64')?.toString('utf8');
      const blob = new Blob([result.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, newFileName);
    },
    onError: (error: any) => {
      console.error('导出失败', error);
    },
  });

  return { run, loading, error };
};
