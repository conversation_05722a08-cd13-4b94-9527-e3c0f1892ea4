import { message } from 'antd';
import { useCallback, useState } from 'react';

let contextHolders: any = null;
let UFlage:boolean = false;
const useValidation = (selectedRowKeys: any) => {
  const [messageApi, contextHolder] = message.useMessage();
  contextHolders = contextHolder;
  const  [successOrNot,setSuccessOrNot] = useState<boolean>(false) 

  
  const validate = useCallback(() => {
    UFlage = false;
    if (selectedRowKeys?.length === 0) {
      messageApi.error('请先选择数据');
      setSuccessOrNot(false)

      return;
    }
    const counterpartyId = selectedRowKeys[0]?.counterpartyId;
    const isSame = selectedRowKeys.every(
      (item: { counterpartyId: any }) => item.counterpartyId === counterpartyId,
    );
    if (!isSame) {
      messageApi.error('已选项中存在不同的服务商,无法进行下一步操作');
      setSuccessOrNot(false)
      return;
    }

    const currencyType = selectedRowKeys[0]?.currencyType;
    const isSame2 = selectedRowKeys.every(
      (item: { currencyType: any }) => item.currencyType === currencyType,
    );
    if (!isSame2) {
      messageApi.error('已选项中存在不同的币种,无法进行下一步操作');
      setSuccessOrNot(false)
      return;
    }
    setSuccessOrNot(true)
    UFlage = true;
  }, [selectedRowKeys]);
  return { validate,successOrNot};
};

export { useValidation, contextHolders,UFlage };
