import { useState, useEffect } from 'react';

function useDownloadFile(url: RequestInfo | URL, defaultFilename?: undefined) {
  const [downloading, setDownloading] = useState<boolean>(false);
  const [filename, setFilename] = useState(defaultFilename);

  const handleDownload = (newFilename: any) => {
    setFilename(newFilename || defaultFilename);
    setDownloading(true);
  };

  useEffect(() => {
    if (downloading) {
      fetch(url)
        .then((response) => response.blob())
        .then((blob) => {
          const link: any = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          setDownloading(false);
          setFilename(defaultFilename); 
        })
        .catch((error) => {
          console.error('下载文件出错', error);
          setDownloading(false);
          setFilename(defaultFilename);
        });
    }
  }, [downloading]);

  return [handleDownload, downloading, filename];
}

export default useDownloadFile;
