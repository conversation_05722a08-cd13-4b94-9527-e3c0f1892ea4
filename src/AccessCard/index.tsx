import React, { useMemo } from 'react';
// import { useModel } from 'umi';
import { useSnapshot } from 'umi';
import { store } from '@/stores';

interface AccessProps {
  children: React.ReactNode; //有权限展示内容
  accessible?: any[] | string; //是否有权限
  dataValidator?: any; //自定义权限校验
  fallback?: any; //不可访问时展示内容
  nonAccessible?: any[] | string; //无权限 要展示的内容
}

const AccessCard = ({
  children,
  accessible,
  fallback,
  dataValidator,
  nonAccessible,
}: AccessProps) => {
  // const { initialState } = useModel?.('@@initialState') ?? {};
  const snap = useSnapshot(store);
  // const { initialState } = useModel('@@initialState') ?? {};
  const { authorities, authorityData } = snap.initialState?.currentUser;
  // const { initialState } = useModel('@@initialState') || {};
  // const { authorities, authorityData } = initialState?.currentUser || {};
  // console.log('authorityData', authorities);
  const isAccessible = useMemo(() => {
    if (typeof accessible === 'boolean') return accessible;
    let accessibleArr = [];
    let pathes = accessible || nonAccessible;

    if (!pathes) return true;

    if (typeof pathes === 'string') {
      accessibleArr = pathes.split(',');
    } else {
      accessibleArr = pathes;
    }
    for (let i = 0; i < accessibleArr.length; i++) {
      let path = accessibleArr[i];
      let authDataArr = authorityData[path];
      if (authDataArr && dataValidator) {
        try {
          for (let j = 0; j < authDataArr.length; j++) {
            let authData = authDataArr[j];
            try {
              if (dataValidator(path, authData)) return true;
            } catch (err) {
              console.error('权限数据返回失败', err);
            }
          }
        } catch (err) {
          console.error('权限数据返回失败', err);
        }
      } else {
        if (
          authorities.filter((a: string) => a.indexOf(path) !== -1).length > 0
        ) {
          return true;
        }
      }
    }
    return false;
  }, [authorities, authorityData, accessible, nonAccessible, dataValidator]);

  return (
    <>
      {(accessible && isAccessible) || (nonAccessible && !isAccessible) ? (
        children
      ) : (
        <div>{fallback || null}</div>
      )}
    </>
  );
};

export default React.memo(AccessCard);
