import { defineConfig } from '@umijs/max';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';
import type { Options as WebUpdateNotificationOptions } from '@plugin-web-update-notification/umijs';
const { REACT_APP_ENV = 'dev' } = process.env;
// const HtmlWebpackPlugin = require('html-webpack-plugin');
export default defineConfig({
  qiankun: {
    master: {
      apps: [
        {
          name: 'app1',  
          entry: 'https://dev-web-common.mingruiyun.com',
          // entry: '//localhost:9999',
          props: {
            isMenu:false
          },
          // activeRule: '/minTest',
        },
        // {
        //   name: 'app2',  
        //   // entry: 'https://dev-web-common.mingruiyun.com',
        //   entry: '//localhost:9527',
        // },
      ],
      // sandbox: false,
      sandbox: {
        strictStyleIsolation: false,
      },
      // autoCaptureError: true,
      // prefetch: true,
    },
  },
  // base:'/index.html',
  // exportStatic: {
  // },
  base: '/',
  // history: { type: 'memory' },
  headScripts: [
    // 解决首次加载时白屏的问题
    { src: '/scripts/loading.js', async: true },
    {
      src: 'https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js',
      async: true,
    }, // 添加要引入的线上JavaScript文件
    {
      src: 'https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js',
      async: true,
    },
    {
      src: 'https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.2/index.js',
    },
  ],
  hash: true,
  theme: {
    'primary-color': defaultSettings.colorPrimary,
  },
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  antd: {},
  model: {},
  access: {},
  initialState: {},
  clickToComponent: {},
  valtio: {},
  request: {
    dataField: '',
  },
  fastRefresh: true,
  layout: {
    // title: '@umijs/max'
    locale: false,
    ...defaultSettings,
  },

  moment2dayjs: {
    preset: 'antd',
    plugins: ['duration'],
  },
  routes,
  mfsu: {
    strategy: 'normal',
  },
  npmClient: 'yarn',
  define: {
    'process.env': {
      REACT_APP_ENV: REACT_APP_ENV,
      UMI_ENV: process.env.UMI_ENV,
    },
  },
  // plugins:[
  //   new HtmlWebpackPlugin({
  //     template: 'src/index.template.html',
  //     // 其他配置项
  //   }),
  // ]
  esbuildMinifyIIFE: true,
  plugins: [
    require.resolve('@umijs/plugins/dist/unocss'),
    '@plugin-web-update-notification/umijs',
  ],
  unocss: {
    watch: ['src/**/*.tsx'],
  },
  webUpdateNotification: {
    logVersion: true,
    checkInterval: 0.5 * 60 * 1000,
    checkOnWindowFocus: true,
    checkImmediately: true,
    checkOnLoadFileError: true,
    // hiddenDismissButton: true,
    notificationProps: {
      title: '版本更新通知',
      description: '检测到当前系统版本已更新,请刷新页面后使用',
      buttonText: '立即刷新',
      dismissButtonText: '忽略',
    },
    notificationConfig: {
      placement: 'topRight',
      primaryColor: '#1890ff',
      secondaryColor: 'rgba(0,0,0,.25)',
    },
  } as WebUpdateNotificationOptions,
});
