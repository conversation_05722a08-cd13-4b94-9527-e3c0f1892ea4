/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/',
    redirect: '/home',
  },
  {
    name: '概览',
    path: '/home',
    icon: 'icon-gailan1',
    component: './Home',
    // wrappers:['@/wrappers/Auth'],
  },
  {
    name: '测试',
    path: '/test',
    icon: 'icon-gailan1',
    component: './Test',
    hideInMenu: true,
    // wrappers:['@/wrappers/Auth'],
  },
  {
    name: '一件代发',
    path: '/dropshipping',
    icon: 'icon-gailan1',
    routes: [
      {
        name: '入库预报单',
        path: '/dropshipping/InManagement/ConfiguredCar',
        component: './Dropshipping/InManagement/ConfiguredCar',
      },
      {
        name: '入库详情',
        path: '/dropshipping/InManagement/ConfiguredCar/Detail',
        component: './Dropshipping/InManagement/ConfiguredCar/Detail',
        hideInMenu: true,
      },
      {
        name: '扫描收货',
        path: '/dropshipping/InManagement/ScanStore',
        component: './Dropshipping/InManagement/ScanStore',
      },
      {
        name: '上架管理',
        path: '/dropshipping/InManagement/PutShelves',
        component: './Dropshipping/InManagement/PutShelves',
      },
      {
        name: '上架',
        path: '/dropshipping/InManagement/PutShelves/PutOn',
        hideInMenu: true,
        component: './Dropshipping/InManagement/PutShelves/components/PutOn',
      },
      {
        name: '出库单',
        path: '/dropshipping/OutManagement/OutList',
        component: './Dropshipping/OutManagement/OutList',
      },
      {
        name: '出库详情',
        path: '/dropshipping/OutManagement/OutList/Detail',
        component: './Dropshipping/OutManagement/OutList/Detail',
        hideInMenu: true,
      },
      {
        name: '拣货详情',
        path: '/dropshipping/OutManagement/OutList/OperateDetail',
        component: './Dropshipping/OutManagement/OutList/OperateDetail',
        hideInMenu: true,
      },
      {
        name: '出库单打包',
        path: '/dropshipping/OutManagement/OutList/PackDetail',
        component: './Dropshipping/OutManagement/OutList/PackDetail',
        hideInMenu: true,
      },
      {
        name: '波次列表',
        path: '/dropshipping/OutManagement/WellenList',
        component: './Dropshipping/OutManagement/WellenList',
      },
      {
        name: 'SKU库存',
        path: '/dropshipping/SkuManagement/stock',
        component: './Dropshipping/SkuManagement/Stock/index',
      },
      /*      {
        name: 'SKU库龄',
        path: '/SkuManagement',
        component: './SkuManagement/StockAge/index',
      },*/
      {
        name: 'SKU库存流水',
        path: '/dropshipping/SkuManagement/flowing',
        component: './Dropshipping/SkuManagement/Flowing/index',
      },
    ],
    /*    routes: [
      {
        name: '入库预报单',
        path: '/InManagement/ConfiguredCar',
        component: './InManagement/ConfiguredCar',
      },
      {
        name: '入库详情',
        path: '/InManagement/ConfiguredCar/Detail',
        component: './InManagement/ConfiguredCar/Detail',
        hideInMenu: true,
      },
      {
        name: '扫描收货',
        path: '/InManagement/ScanStore',
        component: './InManagement/ScanStore',
      },
      {
        name: '上架管理',
        path: '/InManagement/PutShelves',
        component: './InManagement/PutShelves',
      },
      {
        name: '上架',
        path: '/InManagement/PutShelves/PutOn',
        hideInMenu: true,
        component: './InManagement/PutShelves/components/PutOn',
      },
    ],*/
    // wrappers:['@/wrappers/Auth'],
  },
  /*  {
    path: '/OutManagement',
    name: '出库管理',
    icon: 'icon-kehuguanli2',
    accessible: [':DropShipping:'],
    routes: [
      {
        name: '出库单',
        path: '/OutManagement/OutList',
        component: './OutManagement/OutList',
      },
      {
        name: '出库详情',
        path: '/OutManagement/OutList/Detail',
        component: './OutManagement/OutList/Detail',
        hideInMenu: true,
      },
      {
        name: '拣货详情',
        path: '/OutManagement/OutList/OperateDetail',
        component: './OutManagement/OutList/OperateDetail',
        hideInMenu: true,
      },
      {
        name: '出库单打包',
        path: '/OutManagement/OutList/PackDetail',
        component: './OutManagement/OutList/PackDetail',
        hideInMenu: true,
      },
      {
        name: '波次列表',
        path: '/OutManagement/WellenList',
        component: './OutManagement/WellenList',
      },
    ],
  },*/
  /*  {
    path: '/SkuManagement',
    name: '库存管理',
    icon: 'icon-kehuguanli2',
    accessible: [':DropShipping:'],
    routes: [
      {
        name: 'SKU库存',
        path: '/SkuManagement/stock',
        component: './SkuManagement/Stock/index',
      },
      /!*      {
        name: 'SKU库龄',
        path: '/SkuManagement',
        component: './SkuManagement/StockAge/index',
      },*!/
      {
        name: 'SKU库存流水',
        path: '/SkuManagement/flowing',
        component: './SkuManagement/Flowing/index',
      },
      {
        name: '物料管理',
        path: '/SkuManagement/material',
        component: './SkuManagement/Material/index',
      },
    ],
  },*/
  {
    path: '/BringGroup',
    name: '清提拆派',
    icon: 'ProfileOutlined',
    // component: './BringGroup',
    accessible: [':BL:'],
    routes: [
      {
        name: '订单列表',
        path: '/BringGroup/QingtiList',
        component: '@/pages/BringGroup/QingtiList',
        accessible: [':BL:Order'],
      },
      {
        name: '订单列表V4',
        path: '/BringGroup/QingtiListV4',
        component: '@/pages/BringGroup/QingtiList/index2',
        accessible: [':BL:Order'],
      },
      // {
      //   name: '入库扫描',
      //   path: 'BringGroup/Scanning',
      //   component: './BringGroup/Scanning',
      // },
      {
        name: '预配车',
        path: '/BringGroup/PreConfiguredCar',
        icon: 'icon-yupeiche',
        component: '@/pages/PreConfiguredCar',
        // wrappers:['@/wrappers/Auth'],
        accessible: [':BL:PreShipment'],
      },
      {
        name: '货物管理',
        path: '/BringGroup/CargoManagement',
        icon: 'icon-huowuguanli',
        component: './CargoManagement',
        // wrappers:['@/wrappers/Auth'],
        accessible: [':BL:Waybill'],
      },
      {
        name: '托盘管理',
        path: '/BringGroup/TrayManagement',
        icon: 'icon-huowuguanli',
        component: '@/pages/CargoManagement/TrayList',
        // wrappers:['@/wrappers/Auth'],
        accessible: [':BL:Pallets'],
      },
      {
        name: '派送管理',
        path: '/BringGroup/DeliveryManagement',
        icon: 'icon-paisongguanli',
        component: './DeliveryManagement',
        // wrappers:['@/wrappers/Auth'],
        accessible: [':BL:Shipment'],
      },
      {
        name: '仓储单',
        path: '/BringGroup/Storage',
        icon: 'icon-paisongguanli',
        component: '@/pages/Storage',
        // hideInMenu: true,
        accessible: [':BL:Storage'],
      },
      {
        name: '仓储单详情',
        path: '/BringGroup/Storage/Detail',
        icon: 'icon-gailan1',
        component: '@/pages/Storage/Detail',
        hideInMenu: true,
        // wrappers:['@/wrappers/Auth'],
      },
    ],
  },
  {
    name: '清提拆派详情',
    path: '/BringGroup/detail',
    hideInMenu: true,
    component: './BringGroup/Detail',
  },
  {
    name: '财务清提拆派详情',
    path: '/BringGroup/FinanceDetail',
    hideInMenu: true,
    component: './BringGroup/FinanceDetail',
  },
  {
    name: '详情',
    path: '/PreConfiguredCar/Detail',
    icon: 'icon-gailan1',
    component: './PreConfiguredCar/Detail',
    hideInMenu: true,
    // wrappers:['@/wrappers/Auth'],
  },

  {
    name: '托盘详情',
    path: '/CargoManagement/TrayDetail',
    icon: 'icon-gailan1',
    hideInMenu: true,
    component: './CargoManagement/TrayDetail',
  },

  // {
  //   name: '销售管理',
  //   path: '/salesManagement',
  //   icon: 'icon-xiaoshouyubao',
  //   accessible: [':Sales:'],
  //   routes: [
  //     {
  //       name: '销售预报',
  //       path: 'salesManagement/marketForecast',
  //       component: './MarketForecast',
  //       accessible: [':Sales:Report'],
  //     },
  //     {
  //       name: '价格试算',
  //       path: '/salesManagement/InternalTrial',
  //       component: './SalesManagement/InternalTrial',
  //       accessible: [':Sales:PriceTrialAccess'],
  //     },
  //     {
  //       name: '卡派询价',
  //       path: '/salesManagement/calculate',
  //       component: './SalesManagement/Calculate',
  //       // accessible:[':Sales:PriceTrialAccess'],
  //       accessible: [':Sales:TruckTrialAccess'],
  //     },
  //     {
  //       path: '/salesManagement/calculate/priceRecord/priceDetails',
  //       name: '报价记录详情',
  //       hideInMenu: true,
  //       // access:'TruckFullAccess',
  //       component: './SalesManagement/Calculate/PriceRecord/PriceDetails',
  //     },
  //   ],
  // },

  // {
  //   name: '订舱管理',
  //   path: '/Booking',
  //   icon: 'icon-dingcangguanli',
  //   accessible: [':Transport:'],
  //   routes: [
  //     // { path: '/cabin', redirect: '/Booking/Cabin' },
  //     {
  //       name: '订舱',
  //       path: '/Booking/cabin',
  //       component: './Booking/Cabin',
  //       accessible: [':Transport:Space'],
  //     },
  //     {
  //       name: '卖柜',
  //       path: '/Booking/cabinet',
  //       component: './Booking/Cabinet',
  //       accessible: [':Transport:SpaceSalesFullAccess'],
  //     },
  //     {
  //       name: '订舱计划',
  //       path: '/Booking/cabin/book',
  //       hideInMenu: true,
  //       component: './Booking/Cabin/Book',
  //     },
  //     {
  //       name: '航次统计详情',
  //       path: '/Booking/cabin/particulars',
  //       hideInMenu: true,
  //       component: './Booking/Cabin/Particulars',
  //     },
  //     {
  //       name: '卖柜订单详情',
  //       path: '/Booking/cabinet/detail',
  //       component: './Booking/Cabinet/Detail',
  //       hideInMenu: true,
  //       // component: './Booking/Cabin/SellCabinet/Detail',
  //     },
  //     {
  //       name: '航线航次',
  //       path: '/Booking/flight',
  //       // hideInMenu: true,
  //       component: './Booking/Flight',
  //       accessible: [':Transport:Vessel'],
  //     },
  //     {
  //       name: '新增航线',
  //       path: '/Booking/flight/newlyAirLine',
  //       hideInMenu: true,
  //       component: './Booking/Flight/NewlyAirLine',
  //     },
  //     {
  //       name: '新增航次',
  //       path: '/Booking/flight/newlyVoyage',
  //       hideInMenu: true,
  //       component: './Booking/Flight/NewlyVoyage',
  //     },
  //     {
  //       name: '船司/航司',
  //       path: '/Booking/shipping',
  //       component: './Booking/Shipping',
  //       accessible: [':Transport:Properties'],
  //     },
  //     {
  //       name: '船/航司代理详情',
  //       path: '/Booking/shipping/particulars',
  //       hideInMenu: true,
  //       component: './Booking/Shipping/Particulars',
  //     },
  //     {
  //       name: '拖车管理',
  //       path: '/Booking/trailer',
  //       component: './Booking/Trailer',
  //       accessible: [':Transport:Trailer'],
  //     },
  //     {
  //       name: '车队详情',
  //       path: '/Booking/trailer/motorcaDeparticulars',
  //       component: './Booking/Trailer/Motorcadeparticulars',
  //       hideInMenu: true,
  //     },
  //     {
  //       name: '拖车记录详情',
  //       path: '/Booking/trailer/recodeParticulars',
  //       component: './Booking/Trailer/RecodeParticulars',
  //       hideInMenu: true,
  //     },
  //   ],
  // },

  {
    name: '报表管理',
    path: '/report',
    icon: 'fileSearch',
    component: './Report',
    accessible: [':Statistics:'],
  },
  {
    name: '报表详情',
    path: '/report/particulars',
    component: './Report/Particulars',
    hideInMenu: true,
    // accessible: [':Statistics:'],
  },
  // {
  //   name: '运单管理',
  //   path: '/Waybilladministration',
  //   icon: 'icon-yundanguanli2',
  //   accessible: [':Waybill:'],
  //   routes: [
  //     {
  //       name: '运单列表',
  //       path: '/Waybilladministration/waybillList',
  //       component: './Waybilladministration/WaybillList',
  //       accessible: [':Waybill:Search'],
  //     },
  //     {
  //       name: '运单列表-旧',
  //       path: '/Waybilladministration/list',
  //       component: './Waybilladministration/List',
  //       accessible: [':Waybill:Search'],
  //       hideInMenu: true,
  //     },

  //     {
  //       name: '调试test',
  //       path: '/Waybilladministration/test1',
  //       component: './Waybilladministration/Test1',
  //       accessible: [':Waybill:Search'],
  //       hideInMenu: true,
  //     },
  //     {
  //       name: '轨迹维护记录',
  //       path: '/Waybilladministration/MaintenanceTrajectory',
  //       component: './Waybilladministration/List/MaintenanceTrajectory',
  //       hideInMenu: true,
  //       accessible: [':Waybill:DetailAccess'],
  //     },
  //     {
  //       name: '运单详情',
  //       path: '/Waybilladministration/detail',
  //       component: './Waybilladministration/List/Detail',
  //       hideInMenu: true,
  //       accessible: [':Waybill:DetailAccess'],
  //     },
  //     {
  //       name: '上门取货',
  //       path: '/Waybilladministration/visit',
  //       // access: 'Visit',
  //       component: './Waybilladministration/Visit',
  //       accessible: [':Waybill:Picking'],
  //     },
  //     {
  //       name: '内陆转运',
  //       path: '/Waybilladministration/inlandTransfer',
  //       // icon: 'HomeOutlined',
  //       component: './Waybilladministration/InlandTransfer',
  //       accessible: [':Waybill:Transfer'],
  //     },
  //     {
  //       name: '仓库间转运详情',
  //       path: '/Waybilladministration/inlandTransfer/warehouseTransfer/particulars',
  //       component:
  //         './Waybilladministration/InlandTransfer/WarehouseTransfer/Particulars',
  //       hideInMenu: true,
  //     },
  //     {
  //       name: '服务商详情',
  //       path: '/Waybilladministration/inlandTransfer/serverMerchant/particulars',
  //       component:
  //         './Waybilladministration/InlandTransfer/ServerMerchant/Particulars',
  //       hideInMenu: true,
  //     },
  //     {
  //       name: '代理仓库转运详情',
  //       path: '/Waybilladministration/inlandTransfer/agencyWarehouse/particulars',
  //       component:
  //         './Waybilladministration/InlandTransfer/AgencyWarehouse/Particulars',
  //       hideInMenu: true,
  //     },
  //     {
  //       name: '取货单详情',
  //       path: '/Waybilladministration/visit/carriers/particulars',
  //       hideInMenu: true,
  //       component: './Waybilladministration/Visit/Carriers/Particulars',
  //     },
  //     {
  //       name: '运单签入',
  //       path: '/Waybilladministration/waybillCheckIn',
  //       component: './Waybilladministration/WaybillCheckIn',
  //       accessible: [':Waybill:CheckIn', ':Waybill:Confirm'],
  //     },
  //     {
  //       name: '到货总单',
  //       path: '/Waybilladministration/arrivalGeneralDoc',
  //       component: './Waybilladministration/ArrivalGeneralDoc',
  //       accessible: [':Waybill:ArrivalOrder'],
  //     },
  //     {
  //       name: '预报打单',
  //       path: '/Waybilladministration/forecastPrint',
  //       component: './Waybilladministration/ForecastPrint',
  //       accessible: [':Waybill:Channel'],
  //     },
  //   ],
  // },
  // {
  //   name: '预报详情',
  //   path: '/marketForecast/statisticsParticulars',
  //   hideInMenu: true,
  //   component: './MarketForecast/StatisticsParticulars',
  // },
  {
    name: '账号信息',
    path: '/account/settings',
    hideInMenu: true,
    component: './Account/Settings',
    // wrappers:['@/wrappers/Auth'],
  },

  {
    name: '产品与价格',
    path: '/productAndPrice',
    icon: 'icon-chanpinyujiage',
    accessible: [':Product:'],
    routes: [
      {
        path: '/productAndPrice',
        redirect: '/productAndPrice/priceManagement',
      },

      {
        name: '价格管理',
        path: '/productAndPrice/priceManagement',
        component: './ProductAndPrice/PriceManagement/index2',
        // accessible: [':Product:Fee'],
      },
      //提拆派详情
      {
        name: '提拆派详情',
        path: '/productAndPrice/priceManagement/detailsOfDistribution',
        hideInMenu: true,
        component:
          './ProductAndPrice/PriceManagement/components/LiquidationAndSplit/DetailsOfDistribution',
      },
      //创建附加费模版
      {
        name: '创建/编辑附加费模版',
        path: '/productAndPrice/priceManagement/createSurchargeTemplate',
        hideInMenu: true,
        component: './ProductAndPrice/PriceManagement/CreateASurchargeTemplate',
      },
      //创建服务费模版
      {
        name: '创建/编辑服务费模版',
        path: '/productAndPrice/priceManagement/createServiceFeeTemplate',
        hideInMenu: true,
        component: './ProductAndPrice/PriceManagement/CreateServiceFeeTemplate',
      },
      //创建燃油附加费模版
      {
        name: '创建/编辑燃油附加费模版',
        path: '/productAndPrice/priceManagement/createFuelSurchargeTemplate',
        hideInMenu: true,
        component:
          './ProductAndPrice/PriceManagement/CreateFuelSurchargeTemplate',
      },
      {
        name: '创建/编辑派送费模版',
        path: '/productAndPrice/priceManagement/CreateDeliveryFeeTemplate',
        hideInMenu: true,
        component:
          './ProductAndPrice/PriceManagement/CreateDeliveryFeeTemplate',
      },
      //新建仓租费价格表
      {
        name: '仓租费价格表',
        path: '/productAndPrice/priceManagement/addWarehouseRentalPrice',
        hideInMenu: true,
        component:
          './ProductAndPrice/PriceManagement/components/AddWarehouseRentalPrice',
      },
      /* 重构代理渠道 start */
      // {
      //   name: '代理渠道',
      //   path: '/productAndPrice/channelAgent',
      //   component: './ProductAndPrice/ChannelAgent',
      //   accessible: [':Product:Channel'],
      // },
      {
        name: '代理渠道',
        path: '/productAndPrice/channelAgent2',
        component: './ProductAndPrice/ChannelAgent/index2',
        // accessible: [':Product:Channel'],
      },
      {
        name: '代理渠道编辑',
        path: '/productAndPrice/channelAgent/detailAgent',
        hideInMenu: true,
        component: './ProductAndPrice/ChannelAgent/DetailAgent',
      },
      /* 重构代理渠道 end */
      // {
      //   name: '代理渠道',
      //   path: '/productAndPrice/agentChannel',
      //   component: './ProductAndPrice/AgentChannel',
      // },
      {
        name: '新建渠道',
        path: '/productAndPrice/agentChannel/createChannel',
        hideInMenu: true,
        component: './ProductAndPrice/AgentChannel/NewChannel',
      },
      {
        name: '产品管理',
        path: '/productAndPrice/productManagement',
        component: './ProductAndPrice/ProductManagement',
        accessible: [':Product:Product'],
      },
      {
        name: '编辑产品',
        path: '/productAndPrice/productManagement/createProduct',
        hideInMenu: true,
        component: './ProductAndPrice/ProductManagement/NewProduct',
      },
      {
        name: '编辑产品',
        path: '/productAndPrice/productManagement/DetailsOfClearanceAndDemolition',
        hideInMenu: true,
        component:
          './ProductAndPrice/ProductManagement/components/DetailsOfClearanceAndDemolition',
      },
      // 模版管理2
      {
        name: '模版管理',
        path: '/productAndPrice/templateManagement',
        // component: './ProductAndPrice/TemplateManagement',
        //accessible: [':Product:Rule'],
        component: './ProductAndPrice/TemplateManagement/index2',
        // accessible: [':Product:Rule'],
      },
      {
        name: '重量传输模版',
        path: '/productAndPrice/templateManagement/addWeightTemplate',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/WeightTransferRules/AddWeightTemplate',
      },
      {
        name: '体积进位规则详情',
        path: '/productAndPrice/templateManagement/volumeRoundingRules',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/VolumeRule/VolumeRoundingRules',
      },
      {
        name: '分区价格表详情',
        path: '/productAndPrice/templateManagement/partitionPriceDetails',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/PartitionPriceDetails',
      },
      {
        name: '分区模版详情',
        path: '/productAndPrice/templateManagement/partitionTemplateDetails',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/PartitionTemplateDetails',
      },
      {
        name: '重量模版详情',
        path: '/productAndPrice/templateManagement/weightTemplateDetails',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/WeightTemplateDetails',
      },
      {
        name: '附加费表详情',
        path: '/productAndPrice/templateManagement/surchargeDetails',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/SurchargeDetails',
      },
      {
        name: '超长超重规则详情',
        path: '/productAndPrice/templateManagement/overlengthAndOverweightRules',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/ExtraLongOverweightDetails',
      },
      {
        name: '计重规则详情',
        path: '/productAndPrice/templateManagement/billingRules',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/BillingRulesDetails',
      },
      {
        name: '计费重进位详情',
        path: '/productAndPrice/templateManagement/billingReCarryDetails',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/BillingReCarryDetails',
      },
      {
        name: '偏远规则详情',
        path: '/productAndPrice/templateManagement/remoteRulesDetails',
        hideInMenu: true,
        component:
          './ProductAndPrice/TemplateManagement/components/RemoteRules/RemoteRuleDetails',
      },
    ],
  },
  {
    path: '/customer',
    name: '客户管理',
    icon: 'icon-kehuguanli2',
    component: './customer',
    accessible: [':Client:'],
  },

  // {
  //   path: '/financialManagement',
  //   name: '财务管理',
  //   icon: 'icon-caiwuguanli2',
  //   //accessible: [':Finance:'],
  //   routes: [
  //     // {
  //     //   // name: '汇总统计',
  //     //   name: '应收应付查询',
  //     //   path: '/financialManagement/summaryStatistics',
  //     //   component: './FinancialManagement/SummaryStatistics',
  //     //   accessible: [':Finance:StatisticsAccess'],
  //     // },
  //     {
  //       // name: '汇总统计',
  //       name: '应收应付查询',
  //       path: '/financialManagement/summaryStatistics2',
  //       component: './FinancialManagement/SummaryStatistics/index2',
  //       //accessible: [':Finance:StatisticsAccess'],
  //       dataValidator: (path: any, data: any) => {
  //         return (
  //           !/listDenied/.test(data.blno.value) ||
  //           !/listDenied/.test(data.waybill.value)
  //         );
  //       },
  //     },
  //     // {
  //     //   path: '/financialManagement/billsReceivable',
  //     //   // name: '应收账单',
  //     //   name: '应收管理',
  //     //   component: './FinancialManagement/BillsReceivable',
  //     //   accessible: [':Finance:Receivable'],
  //     // },
  //     {
  //       path: '/financialManagement/billsReceivable2',
  //       // name: '应收账单',
  //       name: '应收管理',
  //       component: './FinancialManagement/BillsReceivable/index2',
  //       //accessible: [':Finance:Receivable'],
  //     },
  //     // {
  //     //   path: '/financialManagement/receiptRecords',
  //     //   // name: '收款记录',
  //     //   name: '收款管理',
  //     //   component: './FinancialManagement/ReceiptRecords',
  //     //   accessible: [':Finance:ReceivablesRecord'],
  //     // },
  //     {
  //       path: '/financialManagement/receiptRecords2',
  //       // name: '收款记录',
  //       name: '收款管理',
  //       component: './FinancialManagement/ReceiptRecords/index2',
  //       //accessible: [':Finance:ReceivablesRecord'],
  //     },
  //     {
  //       path: '/financialManagement/receiptClaim',
  //       name: '收款认领',
  //       component: './FinancialManagement/ReceiptClaim',
  //       // accessible: [':Finance:ReceivablesRecord'],
  //     },
  //     // {
  //     //   path: '/financialManagement/billsPayable',
  //     //   // name: '应付账单',
  //     //   name: '应付管理',
  //     //   component: './FinancialManagement/BillsPayable',
  //     //   accessible: [':Finance:Payables'],
  //     // },
  //     {
  //       path: '/financialManagement/billsPayable2',
  //       // name: '应付账单',
  //       name: '应付管理',
  //       component: './FinancialManagement/BillsPayable/index2',
  //       //accessible: [':Finance:Payables'],
  //     },

  //     {
  //       path: '/financialManagement/reconciliationManagement/statementDetails',
  //       name: '对账单详情',
  //       component:
  //         './FinancialManagement/ReconciliationManagement/StatementDetails',
  //       //accessible: [':Finance:Payables'],
  //       hideInMenu: true,
  //     },
  //     // {
  //     //   path: '/financialManagement/paymentRecord',
  //     //   // name: '付款记录',
  //     //   name: '付款管理',
  //     //   component: './FinancialManagement/PaymentRecord',
  //     //   accessible: [':Finance:Payables'],
  //     // },
  //     {
  //       path: '/financialManagement/paymentRecord2',
  //       // name: '付款记录',
  //       name: '付款管理',
  //       component: './FinancialManagement/PaymentRecord/index2',
  //       //accessible: [':Finance:Payables'],
  //     },
  //     {
  //       path: '/financialManagement/reconciliationManagement',
  //       name: '对账管理',
  //       component: './FinancialManagement/ReconciliationManagement',
  //       //accessible: [':Finance:PayablesCheckoutFullAccess'],
  //     },
  //     {
  //       path: '/financialManagement/paymentRecord/detail',
  //       name: '付款记录详情',
  //       hideInMenu: true,
  //       component: './FinancialManagement/PaymentRecord/Detail',
  //     },
  //     {
  //       path: 'financialManagement/maintenanceData',
  //       name: '数据维护',
  //       component: './FinancialManagement/MaintenanceData',
  //       //accessible: [':Finance:Properties'],
  //     },
  //     {
  //       name: '数据维护详情',
  //       path: '/financialManagement/maintenanceData/detailsOfFinancialEntities',
  //       component:
  //         './FinancialManagement/MaintenanceData/SurchargeTable/DetailsOfFinancialEntities',
  //       //accessible: [':Finance:Properties'],
  //       hideInMenu: true,
  //     },
  //     {
  //       path: 'financialManagement/BillingTool',
  //       name: '账单工具',
  //       component: './FinancialManagement/BillingTool',
  //       //accessible: [':Finance:Properties'],
  //     },
  //   ],
  // },
  {
    path: '/financialManagement',
    name: '财务管理',
    icon: 'icon-caiwuguanli2',
    accessible: [':Finance:'],
    routes: [
      // {
      //   // name: '汇总统计',
      //   name: '应收应付查询',
      //   path: '/financialManagement/summaryStatistics',
      //   component: './FinancialManagement/SummaryStatistics',
      //   accessible: [':Finance:StatisticsAccess'],
      // },
      {
        // name: '汇总统计',
        name: '应收应付查询',
        path: '/financialManagement/summaryStatistics2',
        component: './FinancialManagement/SummaryStatistics/index2',
        // hideInMenu: true,

        // accessible: [':Finance:StatisticsAccess'],
        // dataValidator: (path: any, data: any) => {
        //   return (
        //     !/listDenied/.test(data.blno.value) ||
        //     !/listDenied/.test(data.waybill.value)
        //   );
        // },
      },
      // {
      //   path: '/financialManagement/billsReceivable',
      //   // name: '应收账单',
      //   name: '应收管理',
      //   component: './FinancialManagement/BillsReceivable',
      //   accessible: [':Finance:Receivable'],
      // },
      {
        path: '/financialManagement/billsReceivable2',
        // name: '应收账单',
        name: '应收管理',
        component: './FinancialManagement/BillsReceivable/index2',
        accessible: [':Finance:Receivable'],
      },
      // {
      //   path: '/financialManagement/receiptRecords',
      //   // name: '收款记录',
      //   name: '收款管理',
      //   component: './FinancialManagement/ReceiptRecords',
      //   accessible: [':Finance:ReceivablesRecord'],
      // },
      {
        path: '/financialManagement/receiptRecords2',
        // name: '收款记录',
        name: '收款管理',
        component: './FinancialManagement/ReceiptRecords/index2',
        accessible: [':Finance:ReceivablesRecord'],
      },
      {
        path: '/financialManagement/receiptClaim',
        name: '收款认领',
        component: './FinancialManagement/ReceiptClaim',
        // accessible: [':Finance:ReceivablesRecord'],
      },
      // {
      //   path: '/financialManagement/billsPayable',
      //   // name: '应付账单',
      //   name: '应付管理',
      //   component: './FinancialManagement/BillsPayable',
      //   accessible: [':Finance:Payables'],
      // },
      {
        path: '/financialManagement/billsPayable2',
        // name: '应付账单',
        name: '应付管理',
        component: './FinancialManagement/BillsPayable/index2',
        accessible: [':Finance:Payables'],
      },

      {
        path: '/financialManagement/reconciliationManagement/statementDetails',
        name: '对账单详情',
        component:
          './FinancialManagement/ReconciliationManagement/StatementDetails',
        accessible: [':Finance:Payables'],
        hideInMenu: true,
      },
      // {
      //   path: '/financialManagement/paymentRecord',
      //   // name: '付款记录',
      //   name: '付款管理',
      //   component: './FinancialManagement/PaymentRecord',
      //   accessible: [':Finance:Payables'],
      // },
      {
        path: '/financialManagement/paymentRecord2',
        // name: '付款记录',
        name: '付款管理',
        component: './FinancialManagement/PaymentRecord/index2',
        accessible: [':Finance:Payables'],
      },
      {
        path: '/financialManagement/reconciliationManagement',
        name: '对账管理',
        component: './FinancialManagement/ReconciliationManagement',
        accessible: [':Finance:PayablesCheckoutFullAccess'],
      },
      {
        path: '/financialManagement/paymentRecord/detail',
        name: '付款记录详情',
        hideInMenu: true,
        component: './FinancialManagement/PaymentRecord/Detail',
      },
      {
        path: 'financialManagement/maintenanceData',
        name: '数据维护',
        component: './FinancialManagement/MaintenanceData',
        accessible: [':Finance:Properties'],
      },
      {
        name: '数据维护详情',
        path: '/financialManagement/maintenanceData/detailsOfFinancialEntities',
        component:
          './FinancialManagement/MaintenanceData/SurchargeTable/DetailsOfFinancialEntities',
        accessible: [':Finance:Properties'],
        hideInMenu: true,
      },
      {
        path: 'financialManagement/BillingTool',
        name: '账单工具',
        component: './FinancialManagement/BillingTool',
        accessible: [':Finance:Properties'],
      },
    ],
  },
  {
    path: '/customer/Details',
    name: '客户详情',
    icon: 'TeamOutlined',
    hideInMenu: true,
    component: './customer/Details',
  },
  {
    path: '/customer/group/edit',
    name: '编辑客户分组',
    icon: 'TeamOutlined',
    hideInMenu: true,
    component: './customer/GroupList/edit',
  },
  {
    path: '/customer/group/add',
    name: '新增客户分组',
    icon: 'TeamOutlined',
    hideInMenu: true,
    component: './customer/GroupList/edit',
  },
  {
    path: '/customer/group/details',
    name: '客户分组详情',
    icon: 'TeamOutlined',
    hideInMenu: true,
    component: './customer/GroupList/edit',
  },
  {
    path: '/customer/ChannelManagement',
    name: '客户管理-渠道管理',
    hideInMenu: true,
    component: './customer/ChannelManagement',
  },
  /*  {
    name: '提单管理',
    path: '/BOL',
    icon: 'icon-tidanguanli',
    accessible: [':BL:', ':Customs:'],
    routes: [
      {
        name: '清关报关',
        path: '/BOL/ClearReportCustom',
        component: './BOL/ClearReportCustom',
        accessible: [':Customs:'],
      },
      {
        path: '/BOL/ClearReportCustom/ClearDetail',
        hideInMenu: true,
        name: '清关单详情',
        component: './BOL/ClearReportCustom/ClearDetail',
      },
      {
        path: '/BOL/ClearReportCustom/ReportDetail',
        hideInMenu: true,
        name: '报关单详情',
        component: './BOL/ClearReportCustom/ReportDetail',
      },
      {
        path: '/BOL/ClearReportCustom/ClearGroupDetail',
        hideInMenu: true,
        name: '清关行详情',
        component: './BOL/ClearReportCustom/ClearGroup/ClearGroupDetail',
      },
      {
        path: '/BOL/ClearReportCustom/ReportGroupDetail',
        hideInMenu: true,
        name: '报关行详情',
        component: './BOL/ClearReportCustom/ReportGroup/ReportGroupDetail',
      },
      {
        name: '配舱',
        path: '/BOL/PreplanCabin',
        component: './BOL/PreplanCabin',
        accessible: [':BL:Configure'],
      },
      {
        name: '配舱详情',
        path: '/BOL/PreplanCabin/particulars',
        hideInMenu: true,
        component: './BOL/PreplanCabin/Particulars',
      },
      {
        name: '提单列表',
        path: '/BOL/listOfBillsOfLading',
        component: './BOL/ListOfBillsOfLading',
        accessible: [':BL:ReadOnlyAccess', ':BL:FullAccess'],
      },
      {
        name: '海外仓预报列表',
        path: '/BOL/Overseas',
        component: './BOL/Overseas',
        hideInMenu: true,
        // accessible: [':BL:ReadOnlyAccess', ':BL:FullAccess'],
      },
      {
        name: '运单后端',
        path: '/BOL/WaybillBackEnd',
        component: './BOL/WaybillBackEnd',
        accessible: [':BL:WMSWaybillAccess'],
      },
      {
        name: '提单详情',
        path: '/BOL/listOfBillsOfLading/billOfLadingDetails',
        hideInMenu: true,
        component: './BOL/ListOfBillsOfLading/BillOfLadingDetails',
      },
      {
        name: '客户订单',
        path: '/BOL/ClientORder',
        component: './BOL/ClientORder',
        // hideInMenu: true,
      },
      {
        name: '客户订单详情',
        path: '/BOL/ClientORder/Detail',
        component: './BOL/ClientORder/Detail',
        hideInMenu: true,
        // component: './Booking/Cabin/SellCabinet/Detail',
      },
    ],
  },*/
  {
    name: '仓库管理',
    path: '/warehouseManagement',
    icon: 'BarChartOutlined',
    routes: [
      {
        path: '/warehouseManagement/warehouseLocation',
        name: '库位管理',
        component: './WarehouseManagement/WarehouseLocation',
        accessible: [':Address:'],
      },
      {
        name: '物料管理',
        path: '/warehouseManagement/material',
        component: '@/pages/WarehouseManagement/Material/index',
      },
    ],
  },
  {
    name: '服务商',
    path: '/Provider',
    component: './Provider',
    icon: 'BarChartOutlined',
    accessible: [':Provider:'],
  },
  {
    path: '/customer/CardContainerTabs',
    name: '客户管理-财务管理',
    hideInMenu: true,
    component: './customer/CardContainerTabs',
  },
  {
    path: '/dataManagement',
    name: '资料管理',
    icon: 'icon-ziliaoguanli',
    accessible: [':Address:'],
    routes: [
      { path: '/dataManagement', redirect: '/dataManagement/address' },
      {
        path: '/dataManagement/address',
        name: '地址库',
        component: './DataManagement/Address',
        accessible: [':Address:'],
      },
      {
        path: '/dataManagement/PackagingMaterials',
        name: '物料管理',
        component: './DataManagement/PackagingMaterials',
      },
      {
        name: '地区操作',
        path: '/dataManagement/address/addressDetails',
        hideInMenu: true,
        component: './DataManagement/PartitionTemplateDetails',
      },
      // {
      //   name: '服务标签管理',
      //   path: '/dataManagement/tagManagement',
      //   component: './DataManagement/TagManagement',
      // },
    ],
  },
  {
    path: '/SkuManage',
    name: 'SKU管理',
    icon: 'icon-ziliaoguanli',
    accessible: [':Sku:'],
    routes: [
      {
        path: '/SkuManage/Custom',
        name: '客户SKU',
        component: './SkuManage/Custom',
        accessible: [':Sku:ClientSkuAccess'],
      },
      {
        path: '/SkuManage/InStation',
        name: '站内SKU',
        component: './SkuManage/InStation',
        accessible: [':Sku:PrivateSku'],
      },
      {
        path: '/SkuManage/NewSku',
        name: '新建SKU',
        component: './SkuManage/NewSku',
        accessible: [':Sku:PrivateSkuFullAccess'],
      },
    ],
  },
  {
    path: '/logs',
    microApp: 'app1',
    icon: 'icon-caozuorizhi2',
    component: './List',
    accessible: [':Log:ReadOnlyAccess'],
  },
  {
    path: '/permission',
    name: '访问控制',
    icon: 'icon-fangwenkongzhi',
    accessible: [':Ram:'],
    routes: [
      {
        path: '/permission',
        redirect: '/Permission/Role',
      },
      {
        name: '角色管理',
        icon: 'smile',
        path: '/permission/role',
        component: './Permission/Role',
        accessible: [':Ram:Role'],
      },
      {
        name: '业务标签',
        icon: 'smile',
        path: '/permission/BusinessTags',
        component: './Permission/BusinessTags',
        accessible: [':Ram:Role'],
      },
      {
        name: '角色管理详情',
        path: '/permission/role/details',
        component: './Permission/Role/Details',
        hideInMenu: true,
      },
      {
        name: '用户管理',
        icon: 'smile',
        path: '/permission/user',
        component: './Permission/User',
        accessible: [':Ram:Staff'],
      },
      {
        name: '用户管理详情',
        path: '/permission/user/details',
        component: './Permission/User/Details',
        hideInMenu: true,
      },
      {
        name: '岗位人员',
        icon: 'smile',
        path: '/permission/station',
        component: './Permission/Station',
        accessible: [':Ram:Duty'],
      },
      {
        name: '岗位人员详情',
        // icon: 'smile',
        path: '/permission/stationDetails',
        component: './Permission/StationDetails',
        hideInMenu: true,
        // accessible: [':Ram:Duty'],
      },
    ],
  },
  {
    path: '/OpenPlatform',
    name: '开放平台',
    icon: 'icon-tidanguanli',
    component: './OpenPlatform',
    // accessible: [':OpenAPI:'],
  },
  {
    path: '/system',
    name: '系统管理',
    icon: 'icon-xitongguanli',
    accessible: [':System:'],
    routes: [
      {
        name: '系统设置',
        icon: 'smile',
        path: '/system/setting',
        component: './System/Setting',
        accessible: [':System:'],
      },
    ],
  },
  // {
  //   path: '/maintenance',
  //   name: 'IT维护',
  //   icon: 'icon-xitongguanli',
  //   accessible: [':IT:'],
  //   routes: [
  //     {
  //       name: '速递同步',
  //       icon: 'smile',
  //       path: '/maintenance/setting',
  //       component: './Maintenance/Setting',
  //       accessible: [':IT:SpeedTransSyncAccess'],
  //     },
  //     {
  //       name: '费用对比',
  //       icon: 'smile',
  //       path: '/maintenance/CostComparison',
  //       component: './Maintenance/CostComparison',
  //       accessible: [':IT:SpeedTransFeeDiffAccess'],
  //     },
  //     {
  //       name: '异常运单监控',
  //       icon: 'smile',
  //       path: '/maintenance/monitoring',
  //       component: './Maintenance/Monitoring',
  //       accessible: [':IT:SpeedTransCompareAccess'],
  //     },
  //     {
  //       name: '日志操作维护',
  //       icon: 'smile',
  //       path: '/maintenance/log',
  //       component: './Maintenance/Log',
  //       // accessible: [':IT:LogNameUpdateAccess'],
  //     },
  //   ],
  // },
  {
    path: '*',
    layout: false,
    component: './404',
  },
  {
    path: '/403',
    layout: false,
    component: './403',
  },
  {
    // name: '钉钉订舱计划',
    path: '/dinglkBooking',
    // icon: 'HomeOutlined',
    hideInMenu: true,
    layout: false,
    headerRender: false,
    footerRender: false,
    menuRender: false,
    menuHeaderRender: false,
    component: './DinglkBooking',
    // wrappers:['@/wrappers/Auth'],
  },
  {
    // name: '钉钉订舱计划',
    path: '/shippingPar',
    // icon: 'HomeOutlined',
    hideInMenu: true,
    layout: false,
    headerRender: false,
    footerRender: false,
    menuRender: false,
    menuHeaderRender: false,
    component: './shippingPar',
    // wrappers:['@/wrappers/Auth'],
  },
];
