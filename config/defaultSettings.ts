// import { Settings as LayoutSettings } from '@ant-design/pro-layout';
import { Settings as LayoutSettings } from '@ant-design/pro-components';
const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // "navTheme": "realDark",
  colorPrimary: '#4071FF',
  layout: 'side',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  title: '物流运输管理',
  pwa: false,
  // logo: '../assets/logo.png',
  iconfontUrl: '//at.alicdn.com/t/c/font_3877009_o0rg48xl08.js',

  // 菜单配置
  // menuRender: false,
  // token: {
  //   // 参见ts声明，demo 见文档，通过token 修改样式
  //   //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F
  // },
};

export default Settings;
